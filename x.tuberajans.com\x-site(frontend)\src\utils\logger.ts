/**
 * Centralized Logging Utility
 * Provides consistent logging across the application with environment-aware behavior
 */

type LogLevel = 'error' | 'warn' | 'info' | 'debug';

interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: string;
  context?: Record<string, any>;
  stack?: string;
}

class Logger {
  private isDevelopment = import.meta.env.DEV;
  private isProduction = import.meta.env.PROD;
  private logs: LogEntry[] = [];

  /**
   * Log an error message
   */
  error(message: string, context?: Record<string, any>, error?: Error): void {
    const logEntry: LogEntry = {
      level: 'error',
      message,
      timestamp: new Date().toISOString(),
      context,
      stack: error?.stack
    };

    this.addLog(logEntry);

    if (this.isDevelopment) {
      console.error(`[ERROR] ${message}`, context, error);
    }

    // In production, send to logging service
    if (this.isProduction) {
      this.sendToLoggingService(logEntry);
    }
  }

  /**
   * Log a warning message
   */
  warn(message: string, context?: Record<string, any>): void {
    const logEntry: LogEntry = {
      level: 'warn',
      message,
      timestamp: new Date().toISOString(),
      context
    };

    this.addLog(logEntry);

    if (this.isDevelopment) {
      console.warn(`[WARN] ${message}`, context);
    }
  }

  /**
   * Log an info message
   */
  info(message: string, context?: Record<string, any>): void {
    const logEntry: LogEntry = {
      level: 'info',
      message,
      timestamp: new Date().toISOString(),
      context
    };

    this.addLog(logEntry);

    if (this.isDevelopment) {
      console.info(`[INFO] ${message}`, context);
    }
  }

  /**
   * Log a debug message (only in development)
   */
  debug(message: string, context?: Record<string, any>): void {
    if (!this.isDevelopment) return;

    const logEntry: LogEntry = {
      level: 'debug',
      message,
      timestamp: new Date().toISOString(),
      context
    };

    this.addLog(logEntry);
    console.debug(`[DEBUG] ${message}`, context);
  }

  /**
   * Add log entry to internal storage
   */
  private addLog(logEntry: LogEntry): void {
    this.logs.push(logEntry);
    
    // Keep only last 100 logs to prevent memory issues
    if (this.logs.length > 100) {
      this.logs = this.logs.slice(-100);
    }
  }

  /**
   * Send log to external logging service (production only)
   */
  private async sendToLoggingService(logEntry: LogEntry): Promise<void> {
    try {
      // TODO: Implement actual logging service integration
      // Example: Sentry, LogRocket, or custom logging endpoint
      
      // For now, just store in sessionStorage for debugging
      const existingLogs = JSON.parse(sessionStorage.getItem('app_logs') || '[]');
      existingLogs.push(logEntry);
      
      // Keep only last 50 logs in storage
      const recentLogs = existingLogs.slice(-50);
      sessionStorage.setItem('app_logs', JSON.stringify(recentLogs));
    } catch (error) {
      // Fallback: don't break the app if logging fails
      if (this.isDevelopment) {
        console.error('Failed to send log to service:', error);
      }
    }
  }

  /**
   * Get all logs (for debugging)
   */
  getLogs(): LogEntry[] {
    return [...this.logs];
  }

  /**
   * Clear all logs
   */
  clearLogs(): void {
    this.logs = [];
    if (typeof sessionStorage !== 'undefined') {
      sessionStorage.removeItem('app_logs');
    }
  }

  /**
   * Export logs as JSON (for debugging/support)
   */
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }
}

// Create singleton instance
export const logger = new Logger();

// Convenience exports
export const logError = logger.error.bind(logger);
export const logWarn = logger.warn.bind(logger);
export const logInfo = logger.info.bind(logger);
export const logDebug = logger.debug.bind(logger);

// Export types
export type { LogLevel, LogEntry };

// Development helper: expose logger to window for debugging
if (import.meta.env.DEV && typeof window !== 'undefined') {
  (window as any).logger = logger;
}
