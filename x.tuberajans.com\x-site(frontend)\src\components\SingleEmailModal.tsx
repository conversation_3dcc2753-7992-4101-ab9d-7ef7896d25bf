import React, { useState, useEffect } from 'react';
import { Modal, Input, Button, message, Form, Select, Spin, Tabs, Upload, List, Popconfirm } from 'antd';
import { MailOutlined, SendOutlined, EyeOutlined, EditOutlined, PaperClipOutlined, SettingOutlined } from '@ant-design/icons';
import { sendSingleEmail, createEmailTemplate, getEmailTemplates, createNewEmailTemplate, updateEmailTemplate, deleteEmailTemplate, EmailTemplate } from '../services/email-service';
import type { UploadFile } from 'antd/es/upload/interface';
import DOMPurify from 'dompurify';

const { TextArea } = Input;
const { Option } = Select;

interface SingleEmailModalProps {
  isOpen: boolean;
  onClose: () => void;
  recipientEmail?: string;
  recipientName?: string;
  defaultSubject?: string;
  mode?: 'single' | 'bulk';
  bulkRecipients?: Array<{email: string, name: string}>;
  onBulkSend?: (data: {subject: string, content: string, recipients: Array<{email: string, name: string}>, attachments: any[]}) => Promise<void>;
  followerRangeFilter?: string[];
}

const SingleEmailModal: React.FC<SingleEmailModalProps> = ({
  isOpen,
  onClose,
  recipientEmail,
  recipientName,
  defaultSubject = 'Tuber Ajans - İletişim',
  mode = 'single',
  bulkRecipients = [],
  onBulkSend,
  followerRangeFilter = []
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  type TemplateType = 'general' | 'weekly_report' | 'custom';
  const [templateType, setTemplateType] = useState<TemplateType>('general');

  // Type guard for template type validation
  const isValidTemplateType = (type: string): type is TemplateType =>
    ['general', 'weekly_report', 'custom'].includes(type);
  const [selectedTemplateName, setSelectedTemplateName] = useState<string>('');
  const [customMessage, setCustomMessage] = useState('');
  const [previewHtml, setPreviewHtml] = useState('');
  const [fromEmail, setFromEmail] = useState('<EMAIL>');
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [newTemplateModalVisible, setNewTemplateModalVisible] = useState(false);
  const [newTemplateForm] = Form.useForm();
  const [editingTemplate, setEditingTemplate] = useState<EmailTemplate | null>(null);
  const [editTemplateForm] = Form.useForm();

  // Bulk mode için state'ler
  const [selectedFollowerRanges, setSelectedFollowerRanges] = useState<string[]>(followerRangeFilter);

  // Modal açıldığında form'u sıfırla
  useEffect(() => {
    if (isOpen && templates.length > 0) {
      const firstTemplate = templates[0];

      // Template content'i process et (sadece bir kez)
      const processedContent = firstTemplate.content.replace(/{name}/g, recipientName);

      form.setFieldsValue({
        subject: firstTemplate.subject,
        templateType: firstTemplate.name,
        customMessage: processedContent,
        fromEmail: '<EMAIL>'
      });

      if (isValidTemplateType(firstTemplate.type)) {
        setTemplateType(firstTemplate.type);
      }
      setSelectedTemplateName(firstTemplate.name);
      setCustomMessage(processedContent);
      setFromEmail('<EMAIL>');
      setFileList([]);

      // Önizlemeyi güncelle
      updatePreview(firstTemplate.type, processedContent);
    }
  }, [isOpen, templates, recipientName, form]);

  // Şablonları yükle
  useEffect(() => {
    const loadTemplates = async () => {
      try {
        const templateList = await getEmailTemplates();
        setTemplates(templateList);

        // İlk şablonu default olarak seç
        if (templateList.length > 0) {
          const firstTemplate = templateList[0];
          setSelectedTemplateName(firstTemplate.name);
          if (isValidTemplateType(firstTemplate.type)) {
            setTemplateType(firstTemplate.type);
          }
        }
      } catch (error) {
        console.error('❌ Şablonları yükleme hatası:', error);
        message.error('Şablonlar yüklenemedi!');
      }
    };

    if (isOpen) {
      loadTemplates();
    }
  }, [isOpen]);

  // Önizleme güncelleme
  const updatePreview = (type: 'general' | 'weekly_report' | 'custom', message: string) => {
    const html = createEmailTemplate(recipientName, message, type);
    setPreviewHtml(html);
  };

  // Template seçimi değiştiğinde önizlemeyi güncelle
  const handleTemplateChange = (templateName: string) => {
    const selectedTemplate = templates.find(t => t.name === templateName);
    if (selectedTemplate && isValidTemplateType(selectedTemplate.type)) {
      setTemplateType(selectedTemplate.type);

      // Form alanlarını güncelle (global replace kullan)
      const processedContent = selectedTemplate.content.replace(/{name}/g, recipientName);
      form.setFieldsValue({
        subject: selectedTemplate.subject,
        customMessage: processedContent
      });

      setCustomMessage(processedContent);
      updatePreview(selectedTemplate.type, processedContent);
    }
  };

  // Mesaj değiştiğinde önizlemeyi güncelle
  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const message = e.target.value;
    setCustomMessage(message);
    updatePreview(templateType, message);
  };

  // E-posta gönderme
  const handleSend = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // Form'daki customMessage'ı HTML formatına çevir (newline'ları <br> yap)
      const emailContent = values.customMessage.replace(/\n/g, '<br>');

      // Dosya eklerini hazırla
      const attachments = await Promise.all(
        fileList.map(async (file) => {
          if (file.originFileObj) {
            const base64 = await new Promise<string>((resolve) => {
              const reader = new FileReader();
              reader.onload = () => resolve(reader.result as string);
              reader.readAsDataURL(file.originFileObj as File);
            });

            return {
              filename: file.name,
              content: base64.split(',')[1], // Base64 kısmını al
              contentType: file.type || 'application/octet-stream'
            };
          }
          return null;
        })
      );

      if (mode === 'bulk' && onBulkSend) {
        // Bulk mode - toplu e-posta gönder
        await onBulkSend({
          subject: values.subject,
          content: emailContent,
          recipients: bulkRecipients,
          attachments: attachments.filter(Boolean) as any[]
        });
        message.success(`${bulkRecipients.length} kişiye e-posta gönderildi!`);
      } else {
        // Single mode - tekil e-posta gönder
        if (!recipientEmail || !recipientName) {
          message.error('Alıcı bilgileri eksik!');
          return;
        }

        await sendSingleEmail({
          email: recipientEmail,
          subject: values.subject,
          content: emailContent,
          fromEmail: values.fromEmail,
          attachments: attachments.filter(Boolean) as any[]
        });
        message.success(`${recipientName} adlı kişiye e-posta başarıyla gönderildi!`);
      }

      onClose();
      form.resetFields();
      setFileList([]);
    } catch (error) {
      console.error('E-posta gönderme hatası:', error);
      message.error('E-posta gönderilirken bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <MailOutlined />
          <span>{mode === 'bulk' ? 'Toplu E-posta Gönder' : 'E-posta Gönder'}</span>
        </div>
      }
      open={isOpen}
      onCancel={onClose}
      width={900}
      footer={[
        <Button key="cancel" onClick={onClose} disabled={loading}>
          İptal
        </Button>,
        <Button
          key="send"
          type="primary"
          icon={<SendOutlined />}
          loading={loading}
          onClick={handleSend}
        >
          Gönder
        </Button>
      ]}
      styles={{ body: { padding: '20px' } }}
    >
      <Tabs
        defaultActiveKey="form"
        items={[
          {
            key: 'form',
            label: (
              <span style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                <EditOutlined />
                E-posta Oluştur
              </span>
            ),
            children: (
              <Form form={form} layout="vertical">
            {mode === 'single' ? (
              <Form.Item label="Alıcı">
                <Input
                  value={`${recipientName} (${recipientEmail})`}
                  disabled
                  style={{ backgroundColor: '#f5f5f5' }}
                />
              </Form.Item>
            ) : (
              <>
                <Form.Item label="Alıcılar">
                  <Input
                    value={`${bulkRecipients.length} kişi seçildi`}
                    disabled
                    style={{ backgroundColor: '#f5f5f5' }}
                  />
                </Form.Item>

                <Form.Item label="Takipçi Sayısı Aralığı">
                  <div className="flex flex-wrap gap-2">
                    <label>
                      <input
                        type="checkbox"
                        checked={selectedFollowerRanges.includes('all')}
                        onChange={(e) => {
                          if(e.target.checked) {
                            setSelectedFollowerRanges(['all']);
                          } else {
                            setSelectedFollowerRanges(selectedFollowerRanges.filter(r => r !== 'all'));
                          }
                        }}
                        style={{ marginRight: '4px' }}
                      />
                      Tümü
                    </label>
                    <label>
                      <input
                        type="checkbox"
                        checked={selectedFollowerRanges.includes('10k_less')}
                        onChange={(e) => {
                          let newRanges = [...selectedFollowerRanges];
                          if(e.target.checked) {
                            newRanges = newRanges.filter(r => r !== 'all').concat(['10k_less']);
                          } else {
                            newRanges = newRanges.filter(r => r !== '10k_less');
                          }
                          setSelectedFollowerRanges(newRanges);
                        }}
                        style={{ marginRight: '4px' }}
                      />
                      10K'dan az
                    </label>
                    <label>
                      <input
                        type="checkbox"
                        checked={selectedFollowerRanges.includes('10k_50k')}
                        onChange={(e) => {
                          let newRanges = [...selectedFollowerRanges];
                          if(e.target.checked) {
                            newRanges = newRanges.filter(r => r !== 'all').concat(['10k_50k']);
                          } else {
                            newRanges = newRanges.filter(r => r !== '10k_50k');
                          }
                          setSelectedFollowerRanges(newRanges);
                        }}
                        style={{ marginRight: '4px' }}
                      />
                      10K - 50K
                    </label>
                    <label>
                      <input
                        type="checkbox"
                        checked={selectedFollowerRanges.includes('50k_100k')}
                        onChange={(e) => {
                          let newRanges = [...selectedFollowerRanges];
                          if(e.target.checked) {
                            newRanges = newRanges.filter(r => r !== 'all').concat(['50k_100k']);
                          } else {
                            newRanges = newRanges.filter(r => r !== '50k_100k');
                          }
                          setSelectedFollowerRanges(newRanges);
                        }}
                        style={{ marginRight: '4px' }}
                      />
                      50K - 100K
                    </label>
                    <label>
                      <input
                        type="checkbox"
                        checked={selectedFollowerRanges.includes('100k_plus')}
                        onChange={(e) => {
                          let newRanges = [...selectedFollowerRanges];
                          if(e.target.checked) {
                            newRanges = newRanges.filter(r => r !== 'all').concat(['100k_plus']);
                          } else {
                            newRanges = newRanges.filter(r => r !== '100k_plus');
                          }
                          setSelectedFollowerRanges(newRanges);
                        }}
                        style={{ marginRight: '4px' }}
                      />
                      100K+
                    </label>
                  </div>
                </Form.Item>
              </>
            )}

            <Form.Item
              label="Gönderen E-posta"
              name="fromEmail"
              rules={[{ required: true, message: 'Lütfen gönderen e-posta seçin' }]}
            >
              <Select
                value={fromEmail}
                onChange={setFromEmail}
                placeholder="Gönderen e-posta seçin"
              >
                <Option value="<EMAIL>">Tuber Ajans | İş Birliği Ekibi (<EMAIL>)</Option>
                <Option value="<EMAIL>">Tuber Ajans | Rapor Ekibi (<EMAIL>)</Option>
                <Option value="<EMAIL>">Tuber Ajans | Destek Ekibi (<EMAIL>)</Option>
                <Option value="<EMAIL>">Tuber Ajans | Satış Ekibi (<EMAIL>)</Option>
                <Option value="<EMAIL>">Tuber Ajans | Pazarlama Ekibi (<EMAIL>)</Option>
                <Option value="<EMAIL>">Tuber Ajans | Yönetim Ekibi (<EMAIL>)</Option>
              </Select>
            </Form.Item>

            <Form.Item
              label="Konu"
              name="subject"
              rules={[{ required: true, message: 'Lütfen e-posta konusunu girin' }]}
            >
              <Input placeholder="E-posta konusu" />
            </Form.Item>

            <Form.Item label="E-posta Şablonu" name="templateType">
              <Select
                value={selectedTemplateName}
                onChange={(value) => {
                  if (value === 'new_template') {
                    setNewTemplateModalVisible(true);
                    return;
                  }

                  setSelectedTemplateName(value);
                  handleTemplateChange(value);
                }}
                placeholder="Şablon seçin"
                dropdownRender={menu => (
                  <>
                    {menu}
                    <div style={{ borderTop: '1px solid #f0f0f0', padding: '8px' }}>
                      <Button
                        type="link"
                        onClick={() => setNewTemplateModalVisible(true)}
                        style={{ width: '100%', textAlign: 'left' }}
                      >
                        + Yeni Şablon Ekle
                      </Button>
                      <div style={{
                        marginTop: '8px',
                        padding: '8px',
                        backgroundColor: '#f8f9fa',
                        borderRadius: '4px',
                        fontSize: '12px',
                        color: '#666'
                      }}>
                        <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>Kullanılabilir Değişkenler:</div>
                        <div><code>{'{username}'}</code> - Kullanıcı adı</div>
                        <div><code>{'{email}'}</code> - E-posta adresi</div>
                        <div><code>{'{followers}'}</code> - Takipçi sayısı</div>
                        <div><code>{'{platform}'}</code> - Platform (Instagram, TikTok, vb.)</div>
                      </div>
                    </div>
                  </>
                )}
              >
                {templates.map(template => (
                  <Option key={template.id} value={template.name}>
                    {template.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              label="Mesaj İçeriği"
              name="customMessage"
              rules={[{ required: true, message: 'Lütfen mesaj içeriğini girin' }]}
            >
              <TextArea
                rows={6}
                placeholder="Mesajınızı buraya yazın..."
                value={customMessage}
                onChange={handleMessageChange}
              />
            </Form.Item>

            <Form.Item label="Dosya Ekleri">
              <Upload
                fileList={fileList}
                onChange={({ fileList: newFileList }) => setFileList(newFileList)}
                beforeUpload={() => false} // Otomatik upload'u engelle
                accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                multiple
              >
                <Button icon={<PaperClipOutlined />}>
                  Dosya Ekle (PDF, JPEG, PNG, DOC)
                </Button>
              </Upload>
              {fileList.length > 0 && (
                <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
                  {fileList.length} dosya seçildi
                </div>
              )}
            </Form.Item>
          </Form>
            )
          },
          {
            key: 'preview',
            label: (
              <span style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                <EyeOutlined />
                Önizleme
              </span>
            ),
            children: (
              <div>
                <h4 style={{ marginBottom: 16, color: '#666', textAlign: 'center' }}>
                  📧 E-posta Önizlemesi
                </h4>
                <div
                  style={{
                    border: '1px solid #d9d9d9',
                    borderRadius: 8,
                    backgroundColor: '#f8f9fa',
                    minHeight: 500,
                    maxHeight: 600,
                    overflow: 'auto',
                    padding: '20px'
                  }}
                >
                  {loading ? (
                    <div style={{ textAlign: 'center', padding: 50 }}>
                      <Spin />
                    </div>
                  ) : (
                    <div dangerouslySetInnerHTML={{
                      __html: DOMPurify.sanitize(previewHtml, {
                        ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'u', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'ul', 'ol', 'li', 'a'],
                        ALLOWED_ATTR: ['href', 'target', 'style'],
                        ALLOW_DATA_ATTR: false
                      })
                    }} />
                  )}
                </div>
              </div>
            )
          },
          {
            key: '3',
            label: (
              <span style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                <SettingOutlined />
                Şablon Yönetimi
              </span>
            ),
            children: (
              <div>

                <div style={{ marginBottom: '16px' }}>
                  <Button
                    type="primary"
                    onClick={() => setNewTemplateModalVisible(true)}
                    style={{ marginBottom: '16px' }}
                  >
                    Yeni Şablon Ekle
                  </Button>
                </div>

                <List
                  dataSource={templates}
                  renderItem={(template) => (
                    <List.Item
                      actions={[
                        <Button
                          key="edit"
                          type="link"
                          onClick={() => {
                            setEditingTemplate(template);
                            editTemplateForm.setFieldsValue({
                              name: template.name,
                              subject: template.subject,
                              content: template.content
                            });
                          }}
                        >
                          Düzenle
                        </Button>,
                        <Popconfirm
                          key="delete"
                          title="Bu şablonu silmek istediğinizden emin misiniz?"
                          onConfirm={async () => {
                            try {
                              await deleteEmailTemplate(template.id);
                              message.success('Şablon silindi!');
                              const templateList = await getEmailTemplates();
                              setTemplates(templateList);
                            } catch (error) {
                              message.error('Şablon silinemedi!');
                            }
                          }}
                        >
                          <Button type="link" danger>Sil</Button>
                        </Popconfirm>
                      ]}
                    >
                      <List.Item.Meta
                        title={template.name}
                        description={`Konu: ${template.subject}`}
                      />
                    </List.Item>
                  )}
                />

                {templates.length === 0 && (
                  <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
                    Henüz şablon bulunmuyor.
                  </div>
                )}
              </div>
            )
          }
        ]}
      />

      {/* Yeni Şablon Modal'ı */}
      <Modal
        title="Yeni E-posta Şablonu"
        open={newTemplateModalVisible}
        onCancel={() => {
          setNewTemplateModalVisible(false);
          newTemplateForm.resetFields();
        }}
        footer={[
          <Button key="cancel" onClick={() => setNewTemplateModalVisible(false)}>
            İptal
          </Button>,
          <Button
            key="save"
            type="primary"
            loading={loading}
            onClick={async () => {
              try {
                const values = await newTemplateForm.validateFields();
                setLoading(true);

                await createNewEmailTemplate({
                  name: values.name,
                  type: 'custom',
                  subject: values.subject,
                  content: values.content
                });

                message.success('Şablon başarıyla oluşturuldu!');
                setNewTemplateModalVisible(false);
                newTemplateForm.resetFields();

                // Şablonları yeniden yükle
                const templateList = await getEmailTemplates();
                setTemplates(templateList);
              } catch (error) {
                console.error('Şablon oluşturma hatası:', error);
                message.error('Şablon oluşturulamadı!');
              } finally {
                setLoading(false);
              }
            }}
          >
            Kaydet
          </Button>
        ]}
      >
        <Form form={newTemplateForm} layout="vertical">
          <Form.Item
            label="Şablon Adı"
            name="name"
            rules={[{ required: true, message: 'Şablon adı gerekli' }]}
          >
            <Input placeholder="Örn: Hoş Geldin Mesajı" />
          </Form.Item>

          <Form.Item
            label="E-posta Konusu"
            name="subject"
            rules={[{ required: true, message: 'E-posta konusu gerekli' }]}
          >
            <Input placeholder="Örn: Hoş Geldiniz!" />
          </Form.Item>

          <Form.Item
            label="Mesaj İçeriği"
            name="content"
            rules={[{ required: true, message: 'Mesaj içeriği gerekli' }]}
          >
            <TextArea
              rows={6}
              placeholder="Mesaj içeriğinizi yazın..."
            />
            <div style={{
              marginTop: '8px',
              padding: '8px',
              backgroundColor: '#f8f9fa',
              borderRadius: '4px',
              fontSize: '12px',
              color: '#666'
            }}>
              <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>Kullanılabilir Değişkenler:</div>
              <div><code>{'{username}'}</code> - Kullanıcı adı</div>
              <div><code>{'{email}'}</code> - E-posta adresi</div>
              <div><code>{'{followers}'}</code> - Takipçi sayısı</div>
              <div><code>{'{platform}'}</code> - Platform (Instagram, TikTok, vb.)</div>
            </div>
          </Form.Item>
        </Form>
      </Modal>

      {/* Şablon Düzenleme Modal'ı */}
      <Modal
        title="Şablon Düzenle"
        open={!!editingTemplate}
        onCancel={() => {
          setEditingTemplate(null);
          editTemplateForm.resetFields();
        }}
        footer={[
          <Button key="cancel" onClick={() => setEditingTemplate(null)}>
            İptal
          </Button>,
          <Button
            key="save"
            type="primary"
            loading={loading}
            onClick={async () => {
              try {
                const values = await editTemplateForm.validateFields();
                setLoading(true);

                await updateEmailTemplate({
                  ...editingTemplate!,
                  name: values.name,
                  subject: values.subject,
                  content: values.content
                });

                message.success('Şablon güncellendi!');
                setEditingTemplate(null);
                editTemplateForm.resetFields();

                // Şablonları yeniden yükle
                const templateList = await getEmailTemplates();
                setTemplates(templateList);
              } catch (error) {
                console.error('Şablon güncelleme hatası:', error);
                message.error('Şablon güncellenemedi!');
              } finally {
                setLoading(false);
              }
            }}
          >
            Güncelle
          </Button>
        ]}
      >
        <Form form={editTemplateForm} layout="vertical">
          <Form.Item
            label="Şablon Adı"
            name="name"
            rules={[{ required: true, message: 'Şablon adı gerekli' }]}
          >
            <Input placeholder="Örn: Hoş Geldin Mesajı" />
          </Form.Item>

          <Form.Item
            label="E-posta Konusu"
            name="subject"
            rules={[{ required: true, message: 'E-posta konusu gerekli' }]}
          >
            <Input placeholder="Örn: Hoş Geldiniz!" />
          </Form.Item>

          <Form.Item
            label="Mesaj İçeriği"
            name="content"
            rules={[{ required: true, message: 'Mesaj içeriği gerekli' }]}
          >
            <TextArea
              rows={6}
              placeholder="Mesaj içeriğinizi yazın..."
            />
            <div style={{
              marginTop: '8px',
              padding: '8px',
              backgroundColor: '#f8f9fa',
              borderRadius: '4px',
              fontSize: '12px',
              color: '#666'
            }}>
              <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>Kullanılabilir Değişkenler:</div>
              <div><code>{'{username}'}</code> - Kullanıcı adı</div>
              <div><code>{'{email}'}</code> - E-posta adresi</div>
              <div><code>{'{followers}'}</code> - Takipçi sayısı</div>
              <div><code>{'{platform}'}</code> - Platform (Instagram, TikTok, vb.)</div>
            </div>
          </Form.Item>
        </Form>
      </Modal>
    </Modal>
  );
};

export default SingleEmailModal;
