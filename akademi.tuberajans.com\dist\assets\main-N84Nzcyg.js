import{j as e,A as st,m as le}from"./ui-BgdiPixG.js";import{b as Pa,r as o,a as Oa}from"./vendor-BKU87Gzz.js";import{u as be,L as ee,a as it,O as Ia,b as Qt,R as $a,c as ae,B as Ha}from"./router-DZ-waZVq.js";import{G as P}from"./icons-19xgt3qL.js";import{R as Nt,B as Ua,C as _t,X as Ft,Y as St,T as Et,a as Te,L as Va,b as Ka}from"./charts-DyYsho4k.js";(function(){const a=document.createElement("link").relList;if(a&&a.supports&&a.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))s(i);new MutationObserver(i=>{for(const l of i)if(l.type==="childList")for(const n of l.addedNodes)n.tagName==="LINK"&&n.rel==="modulepreload"&&s(n)}).observe(document,{childList:!0,subtree:!0});function r(i){const l={};return i.integrity&&(l.integrity=i.integrity),i.referrerPolicy&&(l.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?l.credentials="include":i.crossOrigin==="anonymous"?l.credentials="omit":l.credentials="same-origin",l}function s(i){if(i.ep)return;i.ep=!0;const l=r(i);fetch(i.href,l)}})();var nt={},Tt=Pa;nt.createRoot=Tt.createRoot,nt.hydrateRoot=Tt.hydrateRoot;function Pe(t){return P({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M448,209.91a210.06,210.06,0,0,1-122.77-39.25V349.38A162.55,162.55,0,1,1,185,188.31V278.2a74.62,74.62,0,1,0,52.23,71.18V0l88,0a121.18,121.18,0,0,0,1.86,22.17h0A122.18,122.18,0,0,0,381,102.39a121.43,121.43,0,0,0,67,20.14Z"},child:[]}]})(t)}function Ya(t){return P({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 224.1-99.6 224.1-222 0-59.3-25.2-115-67.1-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56.2 81.2 56.1 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z"},child:[]}]})(t)}function ye(t){return P({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M257.5 445.1l-22.2 22.2c-9.4 9.4-24.6 9.4-33.9 0L7 273c-9.4-9.4-9.4-24.6 0-33.9L201.4 44.7c9.4-9.4 24.6-9.4 33.9 0l22.2 22.2c9.5 9.5 9.3 25-.4 34.3L136.6 216H424c13.3 0 24 10.7 24 24v32c0 13.3-10.7 24-24 24H136.6l120.5 114.8c9.8 9.3 10 24.8.4 34.3z"},child:[]}]})(t)}function Ga(t){return P({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M16 132h416c8.837 0 16-7.163 16-16V76c0-8.837-7.163-16-16-16H16C7.163 60 0 67.163 0 76v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16z"},child:[]}]})(t)}function ze(t){return P({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M224 512c35.32 0 63.97-28.65 63.97-64H160.03c0 35.35 28.65 64 63.97 64zm215.39-149.71c-19.32-20.76-55.47-51.99-55.47-154.29 0-77.7-54.48-139.9-127.94-155.16V32c0-17.67-14.32-32-31.98-32s-31.98 14.33-31.98 32v20.84C118.56 68.1 64.08 130.3 64.08 208c0 102.3-36.15 133.53-55.47 154.29-6 6.45-8.66 14.16-8.61 21.71.11 16.4 12.98 32 32.1 32h383.8c19.12 0 32-15.6 32.1-32 .05-7.55-2.61-15.27-8.61-21.71z"},child:[]}]})(t)}function Oe(t){return P({attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M576 240c0-23.63-12.95-44.04-32-55.12V32.01C544 23.26 537.02 0 512 0c-7.12 0-14.19 2.38-19.98 7.02l-85.03 68.03C364.28 109.19 310.66 128 256 128H64c-35.35 0-64 28.65-64 64v96c0 35.35 28.65 64 64 64h33.7c-1.39 10.48-2.18 21.14-2.18 32 0 39.77 9.26 77.35 25.56 110.94 5.19 10.69 16.52 17.06 28.4 17.06h74.28c26.05 0 41.69-29.84 25.9-50.56-16.4-21.52-26.15-48.36-26.15-77.44 0-11.11 1.62-21.79 4.41-32H256c54.66 0 108.28 18.81 150.98 52.95l85.03 68.03a32.023 32.023 0 0 0 19.98 7.02c24.92 0 32-22.78 32-32V295.13C563.05 284.04 576 263.63 576 240zm-96 141.42l-33.05-26.44C392.95 311.78 325.12 288 256 288v-96c69.12 0 136.95-23.78 190.95-66.98L480 98.58v282.84z"},child:[]}]})(t)}function Me(t){return P({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M0 464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V192H0v272zm320-196c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zm0 128c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zM192 268c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zm0 128c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12h-40c-6.6 0-12-5.4-12-12v-40zM64 268c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12H76c-6.6 0-12-5.4-12-12v-40zm0 128c0-6.6 5.4-12 12-12h40c6.6 0 12 5.4 12 12v40c0 6.6-5.4 12-12 12H76c-6.6 0-12-5.4-12-12v-40zM400 64h-48V16c0-8.8-7.2-16-16-16h-32c-8.8 0-16 7.2-16 16v48H160V16c0-8.8-7.2-16-16-16h-32c-8.8 0-16 7.2-16 16v48H48C21.5 64 0 85.5 0 112v48h448v-48c0-26.5-21.5-48-48-48z"},child:[]}]})(t)}function Wa(t){return P({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M0 464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48V192H0v272zm64-192c0-8.8 7.2-16 16-16h96c8.8 0 16 7.2 16 16v96c0 8.8-7.2 16-16 16H80c-8.8 0-16-7.2-16-16v-96zM400 64h-48V16c0-8.8-7.2-16-16-16h-32c-8.8 0-16 7.2-16 16v48H160V16c0-8.8-7.2-16-16-16h-32c-8.8 0-16 7.2-16 16v48H48C21.5 64 0 85.5 0 112v48h448v-48c0-26.5-21.5-48-48-48z"},child:[]}]})(t)}function zt(t){return P({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M504 256c0 136.967-111.033 248-248 248S8 392.967 8 256 119.033 8 256 8s248 111.033 248 248zM227.314 387.314l184-184c6.248-6.248 6.248-16.379 0-22.627l-22.627-22.627c-6.248-6.249-16.379-6.249-22.628 0L216 308.118l-70.059-70.059c-6.248-6.248-16.379-6.248-22.628 0l-22.627 22.627c-6.248 6.248-6.248 16.379 0 22.627l104 104c6.249 6.249 16.379 6.249 22.628.001z"},child:[]}]})(t)}function qa(t){return P({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M505 174.8l-39.6-39.6c-9.4-9.4-24.6-9.4-33.9 0L192 374.7 80.6 263.2c-9.4-9.4-24.6-9.4-33.9 0L7 302.9c-9.4 9.4-9.4 24.6 0 34L175 505c9.4 9.4 24.6 9.4 33.9 0l296-296.2c9.4-9.5 9.4-24.7.1-34zm-324.3 106c6.2 6.3 16.4 6.3 22.6 0l208-208.2c6.2-6.3 6.2-16.4 0-22.6L366.1 4.7c-6.2-6.3-16.4-6.3-22.6 0L192 156.2l-55.4-55.5c-6.2-6.3-16.4-6.3-22.6 0L68.7 146c-6.2 6.3-6.2 16.4 0 22.6l112 112.2z"},child:[]}]})(t)}function ea(t){return P({attr:{viewBox:"0 0 320 512"},child:[{tag:"path",attr:{d:"M34.52 239.03L228.87 44.69c9.37-9.37 24.57-9.37 33.94 0l22.67 22.67c9.36 9.36 9.37 24.52.04 33.9L131.49 256l154.02 154.75c9.34 9.38 9.32 24.54-.04 33.9l-22.67 22.67c-9.37 9.37-24.57 9.37-33.94 0L34.52 272.97c-9.37-9.37-9.37-24.57 0-33.94z"},child:[]}]})(t)}function ta(t){return P({attr:{viewBox:"0 0 320 512"},child:[{tag:"path",attr:{d:"M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z"},child:[]}]})(t)}function Ce(t){return P({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M336 64h-80c0-35.3-28.7-64-64-64s-64 28.7-64 64H48C21.5 64 0 85.5 0 112v352c0 26.5 21.5 48 48 48h288c26.5 0 48-21.5 48-48V112c0-26.5-21.5-48-48-48zM96 424c-13.3 0-24-10.7-24-24s10.7-24 24-24 24 10.7 24 24-10.7 24-24 24zm0-96c-13.3 0-24-10.7-24-24s10.7-24 24-24 24 10.7 24 24-10.7 24-24 24zm0-96c-13.3 0-24-10.7-24-24s10.7-24 24-24 24 10.7 24 24-10.7 24-24 24zm96-192c13.3 0 24 10.7 24 24s-10.7 24-24 24-24-10.7-24-24 10.7-24 24-24zm128 368c0 4.4-3.6 8-8 8H168c-4.4 0-8-3.6-8-8v-16c0-4.4 3.6-8 8-8h144c4.4 0 8 3.6 8 8v16zm0-96c0 4.4-3.6 8-8 8H168c-4.4 0-8-3.6-8-8v-16c0-4.4 3.6-8 8-8h144c4.4 0 8 3.6 8 8v16zm0-96c0 4.4-3.6 8-8 8H168c-4.4 0-8-3.6-8-8v-16c0-4.4 3.6-8 8-8h144c4.4 0 8 3.6 8 8v16z"},child:[]}]})(t)}function lt(t){return P({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256,8C119,8,8,119,8,256S119,504,256,504,504,393,504,256,393,8,256,8Zm92.49,313h0l-20,25a16,16,0,0,1-22.49,2.5h0l-67-49.72a40,40,0,0,1-15-31.23V112a16,16,0,0,1,16-16h32a16,16,0,0,1,16,16V256l58,42.5A16,16,0,0,1,348.49,321Z"},child:[]}]})(t)}function aa(t){return P({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256 32C114.6 32 0 125.1 0 240c0 49.6 21.4 95 57 130.7C44.5 421.1 2.7 466 2.2 466.5c-2.2 2.3-2.8 5.7-1.5 8.7S4.8 480 8 480c66.3 0 116-31.8 140.6-51.4 32.7 12.3 69 19.4 107.4 19.4 141.4 0 256-93.1 256-208S397.4 32 256 32z"},child:[]}]})(t)}function Ja(t){return P({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M216 0h80c13.3 0 24 10.7 24 24v168h87.7c17.8 0 26.7 21.5 14.1 34.1L269.7 378.3c-7.5 7.5-19.8 7.5-27.3 0L90.1 226.1c-12.6-12.6-3.7-34.1 14.1-34.1H192V24c0-13.3 10.7-24 24-24zm296 376v112c0 13.3-10.7 24-24 24H24c-13.3 0-24-10.7-24-24V376c0-13.3 10.7-24 24-24h146.7l49 49c20.1 20.1 52.5 20.1 72.6 0l49-49H488c13.3 0 24 10.7 24 24zm-124 88c0-11-9-20-20-20s-20 9-20 20 9 20 20 20 20-9 20-20zm64 0c0-11-9-20-20-20s-20 9-20 20 9 20 20 20 20-9 20-20z"},child:[]}]})(t)}function Za(t){return P({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M328 256c0 39.8-32.2 72-72 72s-72-32.2-72-72 32.2-72 72-72 72 32.2 72 72zm104-72c-39.8 0-72 32.2-72 72s32.2 72 72 72 72-32.2 72-72-32.2-72-72-72zm-352 0c-39.8 0-72 32.2-72 72s32.2 72 72 72 72-32.2 72-72-32.2-72-72-72z"},child:[]}]})(t)}function Xa(t){return P({attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M569.517 440.013C587.975 472.007 564.806 512 527.94 512H48.054c-36.937 0-59.999-40.055-41.577-71.987L246.423 23.985c18.467-32.009 64.72-31.951 83.154 0l239.94 416.028zM288 354c-25.405 0-46 20.595-46 46s20.595 46 46 46 46-20.595 46-46-20.595-46-46-46zm-43.673-165.346l7.418 136c.347 6.364 5.609 11.346 11.982 11.346h48.546c6.373 0 11.635-4.982 11.982-11.346l7.418-136c.375-6.874-5.098-12.654-11.982-12.654h-63.383c-6.884 0-12.356 5.78-11.981 12.654z"},child:[]}]})(t)}function Qa(t){return P({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M432,320H400a16,16,0,0,0-16,16V448H64V128H208a16,16,0,0,0,16-16V80a16,16,0,0,0-16-16H48A48,48,0,0,0,0,112V464a48,48,0,0,0,48,48H400a48,48,0,0,0,48-48V336A16,16,0,0,0,432,320ZM488,0h-128c-21.37,0-32.05,25.91-17,41l35.73,35.73L135,320.37a24,24,0,0,0,0,34L157.67,377a24,24,0,0,0,34,0L435.28,133.32,471,169c15,15,41,4.5,41-17V24A24,24,0,0,0,488,0Z"},child:[]}]})(t)}function er(t){return P({attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M320 400c-75.85 0-137.25-58.71-142.9-133.11L72.2 185.82c-13.79 17.3-26.48 35.59-36.72 55.59a32.35 32.35 0 0 0 0 29.19C89.71 376.41 197.07 448 320 448c26.91 0 52.87-4 77.89-10.46L346 397.39a144.13 144.13 0 0 1-26 2.61zm313.82 58.1l-110.55-85.44a331.25 331.25 0 0 0 81.25-102.07 32.35 32.35 0 0 0 0-29.19C550.29 135.59 442.93 64 320 64a308.15 308.15 0 0 0-147.32 37.7L45.46 3.37A16 16 0 0 0 23 6.18L3.37 31.45A16 16 0 0 0 6.18 53.9l588.36 454.73a16 16 0 0 0 22.46-2.81l19.64-25.27a16 16 0 0 0-2.82-22.45zm-183.72-142l-39.3-30.38A94.75 94.75 0 0 0 416 256a94.76 94.76 0 0 0-121.31-92.21A47.65 47.65 0 0 1 304 192a46.64 46.64 0 0 1-1.54 10l-73.61-56.89A142.31 142.31 0 0 1 320 112a143.92 143.92 0 0 1 144 144c0 21.63-5.29 41.79-13.9 60.11z"},child:[]}]})(t)}function ra(t){return P({attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M572.52 241.4C518.29 135.59 410.93 64 288 64S57.68 135.64 3.48 241.41a32.35 32.35 0 0 0 0 29.19C57.71 376.41 165.07 448 288 448s230.32-71.64 284.52-177.41a32.35 32.35 0 0 0 0-29.19zM288 400a144 144 0 1 1 144-144 143.93 143.93 0 0 1-144 144zm0-240a95.31 95.31 0 0 0-25.31 3.79 47.85 47.85 0 0 1-66.9 66.9A95.78 95.78 0 1 0 288 160z"},child:[]}]})(t)}function tr(t){return P({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M181.9 256.1c-5-16-4.9-46.9-2-46.9 8.4 0 7.6 36.9 2 46.9zm-1.7 47.2c-7.7 20.2-17.3 43.3-28.4 62.7 18.3-7 39-17.2 62.9-21.9-12.7-9.6-24.9-23.4-34.5-40.8zM86.1 428.1c0 .8 13.2-5.4 34.9-40.2-6.7 6.3-29.1 24.5-34.9 40.2zM248 160h136v328c0 13.3-10.7 24-24 24H24c-13.3 0-24-10.7-24-24V24C0 10.7 10.7 0 24 0h200v136c0 13.2 10.8 24 24 24zm-8 171.8c-20-12.2-33.3-29-42.7-53.8 4.5-18.5 11.6-46.6 6.2-64.2-4.7-29.4-42.4-26.5-47.8-6.8-5 18.3-.4 44.1 8.1 77-11.6 27.6-28.7 64.6-40.8 85.8-.1 0-.1.1-.2.1-27.1 13.9-73.6 44.5-54.5 68 5.6 6.9 16 10 21.5 10 17.9 0 35.7-18 61.1-61.8 25.8-8.5 54.1-19.1 79-23.2 21.7 11.8 47.1 19.5 64 19.5 29.2 0 31.2-32 19.7-43.4-13.9-13.6-54.3-9.7-73.6-7.2zM377 105L279 7c-4.5-4.5-10.6-7-17-7h-6v128h128v-6.1c0-6.3-2.5-12.4-7-16.9zm-74.1 255.3c4.1-2.7-2.5-11.9-42.8-9 37.1 15.8 42.8 9 42.8 9z"},child:[]}]})(t)}function Le(t){return P({attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M622.34 153.2L343.4 67.5c-15.2-4.67-31.6-4.67-46.79 0L17.66 153.2c-23.54 7.23-23.54 38.36 0 45.59l48.63 14.94c-10.67 13.19-17.23 29.28-17.88 46.9C38.78 266.15 32 276.11 32 288c0 10.78 5.68 19.85 13.86 25.65L20.33 428.53C18.11 438.52 25.71 448 35.94 448h56.11c10.24 0 17.84-9.48 15.62-19.47L82.14 313.65C90.32 307.85 96 298.78 96 288c0-11.57-6.47-21.25-15.66-26.87.76-15.02 8.44-28.3 20.69-36.72L296.6 284.5c9.06 2.78 26.44 6.25 46.79 0l278.95-85.7c23.55-7.24 23.55-38.36 0-45.6zM352.79 315.09c-28.53 8.76-52.84 3.92-65.59 0l-145.02-44.55L128 384c0 35.35 85.96 64 192 64s192-28.65 192-64l-14.18-113.47-145.03 44.56z"},child:[]}]})(t)}function Ie(t){return P({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M462.3 62.6C407.5 15.9 326 24.3 275.7 76.2L256 96.5l-19.7-20.3C186.1 24.3 104.5 15.9 49.7 62.6c-62.8 53.6-66.1 149.8-9.9 207.9l193.5 199.8c12.5 12.9 32.8 12.9 45.3 0l193.5-199.8c56.3-58.1 53-154.3-9.8-207.9z"},child:[]}]})(t)}function ar(t){return P({attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M280.37 148.26L96 300.11V464a16 16 0 0 0 16 16l112.06-.29a16 16 0 0 0 15.92-16V368a16 16 0 0 1 16-16h64a16 16 0 0 1 16 16v95.64a16 16 0 0 0 16 16.05L464 480a16 16 0 0 0 16-16V300L295.67 148.26a12.19 12.19 0 0 0-15.3 0zM571.6 251.47L488 182.56V44.05a12 12 0 0 0-12-12h-56a12 12 0 0 0-12 12v72.61L318.47 43a48 48 0 0 0-61 0L4.34 251.47a12 12 0 0 0-1.6 16.9l25.5 31A12 12 0 0 0 45.15 301l235.22-193.74a12.19 12.19 0 0 1 15.3 0L530.9 301a12 12 0 0 0 16.9-1.6l25.5-31a12 12 0 0 0-1.7-16.93z"},child:[]}]})(t)}function rr(t){return P({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M464 448H48c-26.51 0-48-21.49-48-48V112c0-26.51 21.49-48 48-48h416c26.51 0 48 21.49 48 48v288c0 26.51-21.49 48-48 48zM112 120c-30.928 0-56 25.072-56 56s25.072 56 56 56 56-25.072 56-56-25.072-56-56-56zM64 384h384V272l-87.515-87.515c-4.686-4.686-12.284-4.686-16.971 0L208 320l-55.515-55.515c-4.686-4.686-12.284-4.686-16.971 0L64 336v48z"},child:[]}]})(t)}function Ne(t){return P({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M172.268 501.67C26.97 291.031 0 269.413 0 192 0 85.961 85.961 0 192 0s192 85.961 192 192c0 77.413-26.97 99.031-172.268 309.67-9.535 13.774-29.93 13.773-39.464 0zM192 272c44.183 0 80-35.817 80-80s-35.817-80-80-80-80 35.817-80 80 35.817 80 80 80z"},child:[]}]})(t)}function sr(t){return P({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M476 3.2L12.5 270.6c-18.1 10.4-15.8 35.6 2.2 43.2L121 358.4l287.3-253.2c5.5-4.9 13.3 2.6 8.6 8.3L176 407v80.5c0 23.6 28.5 32.9 42.5 15.8L282 426l124.6 52.2c14.2 6 30.4-2.9 33-18.2l72-432C515 7.8 493.3-6.8 476 3.2z"},child:[]}]})(t)}function ir(t){return P({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M43.246 466.142c-58.43-60.289-57.341-157.511 1.386-217.581L254.392 34c44.316-45.332 116.351-45.336 160.671 0 43.89 44.894 43.943 117.329 0 162.276L232.214 383.128c-29.855 30.537-78.633 30.111-107.982-.998-28.275-29.97-27.368-77.473 1.452-106.953l143.743-146.835c6.182-6.314 16.312-6.422 22.626-.241l22.861 22.379c6.315 6.182 6.422 16.312.241 22.626L171.427 319.927c-4.932 5.045-5.236 13.428-.648 18.292 4.372 4.634 11.245 4.711 15.688.165l182.849-186.851c19.613-20.062 19.613-52.725-.011-72.798-19.189-19.627-49.957-19.637-69.154 0L90.39 293.295c-34.763 35.56-35.299 93.12-1.191 128.313 34.01 35.093 88.985 35.137 123.058.286l172.06-175.999c6.177-6.319 16.307-6.433 22.626-.256l22.877 22.364c6.319 6.177 6.434 16.307.256 22.626l-172.06 175.998c-59.576 60.938-155.943 60.216-214.77-.485z"},child:[]}]})(t)}function Ct(t){return P({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M497.9 142.1l-46.1 46.1c-4.7 4.7-12.3 4.7-17 0l-111-111c-4.7-4.7-4.7-12.3 0-17l46.1-46.1c18.7-18.7 49.1-18.7 67.9 0l60.1 60.1c18.8 18.7 18.8 49.1 0 67.9zM284.2 99.8L21.6 362.4.4 483.9c-2.9 16.4 11.4 30.6 27.8 27.8l121.5-21.3 262.6-262.6c4.7-4.7 4.7-12.3 0-17l-111-111c-4.8-4.7-12.4-4.7-17.1 0zM124.1 339.9c-5.5-5.5-5.5-14.3 0-19.8l154-154c5.5-5.5 14.3-5.5 19.8 0s5.5 14.3 0 19.8l-154 154c-5.5 5.5-14.3 5.5-19.8 0zM88 424h48v36.3l-64.5 11.3-31.1-31.1L51.7 376H88v48z"},child:[]}]})(t)}function nr(t){return P({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm115.7 272l-176 101c-15.8 8.8-35.7-2.5-35.7-21V152c0-18.4 19.8-29.8 35.7-21l176 107c16.4 9.2 16.4 32.9 0 42z"},child:[]}]})(t)}function Qe(t){return P({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M416 208H272V64c0-17.67-14.33-32-32-32h-32c-17.67 0-32 14.33-32 32v144H32c-17.67 0-32 14.33-32 32v32c0 17.67 14.33 32 32 32h144v144c0 17.67 14.33 32 32 32h32c17.67 0 32-14.33 32-32V304h144c17.67 0 32-14.33 32-32v-32c0-17.67-14.33-32-32-32z"},child:[]}]})(t)}function lr(t){return P({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M505 442.7L405.3 343c-4.5-4.5-10.6-7-17-7H372c27.6-35.3 44-79.7 44-128C416 93.1 322.9 0 208 0S0 93.1 0 208s93.1 208 208 208c48.3 0 92.7-16.4 128-44v16.3c0 6.4 2.5 12.5 7 17l99.7 99.7c9.4 9.4 24.6 9.4 33.9 0l28.3-28.3c9.4-9.4 9.4-24.6.1-34zM208 336c-70.7 0-128-57.2-128-128 0-70.7 57.2-128 128-128 70.7 0 128 57.2 128 128 0 70.7-57.2 128-128 128z"},child:[]}]})(t)}function or(t){return P({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M503.691 189.836L327.687 37.851C312.281 24.546 288 35.347 288 56.015v80.053C127.371 137.907 0 170.1 0 322.326c0 61.441 39.581 122.309 83.333 154.132 13.653 9.931 33.111-2.533 28.077-18.631C66.066 312.814 132.917 274.316 288 272.085V360c0 20.7 24.3 31.453 39.687 18.164l176.004-152c11.071-9.562 11.086-26.753 0-36.328z"},child:[]}]})(t)}function dr(t){return P({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M466.5 83.7l-192-80a48.15 48.15 0 0 0-36.9 0l-192 80C27.7 91.1 16 108.6 16 128c0 198.5 114.5 335.7 221.5 380.3 11.8 4.9 25.1 4.9 36.9 0C360.1 472.6 496 349.3 496 128c0-19.4-11.7-36.9-29.5-44.3zM256.1 446.3l-.1-381 175.9 73.3c-3.3 151.4-82.1 261.1-175.8 307.7z"},child:[]}]})(t)}function cr(t){return P({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M416 448h-84c-6.6 0-12-5.4-12-12v-40c0-6.6 5.4-12 12-12h84c17.7 0 32-14.3 32-32V160c0-17.7-14.3-32-32-32h-84c-6.6 0-12-5.4-12-12V76c0-6.6 5.4-12 12-12h84c53 0 96 43 96 96v192c0 53-43 96-96 96zm-47-201L201 79c-15-15-41-4.5-41 17v96H24c-13.3 0-24 10.7-24 24v96c0 13.3 10.7 24 24 24h136v96c0 21.5 26 32 41 17l168-168c9.3-9.4 9.3-24.6 0-34z"},child:[]}]})(t)}function mr(t){return P({attr:{viewBox:"0 0 496 512"},child:[{tag:"path",attr:{d:"M248 8C111 8 0 119 0 256s111 248 248 248 248-111 248-248S385 8 248 8zm80 168c17.7 0 32 14.3 32 32s-14.3 32-32 32-32-14.3-32-32 14.3-32 32-32zm-160 0c17.7 0 32 14.3 32 32s-14.3 32-32 32-32-14.3-32-32 14.3-32 32-32zm194.8 170.2C334.3 380.4 292.5 400 248 400s-86.3-19.6-114.8-53.8c-13.6-16.3 11-36.7 24.6-20.5 22.4 26.9 55.2 42.2 90.2 42.2s67.8-15.4 90.2-42.2c13.4-16.2 38.1 4.2 24.6 20.5z"},child:[]}]})(t)}function ur(t){return P({attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"},child:[]}]})(t)}function xr(t){return P({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M16 128h416c8.84 0 16-7.16 16-16V48c0-8.84-7.16-16-16-16H16C7.16 32 0 39.16 0 48v64c0 8.84 7.16 16 16 16zm480 80H80c-8.84 0-16 7.16-16 16v64c0 8.84 7.16 16 16 16h416c8.84 0 16-7.16 16-16v-64c0-8.84-7.16-16-16-16zm-64 176H16c-8.84 0-16 7.16-16 16v64c0 8.84 7.16 16 16 16h416c8.84 0 16-7.16 16-16v-64c0-8.84-7.16-16-16-16z"},child:[]}]})(t)}function hr(t){return P({attr:{viewBox:"0 0 352 512"},child:[{tag:"path",attr:{d:"M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"},child:[]}]})(t)}function gr(t){return P({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M432 32H312l-9.4-18.7A24 24 0 0 0 281.1 0H166.8a23.72 23.72 0 0 0-21.4 13.3L136 32H16A16 16 0 0 0 0 48v32a16 16 0 0 0 16 16h416a16 16 0 0 0 16-16V48a16 16 0 0 0-16-16zM53.2 467a48 48 0 0 0 47.9 45h245.8a48 48 0 0 0 47.9-45L416 128H32z"},child:[]}]})(t)}function pr(t){return P({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M304.083 405.907c4.686 4.686 4.686 12.284 0 16.971l-44.674 44.674c-59.263 59.262-155.693 59.266-214.961 0-59.264-59.265-59.264-155.696 0-214.96l44.675-44.675c4.686-4.686 12.284-4.686 16.971 0l39.598 39.598c4.686 4.686 4.686 12.284 0 16.971l-44.675 44.674c-28.072 28.073-28.072 73.75 0 101.823 28.072 28.072 73.75 28.073 101.824 0l44.674-44.674c4.686-4.686 12.284-4.686 16.971 0l39.597 39.598zm-56.568-260.216c4.686 4.686 12.284 4.686 16.971 0l44.674-44.674c28.072-28.075 73.75-28.073 101.824 0 28.072 28.073 28.072 73.75 0 101.823l-44.675 44.674c-4.686 4.686-4.686 12.284 0 16.971l39.598 39.598c4.686 4.686 12.284 4.686 16.971 0l44.675-44.675c59.265-59.265 59.265-155.695 0-214.96-59.266-59.264-155.695-59.264-214.961 0l-44.674 44.674c-4.686 4.686-4.686 12.284 0 16.971l39.597 39.598zm234.828 359.28l22.627-22.627c9.373-9.373 9.373-24.569 0-33.941L63.598 7.029c-9.373-9.373-24.569-9.373-33.941 0L7.029 29.657c-9.373 9.373-9.373 24.569 0 33.941l441.373 441.373c9.373 9.372 24.569 9.372 33.941 0z"},child:[]}]})(t)}function Ae(t){return P({attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M96 224c35.3 0 64-28.7 64-64s-28.7-64-64-64-64 28.7-64 64 28.7 64 64 64zm448 0c35.3 0 64-28.7 64-64s-28.7-64-64-64-64 28.7-64 64 28.7 64 64 64zm32 32h-64c-17.6 0-33.5 7.1-45.1 18.6 40.3 22.1 68.9 62 75.1 109.4h66c17.7 0 32-14.3 32-32v-32c0-35.3-28.7-64-64-64zm-256 0c61.9 0 112-50.1 112-112S381.9 32 320 32 208 82.1 208 144s50.1 112 112 112zm76.8 32h-8.3c-20.8 10-43.9 16-68.5 16s-47.6-6-68.5-16h-8.3C179.6 288 128 339.6 128 403.2V432c0 26.5 21.5 48 48 48h288c26.5 0 48-21.5 48-48v-28.8c0-63.6-51.6-115.2-115.2-115.2zm-223.7-13.4C161.5 263.1 145.6 256 128 256H64c-35.3 0-64 28.7-64 64v32c0 17.7 14.3 32 32 32h65.9c6.3-47.4 34.9-87.3 75.2-109.4z"},child:[]}]})(t)}function $e(t){return P({attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M336.2 64H47.8C21.4 64 0 85.4 0 111.8v288.4C0 426.6 21.4 448 47.8 448h288.4c26.4 0 47.8-21.4 47.8-47.8V111.8c0-26.4-21.4-47.8-47.8-47.8zm189.4 37.7L416 177.3v157.4l109.6 75.5c21.2 14.6 50.4-.3 50.4-25.8V127.5c0-25.4-29.1-40.4-50.4-25.8z"},child:[]}]})(t)}function et(t){return P({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm0 448c-110.5 0-200-89.5-200-200S145.5 56 256 56s200 89.5 200 200-89.5 200-200 200zm61.8-104.4l-84.9-61.7c-3.1-2.3-4.9-5.9-4.9-9.7V116c0-6.6 5.4-12 12-12h32c6.6 0 12 5.4 12 12v141.7l66.8 48.6c5.4 3.9 6.5 11.4 2.6 16.8L334.6 349c-3.9 5.3-11.4 6.5-16.8 2.6z"},child:[]}]})(t)}function sa(t,a){return function(){return t.apply(a,arguments)}}const{toString:fr}=Object.prototype,{getPrototypeOf:gt}=Object,{iterator:Ve,toStringTag:ia}=Symbol,Ke=(t=>a=>{const r=fr.call(a);return t[r]||(t[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),ce=t=>(t=t.toLowerCase(),a=>Ke(a)===t),Ye=t=>a=>typeof a===t,{isArray:ve}=Array,_e=Ye("undefined");function br(t){return t!==null&&!_e(t)&&t.constructor!==null&&!_e(t.constructor)&&ie(t.constructor.isBuffer)&&t.constructor.isBuffer(t)}const na=ce("ArrayBuffer");function kr(t){let a;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?a=ArrayBuffer.isView(t):a=t&&t.buffer&&na(t.buffer),a}const yr=Ye("string"),ie=Ye("function"),la=Ye("number"),Ge=t=>t!==null&&typeof t=="object",vr=t=>t===!0||t===!1,Re=t=>{if(Ke(t)!=="object")return!1;const a=gt(t);return(a===null||a===Object.prototype||Object.getPrototypeOf(a)===null)&&!(ia in t)&&!(Ve in t)},jr=ce("Date"),wr=ce("File"),Nr=ce("Blob"),_r=ce("FileList"),Fr=t=>Ge(t)&&ie(t.pipe),Sr=t=>{let a;return t&&(typeof FormData=="function"&&t instanceof FormData||ie(t.append)&&((a=Ke(t))==="formdata"||a==="object"&&ie(t.toString)&&t.toString()==="[object FormData]"))},Er=ce("URLSearchParams"),[Tr,zr,Cr,Lr]=["ReadableStream","Request","Response","Headers"].map(ce),Ar=t=>t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Se(t,a,{allOwnKeys:r=!1}={}){if(t===null||typeof t>"u")return;let s,i;if(typeof t!="object"&&(t=[t]),ve(t))for(s=0,i=t.length;s<i;s++)a.call(null,t[s],s,t);else{const l=r?Object.getOwnPropertyNames(t):Object.keys(t),n=l.length;let u;for(s=0;s<n;s++)u=l[s],a.call(null,t[u],u,t)}}function oa(t,a){a=a.toLowerCase();const r=Object.keys(t);let s=r.length,i;for(;s-- >0;)if(i=r[s],a===i.toLowerCase())return i;return null}const ge=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,da=t=>!_e(t)&&t!==ge;function ot(){const{caseless:t}=da(this)&&this||{},a={},r=(s,i)=>{const l=t&&oa(a,i)||i;Re(a[l])&&Re(s)?a[l]=ot(a[l],s):Re(s)?a[l]=ot({},s):ve(s)?a[l]=s.slice():a[l]=s};for(let s=0,i=arguments.length;s<i;s++)arguments[s]&&Se(arguments[s],r);return a}const Mr=(t,a,r,{allOwnKeys:s}={})=>(Se(a,(i,l)=>{r&&ie(i)?t[l]=sa(i,r):t[l]=i},{allOwnKeys:s}),t),Rr=t=>(t.charCodeAt(0)===65279&&(t=t.slice(1)),t),Br=(t,a,r,s)=>{t.prototype=Object.create(a.prototype,s),t.prototype.constructor=t,Object.defineProperty(t,"super",{value:a.prototype}),r&&Object.assign(t.prototype,r)},Dr=(t,a,r,s)=>{let i,l,n;const u={};if(a=a||{},t==null)return a;do{for(i=Object.getOwnPropertyNames(t),l=i.length;l-- >0;)n=i[l],(!s||s(n,t,a))&&!u[n]&&(a[n]=t[n],u[n]=!0);t=r!==!1&&gt(t)}while(t&&(!r||r(t,a))&&t!==Object.prototype);return a},Pr=(t,a,r)=>{t=String(t),(r===void 0||r>t.length)&&(r=t.length),r-=a.length;const s=t.indexOf(a,r);return s!==-1&&s===r},Or=t=>{if(!t)return null;if(ve(t))return t;let a=t.length;if(!la(a))return null;const r=new Array(a);for(;a-- >0;)r[a]=t[a];return r},Ir=(t=>a=>t&&a instanceof t)(typeof Uint8Array<"u"&&gt(Uint8Array)),$r=(t,a)=>{const s=(t&&t[Ve]).call(t);let i;for(;(i=s.next())&&!i.done;){const l=i.value;a.call(t,l[0],l[1])}},Hr=(t,a)=>{let r;const s=[];for(;(r=t.exec(a))!==null;)s.push(r);return s},Ur=ce("HTMLFormElement"),Vr=t=>t.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,s,i){return s.toUpperCase()+i}),Lt=(({hasOwnProperty:t})=>(a,r)=>t.call(a,r))(Object.prototype),Kr=ce("RegExp"),ca=(t,a)=>{const r=Object.getOwnPropertyDescriptors(t),s={};Se(r,(i,l)=>{let n;(n=a(i,l,t))!==!1&&(s[l]=n||i)}),Object.defineProperties(t,s)},Yr=t=>{ca(t,(a,r)=>{if(ie(t)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const s=t[r];if(ie(s)){if(a.enumerable=!1,"writable"in a){a.writable=!1;return}a.set||(a.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},Gr=(t,a)=>{const r={},s=i=>{i.forEach(l=>{r[l]=!0})};return ve(t)?s(t):s(String(t).split(a)),r},Wr=()=>{},qr=(t,a)=>t!=null&&Number.isFinite(t=+t)?t:a;function Jr(t){return!!(t&&ie(t.append)&&t[ia]==="FormData"&&t[Ve])}const Zr=t=>{const a=new Array(10),r=(s,i)=>{if(Ge(s)){if(a.indexOf(s)>=0)return;if(!("toJSON"in s)){a[i]=s;const l=ve(s)?[]:{};return Se(s,(n,u)=>{const g=r(n,i+1);!_e(g)&&(l[u]=g)}),a[i]=void 0,l}}return s};return r(t,0)},Xr=ce("AsyncFunction"),Qr=t=>t&&(Ge(t)||ie(t))&&ie(t.then)&&ie(t.catch),ma=((t,a)=>t?setImmediate:a?((r,s)=>(ge.addEventListener("message",({source:i,data:l})=>{i===ge&&l===r&&s.length&&s.shift()()},!1),i=>{s.push(i),ge.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",ie(ge.postMessage)),es=typeof queueMicrotask<"u"?queueMicrotask.bind(ge):typeof process<"u"&&process.nextTick||ma,ts=t=>t!=null&&ie(t[Ve]),f={isArray:ve,isArrayBuffer:na,isBuffer:br,isFormData:Sr,isArrayBufferView:kr,isString:yr,isNumber:la,isBoolean:vr,isObject:Ge,isPlainObject:Re,isReadableStream:Tr,isRequest:zr,isResponse:Cr,isHeaders:Lr,isUndefined:_e,isDate:jr,isFile:wr,isBlob:Nr,isRegExp:Kr,isFunction:ie,isStream:Fr,isURLSearchParams:Er,isTypedArray:Ir,isFileList:_r,forEach:Se,merge:ot,extend:Mr,trim:Ar,stripBOM:Rr,inherits:Br,toFlatObject:Dr,kindOf:Ke,kindOfTest:ce,endsWith:Pr,toArray:Or,forEachEntry:$r,matchAll:Hr,isHTMLForm:Ur,hasOwnProperty:Lt,hasOwnProp:Lt,reduceDescriptors:ca,freezeMethods:Yr,toObjectSet:Gr,toCamelCase:Vr,noop:Wr,toFiniteNumber:qr,findKey:oa,global:ge,isContextDefined:da,isSpecCompliantForm:Jr,toJSONObject:Zr,isAsyncFn:Xr,isThenable:Qr,setImmediate:ma,asap:es,isIterable:ts};function H(t,a,r,s,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=t,this.name="AxiosError",a&&(this.code=a),r&&(this.config=r),s&&(this.request=s),i&&(this.response=i,this.status=i.status?i.status:null)}f.inherits(H,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:f.toJSONObject(this.config),code:this.code,status:this.status}}});const ua=H.prototype,xa={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(t=>{xa[t]={value:t}});Object.defineProperties(H,xa);Object.defineProperty(ua,"isAxiosError",{value:!0});H.from=(t,a,r,s,i,l)=>{const n=Object.create(ua);return f.toFlatObject(t,n,function(g){return g!==Error.prototype},u=>u!=="isAxiosError"),H.call(n,t.message,a,r,s,i),n.cause=t,n.name=t.name,l&&Object.assign(n,l),n};const as=null;function dt(t){return f.isPlainObject(t)||f.isArray(t)}function ha(t){return f.endsWith(t,"[]")?t.slice(0,-2):t}function At(t,a,r){return t?t.concat(a).map(function(i,l){return i=ha(i),!r&&l?"["+i+"]":i}).join(r?".":""):a}function rs(t){return f.isArray(t)&&!t.some(dt)}const ss=f.toFlatObject(f,{},null,function(a){return/^is[A-Z]/.test(a)});function We(t,a,r){if(!f.isObject(t))throw new TypeError("target must be an object");a=a||new FormData,r=f.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(z,b){return!f.isUndefined(b[z])});const s=r.metaTokens,i=r.visitor||x,l=r.dots,n=r.indexes,g=(r.Blob||typeof Blob<"u"&&Blob)&&f.isSpecCompliantForm(a);if(!f.isFunction(i))throw new TypeError("visitor must be a function");function h(N){if(N===null)return"";if(f.isDate(N))return N.toISOString();if(!g&&f.isBlob(N))throw new H("Blob is not supported. Use a Buffer instead.");return f.isArrayBuffer(N)||f.isTypedArray(N)?g&&typeof Blob=="function"?new Blob([N]):Buffer.from(N):N}function x(N,z,b){let w=N;if(N&&!b&&typeof N=="object"){if(f.endsWith(z,"{}"))z=s?z:z.slice(0,-2),N=JSON.stringify(N);else if(f.isArray(N)&&rs(N)||(f.isFileList(N)||f.endsWith(z,"[]"))&&(w=f.toArray(N)))return z=ha(z),w.forEach(function(m,k){!(f.isUndefined(m)||m===null)&&a.append(n===!0?At([z],k,l):n===null?z:z+"[]",h(m))}),!1}return dt(N)?!0:(a.append(At(b,z,l),h(N)),!1)}const v=[],E=Object.assign(ss,{defaultVisitor:x,convertValue:h,isVisitable:dt});function R(N,z){if(!f.isUndefined(N)){if(v.indexOf(N)!==-1)throw Error("Circular reference detected in "+z.join("."));v.push(N),f.forEach(N,function(w,c){(!(f.isUndefined(w)||w===null)&&i.call(a,w,f.isString(c)?c.trim():c,z,E))===!0&&R(w,z?z.concat(c):[c])}),v.pop()}}if(!f.isObject(t))throw new TypeError("data must be an object");return R(t),a}function Mt(t){const a={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(t).replace(/[!'()~]|%20|%00/g,function(s){return a[s]})}function pt(t,a){this._pairs=[],t&&We(t,this,a)}const ga=pt.prototype;ga.append=function(a,r){this._pairs.push([a,r])};ga.toString=function(a){const r=a?function(s){return a.call(this,s,Mt)}:Mt;return this._pairs.map(function(i){return r(i[0])+"="+r(i[1])},"").join("&")};function is(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function pa(t,a,r){if(!a)return t;const s=r&&r.encode||is;f.isFunction(r)&&(r={serialize:r});const i=r&&r.serialize;let l;if(i?l=i(a,r):l=f.isURLSearchParams(a)?a.toString():new pt(a,r).toString(s),l){const n=t.indexOf("#");n!==-1&&(t=t.slice(0,n)),t+=(t.indexOf("?")===-1?"?":"&")+l}return t}class Rt{constructor(){this.handlers=[]}use(a,r,s){return this.handlers.push({fulfilled:a,rejected:r,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(a){this.handlers[a]&&(this.handlers[a]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(a){f.forEach(this.handlers,function(s){s!==null&&a(s)})}}const fa={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ns=typeof URLSearchParams<"u"?URLSearchParams:pt,ls=typeof FormData<"u"?FormData:null,os=typeof Blob<"u"?Blob:null,ds={isBrowser:!0,classes:{URLSearchParams:ns,FormData:ls,Blob:os},protocols:["http","https","file","blob","url","data"]},ft=typeof window<"u"&&typeof document<"u",ct=typeof navigator=="object"&&navigator||void 0,cs=ft&&(!ct||["ReactNative","NativeScript","NS"].indexOf(ct.product)<0),ms=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",us=ft&&window.location.href||"http://localhost",xs=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:ft,hasStandardBrowserEnv:cs,hasStandardBrowserWebWorkerEnv:ms,navigator:ct,origin:us},Symbol.toStringTag,{value:"Module"})),re={...xs,...ds};function hs(t,a){return We(t,new re.classes.URLSearchParams,Object.assign({visitor:function(r,s,i,l){return re.isNode&&f.isBuffer(r)?(this.append(s,r.toString("base64")),!1):l.defaultVisitor.apply(this,arguments)}},a))}function gs(t){return f.matchAll(/\w+|\[(\w*)]/g,t).map(a=>a[0]==="[]"?"":a[1]||a[0])}function ps(t){const a={},r=Object.keys(t);let s;const i=r.length;let l;for(s=0;s<i;s++)l=r[s],a[l]=t[l];return a}function ba(t){function a(r,s,i,l){let n=r[l++];if(n==="__proto__")return!0;const u=Number.isFinite(+n),g=l>=r.length;return n=!n&&f.isArray(i)?i.length:n,g?(f.hasOwnProp(i,n)?i[n]=[i[n],s]:i[n]=s,!u):((!i[n]||!f.isObject(i[n]))&&(i[n]=[]),a(r,s,i[n],l)&&f.isArray(i[n])&&(i[n]=ps(i[n])),!u)}if(f.isFormData(t)&&f.isFunction(t.entries)){const r={};return f.forEachEntry(t,(s,i)=>{a(gs(s),i,r,0)}),r}return null}function fs(t,a,r){if(f.isString(t))try{return(a||JSON.parse)(t),f.trim(t)}catch(s){if(s.name!=="SyntaxError")throw s}return(r||JSON.stringify)(t)}const Ee={transitional:fa,adapter:["xhr","http","fetch"],transformRequest:[function(a,r){const s=r.getContentType()||"",i=s.indexOf("application/json")>-1,l=f.isObject(a);if(l&&f.isHTMLForm(a)&&(a=new FormData(a)),f.isFormData(a))return i?JSON.stringify(ba(a)):a;if(f.isArrayBuffer(a)||f.isBuffer(a)||f.isStream(a)||f.isFile(a)||f.isBlob(a)||f.isReadableStream(a))return a;if(f.isArrayBufferView(a))return a.buffer;if(f.isURLSearchParams(a))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),a.toString();let u;if(l){if(s.indexOf("application/x-www-form-urlencoded")>-1)return hs(a,this.formSerializer).toString();if((u=f.isFileList(a))||s.indexOf("multipart/form-data")>-1){const g=this.env&&this.env.FormData;return We(u?{"files[]":a}:a,g&&new g,this.formSerializer)}}return l||i?(r.setContentType("application/json",!1),fs(a)):a}],transformResponse:[function(a){const r=this.transitional||Ee.transitional,s=r&&r.forcedJSONParsing,i=this.responseType==="json";if(f.isResponse(a)||f.isReadableStream(a))return a;if(a&&f.isString(a)&&(s&&!this.responseType||i)){const n=!(r&&r.silentJSONParsing)&&i;try{return JSON.parse(a)}catch(u){if(n)throw u.name==="SyntaxError"?H.from(u,H.ERR_BAD_RESPONSE,this,null,this.response):u}}return a}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:re.classes.FormData,Blob:re.classes.Blob},validateStatus:function(a){return a>=200&&a<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};f.forEach(["delete","get","head","post","put","patch"],t=>{Ee.headers[t]={}});const bs=f.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),ks=t=>{const a={};let r,s,i;return t&&t.split(`
`).forEach(function(n){i=n.indexOf(":"),r=n.substring(0,i).trim().toLowerCase(),s=n.substring(i+1).trim(),!(!r||a[r]&&bs[r])&&(r==="set-cookie"?a[r]?a[r].push(s):a[r]=[s]:a[r]=a[r]?a[r]+", "+s:s)}),a},Bt=Symbol("internals");function we(t){return t&&String(t).trim().toLowerCase()}function Be(t){return t===!1||t==null?t:f.isArray(t)?t.map(Be):String(t)}function ys(t){const a=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=r.exec(t);)a[s[1]]=s[2];return a}const vs=t=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t.trim());function tt(t,a,r,s,i){if(f.isFunction(s))return s.call(this,a,r);if(i&&(a=r),!!f.isString(a)){if(f.isString(s))return a.indexOf(s)!==-1;if(f.isRegExp(s))return s.test(a)}}function js(t){return t.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(a,r,s)=>r.toUpperCase()+s)}function ws(t,a){const r=f.toCamelCase(" "+a);["get","set","has"].forEach(s=>{Object.defineProperty(t,s+r,{value:function(i,l,n){return this[s].call(this,a,i,l,n)},configurable:!0})})}let ne=class{constructor(a){a&&this.set(a)}set(a,r,s){const i=this;function l(u,g,h){const x=we(g);if(!x)throw new Error("header name must be a non-empty string");const v=f.findKey(i,x);(!v||i[v]===void 0||h===!0||h===void 0&&i[v]!==!1)&&(i[v||g]=Be(u))}const n=(u,g)=>f.forEach(u,(h,x)=>l(h,x,g));if(f.isPlainObject(a)||a instanceof this.constructor)n(a,r);else if(f.isString(a)&&(a=a.trim())&&!vs(a))n(ks(a),r);else if(f.isObject(a)&&f.isIterable(a)){let u={},g,h;for(const x of a){if(!f.isArray(x))throw TypeError("Object iterator must return a key-value pair");u[h=x[0]]=(g=u[h])?f.isArray(g)?[...g,x[1]]:[g,x[1]]:x[1]}n(u,r)}else a!=null&&l(r,a,s);return this}get(a,r){if(a=we(a),a){const s=f.findKey(this,a);if(s){const i=this[s];if(!r)return i;if(r===!0)return ys(i);if(f.isFunction(r))return r.call(this,i,s);if(f.isRegExp(r))return r.exec(i);throw new TypeError("parser must be boolean|regexp|function")}}}has(a,r){if(a=we(a),a){const s=f.findKey(this,a);return!!(s&&this[s]!==void 0&&(!r||tt(this,this[s],s,r)))}return!1}delete(a,r){const s=this;let i=!1;function l(n){if(n=we(n),n){const u=f.findKey(s,n);u&&(!r||tt(s,s[u],u,r))&&(delete s[u],i=!0)}}return f.isArray(a)?a.forEach(l):l(a),i}clear(a){const r=Object.keys(this);let s=r.length,i=!1;for(;s--;){const l=r[s];(!a||tt(this,this[l],l,a,!0))&&(delete this[l],i=!0)}return i}normalize(a){const r=this,s={};return f.forEach(this,(i,l)=>{const n=f.findKey(s,l);if(n){r[n]=Be(i),delete r[l];return}const u=a?js(l):String(l).trim();u!==l&&delete r[l],r[u]=Be(i),s[u]=!0}),this}concat(...a){return this.constructor.concat(this,...a)}toJSON(a){const r=Object.create(null);return f.forEach(this,(s,i)=>{s!=null&&s!==!1&&(r[i]=a&&f.isArray(s)?s.join(", "):s)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([a,r])=>a+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(a){return a instanceof this?a:new this(a)}static concat(a,...r){const s=new this(a);return r.forEach(i=>s.set(i)),s}static accessor(a){const s=(this[Bt]=this[Bt]={accessors:{}}).accessors,i=this.prototype;function l(n){const u=we(n);s[u]||(ws(i,n),s[u]=!0)}return f.isArray(a)?a.forEach(l):l(a),this}};ne.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);f.reduceDescriptors(ne.prototype,({value:t},a)=>{let r=a[0].toUpperCase()+a.slice(1);return{get:()=>t,set(s){this[r]=s}}});f.freezeMethods(ne);function at(t,a){const r=this||Ee,s=a||r,i=ne.from(s.headers);let l=s.data;return f.forEach(t,function(u){l=u.call(r,l,i.normalize(),a?a.status:void 0)}),i.normalize(),l}function ka(t){return!!(t&&t.__CANCEL__)}function je(t,a,r){H.call(this,t??"canceled",H.ERR_CANCELED,a,r),this.name="CanceledError"}f.inherits(je,H,{__CANCEL__:!0});function ya(t,a,r){const s=r.config.validateStatus;!r.status||!s||s(r.status)?t(r):a(new H("Request failed with status code "+r.status,[H.ERR_BAD_REQUEST,H.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function Ns(t){const a=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return a&&a[1]||""}function _s(t,a){t=t||10;const r=new Array(t),s=new Array(t);let i=0,l=0,n;return a=a!==void 0?a:1e3,function(g){const h=Date.now(),x=s[l];n||(n=h),r[i]=g,s[i]=h;let v=l,E=0;for(;v!==i;)E+=r[v++],v=v%t;if(i=(i+1)%t,i===l&&(l=(l+1)%t),h-n<a)return;const R=x&&h-x;return R?Math.round(E*1e3/R):void 0}}function Fs(t,a){let r=0,s=1e3/a,i,l;const n=(h,x=Date.now())=>{r=x,i=null,l&&(clearTimeout(l),l=null),t.apply(null,h)};return[(...h)=>{const x=Date.now(),v=x-r;v>=s?n(h,x):(i=h,l||(l=setTimeout(()=>{l=null,n(i)},s-v)))},()=>i&&n(i)]}const He=(t,a,r=3)=>{let s=0;const i=_s(50,250);return Fs(l=>{const n=l.loaded,u=l.lengthComputable?l.total:void 0,g=n-s,h=i(g),x=n<=u;s=n;const v={loaded:n,total:u,progress:u?n/u:void 0,bytes:g,rate:h||void 0,estimated:h&&u&&x?(u-n)/h:void 0,event:l,lengthComputable:u!=null,[a?"download":"upload"]:!0};t(v)},r)},Dt=(t,a)=>{const r=t!=null;return[s=>a[0]({lengthComputable:r,total:t,loaded:s}),a[1]]},Pt=t=>(...a)=>f.asap(()=>t(...a)),Ss=re.hasStandardBrowserEnv?((t,a)=>r=>(r=new URL(r,re.origin),t.protocol===r.protocol&&t.host===r.host&&(a||t.port===r.port)))(new URL(re.origin),re.navigator&&/(msie|trident)/i.test(re.navigator.userAgent)):()=>!0,Es=re.hasStandardBrowserEnv?{write(t,a,r,s,i,l){const n=[t+"="+encodeURIComponent(a)];f.isNumber(r)&&n.push("expires="+new Date(r).toGMTString()),f.isString(s)&&n.push("path="+s),f.isString(i)&&n.push("domain="+i),l===!0&&n.push("secure"),document.cookie=n.join("; ")},read(t){const a=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return a?decodeURIComponent(a[3]):null},remove(t){this.write(t,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Ts(t){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)}function zs(t,a){return a?t.replace(/\/?\/$/,"")+"/"+a.replace(/^\/+/,""):t}function va(t,a,r){let s=!Ts(a);return t&&(s||r==!1)?zs(t,a):a}const Ot=t=>t instanceof ne?{...t}:t;function fe(t,a){a=a||{};const r={};function s(h,x,v,E){return f.isPlainObject(h)&&f.isPlainObject(x)?f.merge.call({caseless:E},h,x):f.isPlainObject(x)?f.merge({},x):f.isArray(x)?x.slice():x}function i(h,x,v,E){if(f.isUndefined(x)){if(!f.isUndefined(h))return s(void 0,h,v,E)}else return s(h,x,v,E)}function l(h,x){if(!f.isUndefined(x))return s(void 0,x)}function n(h,x){if(f.isUndefined(x)){if(!f.isUndefined(h))return s(void 0,h)}else return s(void 0,x)}function u(h,x,v){if(v in a)return s(h,x);if(v in t)return s(void 0,h)}const g={url:l,method:l,data:l,baseURL:n,transformRequest:n,transformResponse:n,paramsSerializer:n,timeout:n,timeoutMessage:n,withCredentials:n,withXSRFToken:n,adapter:n,responseType:n,xsrfCookieName:n,xsrfHeaderName:n,onUploadProgress:n,onDownloadProgress:n,decompress:n,maxContentLength:n,maxBodyLength:n,beforeRedirect:n,transport:n,httpAgent:n,httpsAgent:n,cancelToken:n,socketPath:n,responseEncoding:n,validateStatus:u,headers:(h,x,v)=>i(Ot(h),Ot(x),v,!0)};return f.forEach(Object.keys(Object.assign({},t,a)),function(x){const v=g[x]||i,E=v(t[x],a[x],x);f.isUndefined(E)&&v!==u||(r[x]=E)}),r}const ja=t=>{const a=fe({},t);let{data:r,withXSRFToken:s,xsrfHeaderName:i,xsrfCookieName:l,headers:n,auth:u}=a;a.headers=n=ne.from(n),a.url=pa(va(a.baseURL,a.url,a.allowAbsoluteUrls),t.params,t.paramsSerializer),u&&n.set("Authorization","Basic "+btoa((u.username||"")+":"+(u.password?unescape(encodeURIComponent(u.password)):"")));let g;if(f.isFormData(r)){if(re.hasStandardBrowserEnv||re.hasStandardBrowserWebWorkerEnv)n.setContentType(void 0);else if((g=n.getContentType())!==!1){const[h,...x]=g?g.split(";").map(v=>v.trim()).filter(Boolean):[];n.setContentType([h||"multipart/form-data",...x].join("; "))}}if(re.hasStandardBrowserEnv&&(s&&f.isFunction(s)&&(s=s(a)),s||s!==!1&&Ss(a.url))){const h=i&&l&&Es.read(l);h&&n.set(i,h)}return a},Cs=typeof XMLHttpRequest<"u",Ls=Cs&&function(t){return new Promise(function(r,s){const i=ja(t);let l=i.data;const n=ne.from(i.headers).normalize();let{responseType:u,onUploadProgress:g,onDownloadProgress:h}=i,x,v,E,R,N;function z(){R&&R(),N&&N(),i.cancelToken&&i.cancelToken.unsubscribe(x),i.signal&&i.signal.removeEventListener("abort",x)}let b=new XMLHttpRequest;b.open(i.method.toUpperCase(),i.url,!0),b.timeout=i.timeout;function w(){if(!b)return;const m=ne.from("getAllResponseHeaders"in b&&b.getAllResponseHeaders()),T={data:!u||u==="text"||u==="json"?b.responseText:b.response,status:b.status,statusText:b.statusText,headers:m,config:t,request:b};ya(function(d){r(d),z()},function(d){s(d),z()},T),b=null}"onloadend"in b?b.onloadend=w:b.onreadystatechange=function(){!b||b.readyState!==4||b.status===0&&!(b.responseURL&&b.responseURL.indexOf("file:")===0)||setTimeout(w)},b.onabort=function(){b&&(s(new H("Request aborted",H.ECONNABORTED,t,b)),b=null)},b.onerror=function(){s(new H("Network Error",H.ERR_NETWORK,t,b)),b=null},b.ontimeout=function(){let k=i.timeout?"timeout of "+i.timeout+"ms exceeded":"timeout exceeded";const T=i.transitional||fa;i.timeoutErrorMessage&&(k=i.timeoutErrorMessage),s(new H(k,T.clarifyTimeoutError?H.ETIMEDOUT:H.ECONNABORTED,t,b)),b=null},l===void 0&&n.setContentType(null),"setRequestHeader"in b&&f.forEach(n.toJSON(),function(k,T){b.setRequestHeader(T,k)}),f.isUndefined(i.withCredentials)||(b.withCredentials=!!i.withCredentials),u&&u!=="json"&&(b.responseType=i.responseType),h&&([E,N]=He(h,!0),b.addEventListener("progress",E)),g&&b.upload&&([v,R]=He(g),b.upload.addEventListener("progress",v),b.upload.addEventListener("loadend",R)),(i.cancelToken||i.signal)&&(x=m=>{b&&(s(!m||m.type?new je(null,t,b):m),b.abort(),b=null)},i.cancelToken&&i.cancelToken.subscribe(x),i.signal&&(i.signal.aborted?x():i.signal.addEventListener("abort",x)));const c=Ns(i.url);if(c&&re.protocols.indexOf(c)===-1){s(new H("Unsupported protocol "+c+":",H.ERR_BAD_REQUEST,t));return}b.send(l||null)})},As=(t,a)=>{const{length:r}=t=t?t.filter(Boolean):[];if(a||r){let s=new AbortController,i;const l=function(h){if(!i){i=!0,u();const x=h instanceof Error?h:this.reason;s.abort(x instanceof H?x:new je(x instanceof Error?x.message:x))}};let n=a&&setTimeout(()=>{n=null,l(new H(`timeout ${a} of ms exceeded`,H.ETIMEDOUT))},a);const u=()=>{t&&(n&&clearTimeout(n),n=null,t.forEach(h=>{h.unsubscribe?h.unsubscribe(l):h.removeEventListener("abort",l)}),t=null)};t.forEach(h=>h.addEventListener("abort",l));const{signal:g}=s;return g.unsubscribe=()=>f.asap(u),g}},Ms=function*(t,a){let r=t.byteLength;if(r<a){yield t;return}let s=0,i;for(;s<r;)i=s+a,yield t.slice(s,i),s=i},Rs=async function*(t,a){for await(const r of Bs(t))yield*Ms(r,a)},Bs=async function*(t){if(t[Symbol.asyncIterator]){yield*t;return}const a=t.getReader();try{for(;;){const{done:r,value:s}=await a.read();if(r)break;yield s}}finally{await a.cancel()}},It=(t,a,r,s)=>{const i=Rs(t,a);let l=0,n,u=g=>{n||(n=!0,s&&s(g))};return new ReadableStream({async pull(g){try{const{done:h,value:x}=await i.next();if(h){u(),g.close();return}let v=x.byteLength;if(r){let E=l+=v;r(E)}g.enqueue(new Uint8Array(x))}catch(h){throw u(h),h}},cancel(g){return u(g),i.return()}},{highWaterMark:2})},qe=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",wa=qe&&typeof ReadableStream=="function",Ds=qe&&(typeof TextEncoder=="function"?(t=>a=>t.encode(a))(new TextEncoder):async t=>new Uint8Array(await new Response(t).arrayBuffer())),Na=(t,...a)=>{try{return!!t(...a)}catch{return!1}},Ps=wa&&Na(()=>{let t=!1;const a=new Request(re.origin,{body:new ReadableStream,method:"POST",get duplex(){return t=!0,"half"}}).headers.has("Content-Type");return t&&!a}),$t=64*1024,mt=wa&&Na(()=>f.isReadableStream(new Response("").body)),Ue={stream:mt&&(t=>t.body)};qe&&(t=>{["text","arrayBuffer","blob","formData","stream"].forEach(a=>{!Ue[a]&&(Ue[a]=f.isFunction(t[a])?r=>r[a]():(r,s)=>{throw new H(`Response type '${a}' is not supported`,H.ERR_NOT_SUPPORT,s)})})})(new Response);const Os=async t=>{if(t==null)return 0;if(f.isBlob(t))return t.size;if(f.isSpecCompliantForm(t))return(await new Request(re.origin,{method:"POST",body:t}).arrayBuffer()).byteLength;if(f.isArrayBufferView(t)||f.isArrayBuffer(t))return t.byteLength;if(f.isURLSearchParams(t)&&(t=t+""),f.isString(t))return(await Ds(t)).byteLength},Is=async(t,a)=>{const r=f.toFiniteNumber(t.getContentLength());return r??Os(a)},$s=qe&&(async t=>{let{url:a,method:r,data:s,signal:i,cancelToken:l,timeout:n,onDownloadProgress:u,onUploadProgress:g,responseType:h,headers:x,withCredentials:v="same-origin",fetchOptions:E}=ja(t);h=h?(h+"").toLowerCase():"text";let R=As([i,l&&l.toAbortSignal()],n),N;const z=R&&R.unsubscribe&&(()=>{R.unsubscribe()});let b;try{if(g&&Ps&&r!=="get"&&r!=="head"&&(b=await Is(x,s))!==0){let T=new Request(a,{method:"POST",body:s,duplex:"half"}),S;if(f.isFormData(s)&&(S=T.headers.get("content-type"))&&x.setContentType(S),T.body){const[d,j]=Dt(b,He(Pt(g)));s=It(T.body,$t,d,j)}}f.isString(v)||(v=v?"include":"omit");const w="credentials"in Request.prototype;N=new Request(a,{...E,signal:R,method:r.toUpperCase(),headers:x.normalize().toJSON(),body:s,duplex:"half",credentials:w?v:void 0});let c=await fetch(N);const m=mt&&(h==="stream"||h==="response");if(mt&&(u||m&&z)){const T={};["status","statusText","headers"].forEach(_=>{T[_]=c[_]});const S=f.toFiniteNumber(c.headers.get("content-length")),[d,j]=u&&Dt(S,He(Pt(u),!0))||[];c=new Response(It(c.body,$t,d,()=>{j&&j(),z&&z()}),T)}h=h||"text";let k=await Ue[f.findKey(Ue,h)||"text"](c,t);return!m&&z&&z(),await new Promise((T,S)=>{ya(T,S,{data:k,headers:ne.from(c.headers),status:c.status,statusText:c.statusText,config:t,request:N})})}catch(w){throw z&&z(),w&&w.name==="TypeError"&&/Load failed|fetch/i.test(w.message)?Object.assign(new H("Network Error",H.ERR_NETWORK,t,N),{cause:w.cause||w}):H.from(w,w&&w.code,t,N)}}),ut={http:as,xhr:Ls,fetch:$s};f.forEach(ut,(t,a)=>{if(t){try{Object.defineProperty(t,"name",{value:a})}catch{}Object.defineProperty(t,"adapterName",{value:a})}});const Ht=t=>`- ${t}`,Hs=t=>f.isFunction(t)||t===null||t===!1,_a={getAdapter:t=>{t=f.isArray(t)?t:[t];const{length:a}=t;let r,s;const i={};for(let l=0;l<a;l++){r=t[l];let n;if(s=r,!Hs(r)&&(s=ut[(n=String(r)).toLowerCase()],s===void 0))throw new H(`Unknown adapter '${n}'`);if(s)break;i[n||"#"+l]=s}if(!s){const l=Object.entries(i).map(([u,g])=>`adapter ${u} `+(g===!1?"is not supported by the environment":"is not available in the build"));let n=a?l.length>1?`since :
`+l.map(Ht).join(`
`):" "+Ht(l[0]):"as no adapter specified";throw new H("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return s},adapters:ut};function rt(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new je(null,t)}function Ut(t){return rt(t),t.headers=ne.from(t.headers),t.data=at.call(t,t.transformRequest),["post","put","patch"].indexOf(t.method)!==-1&&t.headers.setContentType("application/x-www-form-urlencoded",!1),_a.getAdapter(t.adapter||Ee.adapter)(t).then(function(s){return rt(t),s.data=at.call(t,t.transformResponse,s),s.headers=ne.from(s.headers),s},function(s){return ka(s)||(rt(t),s&&s.response&&(s.response.data=at.call(t,t.transformResponse,s.response),s.response.headers=ne.from(s.response.headers))),Promise.reject(s)})}const Fa="1.9.0",Je={};["object","boolean","number","function","string","symbol"].forEach((t,a)=>{Je[t]=function(s){return typeof s===t||"a"+(a<1?"n ":" ")+t}});const Vt={};Je.transitional=function(a,r,s){function i(l,n){return"[Axios v"+Fa+"] Transitional option '"+l+"'"+n+(s?". "+s:"")}return(l,n,u)=>{if(a===!1)throw new H(i(n," has been removed"+(r?" in "+r:"")),H.ERR_DEPRECATED);return r&&!Vt[n]&&(Vt[n]=!0,console.warn(i(n," has been deprecated since v"+r+" and will be removed in the near future"))),a?a(l,n,u):!0}};Je.spelling=function(a){return(r,s)=>(console.warn(`${s} is likely a misspelling of ${a}`),!0)};function Us(t,a,r){if(typeof t!="object")throw new H("options must be an object",H.ERR_BAD_OPTION_VALUE);const s=Object.keys(t);let i=s.length;for(;i-- >0;){const l=s[i],n=a[l];if(n){const u=t[l],g=u===void 0||n(u,l,t);if(g!==!0)throw new H("option "+l+" must be "+g,H.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new H("Unknown option "+l,H.ERR_BAD_OPTION)}}const De={assertOptions:Us,validators:Je},me=De.validators;let pe=class{constructor(a){this.defaults=a||{},this.interceptors={request:new Rt,response:new Rt}}async request(a,r){try{return await this._request(a,r)}catch(s){if(s instanceof Error){let i={};Error.captureStackTrace?Error.captureStackTrace(i):i=new Error;const l=i.stack?i.stack.replace(/^.+\n/,""):"";try{s.stack?l&&!String(s.stack).endsWith(l.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+l):s.stack=l}catch{}}throw s}}_request(a,r){typeof a=="string"?(r=r||{},r.url=a):r=a||{},r=fe(this.defaults,r);const{transitional:s,paramsSerializer:i,headers:l}=r;s!==void 0&&De.assertOptions(s,{silentJSONParsing:me.transitional(me.boolean),forcedJSONParsing:me.transitional(me.boolean),clarifyTimeoutError:me.transitional(me.boolean)},!1),i!=null&&(f.isFunction(i)?r.paramsSerializer={serialize:i}:De.assertOptions(i,{encode:me.function,serialize:me.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),De.assertOptions(r,{baseUrl:me.spelling("baseURL"),withXsrfToken:me.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let n=l&&f.merge(l.common,l[r.method]);l&&f.forEach(["delete","get","head","post","put","patch","common"],N=>{delete l[N]}),r.headers=ne.concat(n,l);const u=[];let g=!0;this.interceptors.request.forEach(function(z){typeof z.runWhen=="function"&&z.runWhen(r)===!1||(g=g&&z.synchronous,u.unshift(z.fulfilled,z.rejected))});const h=[];this.interceptors.response.forEach(function(z){h.push(z.fulfilled,z.rejected)});let x,v=0,E;if(!g){const N=[Ut.bind(this),void 0];for(N.unshift.apply(N,u),N.push.apply(N,h),E=N.length,x=Promise.resolve(r);v<E;)x=x.then(N[v++],N[v++]);return x}E=u.length;let R=r;for(v=0;v<E;){const N=u[v++],z=u[v++];try{R=N(R)}catch(b){z.call(this,b);break}}try{x=Ut.call(this,R)}catch(N){return Promise.reject(N)}for(v=0,E=h.length;v<E;)x=x.then(h[v++],h[v++]);return x}getUri(a){a=fe(this.defaults,a);const r=va(a.baseURL,a.url,a.allowAbsoluteUrls);return pa(r,a.params,a.paramsSerializer)}};f.forEach(["delete","get","head","options"],function(a){pe.prototype[a]=function(r,s){return this.request(fe(s||{},{method:a,url:r,data:(s||{}).data}))}});f.forEach(["post","put","patch"],function(a){function r(s){return function(l,n,u){return this.request(fe(u||{},{method:a,headers:s?{"Content-Type":"multipart/form-data"}:{},url:l,data:n}))}}pe.prototype[a]=r(),pe.prototype[a+"Form"]=r(!0)});let Vs=class Sa{constructor(a){if(typeof a!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(l){r=l});const s=this;this.promise.then(i=>{if(!s._listeners)return;let l=s._listeners.length;for(;l-- >0;)s._listeners[l](i);s._listeners=null}),this.promise.then=i=>{let l;const n=new Promise(u=>{s.subscribe(u),l=u}).then(i);return n.cancel=function(){s.unsubscribe(l)},n},a(function(l,n,u){s.reason||(s.reason=new je(l,n,u),r(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(a){if(this.reason){a(this.reason);return}this._listeners?this._listeners.push(a):this._listeners=[a]}unsubscribe(a){if(!this._listeners)return;const r=this._listeners.indexOf(a);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const a=new AbortController,r=s=>{a.abort(s)};return this.subscribe(r),a.signal.unsubscribe=()=>this.unsubscribe(r),a.signal}static source(){let a;return{token:new Sa(function(i){a=i}),cancel:a}}};function Ks(t){return function(r){return t.apply(null,r)}}function Ys(t){return f.isObject(t)&&t.isAxiosError===!0}const xt={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(xt).forEach(([t,a])=>{xt[a]=t});function Ea(t){const a=new pe(t),r=sa(pe.prototype.request,a);return f.extend(r,pe.prototype,a,{allOwnKeys:!0}),f.extend(r,a,null,{allOwnKeys:!0}),r.create=function(i){return Ea(fe(t,i))},r}const D=Ea(Ee);D.Axios=pe;D.CanceledError=je;D.CancelToken=Vs;D.isCancel=ka;D.VERSION=Fa;D.toFormData=We;D.AxiosError=H;D.Cancel=D.CanceledError;D.all=function(a){return Promise.all(a)};D.spread=Ks;D.isAxiosError=Ys;D.mergeConfig=fe;D.AxiosHeaders=ne;D.formToJSON=t=>ba(f.isHTMLForm(t)?new FormData(t):t);D.getAdapter=_a.getAdapter;D.HttpStatusCode=xt;D.default=D;const{Axios:Yi,AxiosError:Gi,CanceledError:Wi,isCancel:qi,CancelToken:Ji,VERSION:Zi,all:Xi,Cancel:Qi,isAxiosError:en,spread:tn,toFormData:an,AxiosHeaders:rn,HttpStatusCode:sn,formToJSON:nn,getAdapter:ln,mergeConfig:on}=D,Kt="https://akademi.tuberajans.com/backend/api",Ta=o.createContext(void 0),Gs=({children:t})=>{const[a,r]=o.useState(null),[s,i]=o.useState(!0),[l,n]=o.useState(!1),[u,g]=o.useState(null),h=be(),x=b=>{if(console.log("updateAndStoreUserData çağrıldı, userData:",b),r(b),n(!0),localStorage.setItem("user",JSON.stringify(b)),localStorage.setItem("last_auth_check",Date.now().toString()),b.tiktok_open_id){const w={username:b.username,tiktok_username:b.tiktok_username,display_name:b.tiktok_display_name,avatar_url:b.tiktok_avatar_url,linked_at:b.tiktok_linked_at,followers_count:parseInt(String(b.followers_count))||0,following_count:parseInt(String(b.following_count))||0,likes_count:parseInt(String(b.likes_count))||0,video_count:parseInt(String(b.video_count))||0,is_verified:b.is_verified===1,bio:b.bio,tiktok_bio:b.tiktok_bio,tiktok_open_id:b.tiktok_open_id};localStorage.setItem("tiktokUser",JSON.stringify(w))}else localStorage.removeItem("tiktokUser")},v=o.useCallback(async(b,w)=>{var c,m,k,T;try{i(!0),g(null);const S=await D.post("/backend/api/auth.php",{action:"login",username:b,password:w},{timeout:1e4,withCredentials:!0});return S.data.success?(x(S.data.user),window.dispatchEvent(new CustomEvent("userLoggedIn")),h("/dashboard"),{success:!0}):(g(S.data.message||"Giriş başarısız"),{success:!1,message:S.data.message})}catch(S){console.error("Login error:",S);let d="Giriş yapılırken bir hata oluştu";if(S&&typeof S=="object"&&"response"in S){const j=S;((c=j.response)==null?void 0:c.status)===401?d=((m=j.response.data)==null?void 0:m.message)||"Hatalı kullanıcı adı veya şifre":(T=(k=j.response)==null?void 0:k.data)!=null&&T.message?d=j.response.data.message:j.code==="ECONNABORTED"?d="İstek zaman aşımına uğradı. Lütfen tekrar deneyin.":j.response||(d="Sunucuya bağlanılamadı. İnternet bağlantınızı kontrol edin.")}return g(d),{success:!1,message:d}}finally{i(!1)}},[h]),E=o.useCallback(async()=>{try{await D.post("/backend/api/auth.php",{action:"logout"},{withCredentials:!0})}catch(b){console.error("Logout error:",b)}finally{localStorage.removeItem("user"),localStorage.removeItem("token"),localStorage.removeItem("last_auth_check"),localStorage.removeItem("tiktokUser"),r(null),n(!1),h("/login")}},[h]),R=o.useCallback(b=>{if(b){const w=JSON.parse(b);x(w)}else x({username:"tuberajans",email:"<EMAIL>",role:"user"});i(!1)},[]),N=o.useCallback(async()=>{console.log("checkAuth başlatıldı");try{const b=localStorage.getItem("token"),w=localStorage.getItem("user"),c=localStorage.getItem("last_auth_check"),m=Date.now();console.log("checkAuth - storedUser:",!!w,"lastAuthCheck:",c);const k=!1;if(!w){console.log("checkAuth - storedUser yok, çıkış yapılıyor"),r(null),n(!1),i(!1);return}if(c&&m-parseInt(c)<12e4){console.log("checkAuth - 2 dakika kuralı, localStorage'dan user alınıyor");const _=JSON.parse(w);x(_),i(!1);return}const T=new AbortController,S=setTimeout(()=>T.abort(),3e3),d=await fetch(`${Kt}/auth.php?check=true`,{credentials:"include",signal:T.signal});clearTimeout(S);const j=await d.json();if(localStorage.setItem("last_auth_check",m.toString()),j.authenticated){console.log("Auth başarılı, kullanıcı bilgileri alınıyor...");try{const _=await fetch(`${Kt}/user_session.php`,{credentials:"include",headers:{Authorization:`Bearer ${b}`}});if(console.log("User session API yanıtı:",_.status),_.ok){const L=await _.json();if(console.log("User session API verisi:",L),L.status==="success")x(L.data),console.log("Session'dan güncel kullanıcı bilgileri alındı:",L.data),window.dispatchEvent(new CustomEvent("userLoggedIn"));else if(console.log("Session API hatası:",L),w){const O=JSON.parse(w);x(O)}}else if(console.log("Session API yanıt hatası:",_.status),w){const L=JSON.parse(w);x(L)}}catch(_){if(console.error("User session API hatası:",_),w){const L=JSON.parse(w);x(L)}}}else localStorage.removeItem("user"),localStorage.removeItem("token"),localStorage.removeItem("last_auth_check"),localStorage.removeItem("tiktokUser"),r(null),n(!1)}catch(b){console.error("Auth check error:",b);const w=localStorage.getItem("user");if(w)try{const c=JSON.parse(w);x(c),window.dispatchEvent(new CustomEvent("userLoggedIn"))}catch(c){console.error("Error parsing stored user data:",c),localStorage.removeItem("user"),localStorage.removeItem("token"),r(null),n(!1)}else r(null),n(!1)}finally{i(!1)}},[R]);o.useEffect(()=>{N()},[N]);const z=o.useMemo(()=>({user:a,loading:s,error:u,login:v,logout:E,isAuthenticated:l}),[a,s,u,v,E,l]);return e.jsx(Ta.Provider,{value:z,children:t})},ke=()=>{const t=o.useContext(Ta);if(!t)throw new Error("useAuth must be used within an AuthProvider");return t},Yt=()=>{const[t,a]=o.useState(""),[r,s]=o.useState(""),[i,l]=o.useState(!1),[n,u]=o.useState(!1),[g,h]=o.useState(""),[x,v]=o.useState(!1),[E,R]=o.useState(""),[N,z]=o.useState(!1),[b,w]=o.useState(!1),[c,m]=o.useState(""),[k,T]=o.useState(""),[S,d]=o.useState(""),[j,_]=o.useState(!1),[L,O]=o.useState(!1),W=be(),M=!1,F=ke(),K=F.loading,Z=async p=>{p&&p.preventDefault(),h(""),O(!0);try{if(F){const B=await F.login(t,r);B.success?W("/dashboard"):h(B.message||"Giriş işlemi tamamlanamadı.")}else h("Kimlik doğrulama sistemi başlatılamadı.")}catch(B){console.error("Login error:",B),h("Giriş yapılırken bir hata oluştu. Lütfen tekrar deneyin.")}finally{O(!1)}},se=()=>{v(!0)},de=()=>{E&&(O(!0),setTimeout(()=>{z(!0),O(!1),setTimeout(()=>{v(!1),z(!1),R("")},3e3)},1500))},Y=()=>{w(!0)},G=async()=>{if(!c||!k||!S){h("Lütfen tüm alanları doldurun.");return}O(!0),h("");try{const p=await D.post("/backend/api/register.php",{name:k,username:k,email:c,password:S},{headers:{"Content-Type":"application/json"},withCredentials:!0});p.data.status==="success"?(_(!0),setTimeout(()=>{w(!1),_(!1),m(""),T(""),d("")},2e3)):h(p.data.message||"Kayıt işlemi başarısız.")}catch(p){p instanceof Error?h(p.message||"Kayıt işlemi sırasında hata oluştu."):h("Beklenmeyen bir hata oluştu.")}finally{O(!1)}},X=()=>{try{const p="awfw8k9nim1e8dmu",B=encodeURIComponent("https://akademi.tuberajans.com/backend/api/tiktok-callback.php"),q="homepage_"+Math.random().toString(36).substring(2,15);localStorage.setItem("tiktok_oauth_state",q),localStorage.setItem("tiktok_redirect_after","homepage");const y=`https://www.tiktok.com/v2/auth/authorize/?client_key=${p}&response_type=code&scope=user.info.basic,user.info.profile,user.info.stats,video.list&redirect_uri=${B}&state=${q}`;window.location.href=y}catch(p){console.error("TikTok login error:",p),h("TikTok ile giriş yapılırken bir hata oluştu.")}},A=()=>{const p={username:"tuberajans",email:"<EMAIL>",role:"user"};localStorage.setItem("user",JSON.stringify(p)),W("/dashboard"),localStorage.removeItem("tiktok_redirect_after")},U=async()=>{try{const B=await(await fetch("/backend/api/tiktok-api.php?action=check_session")).json();B.status==="success"&&B.has_tiktok_session?W("/dashboard"):h("TikTok ile giriş tamamlanamadı. Lütfen tekrar deneyin.")}catch(p){console.error("Login check error:",p),h("Giriş kontrolü yapılırken bir hata oluştu.")}},C=o.useCallback(()=>{window.history.replaceState({},document.title,window.location.pathname),window.location.hostname==="akademi.tuberajans.com"&&!localStorage.getItem("dev_mode")&&localStorage.setItem("dev_mode","true"),setTimeout(U,2e3)},[M,A,U]),V=o.useCallback(p=>{window.history.replaceState({},document.title,window.location.pathname);const B=p?decodeURIComponent(p):"Bilinmeyen hata";h(`TikTok ile giriş yapılırken hata oluştu: ${B}`),localStorage.getItem("tiktok_redirect_after")==="dashboard"&&setTimeout(()=>{W("/dashboard")},2e3),localStorage.removeItem("tiktok_redirect_after"),localStorage.removeItem("tiktok_oauth_state")},[W]);return o.useEffect(()=>{const p=new URLSearchParams(window.location.search),B=p.get("tiktok_login"),q=p.get("message");B==="success"?C():B==="error"&&V(q)},[W,M,V,C]),e.jsxs("div",{className:"h-screen relative bg-black overflow-hidden",children:[e.jsx("a",{href:"https://api.whatsapp.com/send?phone=+905309157188&text=Merhaba",className:"fixed w-[45px] h-[45px] bottom-5 left-2.5 bg-[#25d366] text-white rounded-full text-center z-50 shadow-md flex items-center justify-center",rel:"nofollow",target:"_blank","aria-label":"WhatsApp üzerinden Tuber Ajans ile iletişime geçin",title:"WhatsApp üzerinden Tuber Ajans ile iletişime geçin",children:e.jsx(Ya,{className:"text-[28px]"})}),e.jsxs("div",{className:"absolute inset-0 z-0",children:[e.jsx("div",{className:"absolute inset-0 bg-black"}),e.jsx("div",{className:"absolute inset-0 bg-cover bg-center bg-no-repeat opacity-30",style:{backgroundImage:`url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800"><defs><pattern id="office" patternUnits="userSpaceOnUse" width="100" height="100"><rect width="100" height="100" fill="%23111111"/><rect x="10" y="10" width="80" height="80" fill="%23222222" opacity="0.3"/><rect x="20" y="20" width="60" height="60" fill="%23333333" opacity="0.2"/></pattern></defs><rect width="100%" height="100%" fill="url(%23office)"/></svg>')`}}),e.jsx("div",{className:"absolute inset-0 bg-black/60"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-red-900/10 via-transparent to-gray-900/20"})]}),x&&e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-md flex items-center justify-center z-50",children:e.jsxs("div",{className:"relative bg-gray-900 rounded-xl overflow-hidden border border-gray-700 p-6 max-w-md w-full mx-4 shadow-2xl",children:[e.jsxs("div",{className:"absolute inset-0 overflow-hidden",children:[e.jsx("div",{className:"absolute -right-20 -top-20 w-40 h-40 bg-red-500/10 rounded-full filter blur-3xl"}),e.jsx("div",{className:"absolute -left-20 -bottom-20 w-40 h-40 bg-gray-500/10 rounded-full filter blur-3xl"})]}),e.jsxs("div",{className:"relative z-10",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-xl font-bold text-white",children:"Şifremi Unuttum"}),e.jsx("button",{onClick:()=>{v(!1),z(!1),R("")},className:"text-gray-400 hover:text-white",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})})]}),N?e.jsxs("div",{className:"text-center p-4",children:[e.jsx("div",{className:"bg-emerald-500/10 text-emerald-400 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4",children:e.jsx("svg",{className:"w-8 h-8",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})})}),e.jsx("h4",{className:"text-lg font-medium text-white mb-2",children:"Sıfırlama Bağlantısı Gönderildi"}),e.jsxs("p",{className:"text-gray-400",children:[E," adresine şifre sıfırlama bağlantısı gönderdik. Lütfen e-postanızı kontrol edin."]})]}):e.jsxs(e.Fragment,{children:[e.jsx("p",{className:"text-gray-400 mb-6",children:"E-posta adresinizi girin, size şifre sıfırlama bağlantısı göndereceğiz."}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-200 mb-2",children:"E-posta Adresi"}),e.jsx("input",{id:"email",type:"email",value:E,onChange:p=>R(p.target.value),className:"block w-full px-4 py-3 rounded-lg text-white bg-white/5 border border-white/10 focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all outline-none",placeholder:"<EMAIL>"})]}),e.jsx("button",{onClick:de,disabled:L||K||!E,className:"w-full flex items-center justify-center py-3 px-4 rounded-lg font-semibold text-white focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed transition-all transform hover:scale-105 shadow-lg",style:{backgroundColor:"#ff0e2a",borderColor:"#ff0e2a"},onMouseEnter:p=>{!L&&!K&&(p.target.style.backgroundColor="#e80000")},onMouseLeave:p=>{!L&&!K&&(p.target.style.backgroundColor="#ff0e2a")},children:L||K?e.jsxs("svg",{className:"animate-spin h-5 w-5 text-white",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):"Sıfırlama Bağlantısı Gönder"})]})]})]})}),b&&e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-md flex items-center justify-center z-50",children:e.jsxs("div",{className:"relative bg-gray-900 rounded-xl overflow-hidden border border-gray-700 p-6 max-w-md w-full mx-4 shadow-2xl",children:[e.jsxs("div",{className:"absolute inset-0 overflow-hidden",children:[e.jsx("div",{className:"absolute -right-20 -top-20 w-40 h-40 bg-red-500/10 rounded-full filter blur-3xl"}),e.jsx("div",{className:"absolute -left-20 -bottom-20 w-40 h-40 bg-gray-500/10 rounded-full filter blur-3xl"})]}),e.jsxs("div",{className:"relative z-10",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-xl font-bold text-white",children:"Yeni Hesap Oluştur"}),e.jsx("button",{onClick:()=>{w(!1),_(!1),m(""),T(""),d("")},className:"text-gray-400 hover:text-white",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})})]}),j?e.jsxs("div",{className:"text-center p-4",children:[e.jsx("div",{className:"bg-emerald-500/10 text-emerald-400 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4",children:e.jsx("svg",{className:"w-8 h-8",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})})}),e.jsx("h4",{className:"text-lg font-medium text-white mb-2",children:"Kayıt Başarılı!"}),e.jsx("p",{className:"text-gray-400",children:"Hesabınız başarıyla oluşturuldu. Şimdi giriş yapabilirsiniz."})]}):e.jsxs(e.Fragment,{children:[e.jsx("p",{className:"text-gray-400 mb-6",children:"Yeni bir hesap oluşturmak için aşağıdaki bilgileri doldurun."}),g&&e.jsx("div",{className:"mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg",children:e.jsx("p",{className:"text-red-500 text-sm",children:g})}),e.jsxs("form",{onSubmit:p=>{p.preventDefault(),G()},className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"register-email",className:"block text-sm font-medium text-gray-200 mb-1",children:"E-posta Adresi"}),e.jsx("input",{id:"register-email",type:"email",value:c,onChange:p=>m(p.target.value),className:"block w-full px-4 py-2 rounded-lg text-white bg-white/5 border border-white/10 focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all outline-none text-sm",placeholder:"<EMAIL>",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"register-username",className:"block text-sm font-medium text-gray-200 mb-1",children:"Kullanıcı Adı"}),e.jsx("input",{id:"register-username",type:"text",value:k,onChange:p=>T(p.target.value),className:"block w-full px-4 py-2 rounded-lg text-white bg-white/5 border border-white/10 focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all outline-none text-sm",placeholder:"Kullanıcı adınızı girin",required:!0,minLength:3})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"register-password",className:"block text-sm font-medium text-gray-200 mb-1",children:"Şifre"}),e.jsx("input",{id:"register-password",type:"password",value:S,onChange:p=>d(p.target.value),className:"block w-full px-4 py-2 rounded-lg text-white bg-white/5 border border-white/10 focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all outline-none text-sm",placeholder:"Güçlü bir şifre oluşturun",required:!0,minLength:6})]}),e.jsx("button",{type:"submit",disabled:L||K||!c||!k||!S,className:"w-full flex items-center justify-center py-3 px-4 rounded-lg font-semibold text-white focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed transition-all transform hover:scale-105 shadow-lg mt-6",style:{backgroundColor:"#ff0e2a",borderColor:"#ff0e2a"},onMouseEnter:p=>{!L&&!K&&(p.target.style.backgroundColor="#e80000")},onMouseLeave:p=>{!L&&!K&&(p.target.style.backgroundColor="#ff0e2a")},children:L||K?e.jsxs("svg",{className:"animate-spin h-5 w-5 text-white",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):"Kayıt Ol"})]})]})]})]})}),e.jsxs("div",{className:"absolute inset-0",children:[e.jsx("div",{className:"absolute inset-0 bg-cover bg-center bg-no-repeat",style:{backgroundImage:"url('/images/arkaplannew.webp')",backgroundPosition:"center center",backgroundSize:"cover"}}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-black/80 via-black/60 to-red-900/40"}),e.jsx("div",{className:"absolute inset-0 opacity-20",style:{backgroundImage:"radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.03) 1px, transparent 1px)",backgroundSize:"50px 50px"}})]}),e.jsxs("div",{className:"relative z-10 flex h-screen flex-col lg:flex-row",children:[e.jsx("div",{className:"relative lg:w-3/5 overflow-hidden lg:flex flex-col justify-center items-start hidden",children:e.jsxs("div",{className:"relative z-10 p-12 lg:p-24 lg:ml-8 flex flex-col items-start justify-center h-full max-w-4xl lg:-mt-16",children:[e.jsx("div",{className:"mb-2 mt-0",children:e.jsx("img",{src:"/images/logotuber1.png",alt:"Tuber Ajans Logo",className:"h-20 w-auto select-none",style:{objectFit:"contain"}})}),e.jsxs("div",{className:"text-left",children:[e.jsx("p",{className:"text-base font-semibold mb-3 tracking-wide uppercase",style:{color:"#ff0e2a"},children:"Tuber Akademi Yayıncı Portalı"}),e.jsxs("h1",{className:"text-4xl md:text-5xl font-bold tracking-tight mb-4",style:{lineHeight:"1.4"},children:[e.jsx("span",{className:"text-white",children:"Yayıncılık"}),e.jsx("br",{}),e.jsx("span",{className:"text-white",children:"yolculuğunuz burada."})]}),e.jsx("p",{className:"text-lg text-gray-300 max-w-2xl mb-6 leading-relaxed",children:"Özel eğitimlerimizle becerilerinizi geliştirin, diğer yayıncılarla beyin fırtınası yapın, teknik destek alın ve topluluk ortamında birlikte büyüyün."}),e.jsxs("div",{className:"space-y-3 mb-4",children:[e.jsxs("div",{className:"flex items-center text-gray-300 text-base",children:[e.jsx("div",{className:"w-2 h-2 rounded-full mr-3",style:{backgroundColor:"#ff0e2a"}}),e.jsx("span",{children:"Özel eğitim içerikleri ve canlı webinarlar"})]}),e.jsxs("div",{className:"flex items-center text-gray-300 text-base",children:[e.jsx("div",{className:"w-2 h-2 rounded-full mr-3",style:{backgroundColor:"#ff0e2a"}}),e.jsx("span",{children:"Yayıncı topluluğu ve beyin fırtınası ortamı"})]}),e.jsxs("div",{className:"flex items-center text-gray-300 text-base",children:[e.jsx("div",{className:"w-2 h-2 rounded-full mr-3",style:{backgroundColor:"#ff0e2a"}}),e.jsx("span",{children:"7/24 teknik destek ve ihlal yönetimi"})]}),e.jsxs("div",{className:"flex items-center text-gray-300 text-base",children:[e.jsx("div",{className:"w-2 h-2 rounded-full mr-3",style:{backgroundColor:"#ff0e2a"}}),e.jsx("span",{children:"Özel etkinlikler ve networking fırsatları"})]})]})]})]})}),e.jsx("div",{className:"lg:w-2/5 flex items-center justify-center p-4 lg:p-12 lg:pr-8 lg:mt-8 relative z-10 h-full",children:e.jsxs("div",{className:"w-full max-w-md lg:-ml-40 lg:mt-12",children:[e.jsx("div",{className:"lg:hidden mb-6 text-center",children:e.jsx("img",{src:"/images/logotuber1.png",alt:"Tuber Ajans Logo",className:"h-24 w-auto select-none mx-auto",style:{objectFit:"contain"}})}),e.jsxs("div",{className:"bg-gray-900/60 backdrop-blur-sm rounded-2xl border border-gray-700/50 shadow-2xl p-6 lg:p-8 transition-all duration-300",children:[e.jsxs("div",{className:"text-center mb-4",children:[e.jsx("h2",{className:"text-xl lg:text-2xl font-bold text-white mb-2",children:"Yayıncı Portalına Giriş"}),e.jsx("p",{className:"text-gray-300 text-xs lg:text-sm",children:"Özel eğitimlere, topluluk ortamına, teknik desteğe ve etkinliklere erişim sağlayın"})]}),g&&e.jsx("div",{className:"mb-6 p-4 rounded-lg text-sm bg-red-900/30 border border-red-500/30 text-red-400",children:e.jsxs("div",{className:"flex",children:[e.jsx("svg",{className:"h-5 w-5 mr-2 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),g]})}),e.jsxs("form",{onSubmit:Z,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-200 mb-1",children:"Kullanıcı Adı"}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(Pe,{className:"h-4 w-4 text-gray-500"})}),e.jsx("input",{id:"username",type:"text",value:t,onChange:p=>a(p.target.value),className:"block w-full pl-10 pr-3 py-3 rounded-lg text-white bg-gray-800/50 border border-gray-600 focus:ring-2 focus:border-transparent transition-all outline-none text-sm",style:{"--tw-ring-color":"#ff0e2a"},onFocus:p=>{p.target.style.borderColor="#ff0e2a",p.target.style.boxShadow="0 0 0 2px rgba(255, 14, 42, 0.2)"},onBlur:p=>{p.target.style.borderColor="#4b5563",p.target.style.boxShadow="none"},placeholder:"Kullanıcı adınızı girin",autoComplete:"username"})]})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between mb-1",children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-200",children:"Şifre"}),e.jsx("button",{type:"button",onClick:se,className:"text-xs font-medium transition-colors",style:{color:"#ff0e2a"},onMouseEnter:p=>p.target.style.color="#e80000",onMouseLeave:p=>p.target.style.color="#ff0e2a",children:"Şifremi Unuttum"})]}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{id:"password",type:n?"text":"password",value:r,onChange:p=>s(p.target.value),className:"block w-full pr-10 px-3 py-3 rounded-lg text-white bg-gray-800/50 border border-gray-600 focus:ring-2 focus:border-transparent transition-all outline-none text-sm",style:{"--tw-ring-color":"#ff0e2a"},onFocus:p=>{p.target.style.borderColor="#ff0e2a",p.target.style.boxShadow="0 0 0 2px rgba(255, 14, 42, 0.2)"},onBlur:p=>{p.target.style.borderColor="#4b5563",p.target.style.boxShadow="none"},placeholder:"Şifrenizi girin",autoComplete:"current-password"}),e.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:e.jsx("button",{type:"button",onClick:()=>u(!n),className:"text-gray-400 hover:text-gray-300 focus:outline-none",children:n?e.jsx(er,{className:"h-4 w-4"}):e.jsx(ra,{className:"h-4 w-4"})})})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{id:"remember-me",name:"remember-me",type:"checkbox",checked:i,onChange:p=>l(p.target.checked),className:"h-4 w-4 rounded bg-gray-800 border-gray-600 focus:ring-offset-gray-900",style:{accentColor:"#ff0e2a"}}),e.jsx("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-300",children:"Beni Hatırla"})]}),e.jsx("button",{type:"submit",disabled:K,className:"w-full flex items-center justify-center py-3 px-4 rounded-lg font-semibold text-white transition-all duration-300 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none",style:{backgroundColor:"#ff0e2a",borderColor:"#ff0e2a"},onMouseEnter:p=>{K||(p.target.style.backgroundColor="#e80000")},onMouseLeave:p=>{K||(p.target.style.backgroundColor="#ff0e2a")},children:K?e.jsxs("svg",{className:"animate-spin h-5 w-5 text-white",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):e.jsxs(e.Fragment,{children:[e.jsx(cr,{className:"mr-2 h-5 w-5"}),"Giriş Yap"]})}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 flex items-center",children:e.jsx("div",{className:"w-full border-t border-gray-600"})}),e.jsx("div",{className:"relative flex justify-center text-xs uppercase",children:e.jsx("span",{className:"px-3 bg-gray-900 text-gray-400 text-xs",children:"veya"})})]}),e.jsxs("button",{type:"button",onClick:X,className:"w-full flex items-center justify-center py-3 px-4 rounded-lg font-medium bg-gray-800/50 hover:bg-gray-700/50 text-white border border-gray-600 transition-all text-sm",children:[e.jsx(Pe,{className:"mr-2 h-5 w-5"}),"TikTok ile Giriş Yap"]})]})]}),e.jsx("div",{className:"mt-4 text-center",children:e.jsxs("p",{className:"text-gray-400 text-sm",children:["Henüz bir hesabın yok mu?"," ",e.jsx("button",{onClick:Y,className:"font-medium transition-colors bg-transparent border-none cursor-pointer p-0",style:{color:"#ff0e2a"},onMouseEnter:p=>p.target.style.color="#e80000",onMouseLeave:p=>p.target.style.color="#ff0e2a",children:"Kayıt Ol"})]})}),e.jsxs("div",{className:"mt-4 text-center",children:[e.jsx("p",{className:"text-xs text-gray-500",children:"© 2025 Tuber Ajans. Tüm hakları saklıdır."}),e.jsxs("div",{className:"mt-1 flex justify-center space-x-3",children:[e.jsx(ee,{to:"/privacy",className:"text-xs text-gray-500 hover:text-gray-400 transition-colors",children:"Gizlilik Politikası"}),e.jsx(ee,{to:"/terms",className:"text-xs text-gray-500 hover:text-gray-400 transition-colors",children:"Kullanım Şartları"})]})]})]})})]})]})},ue=o.createContext({isSidebarOpen:!0,setIsSidebarOpen:()=>{},isMobile:!1,setIsMobile:()=>{},toggleSidebar:()=>{},closeSidebar:()=>{}}),Ws=({children:t})=>{const[a,r]=o.useState(!1),s=o.useCallback(()=>{try{const x=localStorage.getItem("sidebarOpen");return x!==null?JSON.parse(x):!0}catch{return!0}},[]),[i,l]=o.useState(s()),n=o.useCallback(()=>{const x=window.innerWidth<1024;if(x!==a)if(r(x),x)l(!1);else{const v=s();l(v)}},[a,s]);o.useEffect(()=>{let x;const v=()=>{clearTimeout(x),x=setTimeout(n,100)};return n(),window.addEventListener("resize",v),()=>{window.removeEventListener("resize",v),clearTimeout(x)}},[n]),o.useEffect(()=>{i?(document.body.classList.add("sidebar-open"),document.body.classList.remove("sidebar-closed")):(document.body.classList.add("sidebar-closed"),document.body.classList.remove("sidebar-open"))},[i]);const u=o.useCallback(()=>{const x=!i;l(x);try{localStorage.setItem("sidebarOpen",JSON.stringify(x))}catch(v){console.error("Sidebar state kaydedilemedi:",v)}},[i]),g=o.useCallback(()=>{a&&l(!1)},[a]),h=o.useMemo(()=>({isSidebarOpen:i,setIsSidebarOpen:l,isMobile:a,setIsMobile:r,toggleSidebar:u,closeSidebar:g}),[i,a,u,g]);return e.jsx(ue.Provider,{value:h,children:t})},za=()=>{const t=o.useContext(ue);if(!t)throw new Error("useSidebar must be used within a SidebarProvider");return t};function qs({title:t,titleId:a,...r},s){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},r),t?o.createElement("title",{id:a},t):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5"}))}const Ca=o.forwardRef(qs);function Js({title:t,titleId:a,...r},s){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},r),t?o.createElement("title",{id:a},t):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15m3 0 3-3m0 0-3-3m3 3H9"}))}const La=o.forwardRef(Js);function Zs({title:t,titleId:a,...r},s){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},r),t?o.createElement("title",{id:a},t):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0M3.124 7.5A8.969 8.969 0 0 1 5.292 3m13.416 0a8.969 8.969 0 0 1 2.168 4.5"}))}const Xs=o.forwardRef(Zs);function Qs({title:t,titleId:a,...r},s){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},r),t?o.createElement("title",{id:a},t):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))}const ei=o.forwardRef(Qs);function ti({title:t,titleId:a,...r},s){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},r),t?o.createElement("title",{id:a},t):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))}const ai=o.forwardRef(ti);function ri({title:t,titleId:a,...r},s){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},r),t?o.createElement("title",{id:a},t):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"}))}const si=o.forwardRef(ri);function ii({title:t,titleId:a,...r},s){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},r),t?o.createElement("title",{id:a},t):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 1 1 0-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 0 1-1.44-4.282m3.102.069a18.03 18.03 0 0 1-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 0 1 8.835 2.535M10.34 6.66a23.847 23.847 0 0 0 8.835-2.535m0 0A23.74 23.74 0 0 0 18.795 3m.38 1.125a23.91 23.91 0 0 1 1.014 5.395m-1.014 8.855c-.118.38-.245.754-.38 1.125m.38-1.125a23.91 23.91 0 0 0 1.014-5.395m0-3.46c.495.413.811 1.035.811 1.73 0 .695-.316 1.317-.811 1.73m0-3.46a24.347 24.347 0 0 1 0 3.46"}))}const ni=o.forwardRef(ii);function li({title:t,titleId:a,...r},s){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},r),t?o.createElement("title",{id:a},t):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"}))}const oi=o.forwardRef(li);function di({title:t,titleId:a,...r},s){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},r),t?o.createElement("title",{id:a},t):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12.75 19.5v-.75a7.5 7.5 0 0 0-7.5-7.5H4.5m0-6.75h.75c7.87 0 14.25 6.38 14.25 14.25v.75M6 18.75a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z"}))}const ci=o.forwardRef(di);function mi({title:t,titleId:a,...r},s){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},r),t?o.createElement("title",{id:a},t):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"}))}const ui=o.forwardRef(mi);function xi({title:t,titleId:a,...r},s){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":a},r),t?o.createElement("title",{id:a},t):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const Aa=o.forwardRef(xi),hi=({onLogout:t})=>{const{isSidebarOpen:a,isMobile:r,closeSidebar:s}=za();ke();const i=()=>{const g="sidebar-smooth fixed top-0 left-0 h-screen bg-white dark:bg-gray-900 shadow-lg z-30 flex flex-col";return r?`${g} w-64 ${a?"translate-x-0":"-translate-x-full"}`:`${g} ${a?"w-64":"w-16"}`},l=()=>{const g={backfaceVisibility:"hidden",WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",contain:"layout style paint",isolation:"isolate"};return r?{...g,willChange:"transform",transform:a?"translateX(0) translateZ(0)":"translateX(-100%) translateZ(0)",transition:"transform 0.3s cubic-bezier(0.4, 0.0, 0.2, 1)"}:{...g,willChange:"width",transform:"translateZ(0)",transition:"width 0.3s cubic-bezier(0.4, 0.0, 0.2, 1)"}};o.useEffect(()=>{if(r&&a){const g=window.scrollY;document.body.style.overflow="hidden",document.body.style.position="fixed",document.body.style.top=`-${g}px`,document.body.style.width="100%"}else{const g=document.body.style.top;document.body.style.overflow="",document.body.style.position="",document.body.style.top="",document.body.style.width="",g&&window.scrollTo(0,parseInt(g)*-1)}return()=>{document.body.style.overflow="",document.body.style.position="",document.body.style.top="",document.body.style.width=""}},[r,a]);const n=[{icon:ar,text:"Anasayfa",path:"/dashboard"},{icon:ni,text:"Duyurular",path:"/dashboard/announcements"},{icon:ci,text:"Akış",path:"/dashboard/feed"},{icon:Ca,text:"Eğitimler",path:"/dashboard/courses"},{icon:ai,text:"Etkinlikler",path:"/dashboard/events"},{icon:si,text:"Taleplerim",path:"/dashboard/requests"}],u=window.location.pathname;return e.jsxs(e.Fragment,{children:[r&&e.jsx("button",{className:`fixed inset-0 bg-black z-20 transition-opacity duration-300 ease-out border-none cursor-pointer ${a?"opacity-50":"opacity-0 pointer-events-none"}`,onClick:s,onKeyDown:g=>{g.key==="Escape"&&s()},"aria-label":"Sidebar'ı kapat",style:{willChange:"opacity"}}),e.jsxs("aside",{className:i(),style:l(),children:[e.jsx("div",{className:"sidebar-content flex-1 overflow-y-auto py-4",style:{paddingTop:"6rem",transform:"translateZ(0)",backfaceVisibility:"hidden"},children:e.jsx("nav",{className:"space-y-1",style:{transform:"translateZ(0)",backfaceVisibility:"hidden",contain:"layout style",isolation:"isolate"},children:n.map(g=>{const h=u===g.path||g.path!=="/dashboard"&&u.startsWith(g.path);return e.jsxs(ee,{to:g.path,onClick:()=>r&&s(),className:`
                    flex items-center transition-colors duration-200 ease-out
                    ${a||r?"mx-3 px-3 py-2 rounded-lg":"mx-2 px-2 py-2 rounded-lg justify-center"}
                    ${h?"bg-pink-50 text-pink-600 dark:bg-pink-900/20 dark:text-pink-400":"text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800"}
                  `,style:{minHeight:"40px",height:"40px",transform:"translateZ(0)",backfaceVisibility:"hidden",willChange:"opacity",contain:"layout style",isolation:"isolate"},title:!a&&!r?g.text:void 0,children:[e.jsx(g.icon,{className:`w-5 h-5 flex-shrink-0 transition-opacity duration-200 ease-out ${a||r?"mr-3":""}`}),(a||r)&&e.jsx("span",{className:"font-normal truncate transition-opacity duration-200 ease-out",style:{transform:"translateZ(0)",backfaceVisibility:"hidden",willChange:"opacity"},children:g.text})]},g.path)})})}),e.jsxs("div",{className:"pb-4",children:[e.jsxs(ee,{to:"/dashboard/profile",onClick:()=>r&&s(),className:`
              flex items-center transition-colors duration-200 ease-out
              ${a||r?"mx-3 px-3 py-2 rounded-lg":"mx-2 px-2 py-2 rounded-lg justify-center"}
              ${u==="/dashboard/profile"?"bg-pink-50 text-pink-600 dark:bg-pink-900/20 dark:text-pink-400":"text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800"}
            `,style:{minHeight:"40px",height:"40px"},title:!a&&!r?"Profilim":void 0,children:[e.jsx(Aa,{className:`w-5 h-5 flex-shrink-0 transition-opacity duration-200 ease-out ${a||r?"mr-3":""}`}),(a||r)&&e.jsx("span",{className:"font-normal truncate transition-opacity duration-200 ease-out",children:"Profilim"})]}),e.jsx("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-2 mt-2",children:e.jsx("div",{className:`
                ${a||r?"mx-3":"mx-2"}
              `,children:e.jsxs("button",{onClick:()=>{r&&s(),t==null||t()},className:`
                  w-full flex items-center transition-colors duration-200 ease-out
                  ${a||r?"px-3 py-2 rounded-lg":"px-2 py-2 rounded-lg justify-center"}
                  text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800
                `,style:{minHeight:"40px",height:"40px"},title:!a&&!r?"Çıkış Yap":void 0,children:[e.jsx(La,{className:`w-5 h-5 flex-shrink-0 transition-opacity duration-200 ease-out ${a||r?"mr-3":""}`}),(a||r)&&e.jsx("span",{className:"font-normal truncate transition-opacity duration-200 ease-out",children:"Çıkış Yap"})]})})})]})]})]})},Ma=o.createContext(void 0),gi=({children:t})=>{var b;const[a,r]=o.useState(null),[s,i]=o.useState(!0),[l,n]=o.useState(null),u=ke(),g=(u==null?void 0:u.isAuthenticated)||!!localStorage.getItem("token"),h=o.useCallback(async()=>{if(console.log("initializeTikTokUser çağrıldı, isAuthenticated:",g),!g){console.log("Kullanıcı giriş yapmamış, TikTok temizleniyor"),x(),i(!1);return}try{i(!0),n(null),console.log("TikTok API çağrısı yapılıyor...");const w=await D.get("/backend/api/tiktok-api.php?action=get_user",{withCredentials:!0});if(console.log("TikTok API yanıtı:",w.status,w.data),w.data.status==="success"&&w.data.data){const c=w.data.data;if(console.log("API'den kullanıcı bilgileri alındı:",c),c.tiktok_linked===1||c.tiktok_open_id){const m={username:c.username,tiktok_username:c.tiktok_username,display_name:c.tiktok_display_name,avatar_url:c.tiktok_avatar_url,linked_at:c.tiktok_linked_at,followers_count:parseInt(c.followers_count)||0,following_count:parseInt(c.following_count)||0,likes_count:parseInt(c.likes_count)||0,video_count:parseInt(c.video_count)||0,is_verified:c.is_verified===1||c.is_verified===!0,bio:c.tiktok_bio||"",tiktok_bio:c.tiktok_bio||""};r(m),localStorage.setItem("tiktokUser",JSON.stringify(m)),console.log("TikTok kullanıcısı API'den başarıyla yüklendi:",m)}else console.log("TikTok hesabı bağlı değil - tiktok_linked:",c.tiktok_linked),x()}else{const c=localStorage.getItem("user");if(c)try{const m=JSON.parse(c);if(console.log("localStorage user TikTok kontrolü:",{tiktok_open_id:m.tiktok_open_id,tiktok_linked:m.tiktok_linked,tiktok_username:m.tiktok_username}),m.tiktok_open_id||m.tiktok_linked||m.tiktok_username){const k={username:m.username,tiktok_username:m.tiktok_username,display_name:m.tiktok_display_name,avatar_url:m.tiktok_avatar_url,linked_at:m.tiktok_linked_at,followers_count:parseInt(m.followers_count)||0,following_count:parseInt(m.following_count)||0,likes_count:parseInt(m.likes_count)||0,video_count:parseInt(m.video_count)||0,is_verified:m.is_verified===1,bio:m.bio,tiktok_bio:m.tiktok_bio};r(k),localStorage.setItem("tiktokUser",JSON.stringify(k))}else x()}catch(m){console.error("Error parsing stored user data:",m),x()}else x()}}catch(w){console.error("TikTok user initialization error:",w);const c=localStorage.getItem("user");if(c)try{const m=JSON.parse(c);if(m.tiktok_open_id){const k={username:m.username,tiktok_username:m.tiktok_username,display_name:m.tiktok_display_name,avatar_url:m.tiktok_avatar_url,linked_at:m.tiktok_linked_at,followers_count:parseInt(m.followers_count)||0,following_count:parseInt(m.following_count)||0,likes_count:parseInt(m.likes_count)||0,video_count:parseInt(m.video_count)||0,is_verified:m.is_verified===1,bio:m.bio,tiktok_bio:m.tiktok_bio};r(k),localStorage.setItem("tiktokUser",JSON.stringify(k))}else x()}catch(m){console.error("Error parsing stored user data in fallback:",m),x()}else x();n("TikTok bilgileri yüklenirken hata oluştu")}finally{i(!1)}},[g]);o.useEffect(()=>{const w=m=>{m.data.type==="tiktok_oauth_result"&&(console.log("TikTok OAuth sonucu alındı:",m.data),m.data.status==="success"?setTimeout(()=>{h()},1e3):n(m.data.message||"TikTok bağlantısı başarısız"))},c=()=>{console.log("Kullanıcı giriş eventi alındı, TikTok durumu kontrol ediliyor..."),setTimeout(()=>{h()},500)};return window.addEventListener("message",w),window.addEventListener("userLoggedIn",c),()=>{window.removeEventListener("message",w),window.removeEventListener("userLoggedIn",c)}},[h]),o.useEffect(()=>{var w,c;console.log("TikTok useEffect tetiklendi, isAuthenticated:",g,"user:",(w=u==null?void 0:u.user)==null?void 0:w.id),g&&((c=u==null?void 0:u.user)!=null&&c.id)&&h()},[g,(b=u==null?void 0:u.user)==null?void 0:b.id,h]),o.useEffect(()=>{new URLSearchParams(window.location.search).get("tiktok_link")==="success"&&(window.history.replaceState({},document.title,window.location.pathname),setTimeout(()=>{h()},1e3))},[h]),o.useEffect(()=>{const w=()=>{setTimeout(()=>{h()},500)},c=()=>{console.log("TikTok linked event received, refreshing data..."),setTimeout(()=>{h()},500)};return window.addEventListener("userLoggedIn",w),window.addEventListener("tiktokLinked",c),()=>{window.removeEventListener("userLoggedIn",w),window.removeEventListener("tiktokLinked",c)}},[h]);const x=()=>{r(null),localStorage.removeItem("tiktokUser")},v=o.useCallback(async w=>{var c;try{i(!0),n(null);const m=await D.post("/backend/api/tiktok-api.php?action=link_account",w,{withCredentials:!0});if(m.data&&m.data.status==="success"){const k={...JSON.parse(localStorage.getItem("user")||"{}"),tiktok_username:w.username,tiktok_display_name:w.display_name,tiktok_avatar_url:w.avatar_url,tiktok_bio:w.bio_description,followers_count:w.followers_count,following_count:w.following_count,likes_count:w.likes_count,video_count:w.video_count,is_verified:w.is_verified,tiktok_open_id:m.data.tiktok_open_id,tiktok_linked_at:new Date().toISOString()};return localStorage.setItem("user",JSON.stringify(k)),h(),!0}else return n(((c=m.data)==null?void 0:c.message)||"TikTok hesabı bağlanamadı"),!1}catch(m){return console.error("TikTok hesabı bağlanırken hata:",m),n("TikTok hesabı bağlanırken bir hata oluştu"),!1}finally{i(!1)}},[h]),E=o.useCallback(async()=>{var w;try{i(!0),n(null);const c=await D.post("/backend/api/tiktok-api.php?action=unlink",{},{withCredentials:!0});if(c.data&&c.data.status==="success"){const k={...JSON.parse(localStorage.getItem("user")||"{}"),tiktok_username:null,tiktok_display_name:null,tiktok_avatar_url:null,tiktok_bio:null,followers_count:null,following_count:null,likes_count:null,video_count:null,is_verified:null,tiktok_open_id:null,tiktok_linked_at:null};return localStorage.setItem("user",JSON.stringify(k)),x(),!0}else return n(((w=c.data)==null?void 0:w.message)||"TikTok hesap bağlantısı kaldırılamadı"),!1}catch(c){return console.error("TikTok hesap bağlantısı kaldırma hatası:",c),n("TikTok hesap bağlantısı kaldırılırken hata oluştu"),!1}finally{i(!1)}},[]),R=o.useCallback(()=>{n(null)},[]),N=o.useCallback(async()=>{h()},[h]),z=o.useMemo(()=>({tiktokUser:a,loading:s,error:l,linkTikTokAccount:v,unlinkTikTokAccount:E,refreshTikTokData:N,clearError:R}),[a,s,l,v,E,N,R]);return e.jsx(Ma.Provider,{value:z,children:t})},Ze=()=>{const t=o.useContext(Ma);if(t===void 0)throw new Error("useTikTok must be used within a TikTokProvider");return t},pi=({onLogout:t,refreshTrigger:a})=>{const{toggleSidebar:s}=za(),i=ke(),l=i!=null&&i.logout?i.logout:()=>(localStorage.removeItem("user"),localStorage.removeItem("token"),Promise.resolve()),[n,u]=o.useState(!1),[g,h]=o.useState(!1),[x,v]=o.useState(localStorage.getItem("darkMode")==="true"),E=o.useRef(null),R=o.useRef(null),N=o.useCallback(A=>{E.current&&!E.current.contains(A.target)&&u(!1),R.current&&!R.current.contains(A.target)&&h(!1)},[]);o.useEffect(()=>(document.addEventListener("mousedown",N),()=>{document.removeEventListener("mousedown",N)}),[N]),o.useEffect(()=>{x?(document.documentElement.classList.add("dark"),localStorage.setItem("darkMode","true")):(document.documentElement.classList.remove("dark"),localStorage.setItem("darkMode","false"))},[x]);const z=o.useCallback(()=>{v(!x)},[x]),[b,w]=o.useState([]),[c,m]=o.useState(!1),[k,T]=o.useState(null),S=b.filter(A=>!A.read).length,d=()=>{if(L)return e.jsx("div",{className:"w-7 h-7 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse"});const A=(W==null?void 0:W.avatar_url)||(j==null?void 0:j.avatar_url);if(A)return e.jsx("img",{src:A,alt:(W==null?void 0:W.display_name)||(j==null?void 0:j.name),className:"w-7 h-7 rounded-full object-cover shadow-md transition-all duration-200 hover:shadow-lg border-2 border-gray-300 dark:border-gray-600",onError:p=>{p.target.src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face&auto=format&q=80"}});const U=localStorage.getItem("user");let C=null;if(U)try{C=JSON.parse(U)}catch(p){console.error("Stored user parse error:",p)}const V=(C==null?void 0:C.username)||(C==null?void 0:C.name)||(j==null?void 0:j.username)||(j==null?void 0:j.name)||(W==null?void 0:W.username)||(W==null?void 0:W.display_name)||"U";return e.jsx("div",{className:"w-7 h-7 rounded-full bg-gradient-to-r from-tuber-pink to-tuber-purple flex items-center justify-center text-white font-medium shadow-md transition-all duration-200 hover:shadow-lg",children:V.substring(0,1).toUpperCase()})},[j,_]=o.useState(null),[L,O]=o.useState(!1),{tiktokUser:W}=Ze(),M=o.useCallback(async()=>{m(!0),T(null);try{const A=localStorage.getItem("user");let U=1;if(A)try{U=JSON.parse(A).id||1}catch(V){console.error("User parse error:",V)}const C=await D.get("/backend/api/notifications.php",{params:{user_id:U,limit:10}});if(C.data.status==="success"){const V=C.data.data.map(p=>{var I;let B="Bildirim",q=p.content,Q="",y="general";switch(p.type){case"new_course":B="Yeni Eğitim Eklendi",Q=`/dashboard/courses/${p.related_id}`,y="new_course";break;case"new_event":B="Yeni Etkinlik",Q=`/dashboard/events/${p.related_id}`,y="new_event";break;case"course_completed":B="Eğitim Tamamlandı",Q=`/dashboard/courses/${p.related_id}`,y="course_completed";break;case"like":B="Beğeni Aldınız",q=`${p.sender_username||"Birisi"} gönderinizi beğendi`,Q="/dashboard/feed",y="like";break;case"comment":B="Yeni Yorum",q=`${p.sender_username||"Birisi"} gönderinize yorum yaptı`,Q="/dashboard/feed",y="comment";break;case"follow":B="Yeni Takipçi",q=`${p.sender_username||"Birisi"} sizi takip etmeye başladı`,Q="/dashboard/profile",y="follow";break;default:B="Genel Bildirim",y="general"}return{id:p.id,title:B,message:q,time:X(new Date(p.created_at)),read:!!p.is_read,type:p.type||"info",source:y,source_id:(I=p.related_id)==null?void 0:I.toString(),link:Q}});w(V)}else T("Bildirimler alınamadı"),console.error("Bildirimler alınamadı:",C.data.message)}catch(A){T("Bildirimler yüklenirken bir hata oluştu"),console.error("Bildirimler yüklenirken hata:",A)}finally{m(!1)}},[!1]),F=o.useCallback(async()=>{var A,U,C;try{const V=localStorage.getItem("user_info_last_fetch"),p=Date.now();if(V&&p-parseInt(V)<6e4)return;O(!0);const B=!1,q=await D.get("/backend/api/user_session.php");if(localStorage.setItem("user_info_last_fetch",p.toString()),q.data.status==="success")_(q.data.data);else{console.warn("Kullanıcı bilgileri alınamadı:",q.data.message);const Q=localStorage.getItem("user");if(Q){const y=JSON.parse(Q);_({id:y.id||1,name:y.name||y.username||"Kullanıcı",username:y.username||"kullanici",email:y.email||"<EMAIL>",avatar_url:y.avatar_url||y.tiktok_avatar_url||"",is_verified:y.is_verified||!1,role:y.role||"user",tiktok_linked:y.tiktok_linked||0,tiktok_username:y.tiktok_username||null,tiktok_display_name:y.tiktok_display_name||null,tiktok_avatar_url:y.tiktok_avatar_url||null})}else de()}}catch(V){console.error("Kullanıcı bilgileri yüklenirken hata:",V),D.isAxiosError(V)?V.code==="ECONNABORTED"||(A=V.message)!=null&&A.includes("timeout")?console.error("Request timeout"):((U=V.response)==null?void 0:U.status)===404?console.error("User not found"):console.error("API error:",(C=V.response)==null?void 0:C.status):console.error("Unknown error:",V);const p=localStorage.getItem("user");if(p){const B=JSON.parse(p);_({id:B.id||1,name:B.name||B.username||"Kullanıcı",username:B.username||"kullanici",email:B.email||"<EMAIL>",avatar_url:B.avatar_url||B.tiktok_avatar_url||"",is_verified:B.is_verified||!1,role:B.role||"user",tiktok_linked:B.tiktok_linked||0,tiktok_username:B.tiktok_username||null,tiktok_display_name:B.tiktok_display_name||null,tiktok_avatar_url:B.tiktok_avatar_url||null})}else de()}finally{O(!1)}},[]),K=A=>{switch(A){case"info":return"bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300";case"success":return"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300";case"warning":return"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300";case"error":return"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300";default:return"bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-300"}},Z=A=>{if(A.source==="new_event")return"Etkinlik";if(A.source==="new_course")return"Eğitim";if(A.source==="ticket_reply")return"Destek";switch(A.type){case"info":return"Bilgi";case"success":return"Başarılı";case"warning":return"Uyarı";case"error":return"Hata";default:return"Genel"}},se=()=>c?e.jsxs("div",{className:"flex items-center justify-center py-8",children:[e.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-tuber-pink"}),e.jsx("span",{className:"ml-2 text-sm text-gray-600 dark:text-gray-400",children:"Yükleniyor..."})]}):k?e.jsx("div",{className:"p-4 text-center",children:e.jsx("p",{className:"text-sm text-red-600 dark:text-red-400",children:k})}):b.length===0?e.jsx("div",{className:"p-4 text-center",children:e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Henüz bildirim yok"})}):e.jsx("div",{className:"max-h-80 overflow-y-auto",children:b.map(A=>e.jsx("button",{className:`w-full text-left p-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border-none ${A.read?"":"bg-pink-50/50 dark:bg-pink-900/10"}`,onClick:()=>{Y(A.id),A.link&&(window.location.href=A.link)},"aria-label":`Bildirim: ${A.title}`,children:e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:`mt-0.5 w-2 h-2 rounded-full flex-shrink-0 ${A.read?"bg-gray-300 dark:bg-gray-600":"bg-tuber-pink"}`}),e.jsxs("div",{className:"ml-3 flex-1",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:A.title}),e.jsx("p",{className:"text-xs text-gray-600 dark:text-gray-300 mt-0.5",children:A.message}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:A.time}),A.type&&e.jsx("span",{className:`ml-2 text-xs px-1.5 py-0.5 rounded-full ${K(A.type)}`,children:Z(A)})]})]})]})},A.id))});o.useEffect(()=>{M(),F()},[M,F]),o.useEffect(()=>{a&&a>0&&F()},[a,F]);const de=()=>{const A=localStorage.getItem("user");let U;if(A){const C=JSON.parse(A);U={id:C.id||1,name:C.name||C.username||"tuberajans",username:C.username||"tuberajans",email:C.email||"<EMAIL>",avatar_url:C.avatar_url||"",is_verified:C.is_verified||!1,role:C.role||"user"}}else U={id:1,name:"tuberajans",username:"tuberajans",email:"<EMAIL>",avatar_url:"",is_verified:!1,role:"user"};_(U),O(!1)},Y=async A=>{try{const U=localStorage.getItem("user");let C=1;if(U)try{C=JSON.parse(U).id||1}catch(p){console.error("User parse error:",p)}(await D.post("/backend/api/notifications.php",{action:"mark_as_read",notification_id:A,user_id:C})).data.status==="success"&&w(b.map(p=>p.id===A?{...p,read:!0}:p))}catch(U){console.error("Bildirim okundu olarak işaretlenirken hata:",U)}},G=async()=>{try{const A=localStorage.getItem("user");let U=1;if(A)try{U=JSON.parse(A).id||1}catch(V){console.error("User parse error:",V)}(await D.post("/backend/api/notifications.php",{action:"mark_all_as_read",user_id:U})).data.status==="success"&&w(b.map(V=>({...V,read:!0})))}catch(A){console.error("Tüm bildirimler okundu olarak işaretlenirken hata:",A)}},X=A=>{const C=new Date().getTime()-A.getTime(),V=Math.floor(C/1e3),p=Math.floor(V/60),B=Math.floor(p/60),q=Math.floor(B/24);return V<60?`${V} saniye önce`:p<60?`${p} dakika önce`:B<24?`${B} saat önce`:q<30?`${q} gün önce`:A.toLocaleDateString("tr-TR")};return e.jsxs("header",{className:"bg-black dark:bg-black border-b border-gray-800 h-16 flex items-center shadow-sm z-20 fixed top-0 left-0 right-0 transition-colors duration-200",children:[e.jsx("button",{onClick:s,className:"p-2 mr-2 focus:outline-none rounded-lg transition-all text-gray-300 flex items-center justify-center","aria-label":"Toggle sidebar",style:{width:"40px",height:"40px",marginLeft:"12px"},children:e.jsx(Ga,{className:"w-5 h-5"})}),e.jsx("img",{src:"/images/logotuber1.png",alt:"Tuber X Akademi Logo",className:"h-8 w-auto rounded-lg select-none mr-4",style:{objectFit:"contain"}}),e.jsxs("div",{className:"ml-auto flex items-center space-x-2 pr-6",children:[e.jsx("button",{onClick:z,className:"p-2 text-gray-500 dark:text-gray-300 hover:text-tuber-pink dark:hover:text-tuber-pink hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg focus:outline-none transition-all","aria-label":x?"Light mode":"Dark mode",children:x?e.jsx(ui,{className:"h-5 w-5"}):e.jsx(oi,{className:"h-5 w-5"})}),e.jsxs("div",{className:"relative",ref:R,children:[e.jsxs("button",{onClick:()=>h(!g),className:"relative p-2 text-gray-300 hover:text-tuber-pink hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg focus:outline-none transition-all","aria-label":"Notifications",children:[S>0?e.jsx(Xs,{className:"h-5 w-5"}):e.jsx(ei,{className:"h-5 w-5"}),S>0&&e.jsx("span",{className:"absolute top-0 right-0 h-4 w-4 rounded-full bg-red-500 flex items-center justify-center text-xs text-white font-medium",children:S})]}),e.jsx(st,{children:g&&e.jsxs(le.div,{initial:{opacity:0,y:10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:10,scale:.95},transition:{type:"spring",duration:.2,stiffness:500,damping:30},className:"absolute right-0 mt-2 w-80 rounded-xl bg-white dark:bg-gray-800 shadow-lg ring-1 ring-black/5 dark:ring-white/10 overflow-hidden z-50",children:[e.jsxs("div",{className:"p-3 border-b border-gray-100 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 flex items-center justify-between",children:[e.jsx("h3",{className:"font-medium text-sm text-gray-900 dark:text-white",children:"Bildirimler"}),S>0&&e.jsx("button",{onClick:G,className:"text-xs text-tuber-pink dark:text-pink-400 hover:underline font-medium",children:"Tümünü Okundu Olarak İşaretle"})]}),se(),b.length>0&&e.jsx("div",{className:"p-2 border-t border-gray-100 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 text-center",children:e.jsx("button",{className:"text-xs font-medium text-tuber-pink dark:text-pink-400 hover:underline",onClick:()=>window.location.href="/dashboard/notifications",children:"Tüm bildirimleri görüntüle"})})]})})]}),e.jsxs("div",{className:"relative",ref:E,children:[e.jsx("button",{onClick:()=>u(!n),className:"flex items-center focus:outline-none","aria-label":"Open user menu",children:e.jsx("div",{className:"relative",children:d()})}),e.jsx(st,{children:n&&e.jsx(le.div,{initial:{opacity:0,y:10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:10,scale:.95},transition:{type:"spring",duration:.2,stiffness:500,damping:30},className:"absolute right-0 mt-2 w-60 rounded-xl bg-white dark:bg-gray-800 shadow-lg ring-1 ring-black/5 dark:ring-white/10 overflow-hidden z-50",children:e.jsxs("div",{className:"py-1",children:[e.jsxs(ee,{to:"/dashboard/profile",className:"flex items-center px-4 py-2.5 text-sm text-gray-700 dark:text-gray-200 hover:bg-pink-50 dark:hover:bg-gray-700 hover:text-tuber-pink dark:hover:text-tuber-pink transition-colors",children:[e.jsx(Aa,{className:"mr-3 h-5 w-5 text-gray-500 dark:text-gray-400"}),"Profilim"]}),e.jsx("hr",{className:"my-1 border-gray-100 dark:border-gray-700"}),e.jsxs("button",{onClick:async()=>{await l(),t&&t()},className:"flex w-full items-center px-4 py-2.5 text-sm text-gray-700 dark:text-gray-200 hover:bg-pink-50 dark:hover:bg-gray-700 hover:text-red-600 dark:hover:text-red-400 transition-colors text-left",children:[e.jsx(La,{className:"mr-3 h-5 w-5 text-gray-500 dark:text-gray-400"}),"Çıkış Yap"]})]})})})]})]})]})};function he(t){return P({attr:{fill:"currentColor",viewBox:"0 0 16 16"},child:[{tag:"path",attr:{d:"M14 0H2a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2M1 3.857C1 3.384 1.448 3 2 3h12c.552 0 1 .384 1 .857v10.286c0 .473-.448.857-1 .857H2c-.552 0-1-.384-1-.857z"},child:[]},{tag:"path",attr:{d:"M6.5 7a1 1 0 1 0 0-2 1 1 0 0 0 0 2m3 0a1 1 0 1 0 0-2 1 1 0 0 0 0 2m3 0a1 1 0 1 0 0-2 1 1 0 0 0 0 2m-9 3a1 1 0 1 0 0-2 1 1 0 0 0 0 2m3 0a1 1 0 1 0 0-2 1 1 0 0 0 0 2m3 0a1 1 0 1 0 0-2 1 1 0 0 0 0 2m3 0a1 1 0 1 0 0-2 1 1 0 0 0 0 2m-9 3a1 1 0 1 0 0-2 1 1 0 0 0 0 2m3 0a1 1 0 1 0 0-2 1 1 0 0 0 0 2m3 0a1 1 0 1 0 0-2 1 1 0 0 0 0 2"},child:[]}]})(t)}const Gt=t=>({Genel:"bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300",Etkinlik:"bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300",Önemli:"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300","Marka İş Birliği":"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300"})[t]||"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300",fi=t=>({info:"bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300",success:"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300",warning:"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300",error:"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300"})[t]||"bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-300",bi=(t,a)=>{const r={new_event:"Etkinlik",new_course:"Eğitim",ticket_reply:"Destek"},s={info:"Bilgi",success:"Başarılı",warning:"Uyarı",error:"Hata"};return r[t]||s[a]||a};function ht(t){return P({attr:{role:"img",viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"},child:[]}]})(t)}const Wt="https://akademi.tuberajans.com/backend/api",ki=({onLogout:t})=>{ke();const{isSidebarOpen:r,isMobile:s}=o.useContext(ue),[i,l]=o.useState(["Tüm Kategoriler"]),[n,u]=o.useState("Tüm Kategoriler"),[g,h]=o.useState(!0),[x,v]=o.useState(null),[E,R]=o.useState([]),[N,z]=o.useState({Genel:[],Başlangıç:[],"Orta Seviye":[],"İleri Seviye":[],Monetizasyon:[]}),[b,w]=o.useState([]),[c,m]=o.useState(null),[k,T]=o.useState(!1),{tiktokUser:S,loading:d}=Ze(),[j,_]=o.useState(0),[L,O]=o.useState({currentMonth:{totalHours:0,targetHours:15,completedDays:0,targetDays:7},lastUpdate:new Date().toISOString()}),W=o.useCallback(async()=>{var Y;try{const G=await D.get(`${Wt}/broadcast_stats.php`);(Y=G.data)!=null&&Y.success&&O(G.data.data)}catch(G){console.error("Yayın istatistikleri alınamadı:",G)}},[!1]),M=it();o.useEffect(()=>{(async()=>{try{h(!0);const G=new AbortController,X=setTimeout(()=>G.abort(),15e3),A=await D.get(`${Wt}/dashboard_data.php`,{signal:G.signal,timeout:15e3});if(clearTimeout(X),A.data&&A.data.status==="success"){if(A.data.announcements){const U=A.data.announcements.map(C=>({id:C.id,title:C.title,content:C.content,description:C.content?C.content.substring(0,120)+"...":"",dateFormatted:C.created_at?new Date(C.created_at).toLocaleDateString("tr-TR",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}):"",created_at:C.created_at,category:C.category,type:C.type||C.category}));R(U.slice(0,4))}if(A.data.courses){const U=A.data.courses,C=new Set;U.forEach(B=>{const q=B.category||"Genel";C.add(q)});const V=["Tüm Kategoriler",...Array.from(C)];l(V);const p={};Array.from(C).forEach(B=>{p[B]=[]}),U.forEach(B=>{const q=B.category||"Genel";p[q]?p[q].push(B):(p.Genel=p.Genel||[],p.Genel.push(B))}),z(p)}if(A.data.events){const U=A.data.events.map(p=>{const B=new Date(p.start_date),q=new Date(p.end_date);let Q="bg-blue-100 text-blue-600",y="Planlanıyor";return p.status==="active"?(Q="bg-green-100 text-green-600",y="Onaylandı"):p.status==="pending"&&(Q="bg-orange-100 text-orange-600",y="Beklemede"),{...p,date:B,endDate:q,isFeatured:!!p.is_featured,isPastEvent:B<=new Date,statusColor:Q,statusText:y}}),C=new Date,V=U.filter(p=>p.date&&p.date>C&&p.status==="active").sort((p,B)=>p.date.getTime()-B.date.getTime());w(V.slice(0,3))}}}catch(G){if(console.error("Dashboard verileri yüklenirken hata oluştu:",G),D.isCancel(G))v("Sunucu yanıt vermedi. Lütfen daha sonra tekrar deneyin.");else if(G&&typeof G=="object"&&"code"in G&&G.code==="ECONNABORTED")v("Sunucu yanıt vermedi. Lütfen daha sonra tekrar deneyin.");else{const X=G instanceof Error?G.message:"Bilinmeyen hata";v(`Dashboard verileri yüklenirken bir hata oluştu: ${X}`)}R([]),z({Genel:[],Başlangıç:[],"Orta Seviye":[],"İleri Seviye":[],Monetizasyon:[]})}finally{h(!1)}})()},[!1]),o.useEffect(()=>{(M.pathname==="/dashboard"||M.pathname==="/dashboard/")&&W()},[M.pathname,W]),o.useEffect(()=>{new URLSearchParams(window.location.search).get("tiktok_login")==="success"&&(window.history.replaceState({},document.title,window.location.pathname),setTimeout(()=>{_(G=>G+1)},2e3))},[]);const F=it(),K=()=>g?e.jsx("div",{className:"p-6 text-center",children:e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Duyurular yükleniyor..."})}):x?e.jsx("div",{className:"p-6 text-center",children:e.jsx("p",{className:"text-sm text-red-500",children:x})}):E.length===0?e.jsx("div",{className:"p-6 text-center",children:e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Henüz duyuru bulunmamaktadır."})}):E.map(Y=>e.jsx("div",{className:"p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors cursor-pointer",onClick:()=>{m(Y),T(!0)},children:e.jsxs("div",{className:"flex items-stretch",children:[e.jsx("div",{className:"min-w-[4px] bg-[#FF3E71] rounded-full mr-3"}),e.jsxs("div",{className:"flex-1 overflow-hidden",children:[e.jsxs("div",{className:"flex items-center text-xs text-gray-500 dark:text-gray-400 mb-1",children:[e.jsx(he,{className:"text-gray-500 dark:text-gray-400 mr-1 text-xs"}),e.jsx("span",{children:Y.dateFormatted}),["Genel","Etkinlik","Önemli","Marka İş Birliği"].includes(Y.type||"")&&e.jsx("span",{className:`ml-2 px-4 py-0.5 rounded-full text-xs font-medium ${Gt(Y.type||"")}`,children:Y.type})]}),e.jsx("h3",{className:"text-sm font-medium mb-1 text-gray-800 dark:text-white truncate",children:Y.title}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 line-clamp-2",children:Y.description})]})]})},Y.id)),Z=o.useMemo(()=>(n==="Tüm Kategoriler"?Object.values(N).flat():N[n]||[]).slice().sort((G,X)=>new Date(X.created_at||X.date).getTime()-new Date(G.created_at||G.date).getTime()),[n,N]),se=()=>{const Y="awfw8k9nim1e8dmu",G=encodeURIComponent("https://akademi.tuberajans.com/backend/api/tiktok-callback.php"),X="dashboard_"+Math.random().toString(36).substring(2,15);localStorage.setItem("tiktok_oauth_state",X),localStorage.setItem("tiktok_redirect_after","dashboard");const U=`https://www.tiktok.com/v2/auth/authorize/?client_key=${Y}&response_type=code&scope=user.info.basic,user.info.profile,user.info.stats,video.list&redirect_uri=${G}&state=${X}`;window.location.href=U};o.useEffect(()=>{const Y=new URLSearchParams(window.location.search),G=Y.get("tiktok_login"),X=Y.get("state");if(G==="success"){_(U=>U+1);const A=window.location.pathname;window.history.replaceState({},document.title,A)}X&&(localStorage.getItem("tiktok_oauth_state")!==X?console.error("TikTok OAuth state mismatch"):localStorage.removeItem("tiktok_oauth_state"))},[]);const de=()=>{var G,X,A,U;return F.pathname!=="/dashboard"?e.jsx("div",{style:{maxWidth:s?"100%":"none",width:s?"100%":"auto",overflowX:"hidden",position:"relative",left:0,paddingLeft:s?"0.25rem":0,paddingRight:s?"0.25rem":0,boxSizing:"border-box"},children:e.jsx(Ia,{})}):e.jsx("div",{className:"container",style:{maxWidth:s?"100%":"none",width:s?"100%":"auto",overflowX:"hidden",position:"relative",left:0,marginLeft:0,paddingTop:s?"0.5rem":"0.75rem",paddingLeft:s?"0.25rem":"0rem",paddingRight:s?"0.25rem":"1rem",boxSizing:"border-box"},children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-2 sm:gap-4",children:[e.jsxs("div",{className:"w-full md:w-3/4 space-y-4",children:[e.jsxs("div",{className:"bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden",children:[e.jsxs("div",{className:"flex items-center justify-between px-4 py-3 border-b border-gray-100 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Oe,{className:"text-gray-700 dark:text-gray-300 mr-2"}),e.jsx("h2",{className:"text-base font-semibold text-gray-800 dark:text-white",children:"Duyurular"})]}),e.jsx(ee,{to:"/dashboard/announcements",className:"text-[#FF3E71] text-sm font-medium hover:underline",children:"Tümünü Gör"})]}),e.jsx("div",{className:"divide-y divide-gray-100 dark:divide-gray-700",children:K()})]}),e.jsxs("div",{className:"bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden",children:[e.jsxs("div",{className:"flex items-center justify-between px-4 py-3 border-b border-gray-100 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Ca,{className:"w-5 h-5 mr-2 text-gray-700 dark:text-gray-300"}),e.jsx("h2",{className:"text-base font-semibold text-gray-800 dark:text-white",children:"Eğitimler"})]}),e.jsx(ee,{to:"/dashboard/courses",className:"text-[#FF3E71] text-sm font-medium hover:underline",children:"Tümü"})]}),e.jsx("div",{className:"px-4 py-3",children:e.jsx("div",{className:"overflow-x-auto mb-4 px-2 sm:px-0",children:e.jsx("div",{className:"flex gap-2 sm:gap-3 pb-1",style:{minWidth:"max-content"},children:i.map(C=>e.jsx("button",{onClick:()=>u(C),className:`whitespace-nowrap px-4 sm:px-5 py-1.5 sm:py-2 text-xs sm:text-sm font-medium rounded-full transition-all duration-200 flex-shrink-0 ${n===C?"bg-[#FF3E71] text-white":"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:C},C))})})}),e.jsx("div",{className:"px-4 pb-4",children:g?e.jsx("div",{className:"p-6 text-center",children:e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Eğitimler yükleniyor..."})}):x?e.jsx("div",{className:"p-6 text-center",children:e.jsx("p",{className:"text-sm text-red-500",children:x})}):Z.length===0?e.jsx("div",{className:"p-6 text-center",children:e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Bu kategoride henüz eğitim bulunmamaktadır."})}):e.jsx("div",{className:"grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4",children:Z.map(C=>e.jsxs(ee,{to:`/dashboard/courses/${C.id}`,className:"relative group block",children:[e.jsxs("div",{className:"rounded-lg overflow-hidden aspect-video relative",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-[#FF3E71]/90 to-[#FF5F87]/90 brightness-90"}),e.jsx("div",{className:"absolute inset-0 opacity-10",children:e.jsx("div",{className:"absolute inset-0",style:{backgroundImage:"radial-gradient(circle, rgba(255,255,255,0.8) 1px, transparent 1px)",backgroundSize:"20px 20px"}})}),e.jsx("div",{className:"absolute -top-20 -right-20 w-40 h-40 rounded-full bg-white opacity-20 blur-2xl"}),e.jsx("div",{className:"absolute -bottom-10 -left-10 w-32 h-32 rounded-full bg-white opacity-10 blur-xl"}),e.jsx("div",{className:"absolute inset-0 flex flex-col justify-center items-center p-3 sm:p-4 text-center",children:e.jsx("h2",{className:"text-sm sm:text-base lg:text-xl font-bold text-white drop-shadow-lg line-clamp-2 mb-1 sm:mb-2",children:C.title})}),e.jsx("div",{className:"absolute top-2 right-2 bg-black/40 backdrop-blur-md text-white text-[9px] xs:text-[10px] px-2.5 xs:px-3.5 py-0.5 xs:py-1 rounded-full border border-white/20 shadow-lg font-medium tracking-wide",children:C.category||n})]}),e.jsxs("div",{className:"mt-2",children:[e.jsx("h3",{className:"text-xs sm:text-sm font-medium text-gray-800 dark:text-white",children:C.title}),e.jsx("p",{className:"text-[10px] xs:text-xs text-gray-500 dark:text-gray-400 mt-1 line-clamp-1",children:C.description}),e.jsx("div",{className:"flex items-center justify-between text-[9px] xs:text-[10px] sm:text-xs text-gray-500 dark:text-gray-400 mt-1",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(he,{className:"mr-1"}),e.jsx("span",{children:new Date(C.created_at||"").toLocaleDateString("tr-TR")})]})})]})]},C.id))})})]})]}),e.jsxs("div",{className:"w-full md:w-1/4 space-y-4 flex flex-col",style:{paddingRight:s?"0":"1rem"},children:[e.jsxs("div",{className:"bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden",children:[e.jsxs("div",{className:"flex items-center justify-between px-4 py-3 border-b border-gray-100 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Me,{className:"text-gray-700 dark:text-gray-300 mr-2"}),e.jsx("h2",{className:"text-base font-semibold text-gray-800 dark:text-white",children:"Planlı Etkinlikler"})]}),e.jsx(ee,{to:"/dashboard/events",className:"text-[#FF3E71] text-sm font-medium hover:underline",children:"Tümü"})]}),e.jsxs("div",{className:"divide-y divide-gray-100 dark:divide-gray-700",children:[g&&e.jsx("div",{className:"p-6 text-center",children:e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Etkinlikler yükleniyor..."})}),!g&&x&&e.jsx("div",{className:"p-6 text-center",children:e.jsx("p",{className:"text-sm text-red-500",children:x})}),!g&&!x&&b.length>0&&[...b].map((C,V)=>{var B,q,Q;const p=V===0;return e.jsx("div",{className:`p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors ${p?"bg-pink-50/30 dark:bg-pink-900/10":""}`,children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:`flex-shrink-0 rounded-lg px-2 py-1.5 shadow-sm min-w-[60px] ${p?"bg-gradient-to-r from-[#FF3E71]/80 to-[#FF5F87]/80 text-white":"bg-white/90 dark:bg-gray-800/90"}`,children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("div",{className:`text-lg font-bold leading-tight ${p?"text-white":"text-[#FF3E71]"}`,children:(B=C.date)==null?void 0:B.getDate()}),e.jsx("div",{className:`text-xs font-medium leading-tight ${p?"text-white":"text-gray-700 dark:text-gray-300"}`,children:(q=C.date)==null?void 0:q.toLocaleDateString("tr-TR",{month:"short"})})]})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("h4",{className:`text-sm font-medium truncate ${p?"text-gray-700 dark:text-gray-200":"text-gray-900 dark:text-white"}`,children:[C.title,C.isFeatured&&p&&e.jsx("span",{className:"ml-1",children:"✨"})]}),e.jsxs("div",{className:"flex items-center mt-1 text-xs text-gray-500 dark:text-gray-400 gap-4",children:[e.jsxs("span",{className:"flex items-center",children:[e.jsx(Ne,{className:"mr-1 text-gray-500"}),C.location]}),e.jsxs("span",{className:"flex items-center",children:[e.jsx(lt,{className:"mr-1 text-gray-500"}),(Q=C.date)==null?void 0:Q.toLocaleTimeString("tr-TR",{hour:"2-digit",minute:"2-digit"})]})]})]})]})},C.id)}),!g&&!x&&b.length===0&&e.jsx("div",{className:"p-6 text-center",children:e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Yaklaşan etkinlik bulunmamaktadır."})})]})]}),e.jsxs("div",{className:"bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden",children:[e.jsxs("div",{className:"flex items-center justify-between px-4 py-3 border-b border-gray-100 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsxs("svg",{className:"w-5 h-5 mr-2 text-gray-700 dark:text-gray-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10a2 2 0 012 2v14a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"}),e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 12l2 2 4-4"})]}),e.jsx("h2",{className:"text-base font-semibold text-gray-800 dark:text-white",children:"Canlı Yayın Hedefi"})]}),e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:new Date().toLocaleDateString("tr-TR",{month:"long",year:"numeric"})})]}),e.jsxs("div",{className:"p-4 space-y-4",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Geçerli Canlı Yayın Günü"}),e.jsxs("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:[L.currentMonth.completedDays,"/",L.currentMonth.targetDays," gün"]})]}),e.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:e.jsx("div",{className:"bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] h-2 rounded-full transition-all duration-500",style:{width:`${Math.min(L.currentMonth.completedDays/L.currentMonth.targetDays*100,100)}%`}})}),e.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:["%",Math.round(L.currentMonth.completedDays/L.currentMonth.targetDays*100)," tamamlandı"]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Canlı Yayın Saati"}),e.jsxs("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:[L.currentMonth.totalHours,"/",L.currentMonth.targetHours," saat"]})]}),e.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:e.jsx("div",{className:"bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] h-2 rounded-full transition-all duration-500",style:{width:`${Math.min(L.currentMonth.totalHours/L.currentMonth.targetHours*100,100)}%`}})}),e.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:["%",Math.round(L.currentMonth.totalHours/L.currentMonth.targetHours*100)," tamamlandı"]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsxs("svg",{className:"w-4 h-4 mr-2 text-cyan-500 dark:text-cyan-400",fill:"currentColor",viewBox:"0 0 24 24",children:[e.jsx("path",{d:"M6 2L3 6V8C3 12 7 16 12 16S21 12 21 8V6L18 2H6Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",fill:"none"}),e.jsx("path",{d:"M6 8L12 12L18 8",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",fill:"none"})]}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Elmaslar"})]}),e.jsx("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:"0/50.000"})]}),e.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:e.jsx("div",{className:"bg-gradient-to-r from-cyan-400 to-cyan-600 h-2 rounded-full transition-all duration-500",style:{width:"0%"}})}),e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"%0 tamamlandı"})]})]})]}),e.jsxs("div",{className:"bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden",children:[e.jsxs("div",{className:"flex items-center justify-between px-4 py-3 border-b border-gray-100 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsxs("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-gray-700 dark:text-gray-300 mr-2",children:[e.jsx("path",{d:"M3 22H21",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M6 18V11",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M10 18V7",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M14 18V14",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M18 18V4",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),e.jsx("h2",{className:"text-base font-semibold text-gray-800 dark:text-white",children:"İstatistikler"})]}),e.jsxs(ee,{to:"/dashboard/profile",className:"text-xs text-tuber-pink dark:text-pink-400 hover:text-tuber-purple dark:hover:text-purple-400 transition-colors duration-200 flex items-center",children:[e.jsx("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),"Profilim"]})]}),e.jsx("div",{className:"p-4 relative",children:e.jsx("div",{className:"flex flex-col items-center justify-center min-h-[200px]",children:d?e.jsxs("div",{className:"flex items-center justify-center py-8",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 dark:border-white"}),e.jsx("span",{className:"ml-2 text-sm text-gray-500 dark:text-gray-400",children:"Yükleniyor..."})]}):S?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex flex-col items-center mb-4 w-full",children:[e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:S.avatar_url,alt:"Profil",className:"w-16 h-16 rounded-full mb-2 border-2 border-gray-200 shadow",onError:C=>{C.target.src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face&auto=format&q=80"}}),S.is_verified&&e.jsx("div",{className:"absolute bottom-1 right-0 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center border-2 border-white dark:border-gray-800 shadow-md",children:e.jsx("svg",{className:"w-3 h-3 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]}),e.jsx("div",{className:"font-bold text-lg text-center",children:S.display_name}),S.username&&e.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400 text-center mb-1",children:["@",S.username]}),S.tiktok_bio&&e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-300 text-center mb-3 max-w-xs",children:S.tiktok_bio})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-2 w-full",children:[e.jsxs("div",{className:"bg-gradient-to-br from-pink-50 to-pink-100 dark:from-pink-900/20 dark:to-pink-800/20 rounded-lg p-2 flex items-center border border-pink-200 dark:border-pink-700",children:[e.jsx(Ae,{className:"text-pink-600 dark:text-pink-400 text-lg mr-2 flex-shrink-0"}),e.jsxs("div",{className:"flex flex-col min-w-0",children:[e.jsx("div",{className:"font-bold text-sm text-gray-800 dark:text-white truncate",children:((G=S.followers_count)==null?void 0:G.toLocaleString())??"0"}),e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-300",children:"Takipçi"})]})]}),e.jsxs("div",{className:"bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 rounded-lg p-2 flex items-center border border-red-200 dark:border-red-700",children:[e.jsx(Ie,{className:"text-red-600 dark:text-red-400 text-lg mr-2 flex-shrink-0"}),e.jsxs("div",{className:"flex flex-col min-w-0",children:[e.jsx("div",{className:"font-bold text-sm text-gray-800 dark:text-white truncate",children:((X=S.likes_count)==null?void 0:X.toLocaleString())??"0"}),e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-300",children:"Beğeni"})]})]}),e.jsxs("div",{className:"bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-2 flex items-center border border-blue-200 dark:border-blue-700",children:[e.jsx($e,{className:"text-blue-600 dark:text-blue-400 text-lg mr-2 flex-shrink-0"}),e.jsxs("div",{className:"flex flex-col min-w-0",children:[e.jsx("div",{className:"font-bold text-sm text-gray-800 dark:text-white truncate",children:((A=S.video_count)==null?void 0:A.toLocaleString())??"0"}),e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-300",children:"Video"})]})]}),e.jsxs("div",{className:"bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg p-2 flex items-center border border-purple-200 dark:border-purple-700",children:[e.jsx(Ae,{className:"text-purple-600 dark:text-purple-400 text-lg mr-2 flex-shrink-0"}),e.jsxs("div",{className:"flex flex-col min-w-0",children:[e.jsx("div",{className:"font-bold text-sm text-gray-800 dark:text-white truncate",children:((U=S.following_count)==null?void 0:U.toLocaleString())??"0"}),e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-300",children:"Takip"})]})]})]})]}):e.jsxs("div",{className:"relative w-full",children:[e.jsx("div",{className:"absolute inset-0 pointer-events-none",children:e.jsxs("div",{className:"grid grid-cols-2 gap-2 w-full opacity-30 blur-sm",children:[e.jsxs("div",{className:"bg-gradient-to-br from-pink-50 to-pink-100 dark:from-pink-900/20 dark:to-pink-800/20 rounded-lg p-2 flex items-center border border-pink-200 dark:border-pink-700",children:[e.jsx(Ae,{className:"text-pink-600 dark:text-pink-400 text-lg mr-2 flex-shrink-0"}),e.jsxs("div",{className:"flex flex-col min-w-0",children:[e.jsx("div",{className:"font-bold text-sm text-gray-800 dark:text-white truncate",children:"125.4K"}),e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-300",children:"Takipçi"})]})]}),e.jsxs("div",{className:"bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 rounded-lg p-2 flex items-center border border-red-200 dark:border-red-700",children:[e.jsx(Ie,{className:"text-red-600 dark:text-red-400 text-lg mr-2 flex-shrink-0"}),e.jsxs("div",{className:"flex flex-col min-w-0",children:[e.jsx("div",{className:"font-bold text-sm text-gray-800 dark:text-white truncate",children:"2.5M"}),e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-300",children:"Beğeni"})]})]}),e.jsxs("div",{className:"bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-2 flex items-center border border-blue-200 dark:border-blue-700",children:[e.jsx($e,{className:"text-blue-600 dark:text-blue-400 text-lg mr-2 flex-shrink-0"}),e.jsxs("div",{className:"flex flex-col min-w-0",children:[e.jsx("div",{className:"font-bold text-sm text-gray-800 dark:text-white truncate",children:"245"}),e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-300",children:"Video"})]})]}),e.jsxs("div",{className:"bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg p-2 flex items-center border border-purple-200 dark:border-purple-700",children:[e.jsx(Ae,{className:"text-purple-600 dark:text-purple-400 text-lg mr-2 flex-shrink-0"}),e.jsxs("div",{className:"flex flex-col min-w-0",children:[e.jsx("div",{className:"font-bold text-sm text-gray-800 dark:text-white truncate",children:"850"}),e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-300",children:"Takip"})]})]})]})}),e.jsxs("div",{className:"relative z-10 flex flex-col items-center justify-center text-center py-8",children:[e.jsxs("button",{className:"bg-gray-800 text-white dark:bg-white dark:text-gray-800 px-6 py-3 rounded-full font-semibold shadow-lg hover:bg-gray-900 dark:hover:bg-gray-200 transition-all duration-200 flex items-center mx-auto mb-3 hover:scale-105",onClick:se,disabled:d,children:[e.jsx(ht,{className:"w-5 h-5 mr-2"}),"TikTok ile Giriş Yap"]}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 max-w-xs leading-relaxed",children:"TikTok hesabınızla giriş yaparak istatistiklerinizi görüntüleyin"})]})]})})})]})]})]})})};return e.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-[#0d0c11] flex flex-col",style:{width:"100%",maxWidth:"100%",overflowX:"hidden"},children:[e.jsx(pi,{onLogout:t,refreshTrigger:j}),e.jsxs("div",{className:"flex flex-1 overflow-hidden",style:{width:"100%",maxWidth:"100%",overflowX:"hidden"},children:[e.jsx("div",{className:`fixed top-16 left-0 h-[calc(100vh-4rem)] z-10 ${s||r?"w-[280px]":"w-[78px]"} transition-all duration-300 ease-in-out ${s&&!r?"-translate-x-full":""}`,style:{pointerEvents:"auto"},children:e.jsx(hi,{onLogout:t})}),e.jsx("div",{className:`flex-1 flex flex-col overflow-hidden transition-all duration-300 ease-in-out relative z-0 ${r?"sidebar-open":"sidebar-closed"}`,style:{marginLeft:s?0:r?"280px":"78px",width:s?"100%":`calc(100vw - ${r?"280px":"78px"})`,maxWidth:s?"100%":`calc(100vw - ${r?"280px":"78px"})`,overflowX:"hidden",position:"relative"},children:e.jsx("main",{className:"w-full flex-1 overflow-y-auto overflow-x-hidden",style:{width:"100%",maxWidth:"100%",padding:"0.25rem",boxSizing:"border-box"},children:de()})})]}),k&&c&&e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-md flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"relative bg-white dark:bg-[#16151c] rounded-xl overflow-hidden border border-gray-200 dark:border-gray-700 max-w-2xl w-full max-h-[80vh] overflow-y-auto shadow-2xl",children:[e.jsxs("div",{className:"sticky top-0 bg-white dark:bg-[#16151c] border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Oe,{className:"text-[#FF3E71] mr-3 text-lg"}),e.jsx("h2",{className:"text-xl font-bold text-gray-800 dark:text-white",children:"Duyuru Detayı"})]}),e.jsx("button",{onClick:()=>{T(!1),m(null)},className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",children:e.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"flex items-center justify-between mb-4",children:e.jsxs("div",{className:"flex items-center text-sm text-gray-500 dark:text-gray-400",children:[e.jsx(he,{className:"mr-2"}),e.jsx("span",{children:c.dateFormatted}),["Genel","Etkinlik","Önemli","Marka İş Birliği"].includes((c==null?void 0:c.type)||"")&&e.jsx("span",{className:`ml-2 px-3 py-1 rounded-full text-xs font-medium whitespace-nowrap ${Gt(c.type||"")}`,children:c.type})]})}),e.jsx("h1",{className:"text-2xl font-bold text-gray-800 dark:text-white mb-4",children:c.title}),e.jsx("div",{className:"prose prose-gray dark:prose-invert max-w-none",children:e.jsx("p",{className:"text-gray-600 dark:text-gray-300 leading-relaxed whitespace-pre-wrap",children:c.content||c.description})}),c.image_url&&e.jsx("div",{className:"mt-6",children:e.jsx("img",{src:c.image_url,alt:c.title,className:"w-full rounded-lg shadow-md",onError:Y=>{Y.target.style.display="none"}})})]}),e.jsx("div",{className:"sticky bottom-0 bg-gray-50 dark:bg-gray-800/50 px-6 py-4 border-t border-gray-200 dark:border-gray-700",children:e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{onClick:()=>{T(!1),m(null)},className:"px-4 py-2 bg-[#FF3E71] text-white rounded-lg hover:bg-[#FF3E71]/90 transition-colors font-medium",children:"Kapat"})})})]})})]})},oe=t=>{const a=typeof t=="string"?parseInt(t):t;return!a||isNaN(a)?"0":a>=1e9?(a/1e9).toFixed(1)+"B":a>=1e6?(a/1e6).toFixed(1)+"M":a>=1e3?(a/1e3).toFixed(1)+"K":a.toString()},yi=t=>{const a=Math.floor(t/60),r=t%60;return`${a}:${r.toString().padStart(2,"0")}`},Fe=t=>new Date(t).toLocaleDateString("tr-TR",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),qt=()=>{const[t,a]=o.useState("Tümü"),[r,s]=o.useState(null),[i,l]=o.useState([]),[n,u]=o.useState(!0),[g,h]=o.useState(null),x=Qt(),v=be(),E=x.id?parseInt(x.id,10):null,{isMobile:R}=o.useContext(ue),N=(j,_)=>{if(!_){console.error("Dosya yolu bulunamadı");return}try{const L=document.createElement("a");L.href=_,L.download=j,L.target="_blank",document.body.appendChild(L),L.click(),document.body.removeChild(L)}catch(L){console.error("Dosya indirme hatası:",L),alert("Dosya indirilemedi. Lütfen daha sonra tekrar deneyin.")}},z=()=>[{id:1,title:"Yeni Eğitim Programı Başlıyor!",content:"TikTok içerik üretimi konusunda yeni eğitim programımız 15 Şubat'ta başlıyor. Bu programda viral içerik üretimi, algoritma optimizasyonu ve monetizasyon stratejilerini öğreneceksiniz. Kayıtlar sınırlı sayıda olup, erken kayıt indirimi bulunmaktadır.",date:"10 Ocak 2025",category:"Eğitim",author:"Tuber Akademi",priority:"high"},{id:2,title:"Topluluk Etkinliği - Canlı Yayın",content:"Bu akşam saat 20:00'da topluluk canlı yayınımızda buluşuyoruz. Sorularınızı hazırlayın! TikTok trendleri, algoritma değişiklikleri ve başarı hikayeleri hakkında konuşacağız. Katılım ücretsizdir.",date:"8 Ocak 2025",category:"Etkinlik",author:"Topluluk Yöneticisi",priority:"medium"},{id:3,title:"Platform Güncellemesi",content:"Akademi platformumuzda yeni özellikler eklendi. Artık kişisel ilerleme takibi, rozet sistemi ve gelişmiş istatistikler mevcut. Yeni özellikleri keşfetmek için profilinizi ziyaret edin.",date:"5 Ocak 2025",category:"Güncelleme",author:"Geliştirici Ekibi",priority:"low"}],b=()=>window.location.hostname==="localhost"||window.location.hostname==="127.0.0.1"||!1,w=o.useCallback(async()=>{var j;u(!0),h(null);try{if(b()){l(z()),u(!1);return}try{const _=await D.get("/backend/api/api_data.php",{params:{endpoint:"announcements"},timeout:1e4});if(_.data&&_.data.status==="success"){const L=_.data.data.map(O=>({id:O.id,title:O.title,content:O.content,date:O.created_at,category:c(O.category),author:O.author||"Sistem",priority:O.is_important?"high":"low",type:O.type||O.category,important:!!O.is_important}));l(L)}else throw new Error("API yanıtı başarısız: "+(((j=_.data)==null?void 0:j.message)||"Bilinmeyen hata"))}catch(_){console.error("API error:",_),l([]),_ instanceof Error?h(_.message):h("Duyurular yüklenirken bir hata oluştu.")}}catch{h("Duyurular yüklenirken bir hata oluştu. Lütfen sayfayı yenileyin.")}finally{u(!1)}},[]),c=j=>{switch(j){case"general":return"Genel";case"events":return"Etkinlikler";case"important":return"Önemli";case"brand":return"Marka İş Birliği";default:return j}};o.useEffect(()=>{w()},[w]),o.useEffect(()=>{if(E&&i.length>0){const j=i.find(_=>_.id===E);j?s(j):v("/dashboard/announcements")}else E||s(null)},[E,v,i]);const m=["Tümü","Genel","Etkinlik","Önemli","Marka İş Birliği"],k=j=>{switch(j){case"Genel":return"bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300";case"Etkinlik":return"bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300";case"Önemli":return"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300";case"Marka İş Birliği":return"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300";default:return"bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-300"}},T=j=>{switch(j){case"Genel":return"bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300";case"Etkinlik":return"bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300";case"Önemli":return"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300";case"Marka İş Birliği":return"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300";case"Eğitim":return"bg-pink-100 dark:bg-pink-900/30 text-pink-800 dark:text-pink-300";default:return"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300"}},S=t==="Tümü"?i:i.filter(j=>j.category===t),d=()=>n?e.jsx("div",{className:"p-6 text-center",children:e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Duyurular yükleniyor..."})}):g?e.jsxs("div",{className:"p-6 text-center",children:[e.jsx("p",{className:"text-sm text-red-500",children:g}),e.jsx("button",{onClick:w,className:"mt-4 px-4 py-2 bg-[#FF3E71] text-white rounded-full text-sm",children:"Tekrar Dene"})]}):S.length===0?e.jsx("div",{className:"p-6 text-center",children:e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Bu kategoride duyuru bulunmamaktadır."})}):S.map(j=>e.jsx("button",{className:"w-full text-left p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors",onClick:()=>v(`/dashboard/announcements/${j.id}`),"aria-label":`Duyuru: ${j.title}`,children:e.jsxs("div",{className:"flex items-stretch",children:[e.jsx("div",{className:"min-w-[4px] bg-[#FF3E71] rounded-full mr-3"}),e.jsxs("div",{className:"flex-1 overflow-hidden",children:[e.jsxs("div",{className:"flex items-center text-xs text-gray-500 dark:text-gray-400 mb-1",children:[e.jsx(he,{className:"text-gray-500 dark:text-gray-400 mr-1 text-xs"}),e.jsx("span",{children:Fe(j.date)}),["Genel","Etkinlik","Önemli","Marka İş Birliği"].includes(j.type||"")&&e.jsx("span",{className:`ml-2 px-4 py-0.5 rounded-full text-xs font-medium ${k(j.type||"")}`,children:j.type})]}),e.jsx("h3",{className:"text-sm font-medium mb-1 text-gray-800 dark:text-white truncate",children:j.title}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 line-clamp-2",children:j.content})]})]})},j.id));return r?e.jsx("div",{className:"min-h-screen w-full",style:{width:"100%",maxWidth:"100%",overflowX:"hidden"},children:e.jsxs(le.div,{className:"w-full",style:{overflowX:"hidden",paddingLeft:R?"0.25rem":"0rem",paddingRight:R?"0.25rem":"0rem",boxSizing:"border-box"},initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:[e.jsxs("div",{className:"relative mb-8",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-[#FF3E71]/5 via-transparent to-[#FF5F87]/5 rounded-2xl"}),e.jsxs("div",{className:"relative bg-white dark:bg-[#16151c] rounded-2xl shadow-xl border border-gray-100 dark:border-gray-800 overflow-hidden",children:[e.jsx("div",{className:"h-1 bg-gradient-to-r from-[#FF3E71] via-[#FF5F87] to-[#FF7BA3]"}),e.jsxs("div",{className:"p-6 sm:p-8",children:[e.jsx("div",{className:"mb-6",children:e.jsxs("button",{onClick:()=>v("/dashboard/announcements"),className:"inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-[#FF3E71] dark:hover:text-[#FF5F87] transition-colors group",children:[e.jsx(ye,{className:"mr-2 group-hover:-translate-x-1 transition-transform"}),e.jsx("span",{className:"font-medium",children:"Duyurulara Dön"})]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h1",{className:"text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white leading-tight",children:r.title}),e.jsxs("div",{className:"flex flex-wrap items-center gap-4",children:[e.jsxs("div",{className:"flex items-center text-gray-500 dark:text-gray-400",children:[e.jsx(he,{className:"mr-2 text-[#FF3E71]"}),e.jsx("span",{className:"font-medium",children:r.date})]}),e.jsxs("div",{className:"flex items-center text-gray-500 dark:text-gray-400",children:[e.jsx(Oe,{className:"mr-2 text-[#FF3E71]"}),e.jsx("span",{className:"font-medium",children:r.author||"Tuber Akademi"})]})]}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[["Genel","Etkinlik","Önemli","Güncelleme","Bilgilendirme","Marka İş Birliği"].includes(r.category||"")&&e.jsx("span",{className:`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${T(r.category)}`,children:r.category}),r.important&&r.type!=="Önemli"&&e.jsx("span",{className:"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300",children:"⚠️ Önemli"})]})]})]})]})]}),e.jsxs("div",{className:"space-y-8",children:[e.jsx("div",{className:"bg-white dark:bg-[#16151c] rounded-2xl shadow-xl border border-gray-100 dark:border-gray-800 overflow-hidden",children:e.jsxs("div",{className:"p-6 sm:p-8",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"Duyuru İçeriği"}),e.jsx("div",{className:"h-1 w-16 bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] rounded-full"})]}),e.jsx("div",{className:"prose prose-lg max-w-none dark:prose-invert prose-headings:text-gray-900 dark:prose-headings:text-white prose-p:text-gray-700 dark:prose-p:text-gray-300 prose-strong:text-gray-900 dark:prose-strong:text-white prose-a:text-[#FF3E71] dark:prose-a:text-[#FF5F87] prose-a:no-underline hover:prose-a:underline",children:e.jsx("div",{className:"text-gray-700 dark:text-gray-300 leading-relaxed text-base sm:text-lg",dangerouslySetInnerHTML:{__html:(r.content||`
TikTok İçerik Üretimi ve Algoritma Optimizasyonu Eğitimi

Sevgili Tuber Akademi Üyeleri,

Bu kapsamlı eğitim programında, TikTok platformunda başarılı olmak için gereken tüm stratejileri ve teknikleri öğreneceksiniz. Eğitimimiz 4 ana bölümden oluşmaktadır:

**1. TikTok Algoritması Derinlemesine Analiz**
TikTok algoritmasının nasıl çalıştığını, hangi faktörlerin içerik dağıtımını etkilediğini ve algoritmanın favor ettiği içerik türlerini detaylı olarak inceleyeceğiz. Algoritma güncellemelerini takip etme yöntemleri ve değişikliklere hızlı adaptasyon stratejileri üzerinde duracağız.

**2. Viral İçerik Üretim Teknikleri**
Viral olma potansiyeli yüksek içerikler nasıl üretilir? Trend analizi, hashtag stratejileri, optimal paylaşım zamanları ve içerik formatları hakkında pratik bilgiler edineceğiz. Ayrıca, kendi nişinizde trend yaratma yöntemlerini öğreneceksiniz.

**3. Topluluk Yönetimi ve Etkileşim Artırma**
Takipçi kitlenizle güçlü bağlar kurma, yorumlara etkili yanıt verme, live yayın stratejileri ve topluluk oluşturma teknikleri. Sadık bir takipçi kitlesi oluşturmanın sırlarını keşfedeceğiz.

**4. Monetizasyon Stratejileri**
TikTok'ta para kazanmanın farklı yolları: Creator Fund, marka iş birlikleri, affiliate marketing, ürün satışı ve diğer gelir kaynakları. Profesyonel bir içerik üreticisi olma yolunda atacağınız adımları planlayacağız.

**Eğitim Detayları:**
- Süre: 6 hafta (haftada 2 saat)
- Format: Canlı online eğitim + kayıtlı videolar
- Sertifika: Eğitim sonunda katılım sertifikası
- Bonus: Özel WhatsApp grubu ve 1 aylık mentörlük desteği

**Kimler Katılabilir:**
- Yeni başlayan içerik üreticileri
- Mevcut hesabını büyütmek isteyen creators
- Marka temsilcileri ve pazarlama uzmanları
- TikTok'ta iş geliştirmek isteyen girişimciler

**Eğitmen Bilgileri:**
Eğitimimiz, TikTok'ta toplam 50M+ görüntülenme sayısına ulaşmış, birçok viral içerik üretmiş deneyimli creators ve dijital pazarlama uzmanları tarafından verilecektir.

**Kayıt ve Ücret:**
Erken kayıt fırsatı ile %30 indirim! Normal fiyat: 2.500 TL, Erken kayıt: 1.750 TL
Kayıt için akademi portalınızdan "Eğitimler" bölümüne gidiniz.

**Önemli Notlar:**
- Kontenjan sınırlıdır (maksimum 50 kişi)
- Kayıt son tarihi: 15 Şubat 2025
- Eğitim başlangıç tarihi: 1 Mart 2025
- Tüm katılımcılara özel kaynak materyalleri sağlanacaktır

Bu eğitim, TikTok kariyerinizde yeni bir sayfa açmanız için mükemmel bir fırsat. Sorularınız için <EMAIL> adresinden bizimle iletişime geçebilirsiniz.

Başarılar dileriz!
Tuber Akademi Eğitim Ekibi
    `).replace(/\*\*(.*?)\*\*/g,'<strong class="text-gray-900 dark:text-white font-semibold">$1</strong>').replace(/\n\n/g,'</p><p class="mt-4">').replace(/\n/g,"<br>")}})})]})}),r.attachments&&r.attachments.length>0&&e.jsx("div",{className:"bg-white dark:bg-[#16151c] rounded-2xl shadow-xl border border-gray-100 dark:border-gray-800 overflow-hidden",children:e.jsxs("div",{className:"p-6 sm:p-8",children:[e.jsxs("div",{className:"mb-6",children:[e.jsxs("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2 flex items-center",children:[e.jsx(ir,{className:"mr-3 text-[#FF3E71]"}),"Ekler ve Dosyalar"]}),e.jsx("div",{className:"h-1 w-16 bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] rounded-full"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:r.attachments.map((_,L)=>e.jsx(le.div,{whileHover:{scale:1.02},className:"group bg-gradient-to-br from-blue-50 to-blue-100/50 dark:from-blue-900/20 dark:to-blue-800/10 rounded-xl p-4 border border-blue-200 dark:border-blue-800/30 hover:shadow-lg transition-all duration-200",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mr-4 shadow-md",children:_.name.toLowerCase().includes(".pdf")?e.jsx(tr,{className:"text-white text-xl"}):e.jsx(rr,{className:"text-white text-xl"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"text-sm font-semibold text-gray-900 dark:text-white truncate mb-1",children:_.name}),e.jsx("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:_.size})]}),e.jsx("button",{onClick:()=>N(_.name,_.url),className:"flex-shrink-0 p-3 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-all duration-200 group-hover:scale-110",children:e.jsx(Ja,{className:"text-lg"})})]})},L))})]})})]})]})}):e.jsx("div",{className:"container",style:{maxWidth:R?"100%":"none",width:R?"100%":"auto",overflowX:"hidden",position:"relative",left:0,marginLeft:0,paddingTop:R?"0.5rem":"0.75rem",paddingLeft:R?"0.25rem":"0rem",paddingRight:R?"0.25rem":"1rem",boxSizing:"border-box"},children:e.jsx("div",{className:"w-full",children:e.jsx("div",{className:"w-full space-y-4",children:e.jsxs("div",{className:"bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden",children:[e.jsxs("div",{className:"flex items-center justify-between px-4 py-3 border-b border-gray-100 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Oe,{className:"text-gray-700 dark:text-gray-300 mr-2"}),e.jsx("h2",{className:"text-base font-semibold text-gray-800 dark:text-white",children:"Duyurular"})]}),e.jsx("div",{className:"flex items-center",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsx("div",{className:"flex gap-2",style:{minWidth:"max-content"},children:m.map(j=>e.jsx("button",{onClick:()=>a(j),className:`whitespace-nowrap px-2 sm:px-3 py-1 text-xs font-medium rounded-full transition-all duration-200 flex-shrink-0 ${t===j?"bg-[#FF3E71] text-white":"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:j},j))})})})]}),e.jsx("div",{className:"divide-y divide-gray-100 dark:divide-gray-700",children:d()})]})})})})},Jt=t=>{const r=new Date().getTime()-t.getTime(),s=Math.floor(r/1e3),i=Math.floor(s/60),l=Math.floor(i/60),n=Math.floor(l/24);return n>0?`${n} gün önce`:l>0?`${l} saat önce`:i>0?`${i} dakika önce`:"Az önce"},xe="https://akademi.tuberajans.com/backend/api",vi=()=>{const{isMobile:a}=o.useContext(ue);let s=ke().user;if(!s){const y=localStorage.getItem("user");y&&(s=JSON.parse(y))}const[i,l]=o.useState(""),[n,u]=o.useState(!1),[g,h]=o.useState(null),[x,v]=o.useState(null),E=o.useRef(null),[R,N]=o.useState(null),[z,b]=o.useState(null),[w,c]=o.useState(""),[m,k]=o.useState(null),[T,S]=o.useState([]),[d,j]=o.useState(null),[_,L]=o.useState(!0),O=o.useRef(null),[W,M]=o.useState({}),{tiktokUser:F}=Ze(),K=y=>{var $;const I=(($=y.target.files)==null?void 0:$[0])||null;if(I){if(!/image\/(png|jpg|jpeg)/.test(I.type)){alert("Lütfen yalnızca PNG, JPG veya JPEG formatında dosya yükleyin.");return}h(I);const J=new FileReader;J.onload=()=>{v(J.result)},J.readAsDataURL(I)}},Z=()=>{var y;(y=E.current)==null||y.click()},se=()=>{h(null),v(null),E.current&&(E.current.value="")},de=y=>{z?c(I=>I+y.emoji):l(I=>I+y.emoji)},Y=o.useCallback(async()=>{L(!0);try{const y=await D.get(`${xe}/api_data.php`,{params:{endpoint:"feed"},timeout:1e4});if(y.data&&y.data.status==="success")if(Array.isArray(y.data.data)){const I=y.data.data.map($=>({id:$.id,author:{name:$.username||"Kullanıcı",avatar:$.profile_image||null,role:$.role||"Ajans Grubu"},content:$.content,media:$.media_url,createdAt:new Date($.created_at),likes:parseInt(String($.likes_count||0))||0,comments:parseInt(String($.comments_count||0))||0,shares:0,isLiked:!!$.is_liked,comments_data:$.comments_data||[]}));S(I)}else S([]);else console.error("API başarısız yanıt döndü:",y.data),S([])}catch(y){console.error("Feed verileri yüklenirken hata:",y),S([])}finally{L(!1)}},[!1,F,d]),G=o.useCallback(async()=>{try{const y=await D.get(`${xe}/user_session.php`);y.data.status==="success"?j(y.data.data):console.error("Kullanıcı bilgileri alınamadı:",y.data.message)}catch(y){console.error("Kullanıcı bilgileri yüklenirken hata:",y)}},[]);o.useEffect(()=>{console.log("Feed componenti yüklendi. API test ediliyor..."),console.log("Base URL:",window.location.origin),console.log("API_BASE_URL:",xe),console.log("Full API URL:",`${xe}/api_data.php`),Y(),G()},[]),o.useEffect(()=>{const y=I=>{O.current&&!O.current.contains(I.target)&&N(null)};return document.addEventListener("mousedown",y),()=>document.removeEventListener("mousedown",y)},[]);const X=async y=>{try{const I=await D.post(`${xe}/api_data.php`,{endpoint:"like_post",post_id:y,user_id:(s==null?void 0:s.id)||1});I.data.status==="success"?S($=>$.map(J=>J.id===y?{...J,isLiked:!J.isLiked,likes:J.isLiked?J.likes-1:J.likes+1}:J)):(console.error("Beğeni işlemi başarısız:",I.data.message),alert("Beğeni işlemi başarısız: "+I.data.message))}catch(I){console.error("Beğeni işlemi sırasında hata oluştu:",I),I instanceof Error?alert("Beğeni işlemi sırasında hata oluştu: "+I.message):alert("Beğeni işlemi sırasında bilinmeyen bir hata oluştu")}},A=y=>{N(R===y?null:y)},U=y=>{const I=new Date(Date.now()-18e5);if(y.createdAt<I){alert("Bu paylaşım yalnızca ilk 30 dakika içinde düzenlenebilir.");return}b(y.id),c(y.content),k(y.media||null),N(null)},C=()=>{if(!w.trim()){alert("Paylaşım içeriği boş olamaz.");return}S(y=>y.map(I=>I.id===z?{...I,content:w,media:m}:I)),b(null),c(""),k(null)},V=()=>{b(null),c(""),k(null)},p=async y=>{if(window.confirm("Bu paylaşımı silmek istediğinize emin misiniz?"))try{const I=await D.post(`${xe}/api_data.php`,{endpoint:"delete_post",post_id:y,user_id:(s==null?void 0:s.id)||1});I.data.status==="success"?S($=>$.filter(J=>J.id!==y)):alert("Gönderi silinirken bir hata oluştu: "+I.data.message)}catch(I){console.error("Gönderi silinirken hata oluştu:",I),alert("Gönderi silinirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.")}N(null)},B=async()=>{var y,I,$,J,bt,kt,yt,vt,jt,wt;if(!i.trim()&&!g){alert("Lütfen bir metin yazın veya bir medya dosyası ekleyin.");return}try{const te=new FormData;te.append("endpoint","create_post"),te.append("user_id",((y=s==null?void 0:s.id)==null?void 0:y.toString())||"1"),te.append("content",i),g&&te.append("media",g);const Xe=await D.post(`${xe}/api_data.php`,te,{headers:{"Content-Type":"multipart/form-data"}});if(Xe.data.status==="success"){const Ra=(F==null?void 0:F.avatar_url)||(d==null?void 0:d.profile_image)||(d==null?void 0:d.avatar_url);console.log("Yeni gönderi avatar debug:",{tiktokUser_avatar:F==null?void 0:F.avatar_url,userInfo_profile_image:d==null?void 0:d.profile_image,userInfo_avatar_url:d==null?void 0:d.avatar_url,final_avatar:Ra});const Ba={id:Xe.data.post_id||Math.max(0,...T.map(Da=>Da.id))+1,author:{name:(F==null?void 0:F.display_name)||(d==null?void 0:d.username)||"tuberajans",avatar:(F==null?void 0:F.avatar_url)||(d==null?void 0:d.profile_image)||(d==null?void 0:d.avatar_url)||null,role:(d==null?void 0:d.role)||"Ajans Grubu"},content:i,media:x,createdAt:new Date,likes:0,comments:0,shares:0,isLiked:!1};S([Ba,...T]),l(""),h(null),v(null),E.current&&(E.current.value="")}else alert("Gönderi paylaşılırken bir hata oluştu: "+Xe.data.message)}catch(te){console.error("Gönderi paylaşılırken hata oluştu:",te),D.isAxiosError(te)?(console.error("API Hatası:",(I=te.response)==null?void 0:I.status,($=te.response)==null?void 0:$.data),console.error("İstek URL:",(J=te.config)==null?void 0:J.url),console.error("İstek Method:",(bt=te.config)==null?void 0:bt.method),console.error("Base URL:",window.location.origin),((kt=te.response)==null?void 0:kt.status)===404?alert(`API endpoint bulunamadı (404). Lütfen backend ayarlarını kontrol edin.
URL: `+((yt=te.config)==null?void 0:yt.url)):alert(`Gönderi paylaşılamadı. Hata: ${((vt=te.response)==null?void 0:vt.status)||"Bilinmeyen hata"}
Detay: ${((wt=(jt=te.response)==null?void 0:jt.data)==null?void 0:wt.message)||te.message}`)):alert("Gönderi paylaşılırken bir hata oluştu. Lütfen daha sonra tekrar deneyin.")}},q=async y=>{const I=W[y];if(I!=null&&I.trim())try{const $=await D.post(`${xe}/api_data.php`,{endpoint:"add_comment",post_id:y,user_id:(s==null?void 0:s.id)||1,content:I.trim()});$.data.status==="success"?(M(J=>({...J,[y]:""})),await Y(),console.log("Yorum başarıyla gönderildi")):alert("Yorum gönderilemedi: "+$.data.message)}catch($){console.error("Yorum gönderme hatası:",$),alert("Yorum gönderilirken bir hata oluştu.")}},Q=y=>{const I=new Date(Date.now()-18e5);return y>I};return e.jsx("div",{className:"container",style:{maxWidth:a?"100%":"none",width:a?"100%":"auto",overflowX:"hidden",position:"relative",left:0,marginLeft:0,paddingTop:a?"0.5rem":"0.75rem",paddingLeft:a?"0.25rem":"0rem",paddingRight:a?"0.25rem":"1rem",boxSizing:"border-box"},children:e.jsxs("div",{className:"flex gap-0 xl:gap-6 w-full",children:[e.jsxs("div",{className:"flex-1 w-full",children:[e.jsxs("div",{className:"bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden mb-6 p-4 sm:p-6",children:[e.jsxs("div",{className:"flex items-center mb-3 sm:mb-0",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-gray-700 dark:text-gray-300 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),e.jsx("h2",{className:"text-base font-semibold text-gray-800 dark:text-white",children:"Yeni Paylaşım"})]}),e.jsx("div",{className:"p-3",children:e.jsxs("div",{className:"flex space-x-1.5 sm:space-x-2",children:[e.jsx("div",{className:"flex-shrink-0",children:F!=null&&F.avatar_url||d!=null&&d.profile_image||d!=null&&d.avatar_url?e.jsx("img",{src:(F==null?void 0:F.avatar_url)||(d==null?void 0:d.profile_image)||(d==null?void 0:d.avatar_url),alt:(F==null?void 0:F.display_name)||(d==null?void 0:d.username)||"Kullanıcı",className:"w-7 h-7 sm:w-8 sm:h-8 rounded-full object-cover border border-gray-200 dark:border-gray-600"}):e.jsx("div",{className:"w-7 h-7 sm:w-8 sm:h-8 rounded-full bg-[#FF3E71]/10 dark:bg-[#FF3E71]/20 flex items-center justify-center text-[#FF3E71]",children:e.jsx("span",{className:"font-medium text-xs sm:text-sm",children:((F==null?void 0:F.display_name)||(d==null?void 0:d.username)||"T").charAt(0).toUpperCase()})})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"relative",children:[e.jsx("textarea",{className:"w-full p-2 pr-8 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 rounded-lg focus:ring-[#FF3E71] focus:border-[#FF3E71] text-xs sm:text-sm text-gray-700 dark:text-gray-200 placeholder-gray-400 dark:placeholder-gray-500 resize-none",placeholder:"Topluluğa bir şeyler paylaş...",rows:2,value:i,onChange:y=>l(y.target.value)}),e.jsxs("div",{className:"absolute top-3 right-3",children:[e.jsx("button",{className:"text-gray-400 hover:text-[#FF3E71] transition-colors p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700",onClick:()=>u(!n),title:"Emoji ekle",children:e.jsx(mr,{className:"h-5 w-5"})}),n&&e.jsxs("div",{className:"absolute top-8 right-0 z-50 shadow-xl border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden bg-white dark:bg-gray-800 p-2 w-[280px]",children:[e.jsx("button",{className:"fixed inset-0 border-none bg-transparent cursor-pointer",onClick:()=>u(!1),onKeyDown:y=>{y.key==="Escape"&&u(!1)},"aria-label":"Emoji picker'ı kapat"}),e.jsx("div",{className:"relative z-20",children:e.jsx("div",{className:"grid grid-cols-8 gap-1",children:["😀","😃","😄","😁","😆","😅","🤣","😂","🙂","🙃","😉","😊","😇","😍","🥰","😘","😗","☺️","😚","😙","😋","😛","😜","🤪","😝","🤑","🤗","🤭","🤫","🤔","🤐","🤨","😐","😑","😶","😏","😒","🙄","😬","🤥","😌","😔","😪","🤤","😴","😷","🤒","🤕","🤢","🤮","🤧","🥵","🥶","🥴","😵","🤯","🤠","🥳","😎","🤓","🧐","😕","😟","🙁","☹️","😮","😯","😲","😳","🥺","😦","😧","😨","😰","😥","😢","😭","😱","😖","😣","😞","😓","😩","😫","😤","😡","😠","🤬","👍","👎","👏","🙌","👌","🤝","❤️","👋"].map(y=>e.jsx("button",{className:"text-lg hover:bg-gray-100 dark:hover:bg-gray-700 p-1 rounded",onClick:()=>{de({emoji:y}),u(!1)},children:y},y))})})]})]})]}),x&&e.jsxs("div",{className:"mt-3 relative",children:[e.jsxs("div",{className:"rounded-lg overflow-hidden relative",children:[e.jsx("img",{src:x,alt:"Seçilen görsel",className:"max-h-48 w-auto mx-auto object-contain"}),e.jsx("button",{className:"absolute top-2 right-2 bg-gray-800 bg-opacity-50 text-white rounded-full p-1 hover:bg-opacity-70 transition-all",onClick:se,children:e.jsx(hr,{size:16})})]}),e.jsxs("div",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:[g==null?void 0:g.name," (",(g!=null&&g.size?g.size/1024:0).toFixed(1)," KB)"]})]}),e.jsx("input",{type:"file",ref:E,className:"hidden",accept:"image/png, image/jpeg, image/jpg",onChange:K}),e.jsxs("div",{className:"mt-3 sm:mt-4 flex justify-between items-center",children:[e.jsx("div",{className:"flex space-x-2",children:e.jsxs("button",{className:"inline-flex items-center px-4 py-2 border border-gray-200 dark:border-gray-600 rounded-full text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors touch-manipulation",onClick:Z,children:[e.jsx("svg",{className:"h-4 w-4 mr-2 text-[#FF3E71]",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),e.jsx("span",{children:"Medya"})]})}),e.jsx("button",{className:"inline-flex items-center px-6 py-2 border border-transparent rounded-full shadow-sm text-sm font-medium text-white bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] hover:from-[#FF5F87] hover:to-[#FF3E71] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF3E71] transition-all duration-300 touch-manipulation",onClick:B,children:"Paylaş"})]})]})]})})]}),e.jsxs("div",{className:"space-y-1 xl:space-y-4",children:[e.jsx(st,{children:T.map((y,I)=>e.jsx(le.div,{className:"bg-white dark:bg-[#16151c] rounded-none xl:rounded-lg shadow-sm overflow-hidden p-3",initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3,delay:I*.1},children:z===y.id?e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[y.author.avatar||F!=null&&F.avatar_url||d!=null&&d.profile_image||d!=null&&d.avatar_url?e.jsx("img",{src:y.author.avatar||(F==null?void 0:F.avatar_url)||(d==null?void 0:d.profile_image)||(d==null?void 0:d.avatar_url),alt:y.author.name||y.author.username,className:"w-10 h-10 rounded-full object-cover"}):e.jsx("div",{className:"w-10 h-10 rounded-full bg-[#FF3E71]/10 dark:bg-[#FF3E71]/20 flex items-center justify-center text-[#FF3E71]",children:e.jsx("span",{className:"font-medium text-sm",children:(y.author.name||y.author.username||"U").charAt(0).toUpperCase()})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:y.author.name||y.author.username}),e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:y.author.role})]})]}),e.jsx("textarea",{className:"w-full p-3 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 rounded-lg focus:ring-tuber-pink focus:border-tuber-pink text-sm text-gray-700 dark:text-gray-200 placeholder-gray-400 dark:placeholder-gray-500",rows:3,value:w,onChange:$=>c($.target.value)}),m&&e.jsx("div",{className:"mt-3 relative",children:e.jsx("div",{className:"rounded-lg overflow-hidden relative",children:e.jsx("img",{src:m,alt:"Paylaşım görseli",className:"max-h-48 w-auto mx-auto object-contain"})})}),e.jsxs("div",{className:"mt-3 flex justify-end gap-2",children:[e.jsx("button",{className:"inline-flex items-center px-3 py-1.5 border border-gray-200 dark:border-gray-600 rounded-full text-xs font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors",onClick:V,children:"İptal"}),e.jsx("button",{className:"inline-flex items-center px-3 py-1.5 border border-transparent rounded-full shadow-sm text-xs font-medium text-white bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] hover:from-[#FF5F87] hover:to-[#FF3E71] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF3E71] transition-all duration-300",onClick:C,children:"Kaydet"})]})]}):e.jsxs("div",{className:"p-1 sm:p-3 md:p-4",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[y.author.avatar||F!=null&&F.avatar_url||d!=null&&d.profile_image||d!=null&&d.avatar_url?e.jsx("img",{src:y.author.avatar||(F==null?void 0:F.avatar_url)||(d==null?void 0:d.profile_image)||(d==null?void 0:d.avatar_url),alt:y.author.name||y.author.username,className:"w-7 h-7 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full object-cover"}):e.jsx("div",{className:"w-7 h-7 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full bg-[#FF3E71]/10 dark:bg-[#FF3E71]/20 flex items-center justify-center text-[#FF3E71]",children:e.jsx("span",{className:"font-medium text-xs sm:text-sm",children:(y.author.name||y.author.username||"U").charAt(0).toUpperCase()})}),e.jsxs("div",{className:"ml-1.5 sm:ml-2 md:ml-3",children:[e.jsx("div",{className:"text-xs sm:text-sm font-medium text-gray-900 dark:text-white",children:y.author.name||y.author.username}),e.jsx("div",{className:"text-[10px] sm:text-xs text-gray-500 dark:text-gray-400",children:Jt(y.createdAt)})]})]}),e.jsxs("div",{className:"relative",ref:O,children:[e.jsx("button",{className:"text-gray-400 dark:text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 p-1",onClick:()=>A(y.id),"aria-label":"Paylaşım seçenekleri",children:e.jsx(Za,{})}),R===y.id&&e.jsxs("div",{className:"absolute right-0 top-8 w-36 bg-white dark:bg-[#1e1d26] rounded-lg shadow-lg z-10 py-1 border border-gray-100 dark:border-gray-700",children:[Q(y.createdAt)?e.jsxs("button",{className:"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center",onClick:()=>U(y),children:[e.jsx(Ct,{className:"mr-2 text-[#FF3E71]"}),"Düzenle"]}):e.jsxs("button",{className:"w-full text-left px-4 py-2 text-sm text-gray-400 dark:text-gray-500 flex items-center cursor-not-allowed",title:"Paylaşımlar yalnızca ilk 30 dakika içinde düzenlenebilir.",children:[e.jsx(Ct,{className:"mr-2"}),"Düzenle"]}),e.jsxs("button",{className:"w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center",onClick:()=>p(y.id),children:[e.jsx(gr,{className:"mr-2"}),"Sil"]})]})]})]}),e.jsxs("div",{className:"mt-2 sm:mt-3",children:[e.jsx("p",{className:"text-xs sm:text-sm md:text-base text-gray-800 dark:text-gray-200 line-clamp-3",children:y.content}),y.media&&e.jsx("div",{className:"mt-2 sm:mt-3 rounded-lg overflow-hidden",children:e.jsx("img",{src:y.media,alt:"Post media",className:"w-full h-32 sm:h-36 md:h-48 object-cover"})})]}),e.jsx("div",{className:"mt-3 sm:mt-4 flex items-center justify-between border-t border-gray-100 dark:border-gray-700 pt-2 sm:pt-3",children:e.jsxs("div",{className:"flex items-center space-x-1 sm:space-x-2",children:[e.jsxs("button",{className:`flex items-center text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-1.5 rounded-full transition-colors touch-manipulation ${y.isLiked?"text-white bg-[#FF3E71] dark:bg-[#FF3E71]":"text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600"}`,onClick:()=>X(y.id),children:[e.jsx(Ie,{className:"mr-1 sm:mr-1.5 text-xs"}),e.jsx("span",{children:y.likes})]}),e.jsxs("button",{className:"flex items-center text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-1.5 rounded-full text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors touch-manipulation",onClick:()=>{var $;return($=document.getElementById(`comment-input-${y.id}`))==null?void 0:$.focus()},children:[e.jsx(aa,{className:"mr-1 sm:mr-1.5 text-xs"}),e.jsx("span",{children:y.comments})]})]})}),e.jsxs("div",{className:"mt-2 sm:mt-3 border-t border-gray-100 dark:border-gray-700 pt-2 sm:pt-3 px-0 sm:px-2 md:px-3",children:[e.jsx("div",{className:"space-y-2 sm:space-y-3",children:y.comments_data&&y.comments_data.length>0?y.comments_data.map($=>{var J;return e.jsxs("div",{className:"flex space-x-1.5 sm:space-x-2",children:[$.profile_image||F!=null&&F.avatar_url||d!=null&&d.profile_image||d!=null&&d.avatar_url?e.jsx("img",{src:$.profile_image||(F==null?void 0:F.avatar_url)||(d==null?void 0:d.profile_image)||(d==null?void 0:d.avatar_url),alt:$.username,className:"w-6 h-6 sm:w-7 sm:h-7 rounded-full flex-shrink-0"}):e.jsx("div",{className:"w-6 h-6 sm:w-7 sm:h-7 rounded-full bg-[#FF3E71]/10 dark:bg-[#FF3E71]/20 flex items-center justify-center text-[#FF3E71] flex-shrink-0",children:e.jsx("span",{className:"font-medium text-[10px] sm:text-xs",children:((J=$.username)==null?void 0:J.charAt(0).toUpperCase())||"U"})}),e.jsxs("div",{className:"flex-1 bg-gray-50 dark:bg-gray-800/50 rounded-lg p-1.5 sm:p-2",children:[e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsx("div",{className:"text-[10px] sm:text-xs font-medium text-gray-900 dark:text-white",children:$.username}),e.jsx("div",{className:"text-[9px] sm:text-[10px] text-gray-500",children:Jt(new Date($.created_at))})]}),e.jsx("p",{className:"text-[10px] sm:text-xs text-gray-700 dark:text-gray-300 mt-0.5 sm:mt-1",children:$.content})]})]},$.id)}):e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400 text-center py-2",children:"Henüz yorum yapılmamış."})}),e.jsxs("div",{className:"mt-2 flex space-x-1.5 px-0",children:[F!=null&&F.avatar_url||d!=null&&d.profile_image||d!=null&&d.avatar_url?e.jsx("img",{src:(F==null?void 0:F.avatar_url)||(d==null?void 0:d.profile_image)||(d==null?void 0:d.avatar_url),alt:(F==null?void 0:F.display_name)||(d==null?void 0:d.username)||"Kullanıcı",className:"w-6 h-6 rounded-full object-cover border border-gray-200 dark:border-gray-600"}):e.jsx("div",{className:"w-6 h-6 rounded-full bg-[#FF3E71]/10 dark:bg-[#FF3E71]/20 flex items-center justify-center text-[#FF3E71]",children:e.jsx("span",{className:"font-medium text-[10px]",children:((F==null?void 0:F.display_name)||(d==null?void 0:d.username)||"T").charAt(0).toUpperCase()})}),e.jsxs("div",{className:"flex-1 relative",children:[e.jsx("input",{id:`comment-input-${y.id}`,type:"text",className:"w-full p-1.5 text-[10px] sm:text-xs border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 rounded-full focus:ring-[#FF3E71] focus:border-[#FF3E71] text-gray-700 dark:text-gray-200 placeholder-gray-400 dark:placeholder-gray-500",placeholder:"Yorum yaz...",value:W[y.id]||"",onChange:$=>M(J=>({...J,[y.id]:$.target.value})),onKeyDown:$=>{$.key==="Enter"&&q(y.id)}}),e.jsx("button",{className:"absolute right-1.5 top-1/2 -translate-y-1/2 text-[#FF3E71] hover:text-[#FF5F87] transition-colors p-1 hover:bg-[#FF3E71]/10 rounded-full",onClick:()=>q(y.id),children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"})})})]})]})]})]})},y.id))}),T.length===0&&!_&&e.jsxs("div",{className:"text-center py-10 bg-white dark:bg-[#16151c] rounded-lg shadow-sm",children:[e.jsx("div",{className:"w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-300 rounded-full flex items-center justify-center mb-4",children:e.jsx(xr,{size:24})}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Hiç İçerik Bulunamadı"}),e.jsx("p",{className:"mt-1 text-gray-500 dark:text-gray-400",children:"Henüz bu akışta içerik bulunmamaktadır."}),e.jsx("button",{className:"mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-full shadow-sm text-sm font-medium text-white bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] hover:from-[#FF5F87] hover:to-[#FF3E71] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF3E71] transition-all duration-300",children:"İlk Paylaşımı Yap"})]}),_&&e.jsxs("div",{className:"text-center py-10 bg-white dark:bg-[#16151c] rounded-lg shadow-sm",children:[e.jsx("div",{className:"w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-300 rounded-full flex items-center justify-center mb-4",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#FF3E71]"})}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Akış Yükleniyor..."}),e.jsx("p",{className:"mt-1 text-gray-500 dark:text-gray-400",children:"İçerikler getiriliyor, lütfen bekleyin."})]})]})]}),e.jsx("div",{className:"hidden xl:block w-80 2xl:w-96 flex-shrink-0 space-y-6 pr-4 2xl:pr-6"})]})})},ji=()=>{const t=be(),{isMobile:a}=o.useContext(ue),[r,s]=o.useState("Tüm Kategoriler"),[i,l]=o.useState(""),[n,u]=o.useState([]),[g,h]=o.useState(!0),[x,v]=o.useState(null),[E,R]=o.useState(["Tüm Kategoriler"]),N=n.filter(k=>{const T=r==="Tüm Kategoriler"||k.category===r,S=k.title.toLowerCase().includes(i.toLowerCase())||k.description.toLowerCase().includes(i.toLowerCase());return T&&S}),z=o.useCallback(()=>{u([]),R(["Tüm Kategoriler"])},[]),b=o.useCallback(async()=>{h(!0),v(null);try{console.log("API çağrısı yapılıyor...");const k=await D.get("/backend/api/api_data.php",{params:{endpoint:"courses"}});if(console.log("API yanıtı:",k.data),k.data.status==="success"&&k.data.data){const T=k.data.data.map(j=>({id:j.id,title:j.title,description:j.description,content:j.content,category:w(j.category),image:j.image||j.image_url||"",icon:j.icon||c(j.category),featured:j.featured===1||j.featured===!0,created_by:j.created_by||j.instructor,created_at:j.created_at,updated_at:j.updated_at,status:j.status}));u(T);const S=new Set(T.map(j=>j.category)),d=["Tüm Kategoriler",...Array.from(S)];R(d)}else k.data.data&&k.data.data.length===0||v("Eğitimler alınamadı: "+(k.data.message||"Bilinmeyen hata")),z()}catch(k){const T=k instanceof Error?k.message:"Bilinmeyen hata";v("Eğitimler alınamadı. Lütfen daha sonra tekrar deneyin: "+T),z()}finally{h(!1)}},[z]),w=k=>({general:"Genel","video-editing":"Video Editörlüğü","content-creation":"İçerik Üretimi","social-media":"Sosyal Medya",marketing:"Pazarlama",technical:"Teknik",creativity:"Yaratıcılık",business:"İş Geliştirme",algoritma:"Genel",teknik:"Teknik",içerik:"İçerik Üretimi",pazarlama:"Pazarlama",işbirliği:"İş Geliştirme",topluluk:"Sosyal Medya",planlama:"İş Geliştirme","canlı yayın":"Teknik",Genel:"Genel","Video Editörlüğü":"Video Editörlüğü","İçerik Üretimi":"İçerik Üretimi","Sosyal Medya":"Sosyal Medya",Pazarlama:"Pazarlama",Teknik:"Teknik",Yaratıcılık:"Yaratıcılık","İş Geliştirme":"İş Geliştirme"})[k]||(k==null?void 0:k.charAt(0).toUpperCase())+(k==null?void 0:k.slice(1))||"Genel",c=k=>{switch(k){case"general":return"🔑";case"beginner":return"👋";case"intermediate":return"🚀";case"advanced":return"⚡";case"algoritma":return"🔑";case"tiktok":return"🔑";case"etki":return"👋";case"içerik":return"👋";case"sosyal":return"🚀";case"topluluk":return"🚀";case"marka":return"⚡";case"canlı yayın":return"⚡";default:return"📚"}};o.useEffect(()=>{b()},[b]);const m=k=>{console.log(`Eğitim ID: ${k} tıklandı`),t(`/dashboard/courses/${k}`)};return e.jsx("div",{className:"container",style:{maxWidth:a?"100%":"none",width:a?"100%":"auto",overflowX:"hidden",position:"relative",left:0,marginLeft:0,paddingTop:a?"0.5rem":"0.75rem",paddingLeft:a?"0.25rem":"0rem",paddingRight:a?"0.25rem":"1rem",boxSizing:"border-box"},children:e.jsxs("div",{className:"flex flex-col gap-4 sm:gap-6 max-w-full",children:[g&&e.jsxs("div",{className:"text-center py-6 sm:py-10 bg-white dark:bg-[#16151c] rounded-lg shadow-sm px-2",children:[e.jsx("div",{className:"w-12 h-12 sm:w-16 sm:h-16 mx-auto bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-300 rounded-full flex items-center justify-center mb-2 sm:mb-4 animate-pulse",children:e.jsx(Le,{className:"text-base sm:text-xl"})}),e.jsx("h3",{className:"text-sm sm:text-lg font-medium text-gray-900 dark:text-white",children:"Yükleniyor..."}),e.jsx("p",{className:"mt-0.5 sm:mt-1 text-xs sm:text-sm text-gray-500 dark:text-gray-400",children:"Bekleyin."})]}),!g&&x&&e.jsxs("div",{className:"text-center py-6 sm:py-10 bg-white dark:bg-[#16151c] rounded-lg shadow-sm px-2",children:[e.jsx("div",{className:"w-12 h-12 sm:w-16 sm:h-16 mx-auto bg-red-100 dark:bg-red-900/30 text-red-500 dark:text-red-300 rounded-full flex items-center justify-center mb-2 sm:mb-4",children:e.jsx(Le,{className:"text-base sm:text-xl"})}),e.jsx("h3",{className:"text-sm sm:text-lg font-medium text-gray-900 dark:text-white",children:"Hata"}),e.jsx("p",{className:"mt-0.5 sm:mt-1 text-xs sm:text-sm text-gray-500 dark:text-gray-400",children:x}),e.jsx("button",{onClick:b,className:"mt-2 sm:mt-4 px-3 py-1.5 sm:px-4 sm:py-2 bg-[#FF3E71] text-white rounded-full text-xs sm:text-sm",children:"Tekrar Dene"})]}),!g&&!x&&N.length===0&&e.jsxs("div",{className:"text-center py-6 sm:py-10 bg-white dark:bg-[#16151c] rounded-lg shadow-sm px-2",children:[e.jsx("div",{className:"w-12 h-12 sm:w-16 sm:h-16 mx-auto bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-300 rounded-full flex items-center justify-center mb-2 sm:mb-4",children:e.jsx(Le,{className:"text-base sm:text-xl"})}),e.jsx("h3",{className:"text-sm sm:text-lg font-medium text-gray-900 dark:text-white",children:"Eğitim Bulunamadı"}),e.jsx("p",{className:"mt-0.5 sm:mt-1 text-xs sm:text-sm text-gray-500 dark:text-gray-400",children:r==="Tüm Kategoriler"?"Henüz eğitim eklenmemiş.":`"${r}" kategorisinde eğitim bulunamadı.`})]}),!g&&!x&&N.length>0&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-full bg-white dark:bg-[#16151c] rounded-lg shadow-sm py-3 sm:py-4 px-3 sm:px-4 mb-2 overflow-hidden",children:e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 sm:gap-4 sm:items-center",children:[e.jsx("div",{className:"flex-1 overflow-x-auto px-2 sm:px-0",children:e.jsx("div",{className:"flex gap-2 pb-1",style:{minWidth:"max-content"},children:E.map(k=>e.jsx("button",{onClick:()=>s(k),className:`whitespace-nowrap px-4 sm:px-5 py-1.5 sm:py-2 text-xs sm:text-sm font-medium rounded-full transition-all duration-200 flex-shrink-0 ${r===k?"bg-[#FF3E71] text-white":"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:k},k))})}),e.jsx("div",{className:"w-full sm:w-80 sm:flex-shrink-0",children:e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Ara...",className:"w-full px-3 sm:px-4 py-2 sm:py-2.5 pr-10 sm:pr-12 rounded-lg border border-gray-200 dark:border-gray-700 focus:ring-2 focus:ring-[#FF3E71] dark:bg-gray-800 dark:text-white text-xs sm:text-sm",value:i,onChange:k=>l(k.target.value)}),e.jsx("button",{className:"absolute right-0 top-0 h-full px-3 sm:px-4 text-[#FF3E71]",children:e.jsx(lr,{className:"text-xs sm:text-sm"})})]})})]})}),e.jsx("div",{className:"grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-2 sm:gap-3 md:gap-4",children:N.map((k,T)=>e.jsxs(le.div,{className:"bg-white dark:bg-[#16151c] rounded-lg shadow-md overflow-hidden flex flex-col cursor-pointer hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-transparent hover:border-[#FF3E71]/20 w-full",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:T*.1},onClick:()=>m(k.id),children:[e.jsx("div",{className:"relative",children:e.jsxs("div",{className:"h-24 xs:h-28 sm:h-32 md:h-36 relative overflow-hidden rounded-t-lg",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-[#FF3E71]/90 to-[#FF5F87]/90 brightness-90"}),k.image&&!k.image.includes("course1.jpg")&&!k.image.includes("course2.jpg")&&!k.image.includes("course3.jpg")&&!k.image.includes("course4.jpg")&&!k.image.includes("course5.jpg")&&!k.image.includes("course6.jpg")&&!k.image.includes("course7.jpg")&&!k.image.includes("course8.jpg")&&e.jsx("img",{src:k.image,alt:k.title,className:"absolute inset-0 w-full h-full object-cover object-center brightness-75",onError:S=>{S.currentTarget.style.display="none"}}),e.jsx("div",{className:"absolute inset-0 opacity-10",children:e.jsx("div",{className:"absolute inset-0",style:{backgroundImage:"radial-gradient(circle, rgba(255,255,255,0.8) 1px, transparent 1px)",backgroundSize:"15px 15px"}})}),e.jsx("div",{className:"absolute -top-10 -right-10 w-20 h-20 rounded-full bg-white opacity-20 blur-2xl"}),e.jsx("div",{className:"absolute -bottom-5 -left-5 w-16 h-16 rounded-full bg-white opacity-10 blur-xl"}),e.jsx("div",{className:"absolute inset-0 flex flex-col justify-center items-center p-2 text-center",children:e.jsx("h2",{className:"text-xs xs:text-sm sm:text-base font-bold text-white drop-shadow-lg line-clamp-2 leading-tight",children:k.title})}),e.jsx("div",{className:"absolute top-2 right-2 bg-black/40 backdrop-blur-md text-white text-[9px] xs:text-[10px] px-2.5 xs:px-3.5 py-0.5 xs:py-1 rounded-full border border-white/20 shadow-lg font-medium tracking-wide",children:k.category}),k.featured&&e.jsxs("div",{className:"absolute top-2 left-2 bg-gradient-to-r from-yellow-500 to-amber-500 text-white text-[9px] xs:text-[10px] px-2.5 xs:px-3.5 py-0.5 xs:py-1 rounded-full border border-yellow-300/30 shadow-lg font-medium flex items-center gap-1.5",children:[e.jsx(ur,{className:"text-[8px] xs:text-[9px]"}),e.jsx("span",{children:"Öne Çıkan"})]})]})}),e.jsxs("div",{className:"p-2 xs:p-3 flex-1 flex flex-col",children:[e.jsx("p",{className:"text-xs sm:text-sm text-gray-600 dark:text-gray-300 line-clamp-2 mb-2 flex-1",children:k.description}),e.jsxs("div",{className:"flex justify-between items-center mt-auto",children:[e.jsxs("div",{className:"flex items-center text-[9px] xs:text-[10px] sm:text-xs text-gray-500 dark:text-gray-400",children:[e.jsx(he,{className:"mr-1 text-[#FF3E71] text-[9px] xs:text-[10px] sm:text-xs"}),e.jsx("span",{children:new Date(k.created_at||Date.now()).toLocaleDateString("tr-TR")})]}),e.jsx("button",{className:"inline-flex items-center justify-center min-w-[70px] xs:min-w-[80px] px-3 xs:px-4 py-1.5 border border-transparent rounded-2xl shadow-sm text-[9px] xs:text-[10px] sm:text-xs font-medium text-white bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] hover:from-[#FF5F87] hover:to-[#FF3E71] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF3E71] transition-all duration-300",children:"Görüntüle"})]})]})]},k.id))}),N.length===0&&!g&&!x&&e.jsxs("div",{className:"text-center py-6 sm:py-10 bg-white dark:bg-[#16151c] rounded-lg shadow-sm px-2",children:[e.jsx("div",{className:"w-12 h-12 sm:w-16 sm:h-16 mx-auto bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-300 rounded-full flex items-center justify-center mb-2 sm:mb-4",children:e.jsx(Le,{className:"text-base sm:text-xl"})}),e.jsx("h3",{className:"text-sm sm:text-lg font-medium text-gray-900 dark:text-white",children:"Eğitim Yok"}),e.jsx("p",{className:"mt-0.5 sm:mt-1 text-xs sm:text-sm text-gray-500 dark:text-gray-400",children:"Bu kategoride eğitim yok."})]})]})]})})},wi=()=>{var k,T,S;const{isMobile:t}=o.useContext(ue),[a,r]=o.useState(new Date().getMonth()),[s,i]=o.useState(new Date().getFullYear()),[l,n]=o.useState([]),[u,g]=o.useState(!0),[h,x]=o.useState(null),v=d=>{switch(d){case"Webinar":return"bg-blue-500/80 text-white";case"Seminer":return"bg-purple-500/80 text-white";case"Buluşma":return"bg-green-500/80 text-white";case"Atölye":return"bg-yellow-500/80 text-white";default:return"bg-gray-500/80 text-white"}},E=d=>{const j=d.map(_=>{const L=new Date(_.start_date),O=new Date(_.end_date);return{..._,date:L,endDate:O,isFeatured:!!_.is_featured,isPastEvent:L<=new Date,capacity:_.max_participants||50,registered:_.current_participants||0,thumbnail:_.image_url||""}});console.log("İşlenmiş etkinlikler:",j),n(j)},R=o.useCallback(d=>{var j;console.log("API yanıtı:",d.data),d.data&&d.data.status==="success"?Array.isArray(d.data.data)?d.data.data.length===0?(console.log("API yanıtında etkinlik bulunamadı"),n([])):E(d.data.data):(console.error("API yanıtında data dizisi bulunamadı:",d.data),x("API yanıtı beklenen formatta değil.")):(console.error("API başarısız yanıt döndü:",d.data),x(((j=d.data)==null?void 0:j.message)||"Etkinlikler yüklenirken bir hata oluştu."))},[E]),N=o.useCallback(d=>{var j;if(console.error("Etkinlikler yüklenirken hata:",d),d&&typeof d=="object"&&"response"in d){const _=d;console.error("Sunucu yanıtı:",_.response.data),console.error("Durum kodu:",_.response.status),x(`Sunucu hatası: ${_.response.status} - ${((j=_.response.data)==null?void 0:j.message)||"Bilinmeyen hata"}`)}else if(d&&typeof d=="object"&&"request"in d)console.error("İstek yapıldı ama yanıt alınamadı"),x("Sunucuya bağlanılamadı. Lütfen internet bağlantınızı kontrol edin.");else{const _=d instanceof Error?d.message:"Bilinmeyen hata";console.error("İstek hatası:",_),x(`İstek hatası: ${_}`)}},[]);o.useEffect(()=>{(async()=>{g(!0),x(null);try{const j=await D.get("/backend/api/api_data.php",{params:{endpoint:"events"},timeout:1e4});R(j)}catch(j){N(j)}finally{g(!1)}})()},[]);const z=new Date,b=l.length>0?l.filter(d=>(console.log("Etkinlik tarihi:",d.date,"Şu anki tarih:",z,"Gelecek mi?",d.date&&d.date>z),d.date!==void 0)).filter(d=>{const j=d.status==="active",_=d.date?d.date>z:!1;return console.log(`Etkinlik ${d.title}: aktif=${j}, gelecek=${_}`),j}).sort((d,j)=>{var _,L;return(((_=d.date)==null?void 0:_.getTime())||0)-(((L=j.date)==null?void 0:L.getTime())||0)}):[];console.log("Filtrelenmiş etkinlikler:",b);const w=["Ocak","Şubat","Mart","Nisan","Mayıs","Haziran","Temmuz","Ağustos","Eylül","Ekim","Kasım","Aralık"],c=()=>{a===0?(r(11),i(s-1)):r(a-1)},m=()=>{a===11?(r(0),i(s+1)):r(a+1)};return e.jsxs("div",{className:"container",style:{maxWidth:t?"100%":"none",width:t?"100%":"auto",overflowX:"hidden",position:"relative",left:0,marginLeft:0,paddingTop:t?"0.5rem":"0.75rem",paddingLeft:t?"0.25rem":"0rem",paddingRight:t?"0.25rem":"1rem",boxSizing:"border-box"},children:[u&&e.jsx("div",{className:"flex items-center justify-center min-h-screen",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 sm:h-12 sm:w-12 border-t-2 border-b-2 border-[#FF3E71]"})}),h&&!u&&e.jsx("div",{className:"flex items-center justify-center min-h-screen px-2",children:e.jsxs("div",{className:"text-center p-3 sm:p-6 bg-white dark:bg-[#16151c] rounded-lg shadow-sm",children:[e.jsx("div",{className:"text-red-500 text-lg sm:text-xl mb-2 sm:mb-4",children:"⚠️"}),e.jsx("h3",{className:"text-sm sm:text-lg font-medium text-gray-900 dark:text-white",children:"Hata"}),e.jsx("p",{className:"mt-1 sm:mt-2 text-xs sm:text-sm text-gray-500 dark:text-gray-400",children:h}),e.jsx("button",{onClick:()=>window.location.reload(),className:"mt-2 sm:mt-4 px-3 py-1.5 sm:px-4 sm:py-2 bg-[#FF3E71] text-white rounded-md hover:bg-[#FF5F87] transition-colors text-xs sm:text-sm",children:"Yeniden Dene"})]})}),!u&&!h&&e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-7 gap-2 sm:gap-4",children:[e.jsx("div",{className:"lg:hidden mb-4",children:e.jsxs("div",{className:"bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden mb-4",children:[e.jsx("div",{className:"px-4 py-3 border-b border-gray-100 dark:border-gray-700",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Me,{className:"text-[#FF3E71] mr-2"}),e.jsx("h2",{className:"text-sm font-semibold text-gray-800 dark:text-white",children:"Yaklaşan Etkinlikler"})]})}),e.jsxs("div",{className:"p-3",children:[b.slice(0,3).map((d,j)=>{var _,L,O;return e.jsxs("div",{className:`flex items-start space-x-3 ${j!==2?"pb-3 mb-3 border-b border-gray-100 dark:border-gray-700":""}`,children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsxs("div",{className:"w-12 h-12 bg-gradient-to-br from-[#FF3E71] to-[#FF5F87] rounded-lg flex flex-col items-center justify-center text-white",children:[e.jsx("div",{className:"text-xs font-bold",children:(_=d.date)==null?void 0:_.getDate()}),e.jsx("div",{className:"text-[10px]",children:(L=d.date)==null?void 0:L.toLocaleDateString("tr-TR",{month:"short"})})]})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 dark:text-white line-clamp-1",children:d.title}),e.jsxs("div",{className:"flex items-center text-xs text-gray-500 dark:text-gray-400 mt-1",children:[e.jsx(lt,{className:"mr-1 text-[#FF3E71]"}),e.jsx("span",{children:(O=d.date)==null?void 0:O.toLocaleTimeString("tr-TR",{hour:"2-digit",minute:"2-digit"})})]}),e.jsxs("div",{className:"flex items-center text-xs text-gray-500 dark:text-gray-400 mt-1",children:[e.jsx(Ne,{className:"mr-1 text-[#FF3E71]"}),e.jsx("span",{className:"truncate",children:d.location})]})]})]},d.id)}),b.length===0&&e.jsx("div",{className:"text-center py-4",children:e.jsx("div",{className:"text-gray-400 dark:text-gray-500 text-sm",children:"Yaklaşan etkinlik yok"})})]})]})}),e.jsxs("div",{className:"lg:col-span-5",children:[e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-3 mb-2 sm:mb-6",children:b.map((d,j)=>{var _,L,O,W;return e.jsx(le.div,{className:`${d.isFeatured?"bg-gradient-to-br from-white to-pink-50/30 dark:from-[#16151c] dark:to-[#1f1e27] ring-1 ring-[#FF3E71]/20":"bg-white dark:bg-[#16151c]"} rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-all duration-300`,initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:j*.1},children:e.jsxs("div",{className:"relative",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"h-24 sm:h-32 w-full bg-gradient-to-br from-[#FF3E71]/90 to-[#FF5F87]/90 brightness-90"}),e.jsx("img",{className:"absolute inset-0 h-24 sm:h-32 w-full object-cover object-center brightness-75",src:d.thumbnail,alt:d.title,onError:M=>{M.currentTarget.style.display="none"},loading:"lazy"}),e.jsx("div",{className:"absolute inset-0 flex flex-col justify-center items-center p-2 sm:p-3 text-center",children:e.jsx("h2",{className:`text-xs sm:text-sm md:text-base font-bold text-white drop-shadow-lg line-clamp-2 mb-1 ${d.isFeatured&&!d.isPastEvent?"bg-gradient-to-r from-white to-pink-100 bg-clip-text text-transparent":""}`,children:d.title})}),e.jsx("div",{className:"absolute top-0 left-0 right-0 p-1.5 sm:p-2",children:e.jsxs("div",{className:"flex flex-wrap gap-1",children:[e.jsx("span",{className:`inline-flex items-center px-1.5 py-0.5 rounded-full text-[10px] sm:text-xs font-medium backdrop-blur-md ${v(d.category)}`,children:d.category}),d.isFeatured&&!d.isPastEvent&&e.jsx("span",{className:"inline-flex items-center px-1.5 py-0.5 rounded-full text-[10px] sm:text-xs font-medium bg-[#FF3E71]/80 text-white backdrop-blur-md",children:"Öne Çıkan"})]})}),e.jsx("div",{className:`absolute bottom-0 left-0 m-1.5 sm:m-2 ${d.isFeatured?"bg-gradient-to-r from-[#FF3E71]/90 to-[#FF5F87]/90 text-white":"bg-white/90 dark:bg-gray-800/90"} backdrop-blur-md rounded-lg px-1.5 sm:px-2 py-1 shadow-md`,children:e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("div",{className:`text-xs sm:text-sm font-bold ${d.isFeatured?"text-white":"text-[#FF3E71]"}`,children:(_=d.date)==null?void 0:_.getDate()}),e.jsx("div",{className:`text-[10px] sm:text-xs font-medium ${d.isFeatured?"text-white":"text-gray-700 dark:text-gray-300"}`,children:(L=d.date)==null?void 0:L.toLocaleDateString("tr-TR",{month:"short"})}),e.jsx("div",{className:`text-[10px] sm:text-xs font-medium ${d.isFeatured?"text-white/90":"text-gray-600 dark:text-gray-400"}`,children:(O=d.date)==null?void 0:O.toLocaleTimeString("tr-TR",{hour:"2-digit",minute:"2-digit"})})]})})]}),e.jsxs("div",{className:"p-2 sm:p-3",children:[e.jsx("p",{className:"text-[10px] sm:text-xs text-gray-600 dark:text-gray-300 line-clamp-2",children:d.description}),e.jsxs("div",{className:"mt-1.5 sm:mt-2 flex flex-col sm:flex-row gap-1 sm:gap-2",children:[e.jsxs("div",{className:"flex items-center text-[10px] sm:text-xs text-gray-500 dark:text-gray-400",children:[e.jsx(Ne,{className:"mr-1 text-[#FF3E71] text-[10px]"}),e.jsx("span",{className:"truncate",children:d.location})]}),e.jsxs("div",{className:"flex items-center text-[10px] sm:text-xs text-gray-500 dark:text-gray-400",children:[e.jsx(Wa,{className:"mr-1 text-[#FF3E71] text-[10px]"}),e.jsx("span",{className:"truncate",children:(W=d.date)==null?void 0:W.toLocaleDateString("tr-TR",{day:"numeric",month:"long",year:"numeric"})})]})]})]})]})},d.id)})}),b.length===0&&e.jsxs("div",{className:"text-center py-6 sm:py-10 bg-white dark:bg-[#16151c] rounded-lg shadow-sm px-2",children:[e.jsx("div",{className:"w-12 h-12 sm:w-16 sm:h-16 mx-auto bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-300 rounded-full flex items-center justify-center mb-2 sm:mb-4",children:e.jsx(Me,{className:"text-base sm:text-xl"})}),e.jsx("h3",{className:"text-sm sm:text-lg font-medium text-gray-900 dark:text-white",children:"Etkinlik Yok"}),e.jsx("p",{className:"mt-0.5 sm:mt-1 text-xs sm:text-sm text-gray-500 dark:text-gray-400",children:"Bu dönemde etkinlik yok."})]})]}),e.jsxs("div",{className:"hidden lg:block lg:col-span-2",children:[e.jsxs("div",{className:"bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden mb-2 sm:mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between px-4 py-3 border-b border-gray-100 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(he,{className:"text-gray-700 dark:text-gray-300 mr-2"}),e.jsx("h2",{className:"text-sm font-semibold text-gray-800 dark:text-white",children:"Takvim"})]}),e.jsxs("div",{className:"flex space-x-1",children:[e.jsx("button",{onClick:c,className:"p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400","aria-label":"Önceki Ay",title:"Önceki Ay",children:e.jsx(ea,{className:"text-xs"})}),e.jsx("button",{onClick:m,className:"p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400","aria-label":"Sonraki Ay",title:"Sonraki Ay",children:e.jsx(ta,{className:"text-xs"})})]})]}),e.jsxs("div",{className:"p-2 sm:p-3",children:[e.jsx("div",{className:"text-center mb-2 sm:mb-3",children:e.jsxs("h4",{className:"text-sm sm:text-base font-semibold text-[#FF3E71]",children:[w[a]," ",s]})}),e.jsx("div",{className:"grid grid-cols-7 gap-0.5 sm:gap-1 mb-1 sm:mb-2",children:["Pt","Sa","Ça","Pe","Cu","Ct","Pz"].map(d=>e.jsx("div",{className:"text-center text-xs font-medium text-gray-500 dark:text-gray-400 py-0.5",children:d},d))}),e.jsx("div",{className:"grid grid-cols-7 gap-0.5 sm:gap-1",children:(()=>{const d=new Date(s,a,1).getDay(),j=new Date(s,a+1,0).getDate(),_=d===0?6:d-1,L=new Date;L.setHours(0,0,0,0);const O=L;return[...Array(_).fill(null),...Array(j).fill(0).map((M,F)=>F+1)].map((M,F)=>{if(M===null)return e.jsx("div",{},"empty-"+F);const K=new Date(s,a,M);K.setHours(0,0,0,0);const Z=K<O,se=K.getTime()===O.getTime(),de=l.some(Y=>Y.date&&Y.date.getFullYear()===s&&Y.date.getMonth()===a&&Y.date.getDate()===M&&Y.date>=O);return e.jsx("div",{className:`
                              text-center py-1.5 text-xs rounded-md transition-all duration-200 select-none
                              ${Z?"bg-gray-100 text-gray-400 opacity-50 pointer-events-none":""}
                              ${se?"ring-2 ring-[#FF3E71] font-bold bg-white text-[#FF3E71]":""}
                              ${de&&!se?"bg-gradient-to-br from-[#FF3E71] to-[#FF5F87] text-white font-bold shadow-sm":""}
                              ${!Z&&!se&&!de?"hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 hover:shadow-sm cursor-pointer":""}
                            `,children:M},M)})})()})]})]}),e.jsxs("div",{className:"bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden mb-6",children:[e.jsx("div",{className:"flex items-center justify-between px-4 py-3 border-b border-gray-100 dark:border-gray-700",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Me,{className:"text-gray-700 dark:text-gray-300 mr-2"}),e.jsx("h2",{className:"text-sm font-semibold text-gray-800 dark:text-white",children:"Yaklaşan Etkinlikler"})]})}),e.jsxs("div",{className:"divide-y divide-gray-100 dark:divide-gray-700",children:[b.length>0&&b[0].date&&e.jsx("div",{className:"p-3 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors",children:e.jsxs("div",{className:"flex items-start space-x-2",children:[e.jsx("div",{className:"flex-shrink-0 bg-gradient-to-r from-[#FF3E71]/80 to-[#FF5F87]/80 text-white rounded-lg px-2 py-1.5 shadow-sm",children:e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("div",{className:"text-base font-bold text-white",children:(k=b[0].date)==null?void 0:k.getDate()}),e.jsx("div",{className:"text-xs font-medium text-white",children:(T=b[0].date)==null?void 0:T.toLocaleDateString("tr-TR",{month:"short"})}),e.jsx("div",{className:"text-xs font-medium text-white/90",children:(S=b[0].date)==null?void 0:S.toLocaleTimeString("tr-TR",{hour:"2-digit",minute:"2-digit"})})]})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"text-xs font-semibold text-[#FF3E71] dark:text-[#FF5F87] truncate",children:b[0].title}),e.jsxs("div",{className:"flex items-center mt-1 text-xs text-gray-500 dark:text-gray-400",children:[e.jsx(Ne,{className:"mr-1 text-[#FF3E71] text-xs"}),e.jsx("span",{className:"truncate text-xs",children:b[0].location})]})]})]})},b[0].id),b.slice(1,4).map(d=>{var j,_,L;return e.jsx("div",{className:"p-3 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors",children:e.jsxs("div",{className:"flex items-start space-x-2",children:[e.jsx("div",{className:"flex-shrink-0 rounded-lg px-1.5 py-0.5 bg-white dark:bg-[#16151c]",children:e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("div",{className:"text-base font-bold text-[#FF3E71]",children:(j=d.date)==null?void 0:j.getDate()}),e.jsx("div",{className:"text-xs font-medium text-gray-600 dark:text-gray-300",children:(_=d.date)==null?void 0:_.toLocaleDateString("tr-TR",{month:"short"})})]})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"text-xs font-medium text-gray-900 dark:text-white truncate",children:d.title}),e.jsxs("div",{className:"flex items-center mt-1 text-xs text-gray-500 dark:text-gray-400",children:[e.jsx(Ne,{className:"mr-1 text-[#FF3E71] text-xs"}),e.jsx("span",{className:"truncate text-xs",children:d.location})]}),e.jsxs("div",{className:"flex items-center mt-1 text-xs text-[#FF3E71] dark:text-[#FF3E71]",children:[e.jsx(lt,{className:"mr-1 text-[#FF3E71] text-xs"}),e.jsx("span",{className:"text-xs",children:(L=d.date)==null?void 0:L.toLocaleTimeString("tr-TR",{hour:"2-digit",minute:"2-digit"})})]})]})]})},d.id)}),b.length===0&&e.jsx("div",{className:"p-6 text-center",children:e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Yaklaşan etkinlik bulunmamaktadır."})})]})]})]})]})]})},Ni=t=>{switch(t){case"technical":return"Teknik";case"account":return"Hesap";case"content":return"İçerik";case"other":return"Diğer";default:return t}},_i=()=>{const{isMobile:a}=o.useContext(ue),r=ke();let s=null;s=r.user;const[i,l]=o.useState("açık"),[n,u]=o.useState(!1),[g,h]=o.useState(!1),[x,v]=o.useState(null),[E,R]=o.useState(""),[N,z]=o.useState(!0),[b,w]=o.useState(null),[c,m]=o.useState([]),[k,T]=o.useState({category:"technical",title:"",description:"",priority:"medium"}),S=o.useCallback(async()=>{z(!0),w(null);try{const M=await D.get("/backend/api/api_support.php",{params:{action:"list",user_id:(s==null?void 0:s.id)||1}});if(M.data.status==="success"){const F=M.data.data.map(K=>({id:K.id,title:K.subject,description:K.message,date:Fe(K.created_at),status:K.status==="open"?"açık":"tamamlandı",priority:K.priority,category:K.category,assignee:"Destek Ekibi",responses:[]}));m(F)}else w("Destek talepleri alınamadı: "+M.data.message)}catch(M){console.error("Destek talepleri alınırken hata oluştu:",M),w("Destek talepleri alınamadı. Lütfen daha sonra tekrar deneyin.")}finally{z(!1)}},[s==null?void 0:s.id,!1]),d=async M=>{try{const F=await D.get("/backend/api/api_support.php",{params:{action:"detail",ticket_id:M}});if(F.data.status==="success"&&F.data.data.replies){const K=F.data.data.replies.map(Z=>({id:Z.id,author:Z.is_admin?"Destek Ekibi":Z.username||"Kullanıcı",message:Z.message,date:Fe(Z.created_at)}));m(Z=>Z.map(se=>se.id===M?{...se,responses:K}:se))}}catch(F){console.error("Yanıtlar alınırken hata oluştu:",F)}};o.useEffect(()=>{S()},[S]);const j=M=>{const{id:F,value:K}=M.target;T(Z=>({...Z,[F.replace("request-","")]:K}))},_=async()=>{try{const M=await D.post("/backend/api/api_support.php",{action:"create",user_id:(s==null?void 0:s.id)||1,subject:k.title,message:k.description,category:k.category,priority:k.priority});M.data.status==="success"?(S(),u(!1),T({category:"technical",title:"",description:"",priority:"medium"})):w("Talep oluşturulamadı: "+M.data.message)}catch(M){console.error("Talep oluşturulurken hata oluştu:",M),w("Talep oluşturulamadı. Lütfen daha sonra tekrar deneyin.")}},L=async()=>{if(!(!x||!E.trim()))try{const M=await D.post("/backend/api/api_support.php",{action:"reply",ticket_id:x,user_id:(s==null?void 0:s.id)||1,message:E,is_admin:0});M.data.status==="success"?(await d(x),h(!1),v(null),R("")):w("Yanıt gönderilemedi: "+M.data.message)}catch(M){console.error("Yanıt gönderilirken hata oluştu:",M),w("Yanıt gönderilemedi. Lütfen daha sonra tekrar deneyin.")}},O=i==="açık"?c.filter(M=>M.status==="açık"):c.filter(M=>M.status==="tamamlandı"),W=M=>{switch(M){case"açık":return{color:"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300",icon:e.jsx(et,{className:"mr-1.5"})};case"tamamlandı":return{color:"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300",icon:e.jsx(zt,{className:"mr-1.5"})};default:return{color:"bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-300",icon:e.jsx(et,{className:"mr-1.5"})}}};return e.jsxs("div",{className:"container",style:{maxWidth:a?"100%":"none",width:a?"100%":"auto",overflowX:"hidden",position:"relative",left:0,marginLeft:0,paddingTop:a?"0.5rem":"0.75rem",paddingLeft:a?"0.25rem":"0rem",paddingRight:a?"0.25rem":"1rem",boxSizing:"border-box"},children:[e.jsx("div",{className:"bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden mb-2 sm:mb-6",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between px-4 py-3 border-b border-gray-100 dark:border-gray-700 gap-3 sm:gap-0",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Ce,{className:"text-gray-700 dark:text-gray-300 mr-2"}),e.jsx("h2",{className:"text-base font-semibold text-gray-800 dark:text-white",children:"Taleplerim"})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{className:`px-4 py-2 text-sm font-medium rounded-full transition-all duration-200 ${i==="açık"?"bg-[#FF3E71] text-white":"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"}`,onClick:()=>l("açık"),children:"Açık"}),e.jsx("button",{className:`px-4 py-2 text-sm font-medium rounded-full transition-all duration-200 ${i==="tamamlanan"?"bg-[#FF3E71] text-white":"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"}`,onClick:()=>l("tamamlanan"),children:"Tamamlanan"})]})]}),e.jsxs("button",{onClick:()=>u(!0),className:`inline-flex items-center justify-center border border-transparent rounded-full shadow-sm font-medium text-white bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] hover:from-[#FF5F87] hover:to-[#FF3E71] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF3E71] transition-all duration-300 ${a?"px-3 py-2 text-sm":"px-4 py-2 text-sm"}`,children:[e.jsx(Qe,{className:`${a?"mr-1":"mr-2"}`})," Yeni Talep"]})]})}),N?e.jsxs("div",{className:"text-center py-6 sm:py-10 bg-white dark:bg-[#16151c] rounded-lg shadow-sm px-2",children:[e.jsx("div",{className:"w-12 h-12 sm:w-16 sm:h-16 mx-auto bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-300 rounded-full flex items-center justify-center mb-2 sm:mb-4 animate-pulse",children:e.jsx(Ce,{className:"text-base sm:text-xl"})}),e.jsx("h3",{className:"text-sm sm:text-lg font-medium text-gray-900 dark:text-white",children:"Yükleniyor..."}),e.jsx("p",{className:"mt-0.5 sm:mt-1 text-xs sm:text-sm text-gray-500 dark:text-gray-400",children:"Bekleyin."})]}):b?e.jsxs("div",{className:"text-center py-6 sm:py-10 bg-white dark:bg-[#16151c] rounded-lg shadow-sm px-2",children:[e.jsx("div",{className:"w-12 h-12 sm:w-16 sm:h-16 mx-auto bg-red-100 dark:bg-red-900/30 text-red-500 dark:text-red-300 rounded-full flex items-center justify-center mb-2 sm:mb-4",children:e.jsx(Ce,{className:"text-base sm:text-xl"})}),e.jsx("h3",{className:"text-sm sm:text-lg font-medium text-gray-900 dark:text-white",children:"Hata"}),e.jsx("p",{className:"mt-0.5 sm:mt-1 text-xs sm:text-sm text-gray-500 dark:text-gray-400",children:b}),e.jsx("button",{onClick:S,className:"mt-2 sm:mt-4 px-3 py-1.5 sm:px-4 sm:py-2 bg-[#FF3E71] text-white rounded-full text-xs sm:text-sm",children:"Tekrar Dene"})]}):e.jsxs("div",{className:"space-y-2 sm:space-y-4",children:[O.map((M,F)=>{const K=W(M.status);return e.jsx(le.div,{className:"bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:F*.1},children:e.jsxs("div",{className:"p-2 sm:p-5",children:[e.jsx("div",{className:"flex flex-col gap-2 sm:gap-0 sm:flex-row sm:items-start justify-between",children:e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-sm sm:text-lg font-semibold text-gray-900 dark:text-white line-clamp-2",children:M.title}),e.jsxs("div",{className:"flex flex-wrap items-center text-xs sm:text-sm text-gray-500 dark:text-gray-400 mt-1 gap-2",children:[e.jsxs("span",{className:"inline-flex items-center",children:[e.jsx(et,{className:"mr-1 text-[#FF3E71]"}),M.date]}),e.jsxs("span",{className:`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${K.color}`,children:[K.icon,M.status.charAt(0).toUpperCase()+M.status.slice(1)]}),e.jsx("span",{className:"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-0.5 rounded-full text-xs",children:Ni(M.category)})]})]})}),e.jsx("div",{className:"mt-4 bg-gray-50 dark:bg-gray-800/30 p-4 rounded-lg",children:e.jsx("p",{className:"text-gray-600 dark:text-gray-300 text-sm",children:M.description})}),M.responses.length>0&&e.jsxs("div",{className:"mt-4 bg-gray-50 dark:bg-gray-800/30 p-4 rounded-lg border-l-4 border-[#FF3E71]",children:[e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx("span",{className:"font-medium text-sm text-gray-700 dark:text-gray-300",children:M.responses[M.responses.length-1].author}),e.jsx("span",{className:"mx-2 text-gray-400",children:"•"}),e.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:M.responses[M.responses.length-1].date})]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:M.responses[M.responses.length-1].message})]}),e.jsx("div",{className:"mt-4 pt-4 border-t dark:border-gray-700 flex flex-wrap justify-end items-center",children:e.jsx("div",{className:"mt-2 sm:mt-0 flex items-center space-x-3",children:M.status==="açık"?e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:()=>{v(M.id),h(!0)},className:"inline-flex items-center px-4 py-2 border border-transparent rounded-full shadow-sm text-sm font-medium text-white bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] hover:from-[#FF5F87] hover:to-[#FF3E71] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF3E71] transition-all duration-300",children:"Yanıtla"}),e.jsx("button",{className:"inline-flex items-center px-4 py-2 border border-gray-200 dark:border-gray-700 rounded-full text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-[#16151c] hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200",children:"İptal Et"})]}):e.jsxs("div",{className:"text-sm text-gray-500 dark:text-gray-400 flex items-center",children:[e.jsx(zt,{className:"mr-2 text-green-500"})," Bu talep tamamlanmıştır"]})})})]})},M.id)}),O.length===0&&e.jsxs("div",{className:"text-center py-10 bg-white dark:bg-[#16151c] rounded-lg shadow-sm",children:[e.jsx("div",{className:"w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-800 text-[#FF3E71] rounded-full flex items-center justify-center mb-4",children:e.jsx(Ce,{size:24})}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Talep Bulunamadı"}),e.jsx("p",{className:"mt-1 text-gray-500 dark:text-gray-400",children:"Bu kriterlere uygun talep bulunmamaktadır."}),e.jsxs("button",{onClick:()=>u(!0),className:"mt-6 inline-flex items-center px-4 py-2 border border-transparent rounded-full shadow-sm text-sm font-medium text-white bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] hover:from-[#FF5F87] hover:to-[#FF3E71] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF3E71] transition-all duration-300",children:[e.jsx(Qe,{className:"mr-2"})," Yeni Talep Oluştur"]})]})]}),n&&e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 transition-opacity","aria-hidden":"true",children:e.jsx("div",{className:"absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"})}),e.jsx("span",{className:"hidden sm:inline-block sm:align-middle sm:h-screen","aria-hidden":"true",children:"​"}),e.jsxs("div",{className:"inline-block align-bottom bg-white dark:bg-[#16151c] rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[e.jsxs("div",{className:"bg-white dark:bg-[#16151c] px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4 pb-3 border-b border-gray-100 dark:border-gray-700",children:[e.jsxs("h3",{className:"text-lg leading-6 font-semibold text-gray-900 dark:text-white flex items-center",id:"modal-title",children:[e.jsx(Qe,{className:"mr-2 text-[#FF3E71]"})," Yeni Talep Oluştur"]}),e.jsx("button",{type:"button",onClick:()=>u(!1),className:"text-gray-400 hover:text-gray-500 dark:hover:text-gray-300",children:e.jsx("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"request-category",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Kategori"}),e.jsxs("select",{id:"request-category",value:k.category,onChange:j,className:"block w-full px-4 py-2.5 border border-gray-200 dark:border-gray-700 bg-white dark:bg-[#1e1d26] rounded-lg shadow-sm focus:ring-[#FF3E71] focus:border-[#FF3E71] sm:text-sm text-gray-700 dark:text-gray-200",children:[e.jsx("option",{value:"account",children:"Hesap Analizi"}),e.jsx("option",{value:"technical",children:"Teknik Destek"}),e.jsx("option",{value:"content",children:"Eğitim Talebi"}),e.jsx("option",{value:"other",children:"Danışmanlık"})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"request-title",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Talep Başlığı"}),e.jsx("input",{type:"text",id:"request-title",value:k.title,onChange:j,className:"block w-full px-4 py-2.5 border border-gray-200 dark:border-gray-700 bg-white dark:bg-[#1e1d26] rounded-lg shadow-sm focus:ring-[#FF3E71] focus:border-[#FF3E71] sm:text-sm text-gray-700 dark:text-gray-200",placeholder:"Talebiniz için kısa bir başlık"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"request-description",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Açıklama"}),e.jsx("textarea",{id:"request-description",rows:4,value:k.description,onChange:j,className:"block w-full px-4 py-2.5 border border-gray-200 dark:border-gray-700 bg-white dark:bg-[#1e1d26] rounded-lg shadow-sm focus:ring-[#FF3E71] focus:border-[#FF3E71] sm:text-sm text-gray-700 dark:text-gray-200",placeholder:"Talebinizi detaylı olarak açıklayın"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"request-priority",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Öncelik"}),e.jsxs("select",{id:"request-priority",value:k.priority,onChange:j,className:"block w-full px-4 py-2.5 border border-gray-200 dark:border-gray-700 bg-white dark:bg-[#1e1d26] rounded-lg shadow-sm focus:ring-[#FF3E71] focus:border-[#FF3E71] sm:text-sm text-gray-700 dark:text-gray-200",children:[e.jsx("option",{value:"low",children:"Düşük"}),e.jsx("option",{value:"medium",children:"Orta"}),e.jsx("option",{value:"high",children:"Yüksek"})]})]})]})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-[#1e1d26] px-4 py-4 sm:px-6 sm:flex sm:flex-row-reverse",children:[e.jsx("button",{type:"button",onClick:_,className:"w-full inline-flex justify-center rounded-full border border-transparent shadow-sm px-4 py-2.5 bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] hover:from-[#FF5F87] hover:to-[#FF3E71] text-base font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF3E71] transition-all duration-300 sm:ml-3 sm:w-auto sm:text-sm",children:"Talebi Gönder"}),e.jsx("button",{type:"button",onClick:()=>u(!1),className:"mt-3 w-full inline-flex justify-center rounded-full border border-gray-200 dark:border-gray-700 shadow-sm px-4 py-2.5 bg-white dark:bg-[#16151c] text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-300 transition-all duration-200 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm",children:"İptal"})]})]})]})}),g&&e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[e.jsx("div",{className:"fixed inset-0 transition-opacity","aria-hidden":"true",children:e.jsx("div",{className:"absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"})}),e.jsx("span",{className:"hidden sm:inline-block sm:align-middle sm:h-screen","aria-hidden":"true",children:"​"}),e.jsxs("div",{className:"inline-block align-bottom bg-white dark:bg-[#16151c] rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[e.jsxs("div",{className:"bg-white dark:bg-[#16151c] px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4 pb-3 border-b border-gray-100 dark:border-gray-700",children:[e.jsxs("h3",{className:"text-lg leading-6 font-semibold text-gray-900 dark:text-white flex items-center",id:"modal-title",children:[e.jsx(sr,{className:"mr-2 text-[#FF3E71]"})," Talebe Yanıt Ver"]}),e.jsx("button",{type:"button",onClick:()=>h(!1),className:"text-gray-400 hover:text-gray-500 dark:hover:text-gray-300",children:e.jsx("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsx("div",{className:"mt-4 space-y-4",children:e.jsxs("div",{children:[e.jsx("label",{htmlFor:"reply-text",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Yanıtınız"}),e.jsx("textarea",{id:"reply-text",rows:4,value:E,onChange:M=>R(M.target.value),className:"block w-full px-4 py-2.5 border border-gray-200 dark:border-gray-700 bg-white dark:bg-[#1e1d26] rounded-lg shadow-sm focus:ring-[#FF3E71] focus:border-[#FF3E71] sm:text-sm text-gray-700 dark:text-gray-200",placeholder:"Yanıtınızı buraya yazın..."})]})})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-[#1e1d26] px-4 py-4 sm:px-6 sm:flex sm:flex-row-reverse",children:[e.jsx("button",{type:"button",onClick:L,className:"w-full inline-flex justify-center rounded-full border border-transparent shadow-sm px-4 py-2.5 bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] hover:from-[#FF5F87] hover:to-[#FF3E71] text-base font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF3E71] transition-all duration-300 sm:ml-3 sm:w-auto sm:text-sm",children:"Yanıtı Gönder"}),e.jsx("button",{type:"button",onClick:()=>{h(!1),v(null),R("")},className:"mt-3 w-full inline-flex justify-center rounded-full border border-gray-200 dark:border-gray-700 shadow-sm px-4 py-2.5 bg-white dark:bg-[#16151c] text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-300 transition-all duration-200 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm",children:"İptal"})]})]})]})})]})},Fi=({notification:t,onMarkAsRead:a})=>{const r=()=>{a(t.id),t.link&&(window.location.href=t.link)},s=i=>{(i.key==="Enter"||i.key===" ")&&(i.preventDefault(),r())};return e.jsx("div",{role:"button",tabIndex:0,className:`p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors cursor-pointer ${t.read?"":"bg-blue-50/50 dark:bg-blue-900/10"}`,onClick:r,onKeyDown:s,children:e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:`min-w-[4px] h-16 ${t.read?"bg-gray-200 dark:bg-gray-600":"bg-[#FF3E71]"} rounded-full mr-3`}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex flex-wrap items-center text-xs text-gray-500 dark:text-gray-400 mb-1",children:[e.jsx(he,{className:"text-gray-500 dark:text-gray-400 mr-1 text-xs"}),e.jsx("span",{children:t.date}),!t.read&&e.jsx("span",{className:"ml-2 px-2 py-0.5 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs",children:"Yeni"}),t.type&&e.jsx("span",{className:`ml-2 px-2 py-0.5 rounded-full text-xs ${fi(t.type)}`,children:bi(t.source,t.type)})]}),e.jsx("h3",{className:"text-sm font-medium mb-1 text-gray-800 dark:text-white",children:t.title}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:t.message})]})]})})},Si=t=>{switch(t){case"like":return"Beğeni";case"follow":return"Takip";case"comment":return"Yorum";case"announcement":return"Duyuru";case"event":return"Etkinlik";case"course":return"Eğitim";case"welcome":return"Hoş Geldiniz";default:return"Bildirim"}},Ei=(t,a)=>{switch(t){case"like":case"comment":return"/dashboard/feed";case"follow":return"/dashboard/profile";case"announcement":return a?`/dashboard/announcements/${a}`:"/dashboard/announcements";case"event":return"/dashboard/events";case"course":return"/dashboard/courses";default:return"/dashboard"}},Ti=()=>{const[t,a]=o.useState([]),[r,s]=o.useState(!1),[i,l]=o.useState(null),n=t.filter(v=>!v.read).length,u=o.useCallback(async()=>{s(!0),l(null);try{const E=await D.get("/backend/api/api_data.php",{params:{endpoint:"notifications",user_id:1}});if(E.data.status==="success"){const R=E.data.data.map(N=>({id:N.id,title:Si(N.type||"info"),message:N.content,date:Fe(N.created_at),read:N.is_read===1,type:N.type||"info",source:"system",source_id:N.related_id,link:Ei(N.type||"info",N.related_id)}));a(R)}else l("Bildirimler alınamadı"),console.error("Bildirimler alınamadı:",E.data.message)}catch(v){l("Bildirimler yüklenirken bir hata oluştu"),console.error("Bildirimler yüklenirken hata:",v)}finally{s(!1)}},[]);o.useEffect(()=>{u()},[u]);const g=async v=>{try{(await D.post("/backend/api/api_data.php?endpoint=notifications",{action:"mark_as_read",notification_id:v,user_id:2})).data.status==="success"&&a(R=>R.map(N=>N.id===v?{...N,read:!0}:N))}catch(E){console.error("Bildirim okundu olarak işaretlenirken hata:",E)}},h=async()=>{try{(await D.post("/backend/api/api_data.php?endpoint=notifications",{action:"mark_all_as_read",user_id:2})).data.status==="success"&&a(t.map(E=>({...E,read:!0})))}catch(v){console.error("Tüm bildirimler okundu olarak işaretlenirken hata:",v)}},x=()=>r?e.jsxs("div",{className:"text-center py-10",children:[e.jsx("div",{className:"w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-300 rounded-full flex items-center justify-center mb-4 animate-pulse",children:e.jsx(ze,{className:"w-6 h-6"})}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Bildirimler Yükleniyor"}),e.jsx("p",{className:"mt-1 text-gray-500 dark:text-gray-400",children:"Lütfen bekleyin..."})]}):i?e.jsxs("div",{className:"text-center py-10",children:[e.jsx("div",{className:"w-16 h-16 mx-auto bg-red-100 dark:bg-red-900/30 text-red-500 dark:text-red-400 rounded-full flex items-center justify-center mb-4",children:e.jsx(ze,{className:"w-6 h-6"})}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Hata Oluştu"}),e.jsx("p",{className:"mt-1 text-red-500 dark:text-red-400",children:i})]}):t.length===0?e.jsxs("div",{className:"text-center py-10",children:[e.jsx("div",{className:"w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-300 rounded-full flex items-center justify-center mb-4",children:e.jsx(ze,{className:"w-6 h-6"})}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Bildirim Bulunamadı"}),e.jsx("p",{className:"mt-1 text-gray-500 dark:text-gray-400",children:"Henüz bildiriminiz bulunmamaktadır."})]}):t.map(v=>e.jsx(Fi,{notification:v,onMarkAsRead:g},v.id));return e.jsx("div",{children:e.jsxs("div",{className:"bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between px-4 py-3 border-b border-gray-100 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(ze,{className:"text-gray-700 dark:text-gray-300 mr-2"}),e.jsx("h2",{className:"text-base font-semibold text-gray-800 dark:text-white",children:"Bildirimler"}),n>0&&e.jsx("span",{className:"ml-2 bg-[#FF3E71] text-white text-xs px-2 py-0.5 rounded-full",children:n})]}),n>0&&e.jsxs("button",{onClick:h,className:"text-[#FF3E71] text-sm font-medium hover:underline flex items-center",children:[e.jsx(qa,{className:"mr-1"}),e.jsx("span",{children:"Tümünü Okundu İşaretle"})]})]}),e.jsx("div",{className:"divide-y divide-gray-100 dark:divide-gray-700",children:x()})]})})};function zi(t){return P({attr:{viewBox:"0 0 24 24"},child:[{tag:"path",attr:{fill:"none",d:"M0 0h24v24H0z"},child:[]},{tag:"path",attr:{d:"m23 12-2.44-2.79.34-3.69-3.61-.82-1.89-3.2L12 2.96 8.6 1.5 6.71 4.69 3.1 5.5l.34 3.7L1 12l2.44 2.79-.34 3.7 3.61.82L8.6 22.5l3.4-1.47 3.4 1.46 1.89-3.19 3.61-.82-.34-3.69L23 12zm-12.91 4.72-3.8-3.81 1.48-1.48 2.32 2.33 5.85-5.87 1.48 1.48-7.33 7.35z"},child:[]}]})(t)}function Zt(t){return P({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"},child:[]}]})(t)}function Ci(t){return P({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z",clipRule:"evenodd"},child:[]}]})(t)}const Li=()=>{var z,b,w;const{isMobile:t}=o.useContext(ue),{tiktokUser:a,loading:r,unlinkTikTokAccount:s}=Ze(),[i,l]=o.useState([]),[n,u]=o.useState(null),[g,h]=o.useState(!1),[x,v]=o.useState(!0);o.useEffect(()=>{(async()=>{v(!1)})()},[]),o.useEffect(()=>{const c=new URLSearchParams(window.location.search),m=c.get("tiktok_link"),k=c.get("message");m==="success"?(console.log("TikTok bağlantısı başarılı, context yenileniyor..."),window.history.replaceState({},document.title,window.location.pathname),window.dispatchEvent(new CustomEvent("tiktokLinked")),setTimeout(()=>{window.location.reload()},1e3)):m==="error"&&k&&(alert("TikTok bağlantı hatası: "+decodeURIComponent(k)),window.history.replaceState({},document.title,window.location.pathname))},[]),o.useEffect(()=>{(async()=>{var m;if(console.log("fetchVideos çağrıldı, tiktokUser:",a),a)try{console.log("Video API çağrısı yapılıyor...");const k=await fetch("/backend/api/tiktok-videos.php",{credentials:"include"});console.log("Video API yanıtı:",k.status);const T=await k.json();console.log("Video API verisi:",T),T.status==="success"?(l(T.data.videos||[]),u(T.data.analytics||null),console.log("Videolar set edildi:",((m=T.data.videos)==null?void 0:m.length)||0)):(console.error("Videos API error:",T.message),l([]),u(null))}catch(k){console.error("Videos yüklenirken hata:",k),l([]),u(null)}else console.log("TikTok user yok, videolar temizleniyor"),l([]),u(null)})()},[a]);const E=async()=>{h(!0);try{await s()?(l([]),u(null)):console.error("TikTok bağlantısı kaldırılamadı")}catch(c){console.error("TikTok bağlantısı kaldırılırken hata:",c)}finally{h(!1)}},R=()=>{const c="awfw8k9nim1e8dmu",m=encodeURIComponent("https://akademi.tuberajans.com/backend/api/tiktok-callback.php"),k="profile_"+Math.random().toString(36).substring(2,15),S=`https://www.tiktok.com/v2/auth/authorize/?client_key=${c}&scope=user.info.basic,user.info.profile,user.info.stats,video.list&response_type=code&redirect_uri=${m}&state=${k}`;localStorage.setItem("tiktok_oauth_state",k),localStorage.setItem("tiktok_redirect_after","profile"),window.location.href=S};return o.useEffect(()=>{const c=m=>{console.log("Received message:",m.data),m.data.type==="tiktok_oauth_result"&&(m.data.status==="success"?(console.log("TikTok bağlantısı başarılı, context yenileniyor..."),window.dispatchEvent(new CustomEvent("tiktokLinked")),setTimeout(()=>{window.location.reload()},500)):alert("TikTok bağlantı hatası: "+m.data.message))};return window.addEventListener("message",c),()=>window.removeEventListener("message",c)},[]),r||x?e.jsx("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-tuber-pink mx-auto mb-4"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Yükleniyor..."})]})}):e.jsx("div",{className:"container",style:{maxWidth:t?"100%":"none",width:t?"100%":"auto",overflowX:"hidden",position:"relative",left:0,marginLeft:0,paddingTop:t?"0.5rem":"0.75rem",paddingLeft:t?"0.25rem":"0rem",paddingRight:t?"0.25rem":"1rem",boxSizing:"border-box"},children:e.jsx("div",{className:"py-6",children:a?e.jsxs(le.div,{className:"space-y-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.1},children:[e.jsx("div",{className:"bg-white dark:bg-[#16151c] rounded-xl shadow-lg border border-gray-100 dark:border-gray-800 overflow-hidden",children:e.jsx("div",{className:"p-4 lg:p-6",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{className:"relative flex-shrink-0",children:[e.jsx("img",{src:a.avatar_url,alt:a.display_name,className:"w-20 h-20 lg:w-24 lg:h-24 rounded-full object-cover border-4 border-white dark:border-gray-700 shadow-lg"}),a.is_verified&&e.jsx("div",{className:"absolute -bottom-1 -right-1 w-7 h-7 bg-blue-500 rounded-full flex items-center justify-center border-2 border-white dark:border-gray-700",children:e.jsx(zi,{className:"w-4 h-4 text-white"})})]}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("div",{className:"flex items-center mb-2",children:e.jsxs("h2",{className:"text-xl lg:text-2xl font-bold text-gray-900 dark:text-white flex items-center",children:["Tuber Ajans"," ",e.jsx("button",{onClick:E,disabled:g,className:"ml-3 text-gray-400 hover:text-red-500 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed p-1 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20",title:g?"Bağlantı kesiliyor...":"TikTok bağlantısını kes",children:e.jsx(pr,{className:"text-sm"})})]})}),e.jsxs("p",{className:"text-gray-600 dark:text-gray-400",children:["@",a.username]}),a.tiktok_bio&&e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:a.tiktok_bio})]})]}),e.jsx("div",{className:"ml-20 lg:ml-24",children:e.jsxs("div",{className:"flex justify-start space-x-4 md:space-x-6 lg:space-x-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-sm lg:text-2xl font-bold text-gray-900 dark:text-white",children:oe(a.followers_count)}),e.jsx("div",{className:"text-xs lg:text-sm text-gray-600 dark:text-gray-400",children:"Takipçi"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-sm lg:text-2xl font-bold text-gray-900 dark:text-white",children:oe(a.following_count)}),e.jsx("div",{className:"text-xs lg:text-sm text-gray-600 dark:text-gray-400",children:"Takip"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-sm lg:text-2xl font-bold text-gray-900 dark:text-white",children:oe(a.likes_count)}),e.jsx("div",{className:"text-xs lg:text-sm text-gray-600 dark:text-gray-400",children:"Beğeni"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-sm lg:text-2xl font-bold text-gray-900 dark:text-white",children:oe(a.video_count)}),e.jsx("div",{className:"text-xs lg:text-sm text-gray-600 dark:text-gray-400",children:"Video"})]})]})})]})})}),n&&e.jsxs("div",{className:"bg-white dark:bg-[#16151c] rounded-xl shadow-lg border border-gray-100 dark:border-gray-800",children:[e.jsx("div",{className:"px-4 lg:px-6 py-4 border-b border-gray-100 dark:border-gray-700",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Zt,{className:"text-tuber-pink mr-2 text-xl"}),e.jsx("h3",{className:"text-lg lg:text-xl font-semibold text-gray-900 dark:text-white",children:"Toplam Performans Metrikleri"})]})}),e.jsx("div",{className:"p-4 lg:p-6",children:e.jsxs("div",{className:"grid grid-cols-2 lg:grid-cols-4 xl:grid-cols-7 gap-2 lg:gap-4",children:[e.jsxs("div",{className:"bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 rounded-xl p-4 border border-red-200 dark:border-red-800",children:[e.jsx("div",{className:"text-xs text-red-600 dark:text-red-400 mb-1",children:"Toplam İzlenme"}),e.jsx("div",{className:"text-xl font-bold text-red-700 dark:text-red-300",children:oe(n.total_views)})]}),e.jsxs("div",{className:"bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-xl p-4 border border-blue-200 dark:border-blue-800",children:[e.jsx("div",{className:"text-xs text-blue-600 dark:text-blue-400 mb-1",children:"Etkileşim Oranı"}),e.jsxs("div",{className:"text-xl font-bold text-blue-700 dark:text-blue-300",children:[n.engagement_rate,"%"]})]}),e.jsxs("div",{className:"bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-xl p-4 border border-orange-200 dark:border-orange-800",children:[e.jsx("div",{className:"text-xs text-orange-600 dark:text-orange-400 mb-1",children:"Ortalama İzlenme"}),e.jsx("div",{className:"text-xl font-bold text-orange-700 dark:text-orange-300",children:oe(n.average_views)})]}),e.jsxs("div",{className:"bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-xl p-4 border border-green-200 dark:border-green-800",children:[e.jsx("div",{className:"text-xs text-green-600 dark:text-green-400 mb-1",children:"Ortalama Video Beğeni"}),e.jsx("div",{className:"text-xl font-bold text-green-700 dark:text-green-300",children:oe(n.average_likes)})]}),e.jsxs("div",{className:"bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-xl p-4 border border-purple-200 dark:border-purple-800",children:[e.jsx("div",{className:"text-xs text-purple-600 dark:text-purple-400 mb-1",children:"Ortalama Video Yorum"}),e.jsx("div",{className:"text-xl font-bold text-purple-700 dark:text-purple-300",children:n.average_comments})]}),e.jsxs("div",{className:"bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-xl p-4 border border-orange-200 dark:border-orange-800",children:[e.jsx("div",{className:"text-xs text-orange-600 dark:text-orange-400 mb-1",children:"Ortalama Video Paylaşım"}),e.jsx("div",{className:"text-xl font-bold text-orange-700 dark:text-orange-300",children:n.average_shares})]})]})})]}),n&&(Object.keys(n.most_used_hashtags||{}).length>0||!0)&&e.jsxs("div",{className:"bg-white dark:bg-[#16151c] rounded-xl shadow-lg border border-gray-100 dark:border-gray-800",children:[e.jsx("div",{className:"px-4 lg:px-6 py-4 border-b border-gray-100 dark:border-gray-700",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"text-tuber-pink mr-2 text-xl",children:"#"}),e.jsx("h3",{className:"text-lg lg:text-xl font-semibold text-gray-900 dark:text-white",children:"En Çok Kullanılan Hashtag'ler"})]})}),e.jsx("div",{className:"p-4 lg:p-6",children:e.jsx("div",{className:"flex flex-wrap gap-3",children:n.most_used_hashtags&&Object.keys(n.most_used_hashtags).length>0?Object.entries(n.most_used_hashtags).map(([c,m])=>e.jsxs("span",{className:"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-tuber-pink/10 to-tuber-purple/10 text-tuber-pink border border-tuber-pink/20 hover:border-tuber-pink/40 hover:shadow-sm transition-all duration-200",children:[e.jsx("span",{className:"font-semibold",children:"#"}),e.jsx("span",{className:"ml-0.5",children:c}),e.jsx("span",{className:"ml-2 flex items-center justify-center w-5 h-5 bg-tuber-pink/20 text-tuber-pink text-xs rounded-full font-semibold",children:m})]},c)):[{hashtag:"cover",count:2},{hashtag:"kışarkıları",count:1},{hashtag:"animasyonmüzikleri",count:1},{hashtag:"athena",count:1},{hashtag:"yorulmakolmaz",count:1},{hashtag:"duman",count:1},{hashtag:"manga",count:1},{hashtag:"wecouldbethesame",count:1}].map(({hashtag:c,count:m})=>e.jsxs("span",{className:"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-tuber-pink/10 to-tuber-purple/10 text-tuber-pink border border-tuber-pink/20 hover:border-tuber-pink/40 hover:shadow-sm transition-all duration-200",children:[e.jsx("span",{className:"font-semibold",children:"#"}),e.jsx("span",{className:"ml-0.5",children:c}),e.jsx("span",{className:"ml-2 flex items-center justify-center w-5 h-5 bg-tuber-pink/20 text-tuber-pink text-xs rounded-full font-semibold",children:m})]},c))})})]}),e.jsxs("div",{className:"bg-white dark:bg-[#16151c] rounded-xl shadow-lg border border-gray-100 dark:border-gray-800",children:[e.jsx("div",{className:"px-4 md:px-6 py-4 border-b border-gray-100 dark:border-gray-700",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx($e,{className:"text-tuber-pink mr-2 text-xl"}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg lg:text-xl font-semibold text-gray-900 dark:text-white",children:["Video Detayları (",(i==null?void 0:i.length)||0," video)"]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"Son video performansları"})]})]})}),e.jsx("div",{className:"p-4 md:p-6",children:i&&i.length>0?e.jsx("div",{className:"grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-8 gap-2 md:gap-3",children:i.map((c,m)=>e.jsxs(le.div,{className:"group cursor-pointer bg-gray-50 dark:bg-gray-800/50 rounded-lg overflow-hidden hover:shadow-lg hover:scale-105 transition-all duration-300 border border-gray-200 dark:border-gray-700",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:m*.1},children:[e.jsxs("div",{className:"relative aspect-[9/16] bg-gray-200 dark:bg-gray-700 overflow-hidden",children:[e.jsx("img",{src:c.cover_image_url||c.thumbnail||`https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=300&h=400&fit=crop&random=${c.video_id||m}`,alt:c.title,className:"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300",onError:k=>{const T=k.target,S=`https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=300&h=400&fit=crop&random=${c.video_id||m}`;T.src!==S?T.src=S:T.src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik04MCA3MFYxMzBMMTMwIDEwMEw4MCA3MFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+"}}),e.jsx("div",{className:"absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-colors duration-300"}),e.jsx("div",{className:"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:e.jsx(nr,{className:"text-white text-2xl"})}),e.jsx("div",{className:"absolute top-1 left-1 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded",children:yi(c.duration)}),e.jsxs("div",{className:"absolute top-1 right-1 bg-tuber-pink/90 text-white text-xs px-1.5 py-0.5 rounded",children:["%",c.engagement_rate||0]})]}),e.jsxs("div",{className:"p-2",children:[e.jsxs("div",{className:"flex items-start justify-between mb-1 gap-1",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white text-xs line-clamp-1 mb-1 pr-1",children:c.title||"Başlıksız Video"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:Fe(c.create_time)})]}),e.jsx("a",{href:c.share_url,target:"_blank",rel:"noopener noreferrer",className:"text-tuber-pink hover:text-tuber-purple transition-colors flex-shrink-0 p-1 hover:bg-tuber-pink/10 rounded min-w-[24px] min-h-[24px] flex items-center justify-center",title:"Videoyu TikTok'ta aç",children:e.jsx(Qa,{className:"text-sm"})})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-x-2 gap-y-1 text-xs",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(ra,{className:"text-blue-500 text-xs"}),e.jsx("span",{className:"text-gray-600 dark:text-gray-400 text-xs ml-1",children:oe(c.view_count||0)})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(Ie,{className:"text-red-500 text-xs"}),e.jsx("span",{className:"text-gray-600 dark:text-gray-400 text-xs ml-1",children:oe(c.like_count||0)})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(aa,{className:"text-green-500 text-xs"}),e.jsx("span",{className:"text-gray-600 dark:text-gray-400 text-xs ml-1",children:oe(c.comment_count||0)})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(or,{className:"text-purple-500 text-xs"}),e.jsx("span",{className:"text-gray-600 dark:text-gray-400 text-xs ml-1",children:oe(c.share_count||0)})]})]}),c.hashtags&&c.hashtags.length>0&&e.jsx("div",{className:"mt-1.5 pt-1.5 border-t border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"flex flex-wrap gap-0.5",children:[c.hashtags.slice(0,1).map(k=>e.jsxs("span",{className:"text-xs bg-tuber-pink/10 text-tuber-pink px-1 py-0.5 rounded",children:["#",k]},k)),c.hashtags.length>1&&e.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400 px-0.5",children:["+",c.hashtags.length-1]})]})})]})]},c.id))}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx($e,{className:"mx-auto text-6xl text-gray-300 dark:text-gray-600 mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Henüz video bulunamadı"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"TikTok hesabınızda video bulunmuyor veya videolarınız henüz yüklenmemiş."})]})})]}),(n==null?void 0:n.performance_data)&&n.performance_data.length>0&&e.jsxs("div",{className:"bg-white dark:bg-[#16151c] rounded-xl shadow-lg border border-gray-100 dark:border-gray-800",children:[e.jsx("div",{className:"px-4 md:px-6 py-4 border-b border-gray-100 dark:border-gray-700",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Zt,{className:"text-tuber-pink mr-2 text-xl"}),e.jsx("h3",{className:"text-lg lg:text-xl font-semibold text-gray-900 dark:text-white",children:"Video Performans Analizi"})]}),e.jsxs("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Son ",((z=n==null?void 0:n.performance_data)==null?void 0:z.length)||0," video"]})]})}),e.jsxs("div",{className:"p-4 md:p-6",children:[e.jsxs("div",{className:"grid grid-cols-2 md:flex md:flex-wrap gap-2 mb-4 md:mb-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-3 h-3 bg-blue-500 rounded mr-2"}),e.jsx("span",{className:"text-xs md:text-sm text-gray-600 dark:text-gray-400",children:"İzlenme"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-3 h-3 bg-red-500 rounded mr-2"}),e.jsx("span",{className:"text-xs md:text-sm text-gray-600 dark:text-gray-400",children:"Beğeni"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-3 h-3 bg-green-500 rounded mr-2"}),e.jsx("span",{className:"text-xs md:text-sm text-gray-600 dark:text-gray-400",children:"Yorum"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-3 h-3 bg-purple-500 rounded mr-2"}),e.jsx("span",{className:"text-xs md:text-sm text-gray-600 dark:text-gray-400",children:"Paylaşım"})]})]}),e.jsx("div",{className:"h-72 md:h-96",children:e.jsx(Nt,{width:"100%",height:"100%",children:e.jsxs(Ua,{data:(b=n==null?void 0:n.performance_data)==null?void 0:b.slice(-8).map(c=>({...c,views_scaled:Math.log10(Math.max(c.views,1)),likes_scaled:Math.log10(Math.max(c.likes,1))*2,comments_scaled:Math.log10(Math.max(c.comments,1))*3,shares_scaled:Math.log10(Math.max(c.shares,1))*4})),margin:{top:5,right:5,left:5,bottom:60},barCategoryGap:"20%",children:[e.jsx(_t,{strokeDasharray:"3 3",stroke:"#e0e7ff"}),e.jsx(Ft,{dataKey:"date",tick:{fontSize:9,fill:"#6b7280"},angle:-45,textAnchor:"end",height:60,interval:0,tickFormatter:c=>{const m=new Date(c);return window.innerWidth<768?`${m.getDate().toString().padStart(2,"0")}/${(m.getMonth()+1).toString().padStart(2,"0")}`:`${m.getDate().toString().padStart(2,"0")}/${(m.getMonth()+1).toString().padStart(2,"0")}/${m.getFullYear()} ${m.getHours().toString().padStart(2,"0")}:${m.getMinutes().toString().padStart(2,"0")}`}}),e.jsx(St,{tick:{fontSize:11,fill:"#6b7280"},tickFormatter:c=>`${c.toFixed(1)}`}),e.jsx(Et,{contentStyle:{backgroundColor:"#ffffff",border:"1px solid #e5e7eb",borderRadius:"12px",fontSize:"13px",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1)"},labelFormatter:c=>{const m=new Date(c);return`${m.getDate().toString().padStart(2,"0")}/${(m.getMonth()+1).toString().padStart(2,"0")}/${m.getFullYear()} ${m.getHours().toString().padStart(2,"0")}:${m.getMinutes().toString().padStart(2,"0")}`},formatter:(c,m,k)=>{var _;let T=0;const S=(_=n==null?void 0:n.performance_data)==null?void 0:_.find(L=>{var O;return L.date===((O=k.payload)==null?void 0:O.date)});return S&&(m==="views_scaled"?T=S.views:m==="likes_scaled"?T=S.likes:m==="comments_scaled"?T=S.comments:m==="shares_scaled"&&(T=S.shares)),[oe(T),{views_scaled:"İzlenme",likes_scaled:"Beğeni",comments_scaled:"Yorum",shares_scaled:"Paylaşım"}[m]||m]}}),e.jsx(Te,{dataKey:"views_scaled",fill:"#3b82f6",radius:[1,1,0,0],minPointSize:3}),e.jsx(Te,{dataKey:"likes_scaled",fill:"#ef4444",radius:[1,1,0,0],minPointSize:3}),e.jsx(Te,{dataKey:"comments_scaled",fill:"#10b981",radius:[1,1,0,0],minPointSize:3}),e.jsx(Te,{dataKey:"shares_scaled",fill:"#8b5cf6",radius:[1,1,0,0],minPointSize:3})]})})}),e.jsx("div",{className:"mt-0 text-center",children:e.jsx("p",{className:"text-xs md:text-sm text-gray-500 dark:text-gray-400",children:"Son 8 video performansı • Grafik üzerine gelerek detaylı bilgi alabilirsiniz"})})]})]}),(n==null?void 0:n.performance_data)&&n.performance_data.length>0&&e.jsxs("div",{className:"bg-white dark:bg-[#16151c] rounded-xl shadow-lg border border-gray-100 dark:border-gray-800",children:[e.jsx("div",{className:"px-4 md:px-6 py-4 border-b border-gray-100 dark:border-gray-700",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Ci,{className:"text-tuber-pink mr-2 text-xl"}),e.jsx("h3",{className:"text-lg lg:text-xl font-semibold text-gray-900 dark:text-white",children:"Etkileşim Oranı Trendi"})]}),e.jsxs("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Ortalama: ",n==null?void 0:n.engagement_rate,"%"]})]})}),e.jsx("div",{className:"p-4 md:p-6",children:e.jsx("div",{className:"h-56 md:h-64",children:e.jsx(Nt,{width:"100%",height:"100%",children:e.jsxs(Va,{data:((w=n==null?void 0:n.performance_data)==null?void 0:w.slice(-10))||[],margin:{top:5,right:5,left:5,bottom:5},children:[e.jsx(_t,{strokeDasharray:"3 3",className:"opacity-30"}),e.jsx(Ft,{dataKey:"date",tick:{fontSize:10},tickFormatter:c=>{const m=new Date(c);return window.innerWidth<768?`${m.getDate()}/${m.getMonth()+1}`:`${m.getDate().toString().padStart(2,"0")}/${(m.getMonth()+1).toString().padStart(2,"0")}`}}),e.jsx(St,{tick:{fontSize:10},domain:["dataMin - 0.1","dataMax + 0.1"]}),e.jsx(Et,{contentStyle:{backgroundColor:"rgba(255, 255, 255, 0.95)",border:"1px solid #e5e7eb",borderRadius:"8px",fontSize:"11px"},formatter:c=>[`${c}%`,"Etkileşim Oranı"],labelFormatter:c=>{const m=new Date(c);return`${m.getDate().toString().padStart(2,"0")}/${(m.getMonth()+1).toString().padStart(2,"0")}/${m.getFullYear()}`}}),e.jsx(Ka,{type:"monotone",dataKey:"engagement_rate",stroke:"#ec4899",strokeWidth:2,dot:{fill:"#ec4899",strokeWidth:1,r:3},activeDot:{r:5}})]})})})})]})]}):e.jsx("div",{className:"max-w-2xl mx-auto",children:e.jsxs("div",{className:"bg-white dark:bg-[#16151c] rounded-xl shadow-lg p-6 text-center",children:[e.jsx("div",{className:"mb-6",children:e.jsx(ht,{className:"w-16 h-16 mx-auto text-tuber-pink"})}),e.jsx("h2",{className:"text-2xl font-bold mb-4 text-gray-900 dark:text-white",children:"TikTok Hesabını Bağla"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"TikTok hesabınızı bağlayarak video performansınızı takip edebilir ve detaylı analitiklerinizi görüntüleyebilirsiniz."}),e.jsxs("button",{onClick:R,className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-full shadow-sm text-white bg-tuber-pink hover:bg-tuber-pink-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-tuber-pink transition-colors duration-200",children:[e.jsx(ht,{className:"w-5 h-5 mr-2"}),"TikTok ile Bağlan"]})]})})})})},Ai=()=>e.jsxs("div",{className:"min-h-screen bg-[#0a0a0a] text-white",children:[e.jsxs("header",{className:"bg-gradient-to-r from-emerald-500 to-blue-600 py-10 relative overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 bg-black/10"}),e.jsxs("div",{className:"container mx-auto px-4 relative z-10 text-center",children:[e.jsx(ee,{to:"/",children:e.jsx("img",{src:"/images/logotuber1.png",alt:"Tuber Akademi Logo",className:"h-12 mx-auto mb-4"})}),e.jsx("h1",{className:"text-3xl md:text-4xl font-bold mb-2",children:"Kullanım Şartları"}),e.jsx("p",{className:"text-lg text-white/80 max-w-2xl mx-auto",children:"Tuber Akademi Yayıncı Portalı'nı kullanırken uymanız gereken şartlar ve koşullar"})]})]}),e.jsx("main",{className:"container mx-auto px-4 py-10",children:e.jsxs("div",{className:"max-w-4xl mx-auto bg-white/5 backdrop-blur-sm rounded-xl border border-white/10 p-6 md:p-8 shadow-xl",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold text-emerald-400 pb-2 border-b border-white/10 mb-4",children:"Kullanım Şartları ve Koşulları"}),e.jsxs("p",{className:"text-gray-300 mb-4",children:["Son güncelleme: ",new Date().toLocaleDateString("tr-TR")]}),e.jsx("p",{className:"text-gray-300",children:"Tuber Akademi Yayıncı Portalı'na hoş geldiniz. Bu portalı kullanarak aşağıdaki şartları ve koşulları kabul etmiş olursunuz. Lütfen bu şartları dikkatlice okuyunuz."})]}),e.jsxs("div",{className:"bg-emerald-500/10 border-l-4 border-emerald-500 rounded-r-lg p-4 mb-8",children:[e.jsxs("h3",{className:"flex items-center text-xl font-semibold text-emerald-400 mb-2",children:[e.jsx(Pe,{className:"mr-2"})," TikTok API Entegrasyonu Hakkında"]}),e.jsx("p",{className:"text-gray-300 mb-2",children:"Tuber Akademi Yayıncı Portalı, TikTok API'sini kullanarak TikTok yayıncılarına özel hizmetler sunmaktadır. Portalımız, TikTok geliştiriciler için politikalarına ve TikTok Platformu Koşullarına uygun bir şekilde hizmet vermektedir."}),e.jsx("p",{className:"text-gray-300 mb-2",children:"Kullandığımız TikTok özellikleri ve kapsamları:"}),e.jsxs("ul",{className:"list-disc pl-5 text-gray-300 mb-2",children:[e.jsxs("li",{children:[e.jsx("strong",{children:"Login Kit:"})," Kullanıcıların TikTok hesaplarıyla güvenli bir şekilde giriş yapmasını sağlar."]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Share Kit:"})," Kullanıcıların içeriklerini TikTok platformunda doğrudan paylaşabilmesini sağlar."]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Display API:"})," TikTok'taki içerik performansının portal üzerinde görüntülenmesini sağlar."]}),e.jsxs("li",{children:[e.jsx("strong",{children:"Analytics API:"})," İçerik analizleri ve performans verilerinin portal üzerinden izlenebilmesini sağlar."]})]}),e.jsxs("p",{className:"text-gray-300",children:["Bu portalı kullanarak, TikTok'un kendi ",e.jsx("a",{href:"https://www.tiktok.com/legal/terms-of-service",target:"_blank",rel:"noopener noreferrer",className:"text-emerald-400 hover:text-emerald-300",children:"Kullanım Şartları"})," ve ",e.jsx("a",{href:"https://www.tiktok.com/legal/privacy-policy",target:"_blank",rel:"noopener noreferrer",className:"text-emerald-400 hover:text-emerald-300",children:"Gizlilik Politikası"}),"'nı da kabul etmiş olursunuz."]})]}),e.jsxs("div",{className:"space-y-8",children:[e.jsxs("section",{children:[e.jsx("h3",{className:"text-xl font-semibold text-emerald-400 mb-3",children:"1. Tanımlar"}),e.jsx("p",{className:"text-gray-300 mb-2",children:"Bu kullanım şartlarında geçen:"}),e.jsxs("ul",{className:"list-disc pl-5 text-gray-300",children:[e.jsx("li",{children:`"Tuber Ajans", "biz", "bizim" veya "bizleri" ifadeleri Tuber Ajans'ı,`}),e.jsx("li",{children:`"Portal", Yayıncı Portalı'nı,`}),e.jsx("li",{children:'"Kullanıcı", "siz", "sizin" ifadeleri portala kayıt olan veya portalı ziyaret eden kişileri,'}),e.jsx("li",{children:'"Yayıncı", TikTok platformunda içerik üreten ve portala kayıtlı olan kişileri,'}),e.jsx("li",{children:'"TikTok", TikTok sosyal medya platformunu ifade eder.'})]})]}),e.jsxs("section",{children:[e.jsx("h3",{className:"text-xl font-semibold text-emerald-400 mb-3",children:"2. Portal Kullanımı"}),e.jsx("p",{className:"text-gray-300 mb-2",children:"2.1. Portala üye olabilmek için 18 yaşını doldurmuş olmanız gerekmektedir. 18 yaşından küçükseniz, ebeveyn veya yasal velinizin onayıyla kayıt olabilirsiniz."}),e.jsx("p",{className:"text-gray-300 mb-2",children:"2.2. Kayıt olurken doğru ve güncel bilgiler sağlamakla yükümlüsünüz. Hesap bilgilerinizin güvenliğinden siz sorumlusunuz."}),e.jsx("p",{className:"text-gray-300 mb-2",children:"2.3. Hesabınıza erişimi sağlayan kullanıcı adı ve şifrenizin gizliliğini korumak sizin sorumluluğunuzdadır. Hesabınızda gerçekleşen tüm aktivitelerden siz sorumlusunuz."}),e.jsx("p",{className:"text-gray-300",children:"2.4. Portalı kullanırken yerel, ulusal ve uluslararası kanunlara ve düzenlemelere uymak zorundasınız."})]}),e.jsxs("section",{children:[e.jsx("h3",{className:"text-xl font-semibold text-emerald-400 mb-3",children:"3. Hesap Onayı ve Kayıt Süreci"}),e.jsx("p",{className:"text-gray-300 mb-2",children:"3.1. Portala kayıt olduktan sonra, hesabınız yönetici onayına tabi olacaktır. Onay sürecinde TikTok profiliniz incelenecek ve uygun bulunduğu takdirde hesabınız aktifleştirilecektir."}),e.jsx("p",{className:"text-gray-300 mb-2",children:"3.2. Kayıt sırasında TikTok kullanıcı adınızı ve e-posta adresinizi doğru bir şekilde belirtmeniz gerekmektedir."}),e.jsx("p",{className:"text-gray-300",children:"3.3. Kayıt işleminiz tamamlandıktan sonra, sistem TikTok hesabınızdan bazı temel bilgileri (profil resmi, takipçi sayısı, beğeni sayısı gibi) çekecektir."})]}),e.jsxs("section",{children:[e.jsx("h3",{className:"text-xl font-semibold text-emerald-400 mb-3",children:"4. Portal İçeriği ve Özellikler"}),e.jsx("p",{className:"text-gray-300 mb-2",children:"4.1. Portal, TikTok yayıncılarına yönelik eğitim içerikleri, istatistik analizi, topluluk desteği ve marka işbirliği fırsatları sunmaktadır."}),e.jsx("p",{className:"text-gray-300 mb-2",children:"4.2. Portal içerisindeki tüm içerikler telif hakkı ile korunmaktadır. İçerikleri izinsiz kopyalayamaz, dağıtamaz veya ticari amaçla kullanamazsınız."}),e.jsx("p",{className:"text-gray-300",children:"4.3. Tuber Ajans, portal üzerindeki içerikleri ve özellikleri önceden bildirmeksizin değiştirme, ekleme veya kaldırma hakkını saklı tutar."})]}),e.jsxs("div",{className:"text-center mt-10",children:[e.jsx("p",{className:"text-gray-300 mb-6 font-semibold",children:"Bu kullanım şartlarını kabul ederek, içerdiği tüm koşulları okuduğunuzu, anladığınızı ve bunlara uymayı kabul ettiğinizi beyan etmiş olursunuz."}),e.jsxs(ee,{to:"/",className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-emerald-500 to-blue-600 rounded-lg text-white font-medium transition-all hover:from-emerald-600 hover:to-blue-700 transform hover:-translate-y-1",children:[e.jsx(ye,{className:"mr-2"})," Giriş Sayfasına Dön"]})]})]})]})})]}),Mi=()=>e.jsxs("div",{className:"min-h-screen bg-[#0a0a0a] text-white",children:[e.jsxs("header",{className:"bg-gradient-to-r from-emerald-500 to-blue-600 py-10 relative overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 bg-black/10"}),e.jsxs("div",{className:"container mx-auto px-4 relative z-10 text-center",children:[e.jsx(ee,{to:"/",children:e.jsx("img",{src:"/images/logotuber1.png",alt:"Tuber Akademi Logo",className:"h-12 mx-auto mb-4"})}),e.jsx("h1",{className:"text-3xl md:text-4xl font-bold mb-2",children:"Gizlilik Politikası"}),e.jsx("p",{className:"text-lg text-white/80 max-w-2xl mx-auto",children:"Tuber Akademi Yayıncı Portalı'nda bilgilerinizi nasıl topluyor, kullanıyor ve koruyoruz?"})]})]}),e.jsx("main",{className:"container mx-auto px-4 py-10",children:e.jsxs("div",{className:"max-w-4xl mx-auto bg-white/5 backdrop-blur-sm rounded-xl border border-white/10 p-6 md:p-8 shadow-xl",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold text-emerald-400 pb-2 border-b border-white/10 mb-4",children:"Gizlilik Politikası"}),e.jsxs("p",{className:"text-gray-300 mb-4",children:["Son güncelleme: ",new Date().toLocaleDateString("tr-TR")]}),e.jsx("p",{className:"text-gray-300",children:"Bu gizlilik politikası, Tuber Akademi Yayıncı Portalı'nın nasıl bilgi topladığını, kullandığını ve koruduğunu açıklamaktadır. Gizliliğiniz bizim için önemlidir ve kişisel verilerinizin güvenliğini sağlamak için gerekli tüm önlemleri alıyoruz."})]}),e.jsxs("div",{className:"bg-blue-500/10 border-l-4 border-blue-500 rounded-r-lg p-4 mb-8",children:[e.jsxs("h3",{className:"flex items-center text-xl font-semibold text-blue-400 mb-2",children:[e.jsx(dr,{className:"mr-2"})," Gizlilik Taahhüdümüz"]}),e.jsx("p",{className:"text-gray-300 mb-2",children:"Tuber Ajans olarak, kişisel verilerinizin gizliliğini ve güvenliğini sağlamayı taahhüt ediyoruz. Verilerinizi yalnızca bu gizlilik politikasında belirtilen amaçlar doğrultusunda topluyor ve kullanıyoruz."}),e.jsx("p",{className:"text-gray-300",children:"Verilerinizi üçüncü taraflarla yalnızca sizin izninizle veya yasal zorunluluklar gerektirdiğinde paylaşıyoruz."})]}),e.jsxs("div",{className:"bg-emerald-500/10 border-l-4 border-emerald-500 rounded-r-lg p-4 mb-8",children:[e.jsxs("h3",{className:"flex items-center text-xl font-semibold text-emerald-400 mb-2",children:[e.jsx(Pe,{className:"mr-2"})," TikTok Veri Entegrasyonu"]}),e.jsx("p",{className:"text-gray-300 mb-2",children:"Tuber Akademi Yayıncı Portalı, TikTok API'sini kullanarak TikTok hesap bilgilerinize erişmektedir. Bu erişim, TikTok'un geliştiriciler için veri politikalarına uygun şekilde gerçekleştirilmektedir."}),e.jsx("p",{className:"text-gray-300 mb-2",children:"TikTok hesabınızdan topladığımız veriler şunları içerebilir:"}),e.jsxs("ul",{className:"list-disc pl-5 text-gray-300 mb-2",children:[e.jsx("li",{children:"Profil bilgileri (kullanıcı adı, profil fotoğrafı, biyografi)"}),e.jsx("li",{children:"İstatistiki veriler (takipçi sayısı, beğeni sayısı, video performans metrikleri)"}),e.jsx("li",{children:"İçerik bilgileri (video başlıkları, açıklamalar, etiketler)"})]}),e.jsx("p",{className:"text-gray-300",children:"Bu verileri, yayıncılık kariyerinizi geliştirmek, performansınızı analiz etmek ve size özel içerik önerileri sunmak için kullanıyoruz."}),e.jsxs("p",{className:"text-gray-300",children:["TikTok verilerinizin kullanımı, aynı zamanda TikTok'un kendi ",e.jsx("a",{href:"https://www.tiktok.com/legal/privacy-policy",target:"_blank",rel:"noopener noreferrer",className:"text-emerald-400 hover:text-emerald-300",children:"Gizlilik Politikası"}),"'na da tabidir."]})]}),e.jsxs("div",{className:"space-y-8",children:[e.jsxs("section",{children:[e.jsx("h3",{className:"text-xl font-semibold text-emerald-400 mb-3",children:"1. Toplanan Bilgiler"}),e.jsx("p",{className:"text-gray-300 mb-2",children:"1.1. Kişisel Bilgiler"}),e.jsx("p",{className:"text-gray-300 mb-2",children:"Tuber Akademi Yayıncı Portalı'na kayıt olduğunuzda veya portalı kullandığınızda aşağıdaki kişisel bilgilerinizi toplayabiliriz:"}),e.jsxs("ul",{className:"list-disc pl-5 text-gray-300",children:[e.jsx("li",{children:"İletişim bilgileri (e-posta adresi)"}),e.jsx("li",{children:"TikTok kullanıcı adınız"}),e.jsx("li",{children:"TikTok profil bilgileriniz (profil resmi, bio vb.)"}),e.jsx("li",{children:"TikTok istatistikleriniz (takipçi sayısı, beğeni sayısı, izlenme sayısı vb.)"})]}),e.jsx("p",{className:"text-gray-300 mt-2 mb-2",children:"1.2. Otomatik Olarak Toplanan Bilgiler"}),e.jsx("p",{className:"text-gray-300 mb-2",children:"Portal ile etkileşimde bulunduğunuzda aşağıdaki bilgileri otomatik olarak toplayabiliriz:"}),e.jsxs("ul",{className:"list-disc pl-5 text-gray-300",children:[e.jsx("li",{children:"IP adresi"}),e.jsx("li",{children:"Tarayıcı türü ve dil ayarları"}),e.jsx("li",{children:"Erişim tarihleri ve saatleri"}),e.jsx("li",{children:"Ziyaret edilen sayfalar"}),e.jsx("li",{children:"Tıklama davranışları ve portal içi aktiviteler"}),e.jsx("li",{children:"Cihaz bilgileri"})]})]}),e.jsxs("section",{children:[e.jsx("h3",{className:"text-xl font-semibold text-emerald-400 mb-3",children:"2. Bilgilerin Kullanımı"}),e.jsx("p",{className:"text-gray-300 mb-2",children:"Topladığımız bilgileri aşağıdaki amaçlar için kullanırız:"}),e.jsxs("ul",{className:"list-disc pl-5 text-gray-300",children:[e.jsx("li",{children:"Portal hesabınızı oluşturmak ve yönetmek"}),e.jsx("li",{children:"TikTok yayıncılık kariyerinizde size destek olmak"}),e.jsx("li",{children:"Size özelleştirilmiş içerik, öneriler ve eğitim materyalleri sunmak"}),e.jsx("li",{children:"İstatistiklerinizi analiz etmek ve performans raporları oluşturmak"}),e.jsx("li",{children:"Topluluk içinde diğer yayıncılarla etkileşime geçmenizi sağlamak"}),e.jsx("li",{children:"Size uygun marka işbirliği fırsatları sunmak"}),e.jsx("li",{children:"Portal ile ilgili güncellemeler, duyurular ve önemli bilgileri iletmek"})]})]}),e.jsxs("section",{children:[e.jsx("h3",{className:"text-xl font-semibold text-emerald-400 mb-3",children:"3. Veri Güvenliği"}),e.jsx("p",{className:"text-gray-300 mb-2",children:"Kişisel verilerinizi korumak için endüstri standardı güvenlik önlemleri uyguluyoruz. Bu önlemler şunları içerir:"}),e.jsxs("ul",{className:"list-disc pl-5 text-gray-300",children:[e.jsx("li",{children:"Şifreleme protokolleri (SSL/TLS)"}),e.jsx("li",{children:"Şifrelerin hashli bir formatta saklanması"}),e.jsx("li",{children:"Güvenlik duvarları ve erişim kontrolleri"}),e.jsx("li",{children:"Düzenli güvenlik değerlendirmeleri ve testleri"}),e.jsx("li",{children:"Veri tabanı güvenliği"})]})]}),e.jsxs("div",{className:"text-center mt-10",children:[e.jsx("p",{className:"text-gray-300 mb-6 font-semibold",children:"Tuber Akademi Yayıncı Portalı'nı kullanarak, bu gizlilik politikasını okuduğunuzu, anladığınızı ve kabul ettiğinizi beyan etmiş olursunuz."}),e.jsxs(ee,{to:"/",className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-emerald-500 to-blue-600 rounded-lg text-white font-medium transition-all hover:from-emerald-600 hover:to-blue-700 transform hover:-translate-y-1",children:[e.jsx(ye,{className:"mr-2"})," Giriş Sayfasına Dön"]})]})]})]})})]}),Ri=()=>{const t=it(),a=be(),s=new URLSearchParams(t.search).get("message")||"Bir hata oluştu";return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4",children:e.jsx(le.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"max-w-md w-full bg-white dark:bg-gray-800 rounded-xl shadow-xl p-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx(le.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"mx-auto h-16 w-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mb-4",children:e.jsx(Xa,{className:"h-8 w-8 text-red-600 dark:text-red-500"})}),e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"Hata Oluştu"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-8",children:s}),e.jsxs(le.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>a(-1),className:"inline-flex items-center px-6 py-3 border border-transparent rounded-lg shadow-sm text-base font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors",children:[e.jsx(ye,{className:"mr-2 -ml-1"}),"Geri Dön"]}),e.jsx(ee,{to:"/",className:"block mt-4 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white",children:"Ana Sayfaya Dön"})]})})})},Xt={1:{id:1,title:"TikTok Algoritmasını Anlamak",description:"Bu eğitimde TikTok algoritmasının nasıl çalıştığını ve içeriklerinizin daha fazla kişiye ulaşması için neler yapabileceğinizi öğreneceksiniz.",category:"Genel",image:"course1.jpg",icon:"chart-line",featured:!0,created_by:1,created_at:"2023-05-01 12:00:00",updated_at:"2023-05-01 12:00:00",status:"active",content:"<h2>TikTok Algoritmasını Anlamak</h2><p>Bu eğitimde TikTok algoritmasının nasıl çalıştığını ve içeriklerinizin daha fazla kişiye ulaşması için neler yapabileceğinizi öğreneceksiniz.</p><h3>Algoritma Temelleri</h3><p>TikTok algoritması, kullanıcıların etkileşimlerine dayalı olarak çalışan karmaşık bir sistemdir. Algoritma, kullanıcıların izleme alışkanlıklarını, beğenilerini, yorumlarını ve paylaşımlarını analiz ederek kişiselleştirilmiş bir içerik akışı oluşturur.</p><p>Algoritmanın temel çalışma prensipleri şunlardır:</p><ul><li><strong>İçerik Analizi:</strong> Algoritma, videoların içeriğini, kullanılan müzikleri, hashtag'leri ve açıklamaları analiz eder.</li><li><strong>Kullanıcı Etkileşimi:</strong> Kullanıcıların hangi videoları ne kadar süreyle izlediği, beğendiği, yorum yaptığı ve paylaştığı gibi etkileşimler önemlidir.</li><li><strong>Cihaz ve Hesap Ayarları:</strong> Kullanıcının cihaz türü, dil ayarları ve konum bilgisi gibi faktörler de algoritmanın kararlarını etkiler.</li></ul>"},2:{id:2,title:"Viral İçerik Oluşturma Teknikleri",description:"Viral olma potansiyeli taşıyan içerikler oluşturmanın püf noktaları ve teknikler.",category:"İçerik",image:"course2.jpg",icon:"fire",featured:!0,created_by:1,created_at:"2023-05-15 12:00:00",updated_at:"2023-05-15 12:00:00",status:"active",content:"<h2>Viral İçerik Oluşturma Teknikleri</h2><p>Viral olma potansiyeli taşıyan içerikler oluşturmanın püf noktaları ve teknikler.</p><h3>Viral İçeriğin Temelleri</h3><p>Viral içerik, kısa sürede geniş kitlelere ulaşan ve hızla yayılan içerik türüdür. Viral içerikler genellikle duygusal tepki uyandıran, ilgi çekici ve paylaşılabilir özelliklere sahiptir.</p>"},3:{id:3,title:"TikTok İçin Video Düzenleme",description:"Profesyonel görünümlü TikTok videoları oluşturmak için düzenleme teknikleri.",category:"Teknik",image:"course3.jpg",icon:"video",featured:!1,created_by:1,created_at:"2023-06-10 12:00:00",updated_at:"2023-06-10 12:00:00",status:"active",content:"<h2>TikTok İçin Video Düzenleme</h2><p>Profesyonel görünümlü TikTok videoları oluşturmak için düzenleme teknikleri.</p><h3>Temel Video Düzenleme</h3><p>Başarılı TikTok videoları oluşturmak için temel düzenleme becerilerine sahip olmak önemlidir. Bu bölümde, video kesme, birleştirme, ses ekleme ve efekt uygulama gibi temel düzenleme tekniklerini öğreneceksiniz.</p>"}},Bi=()=>{const{id:t}=Qt(),a=Number(t),r=be(),[s,i]=o.useState(null),[l,n]=o.useState(!0),[u,g]=o.useState(null),[h,x]=o.useState(null),[v,E]=o.useState(null),{isMobile:R,isSidebarOpen:N}=o.useContext(ue);o.useEffect(()=>{(async()=>{n(!0),g(null);try{console.log("Kurs ID:",a);const m=await D.get(`/backend/api/course_detail.php?id=${a}`);if(console.log("API yanıtı:",m.data),m.data.success&&m.data.data)console.log("Kurs verisi başarıyla alındı:",m.data.data),i(m.data.data),m.data.navigation&&(x(m.data.navigation.prev_course_id),E(m.data.navigation.next_course_id));else{console.error("API başarısız yanıt döndü:",m.data),g("Kurs verileri alınamadı: "+(m.data.message||"Bilinmeyen hata")),console.log("Fallback veri kullanılıyor, courseId:",a);const k=Xt[a];k?(console.log("Fallback kurs verisi bulundu:",k),i(k)):console.error("Fallback kurs verisi bulunamadı")}}catch(m){console.error("Kurs verileri çekilirken hata oluştu:",m);const k=m instanceof Error?m.message:"Bilinmeyen hata";g("Kurs verileri çekilirken bir hata oluştu: "+k),console.log("Fallback veri kullanılıyor, courseId:",a);const T=Xt[a];T?(console.log("Fallback kurs verisi bulundu:",T),i(T)):console.error("Fallback kurs verisi bulunamadı")}finally{n(!1)}})()},[a]);const z=()=>{h!==null&&r(`/dashboard/courses/${h}`)},b=()=>{v!==null&&r(`/dashboard/courses/${v}`)};if(l)return e.jsxs("div",{className:"p-8 flex flex-col items-center justify-center min-h-[60vh]",children:[e.jsx("div",{className:"w-24 h-24 mx-auto bg-primary-50 dark:bg-primary-900/20 text-primary-500 dark:text-primary-400 rounded-full flex items-center justify-center mb-6 border-2 border-primary-100 dark:border-primary-800 animate-pulse",children:e.jsx("svg",{className:"w-12 h-12",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"})})}),e.jsx("h3",{className:"text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-3",children:"Eğitim Yükleniyor"}),e.jsx("p",{className:"text-base text-gray-500 dark:text-gray-400 max-w-md text-center mb-8",children:"Eğitim içeriği yükleniyor, lütfen bekleyin..."})]});if(!s||u)return e.jsxs("div",{className:"p-8 flex flex-col items-center justify-center min-h-[60vh]",children:[e.jsx("div",{className:"w-24 h-24 mx-auto bg-primary-50 dark:bg-primary-900/20 rounded-full flex items-center justify-center mb-6 border-2 border-primary-100 dark:border-primary-800",children:e.jsx("svg",{className:"w-12 h-12 text-gray-400 dark:text-gray-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsx("h3",{className:"text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-3",children:"Eğitim Bulunamadı"}),e.jsx("p",{className:"text-base text-gray-500 dark:text-gray-400 max-w-md text-center mb-8",children:u||"Aradığınız eğitim içeriği mevcut değil veya kaldırılmış olabilir. Lütfen tüm eğitimler sayfasına dönüp başka bir eğitim seçin."}),e.jsxs(ee,{to:"/dashboard/courses",className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-[#FF3E71] hover:bg-[#FF5F87] dark:bg-primary-700 dark:hover:bg-primary-800 focus:outline-none transition-colors",children:[e.jsx(ye,{className:"mr-2"}),"Tüm Eğitimlere Dön"]})]});const w=()=>R?"100%":N?"calc(100vw - 280px - 30px)":"calc(100vw - 78px - 30px)";return e.jsxs("div",{className:"container",style:{maxWidth:w(),overflowX:"hidden"},children:[e.jsx("div",{className:"bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden mb-6",children:e.jsxs("div",{className:"p-8 border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-primary-50 to-white dark:from-[#16151c] dark:to-[#16151c]",children:[e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-800 dark:text-white mb-4",children:s.title}),e.jsxs("div",{className:"flex flex-wrap items-center gap-3 mb-4",children:[s.category&&e.jsx("div",{className:"px-3 py-1.5 bg-primary-100 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400 rounded-full text-xs font-medium border border-primary-200 dark:border-primary-800",children:s.category}),s.featured&&e.jsx("div",{className:"px-3 py-1.5 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-xs font-medium",children:"Öne Çıkan"})]}),e.jsx("p",{className:"text-gray-700 dark:text-gray-300 text-base sm:text-lg",children:s.description})]})}),e.jsx("div",{className:"bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden",children:e.jsx("div",{className:"p-8",children:s.content?e.jsx("div",{className:"prose prose-lg dark:prose-invert max-w-none prose-headings:font-bold prose-headings:text-[#FF3E71] dark:prose-headings:text-[#FF5F87] prose-a:text-[#FF3E71] dark:prose-a:text-[#FF5F87] prose-img:rounded-xl prose-img:shadow-md",children:e.jsx("div",{dangerouslySetInnerHTML:{__html:s.content}})}):e.jsx("div",{className:"prose prose-base dark:prose-invert max-w-none prose-headings:font-bold prose-headings:text-[#FF3E71] dark:prose-headings:text-[#FF5F87] prose-a:text-[#FF3E71] dark:prose-a:text-[#FF5F87] prose-img:rounded-xl prose-img:shadow-md",children:e.jsx("p",{className:"text-gray-500 dark:text-gray-400 italic text-center py-10 text-lg",children:"Bu eğitim için henüz içerik eklenmemiş."})})})}),e.jsx("div",{className:"bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden mt-6 p-6",children:e.jsxs("div",{className:"flex flex-col sm:flex-row items-center justify-between gap-4",children:[e.jsxs(ee,{to:"/dashboard/courses",className:"inline-flex items-center px-5 py-3 rounded-lg text-sm font-medium bg-white dark:bg-[#16151c] text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 border border-gray-200 dark:border-gray-700 w-full sm:w-auto justify-center order-3 sm:order-1",children:[e.jsx(ye,{className:"mr-2",size:14}),e.jsx("span",{children:"Tüm Eğitimlere Dön"})]}),e.jsxs("div",{className:"flex flex-row gap-4 w-full sm:w-auto order-1 sm:order-2 sm:ml-auto",children:[e.jsxs(ee,{to:h!==null?`/dashboard/courses/${h}`:"#",onClick:c=>{h===null?c.preventDefault():z()},className:`flex items-center px-5 py-3 rounded-lg text-sm font-medium transition-colors justify-center ${h===null?"bg-gray-100 text-gray-400 dark:bg-gray-800 dark:text-gray-500 cursor-not-allowed":"bg-[#FF3E71] text-white hover:bg-[#FF5F87]"}`,"aria-disabled":h===null,children:[e.jsx(ea,{className:"mr-2",size:14}),e.jsx("span",{children:"Önceki Eğitim"})]}),e.jsxs(ee,{to:v!==null?`/dashboard/courses/${v}`:"#",onClick:c=>{v===null?c.preventDefault():b()},className:`flex items-center px-5 py-3 rounded-lg text-sm font-medium transition-colors justify-center ${v===null?"bg-gray-100 text-gray-400 dark:bg-gray-800 dark:text-gray-500 cursor-not-allowed":"bg-[#FF3E71] text-white hover:bg-[#FF5F87]"}`,"aria-disabled":v===null,children:[e.jsx("span",{children:"Sonraki Eğitim"}),e.jsx(ta,{className:"ml-2",size:14})]})]})]})})]})},Di=({onLogout:t})=>e.jsx(gi,{children:e.jsx(Ws,{children:e.jsx(ki,{onLogout:t})})});function Pi(){const t=be();return e.jsx("div",{className:"App",children:e.jsx(Gs,{children:e.jsxs($a,{children:[e.jsx(ae,{path:"/",element:e.jsx(Yt,{})}),e.jsx(ae,{path:"/home",element:e.jsx(Yt,{})}),e.jsx(ae,{path:"/terms",element:e.jsx(Ai,{})}),e.jsx(ae,{path:"/privacy",element:e.jsx(Mi,{})}),e.jsxs(ae,{path:"/dashboard",element:e.jsx(Di,{onLogout:()=>t("/")}),children:[e.jsx(ae,{index:!0,element:null})," ",e.jsx(ae,{path:"announcements",element:e.jsx(qt,{})}),e.jsx(ae,{path:"announcements/:id",element:e.jsx(qt,{})}),e.jsx(ae,{path:"feed",element:e.jsx(vi,{})}),e.jsx(ae,{path:"courses",element:e.jsx(ji,{})}),e.jsx(ae,{path:"courses/:id",element:e.jsx(Bi,{})}),e.jsx(ae,{path:"events",element:e.jsx(wi,{})}),e.jsx(ae,{path:"requests",element:e.jsx(_i,{})}),e.jsx(ae,{path:"notifications",element:e.jsx(Ti,{})}),e.jsx(ae,{path:"profile",element:e.jsx(Li,{})})]}),e.jsx(ae,{path:"*",element:e.jsx(Ri,{})})]})})})}{const t=console.error;console.error=(...a)=>{const r=String(a[0]);r.includes("browser is not defined")||r.includes("checkPageManual")||r.includes("overlays.js")||r.includes("content.js")||r.includes("ReferenceError: browser is not defined")||t(...a)},window.addEventListener("error",a=>{const r=a.message||"";if(r.includes("browser is not defined")||r.includes("checkPageManual")||r.includes("overlays.js")||r.includes("content.js"))return a.preventDefault(),!1}),window.addEventListener("unhandledrejection",a=>{const r=String(a.reason);if(r.includes("browser is not defined")||r.includes("checkPageManual")||r.includes("overlays.js")||r.includes("content.js"))return a.preventDefault(),!1})}nt.createRoot(document.getElementById("root")).render(e.jsx(Oa.StrictMode,{children:e.jsx(Ha,{children:e.jsx(Pi,{})})}));
