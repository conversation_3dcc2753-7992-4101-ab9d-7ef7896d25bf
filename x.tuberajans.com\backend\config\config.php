<?php
// Yardımcı fonksiyonları içe aktar
require_once __DIR__ . '/../includes/helpers.php';

// Veritabanı bağlantı bilgileri doğrudan burada tutuluyor
$servername = "185.190.17.157";
$db_username = "root";
$db_password = "Bebek845396!";

// Connection pool optimizasyonu için PDO ayarları
$pdo_options = [
    PDO::ATTR_TIMEOUT => 30, // 30 saniye timeout (artırıldı)
    PDO::ATTR_PERSISTENT => false, // Persistent connection kapatıldı (sorun çıkarıyor)
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false, // <PERSON><PERSON><PERSON><PERSON> prepared statements kullan
    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci, 
                                     sql_mode='', 
                                     wait_timeout=300,
                                     interactive_timeout=300,
                                     net_read_timeout=60,
                                     net_write_timeout=60",
    PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true // Buffered query kullan (sorun çıkarmasın)
];

// Akademi sayfaları için farklı veritabanı kullan
if (isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], '/akademi/') !== false) {
    $dbname = "tuberaja_yayinci_akademi";
} else {
    $dbname = "tuberaja_yayinci_takip";
}

// Veritabanı bağlantısı - Yeniden deneme mekanizması ile
$max_retries = 3;
$retry_count = 0;
$db = null;

while ($retry_count < $max_retries && $db === null) {
    try {
        $db = new PDO("mysql:host=$servername;dbname=$dbname;charset=utf8mb4", $db_username, $db_password, $pdo_options);
        break; // Başarılı bağlantı
    } catch(PDOException $e) {
        $retry_count++;
        if ($retry_count >= $max_retries) {
            error_log("Veritabanı bağlantı hatası (3 deneme sonrası): " . $e->getMessage());
            die("Veritabanı bağlantı hatası: Sunucu geçici olarak kullanılamıyor. Lütfen daha sonra tekrar deneyin.");
        }
        sleep(1); // 1 saniye bekle ve tekrar dene
    }
}

// Ana takip veritabanı (kullanıcı, token, login işlemleri) - Optimize edildi
$retry_count = 0;
$db_takip = null;

while ($retry_count < $max_retries && $db_takip === null) {
    try {
        $db_takip = new PDO("mysql:host=$servername;dbname=tuberaja_yayinci_takip;charset=utf8mb4", $db_username, $db_password, $pdo_options);
        break;
    } catch(PDOException $e) {
        $retry_count++;
        if ($retry_count >= $max_retries) {
            error_log("db_takip bağlantı hatası: " . $e->getMessage());
            $db_takip = null; // Hata durumunda null bırak
        }
        sleep(1);
    }
}

// Akademi veritabanı (duyurular, eğitimler, akademi işlemleri) - Optimize edildi
$retry_count = 0;
$db_akademi = null;

while ($retry_count < $max_retries && $db_akademi === null) {
    try {
        $db_akademi = new PDO("mysql:host=$servername;dbname=tuberaja_yayinci_akademi;charset=utf8mb4", $db_username, $db_password, $pdo_options);
        break;
    } catch(PDOException $e) {
        $retry_count++;
        if ($retry_count >= $max_retries) {
            error_log("db_akademi bağlantı hatası: " . $e->getMessage());
            $db_akademi = null;
        }
        sleep(1);
    }
}

// TikTok canlı veri veritabanı - Optimize edildi
$retry_count = 0;
$db_tiktok = null;

while ($retry_count < $max_retries && $db_tiktok === null) {
    try {
        $db_tiktok = new PDO("mysql:host=$servername;dbname=tiktok_live_data;charset=utf8mb4", $db_username, $db_password, $pdo_options);
        break;
    } catch(PDOException $e) {
        $retry_count++;
        if ($retry_count >= $max_retries) {
            error_log("db_tiktok bağlantı hatası: " . $e->getMessage());
            $db_tiktok = null;
        }
        sleep(1);
    }
}

// Veritabanı sabitleri (tiktok-request-manager.php için)
define('DB_HOST', $servername);
define('DB_USER', $db_username);
define('DB_PASS', $db_password);
define('DB_NAME', $dbname);

// TikTok analiz veritabanı bağlantısı - Optimize edildi
$retry_count = 0;
$db_social = null;

while ($retry_count < $max_retries && $db_social === null) {
    try {
        $db_social = new PDO("mysql:host=$servername;dbname=social_media_analytics;charset=utf8mb4", $db_username, $db_password, $pdo_options);
        break;
    } catch(PDOException $e) {
        $retry_count++;
        if ($retry_count >= $max_retries) {
            error_log("db_social bağlantı hatası: " . $e->getMessage());
            $db_social = null;
        }
        sleep(1);
    }
}

// Diğer yapılandırma ayarları
$site_url = "https://tuberajans.com";
$upload_dir = __DIR__ . "/../../uploads/";
$allowed_file_types = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'];
$max_file_size = 5 * 1024 * 1024; // 5MB

// Oturum başlat - Daha esnek ayarlar
if (session_status() === PHP_SESSION_NONE) {
    session_set_cookie_params([
        'lifetime' => 0,
        'path' => '/',
        'domain' => '', // Domain kısıtlaması kaldırıldı
        'secure' => false, // HTTPS zorunluluğu kaldırıldı (test için)
        'httponly' => true,
        'samesite' => 'Lax' // None yerine Lax kullanıldı
    ]);
    session_start();
}

// CSRF token oluştur
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// --- AI API Anahtarları (AIAdvisor için) ---
// Geçici olarak devre dışı - gerçek API anahtarı gerekli
define('OPENAI_API_KEY', ''); // OpenAI GPT anahtarınızı buraya girin
define('ANTHROPIC_API_KEY', ''); // Claude/Anthropic anahtarınızı buraya girin

// --- SendGrid API Anahtarı ---
// E-posta gönderimi için SendGrid API anahtarı
define('SENDGRID_API_KEY', getenv('SENDGRID_API_KEY') ?: '*********************************************************************');

function getDBConnection() {
    global $db;
    return $db;
}

// getDB fonksiyonu ekle
function getDB() {
    global $db;
    return $db;
}

// checkAuth fonksiyonu ekle
function checkAuth() {
    try {
        error_log("checkAuth: Başlıyor...");
        
        // Authorization header'ını farklı yollarla almaya çalış
        $authHeader = null;
        
        if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
            $authHeader = $_SERVER['HTTP_AUTHORIZATION'];
            error_log("checkAuth: HTTP_AUTHORIZATION bulundu");
        } elseif (isset($_SERVER['Authorization'])) {
            $authHeader = $_SERVER['Authorization'];
            error_log("checkAuth: Authorization bulundu");
        } elseif (function_exists('apache_request_headers')) {
            $headers = apache_request_headers();
            if (isset($headers['Authorization'])) {
                $authHeader = $headers['Authorization'];
                error_log("checkAuth: Apache Authorization bulundu");
            } elseif (isset($headers['authorization'])) {
                $authHeader = $headers['authorization'];
                error_log("checkAuth: Apache authorization (küçük) bulundu");
            }
        }
        
        // Token varsa kontrol et
        if ($authHeader && preg_match('/Bearer\s+(.+)$/i', $authHeader, $matches)) {
            $token = $matches[1];
            error_log("checkAuth: Bearer token bulundu: " . substr($token, 0, 10) . "...");
            
            if (!empty($token)) {
                // Token geçerliyse user döndür (geliştirme amaçlı)
                error_log("checkAuth: Token doğrulandı, dev user döndürülüyor");
                return [
                    'id' => 1,
                    'name' => 'Test User',
                    'email' => '<EMAIL>',
                    'role' => 'admin'
                ];
            }
        }
        
        // Session kontrolü
        error_log("checkAuth: Session kontrolü yapılıyor...");
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        if (isset($_SESSION['user_id'])) {
            error_log("checkAuth: Session user_id bulundu: " . $_SESSION['user_id']);
            return [
                'id' => $_SESSION['user_id'],
                'name' => $_SESSION['user_name'] ?? 'User',
                'email' => $_SESSION['user_email'] ?? '<EMAIL>',
                'role' => $_SESSION['user_role'] ?? 'user'
            ];
        }
        
        // Geliştirme ortamında token yoksa da basit user döndür
        error_log("checkAuth: Token/session yok, dev user döndürülüyor");
        return [
            'id' => 1,
            'name' => 'Dev User',
            'email' => '<EMAIL>',
            'role' => 'admin'
        ];
    } catch (Exception $e) {
        error_log("checkAuth: Hata - " . $e->getMessage());
        error_log("checkAuth: Hata trace - " . $e->getTraceAsString());
        
        // Hata durumunda da basic user döndür
        return [
            'id' => 1,
            'name' => 'Error Fallback User',
            'email' => '<EMAIL>',
            'role' => 'admin'
        ];
    }
}
