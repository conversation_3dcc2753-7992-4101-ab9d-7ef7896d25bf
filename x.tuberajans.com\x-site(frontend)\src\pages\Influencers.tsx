import React, { useState, useRef, useEffect, useMemo } from 'react';
import {
  Search,
  UserPlus,
  Trash2,
  Save,
  X,
  Check,
  FileSpreadsheet,
  RefreshCw,
  Mail,
  Send,
  Filter,
  Eye,
  EyeOff,
  ArrowLeft,
  ArrowRight,
  MessageSquare
} from 'lucide-react';
import { Popover, Button, message, Tooltip, Table, Input, Space, Checkbox, Divider, Modal, Form, Select, InputNumber, Upload, Alert, Dropdown, Menu, Progress } from 'antd';
import {
  UploadOutlined,
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UsergroupAddOutlined,
  DownloadOutlined,
  ReloadOutlined,
  WhatsAppOutlined,
  MailOutlined,
  EllipsisOutlined,
  CloudDownloadOutlined,
  UserOutlined
} from '@ant-design/icons';
import * as XLSX from 'xlsx';
import {
  getInfluencers,
  createInfluencer,
  updateInfluencer,
  deleteInfluencer,
  Influencer,
  sendEmailToInfluencers
} from '../lib/api';
import { API_CONFIG } from '../config';
import { colors, PageTitle, RefreshButton } from '../components/ThemeStyles';
import { useTheme } from '../contexts/ThemeContext';
import { toast, Toaster } from 'react-hot-toast';
import { useAuth } from '../contexts/AuthContext';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import SingleEmailModal from '../components/SingleEmailModal';
import { HTML5Backend } from 'react-dnd-html5-backend';
import type { ColumnsType } from 'antd/es/table';
import { Avatar } from 'antd';
import { SiTiktok as TikTokIcon } from 'react-icons/si';

// Column drag-drop support for Table
interface DraggableHeaderCellProps {
  index: number;
  title: string;
  moveColumn: (from: number, to: number) => void;
}

const DraggableHeaderCell: React.FC<DraggableHeaderCellProps> = ({ index, title, moveColumn }) => {
  const ref = React.useRef<HTMLTableCellElement>(null);

  const [{ isDragging }, drag] = useDrag({
    type: 'column',
    item: { index },
    collect: monitor => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [, drop] = useDrop({
    accept: 'column',
    hover: (item: { index: number }) => {
      if (item.index !== index) {
        moveColumn(item.index, index);
        item.index = index;
      }
    },
  });

  drag(drop(ref));

  return (
    <th
      ref={ref}
      style={{
        cursor: 'move',
        opacity: isDragging ? 0.5 : 1,
        padding: '12px 8px',
        fontWeight: 'bold',
        fontSize: '13px',
        textTransform: 'uppercase',
        borderBottom: '1px solid #eee',
        color: '#555'
      }}
    >
      {title}
    </th>
  );
};

interface InfluencersProps {
  hideHeader?: boolean;
  isMobileView?: boolean;
  onDataChange?: (count: number) => void;
  initialCount?: number;
}

// File'ı base64 string'e çeviren yardımcı fonksiyon
function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  });
}

// Progress bar için global state
const getInitialProgress = () => {
  const saved = localStorage.getItem('profileImageProgress');
  if (saved) {
    try {
      return JSON.parse(saved);
    } catch (error) {
      console.error("profileImageProgress parse hatası:", error);
      localStorage.removeItem('profileImageProgress');
      return { total: 0, current: 0, success: 0, fail: 0, running: false };
    }
  }
  return { total: 0, current: 0, success: 0, fail: 0, running: false };
};

export default function Influencers({ hideHeader = false, isMobileView = false, onDataChange, initialCount }: InfluencersProps) {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [emailModalVisible, setEmailModalVisible] = useState(false);

  const { darkMode } = useTheme();
  const [influencers, setInfluencers] = useState<Influencer[]>(initialCount ? Array(initialCount).fill({}) : []);
  const [tableData, setTableData] = useState<Influencer[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [editingCell, setEditingCell] = useState<{id:number, field:keyof Influencer} | null>(null);
  const [editValue, setEditValue] = useState<any>('');
  const [loading, setLoading] = useState(initialCount ? true : false);
  const [filterVisible, setFilterVisible] = useState(false);
  const [filterValues, setFilterValues] = useState<{
    category: string[];
    status: string[];
    location: string[];
    followers?: string[];
    hasTelefon?: boolean;
  }>({
    category: [],
    status: [],
    location: [],
    followers: [],
    hasTelefon: false
  });

  const [minFollowersFilter, setMinFollowersFilter] = useState<number | null>(null);
  const [maxFollowersFilter, setMaxFollowersFilter] = useState<number | null>(null);

  const [whatsappModalVisible, setWhatsappModalVisible] = useState(false);
  const [whatsappForm] = Form.useForm();
  const [whatsappTemplates, setWhatsappTemplates] = useState<{name:string;content:string}[]>([]);
  const [selectedWhatsappTemplateName, setSelectedWhatsappTemplateName] = useState<string | undefined>(undefined);
  const [newWhatsappTemplateModalVisible, setNewWhatsappTemplateModalVisible] = useState(false);
  const [editWhatsappTemplateModalVisible, setEditWhatsappTemplateModalVisible] = useState(false);
  const [currentTemplateForEdit, setCurrentTemplateForEdit] = useState<{name:string;content:string} | null>(null);
  const [whatsappTemplateForm] = Form.useForm();

  const [selectedFollowerRanges, setSelectedFollowerRanges] = useState<string[]>([]);
  // 1. Sayı state'i ekle
  const [influencerCount, setInfluencerCount] = useState<number>(initialCount || 0);
  // Yeni Influencer ekleme için state'ler
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [addForm] = Form.useForm();
  const [progress, setProgress] = useState(getInitialProgress());

  const handleAddInfluencer = async () => {
    try {
      const values = await addForm.validateFields();
      await createInfluencer(values);
      message.success('Influencer başarıyla eklendi');
      setAddModalVisible(false);
      addForm.resetFields();
      fetchInfluencers();
    } catch (err) {
      message.error('Influencer eklenirken hata oluştu');
    }
  };

  // Column configuration
  const BASE_URL = "https://x.tuberajans.com";
  const columns: ColumnsType<Influencer> = [
    {
      title: ' ',
      dataIndex: 'profile_image',
      key: 'profile_image',
      width: 48,
      render: (_: any, record: Influencer) => {
        // Profil resmi URL'ini oluştur
        const imageUrl = record.profile_image
          ? `${BASE_URL}${record.profile_image}`
          : `${BASE_URL}/uploads/profile_images/${record.username}.jpg`;

        return (
          <Avatar
            src={imageUrl}
            icon={<UserOutlined />}
            size={36}
            style={{ backgroundColor: '#f0f0f0', marginRight: 8 }}
            alt={record.username}
            onError={() => {
              // Resim yüklenemezse sadece icon göster
              console.log(`Profil resmi yüklenemedi: ${record.username}`);
              return false;
            }}
          />
        );
      },
      fixed: isMobileView ? undefined : 'left',
    },
    {
      title: 'Kullanıcı Adı',
      dataIndex: 'username',
      key: 'username',
      sorter: (a, b) => (a.username || '').localeCompare(b.username || ''),
      width: 150,
      render: (text, record) => renderCell(text, record, 'username')
    },
    {
      title: 'E-posta',
      dataIndex: 'email',
      key: 'email',
      sorter: (a, b) => (a.email || '').localeCompare(b.email || ''),
      width: 200,
      render: (text, record) => renderCell(text, record, 'email')
    },
    {
      title: 'Takipçi Sayısı',
      dataIndex: 'followers',
      key: 'followers',
      defaultSortOrder: 'descend',
      sorter: (a, b) => (Number(a.followers) || 0) - (Number(b.followers) || 0),
      width: 150,
      render: (text, record) => renderCell(text || '-', record, 'followers')
    },
    {
      title: 'Telefon',
      dataIndex: 'telefon',
      key: 'telefon',
      width: 150,
      render: (text, record) => renderCell(text || '-', record, 'telefon')
    },
    {
      title: 'Profil Linki',
      dataIndex: 'sosyal_medya',
      key: 'sosyal_medya',
      width: 180,
      render: (text, record) => {
        if (editingCell && editingCell.id === record.id && editingCell.field === 'sosyal_medya') {
          return (
            <Input
              value={editValue}
              onChange={e => setEditValue(e.target.value)}
              onBlur={() => saveEdit(record.id!, 'sosyal_medya')}
              onPressEnter={() => saveEdit(record.id!, 'sosyal_medya')}
              autoFocus
            />
          );
        }
        // TikTok profil linki oluştur
        const tiktokUrl = record.username ? `https://www.tiktok.com/@${record.username}` : (text?.startsWith('http') ? text : `https://${text}`);
        return (
          <div onDoubleClick={() => startEdit(record.id!, 'sosyal_medya', record.sosyal_medya)}>
            {record.username ? (
              <Tooltip title="TikTok Profilini Aç">
                <a
                  href={tiktokUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="tiktok-profile-link"
                  onClick={e => e.stopPropagation()}
                  style={{ display: 'inline-flex', alignItems: 'center', justifyContent: 'center' }}
                >
                  {React.createElement(TikTokIcon as any, { size: 19, color: '#000' })}
                </a>
              </Tooltip>
            ) : (
              '-'
            )}
          </div>
        );
      }
    },
    {
      title: 'İşlemler',
      key: 'action',
      fixed: 'right',
      width: 150,
      align: 'center',
      render: (_, record) => (
        <div className="flex justify-center items-center space-x-3">
          <Tooltip title="WhatsApp'a Mesaj Gönder">
            <button
              onClick={() => handleWhatsAppMessage(record)}
              className="text-green-500 hover:text-green-700 flex items-center justify-center transition-colors duration-200"
              style={{ background: 'none', border: 'none', cursor: 'pointer', padding: '4px', width: '32px', height: '32px', borderRadius: '4px' }}
            >
              <WhatsAppOutlined style={{ fontSize: '19px' }} />
            </button>
          </Tooltip>
          {record.email && (
            <Tooltip title="E-posta Gönder">
              <button
                onClick={() => handleEmailMessage(record)}
                className="text-blue-500 hover:text-blue-700 flex items-center justify-center transition-colors duration-200"
                style={{ background: 'none', border: 'none', cursor: 'pointer', padding: '4px', width: '32px', height: '32px', borderRadius: '4px' }}
              >
                <MailOutlined style={{ fontSize: '19px' }} />
              </button>
            </Tooltip>
          )}
          <Tooltip title="Sil">
            <button
              onClick={() => handleDelete(record.id)}
              className="text-red-500 hover:text-red-700 flex items-center justify-center transition-colors duration-200"
              style={{ background: 'none', border: 'none', cursor: 'pointer', padding: '4px', width: '32px', height: '32px', borderRadius: '4px' }}
            >
              <Trash2 size={19} />
            </button>
          </Tooltip>
        </div>
      )
    }
  ];

  // Move column function
  const moveColumn = (from: number, to: number) => {
    // Bu fonksiyon Table bileşenindeki sütunlar ile entegre çalışacak
    console.log(`Sütun taşınıyor: ${from} => ${to}`);
    // Not: antd Table bileşeninde sütun sırasını değiştirmek için
    // normal düzenleme yapmak yetiyor; sütunları DOM'da sürükleyebiliyoruz
  };

  // Helper function to render editable cells
  const renderCell = (text: any, record: Influencer, field: keyof Influencer) => {
    if (editingCell && editingCell.id === record.id && editingCell.field === field) {
      return (
        <Input
          value={editValue}
          onChange={e => setEditValue(e.target.value)}
          onBlur={() => saveEdit(record.id!, field)}
          onPressEnter={() => saveEdit(record.id!, field)}
          autoFocus
        />
      );
    }
    return (
      <div onDoubleClick={() => startEdit(record.id!, field, record[field])}>
        {text}
      </div>
    );
  };

  // Start inline edit
  const startEdit = (id: number, field: keyof Influencer, value: any) => {
    setEditingCell({ id, field });
    setEditValue(value);
  };

  // Save inline edit
  const saveEdit = async (id: number, field: keyof Influencer) => {
    if (!editingCell) return;
    const prevValue = tableData.find(item => item.id === id)?.[field];
    try {
      const apiResult = await updateInfluencer(id, { [field]: editValue });
      if (!apiResult.success) throw new Error(apiResult.error || 'Güncelleme başarısız');
      setTableData(prev => prev.map(item => item.id === id ? { ...item, [field]: editValue } : item));
      message.success('Güncellendi');
    } catch (err: any) {
      setTableData(prev => prev.map(item => item.id === id ? { ...item, [field]: prevValue } : item));
      message.error('Güncelleme hatası: ' + (err.message || ''));
    } finally {
      setEditingCell(null);
    }
  };

  // Delete influencer
  const handleDelete = async (id?: number) => {
    if (!id) return;
    try {
      await deleteInfluencer(id);
      setTableData(prev => prev.filter(item => item.id !== id));
      message.success('Silindi');
    } catch {
      message.error('Silme hatası');
    }
  };

  // Fetch API data
  const fetchInfluencers = async () => {
    setLoading(true);
    try {
      console.log('Influencers verisi çekiliyor...');
      const res = await getInfluencers();
      console.log('API Response:', res);

      // Güvenli veri kontrolü
      if (!res || typeof res !== 'object') {
        console.error('Geçersiz API yanıtı:', res);
        throw new Error('Geçersiz API yanıtı');
      }

      // success kontrolü
      if (res.success === false) {
        console.error('API hatası:', res.error || res.message);
        throw new Error(res.error || res.message || 'API hatası');
      }

      // data kontrolü
      const rawData = Array.isArray(res.data) ? (res.data as Influencer[]) : [];
      console.log('Ham veri:', rawData);

      // Veri filtreleme - null/undefined değerleri güvenli şekilde kontrol et
      const data = rawData.filter((item: Influencer) => {
        return item &&
               typeof item === 'object' &&
               item.username &&
               item.username.trim() !== '' &&
               item.email &&
               item.email.trim() !== '';
      });

      console.log('Filtrelenmiş veri:', data);

      setInfluencers(data);
      setTableData(data);
      setInfluencerCount(data.length);
      if (onDataChange) onDataChange(data.length);

    } catch (err) {
      console.error('Influencers veri çekme hatası:', err);
      setInfluencers([]);
      setTableData([]);
      setInfluencerCount(0);
      if (onDataChange) onDataChange(0);

      // Kullanıcıya hata mesajı göster
      message.error('Influencer verileri yüklenirken hata oluştu: ' + (err instanceof Error ? err.message : 'Bilinmeyen hata'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchInfluencers();
  }, []);



  // Load whatsapp templates from localStorage
  useEffect(() => {
    const defaultWhatsappTemplates = [
      {
        name: 'Marka İş Birliği Şablonu',
        content: 'Merhaba {username}, yeni bir marka iş birliği fırsatımız var. Detaylar için dönüş yapabilir misiniz?'
      },
      {
        name: 'Marka Brief Şablonu',
        content: 'Merhaba {username}, size yeni bir marka briefingi göndermek istiyorum. Müsait olduğunuzda iletişime geçebilir miyiz?'
      }
    ];
    try {
      const saved = localStorage.getItem('whatsappTemplates');
      if (saved) {
        setWhatsappTemplates(JSON.parse(saved));
      } else {
        setWhatsappTemplates(defaultWhatsappTemplates);
        localStorage.setItem('whatsappTemplates', JSON.stringify(defaultWhatsappTemplates));
      }
    } catch (error) {
      console.error('Influencer WhatsApp şablonları yüklenirken hata:', error);
      setWhatsappTemplates(defaultWhatsappTemplates);
      localStorage.setItem('whatsappTemplates', JSON.stringify(defaultWhatsappTemplates));
    }
  }, []);



  // Add new WhatsApp template
  const addWhatsappTemplate = (vals: any) => {
    const newTemp = { name: vals.name, content: vals.content };
    const updated = [...whatsappTemplates, newTemp];
    setWhatsappTemplates(updated);
    localStorage.setItem('whatsappTemplates', JSON.stringify(updated));
    setNewWhatsappTemplateModalVisible(false);
    whatsappTemplateForm.resetFields();
    message.success('Şablon eklendi');
  };

  // Apply search and filters
  const filtered = tableData.filter((item: Influencer) => {
    // Arama filtresi
    const matchSearch = searchTerm
      ? (item.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
         item.email?.toLowerCase().includes(searchTerm.toLowerCase()))
      : true;

    // Takipçi sayısı filtresi
    let matchFollowers = true;
    if (filterValues.followers && filterValues.followers.length > 0) {
      matchFollowers = (filterValues.followers as string[]).some((range: string) => {
        const followers = Number(item.followers || 0);
        if (range === '5k-10k') return followers >= 5000 && followers <= 10000;
        if (range === '10k-50k') return followers >= 10000 && followers <= 50000;
        if (range === '50k-100k') return followers >= 50000 && followers <= 100000;
        if (range === '100k-500k') return followers >= 100000 && followers <= 500000;
        if (range === '500k+') return followers > 500000;
        return true;
      });
    }

    // Telefon numarası filtresi
    let matchTelefon = true;
    if (filterValues.hasTelefon) {
      matchTelefon = item.telefon ? true : false;
    }

    return matchSearch && matchFollowers && matchTelefon;
  });

  // Handle Excel file upload to server-side import endpoint
  const handleImportExcel = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    message.loading({ content: 'Excel dosyası yükleniyor...', key: 'import' });
    try {
      const formData = new FormData();
      formData.append('excelFile', file);
      // send file to API for server-side import
      const res = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.INFLUENCERS}`, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Authorization': localStorage.getItem('user') ? `Bearer ${JSON.parse(localStorage.getItem('user') as string)?.token}` : ''
        },
        body: formData
      });
      const data = await res.json();
      if (data.success) {
        message.success({ content: `${data.count || 0} influencer başarıyla içe aktarıldı.`, key: 'import' });
      } else {
        message.error({ content: data.message || data.error || 'İçe aktarım hatası', key: 'import' });
      }
      fetchInfluencers();
    } catch (err) {
      console.error('Dosya yükleme hatası:', err);
      message.error({ content: 'Dosya yükleme sırasında hata oluştu', key: 'import' });
    } finally {
      e.target.value = '';
    }
  };

  // Takipçi Sayılarını Güncelle fonksiyonu
  const updateFollowers = async () => {
    setLoading(true);
    try {
      const usernames = tableData.map(i => i.username).filter(Boolean);
      const token = localStorage.getItem('x_tuber_token');
      if (!token) {
        message.error('Oturum bilgileriniz bulunamadı. Lütfen tekrar giriş yapın.');
        setLoading(false);
        setTimeout(() => { window.location.href = '/login'; }, 1500);
        return;
      }
      const response = await fetch(`${API_CONFIG.X_SITE_BASE_URL}/update_followers.php`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ usernames })
      });
      const data = await response.json();
      if (data.success) {
        const successCount = data.results.filter((r: any) => r.success).length;
        const failCount = data.results.length - successCount;
        message.success(`Takipçi sayıları güncellendi! Başarılı: ${successCount}, Başarısız: ${failCount}`);
      } else {
        message.error(data.error || 'Takipçi sayıları güncellenemedi.');
      }
    } catch (err) {
      message.error('Takipçi sayıları güncellenirken hata oluştu.');
    } finally {
      setLoading(false);
      fetchInfluencers(); // Tabloyu güncelle
    }
  };



  const rowSelection = {
    selectedRowKeys,
    onChange: (keys: React.Key[]) => setSelectedRowKeys(keys),
  };

  // WhatsApp mesaj gönderme fonksiyonu
  const handleWhatsAppMessage = (record: Influencer) => {
    if (!record.telefon) {
      message.warning('Telefon numarası bulunamadı!');
      return;
    }
    let phone = record.telefon.toString().replace(/\s+/g, '');
    if (phone.startsWith('0')) {
      phone = phone.substring(1);
    }
    if (!phone.startsWith('+')) {
      phone = '+90' + phone;
    }
    const messageText = `Merhaba ${record.username},`;
    const whatsappUrl = `https://wa.me/${phone}?text=${encodeURIComponent(messageText)}`;
    window.open(whatsappUrl, '_blank');
    message.success('WhatsApp mesajı açılıyor...');
  };

  // Tekil e-posta modal state'leri
  const [singleEmailModalVisible, setSingleEmailModalVisible] = useState(false);
  const [selectedInfluencerForEmail, setSelectedInfluencerForEmail] = useState<Influencer | null>(null);

  // E-posta gönderme fonksiyonu
  const handleEmailMessage = (record: Influencer) => {
    if (!record.email) {
      message.error('Bu influencer\'ın e-posta adresi bulunamadı');
      return;
    }

    // Seçili influencer'ı set et ve modal'ı aç
    setSelectedInfluencerForEmail(record);
    setSingleEmailModalVisible(true);
  };

  // Toplu WhatsApp mesaj gönderme
  const sendBulkWhatsApp = async () => {
    try {
      const values = await whatsappForm.validateFields();

      // Seçilen influencerlar veya hiç seçim yapılmadıysa filtrelenmiş tüm influencerlar
      let recipients: Influencer[] = [];

      if (selectedRowKeys.length === 0) {
        message.info('Hiçbir influencer seçmediniz. Filtrelenmiş tüm influencerlar işleme dahil edilecek.');

        // Takipçi aralığı filtresini uygula
        let filteredByFollowers = filtered;

        // Eğer seçilmiş takipçi aralıkları varsa
        if (selectedFollowerRanges.length > 0) {
          filteredByFollowers = filtered.filter(i => {
            const followers = Number(i.followers || 0);

            // "Tümü" seçiliyse tüm influencerları dahil et
            if (selectedFollowerRanges.includes('all')) {
              return true;
            }

            return selectedFollowerRanges.some(range => {
              if (range === '10k_less') return followers < 10000;
              if (range === '10k_50k') return followers >= 10000 && followers <= 50000;
              if (range === '50k_100k') return followers >= 50000 && followers <= 100000;
              if (range === '100k_plus') return followers > 100000;
              return false;
            });
          });
        }

        recipients = filteredByFollowers.filter(i => i.telefon);
      } else {
        let selectedInfluencers = influencers.filter(i =>
          selectedRowKeys.includes(i.id as number) && i.telefon
        );

        // Eğer seçilmiş takipçi aralıkları varsa
        if (selectedFollowerRanges.length > 0) {
          selectedInfluencers = selectedInfluencers.filter(i => {
            const followers = Number(i.followers || 0);

            // "Tümü" seçiliyse tüm influencerları dahil et
            if (selectedFollowerRanges.includes('all')) {
              return true;
            }

            return selectedFollowerRanges.some(range => {
              if (range === '10k_less') return followers < 10000;
              if (range === '10k_50k') return followers >= 10000 && followers <= 50000;
              if (range === '50k_100k') return followers >= 50000 && followers <= 100000;
              if (range === '100k_plus') return followers > 100000;
              return false;
            });
          });
        }

        recipients = selectedInfluencers;
      }

      if (recipients.length === 0) {
        message.error('Geçerli telefon numarası olan influencer bulunamadı');
        return;
      }

      // İlk alıcıdan başlayarak sırayla WhatsApp mesajlarını aç
      recipients.forEach((recipient, index) => {
        // Her mesaj arasında kısa bir gecikme ekle (tarayıcı engellemesin diye)
        setTimeout(() => {
          if (!recipient.telefon) return;

          let phone = recipient.telefon.toString().replace(/\s+/g, '');
          if (phone.startsWith('0')) {
            phone = phone.substring(1);
          }
          if (!phone.startsWith('+')) {
            phone = '+90' + phone;
          }

          const messageText = values.content.replace('{username}', recipient.username || '');
          const whatsappUrl = `https://wa.me/${phone}?text=${encodeURIComponent(messageText)}`;
          window.open(whatsappUrl, '_blank');
        }, index * 300); // Her mesaj arasında 300ms gecikme
      });

      message.success('WhatsApp mesajları açılıyor...');
      setWhatsappModalVisible(false);
      whatsappForm.resetFields();
      setSelectedFollowerRanges([]);
    } catch (err) {
      message.error('WhatsApp mesajları gönderilemedi');
    }
  };

  // Update WhatsApp template
  const updateWhatsappTemplate = (vals: any) => {
    if (!currentTemplateForEdit) return;

    const updated = whatsappTemplates.map(t =>
      t.name === currentTemplateForEdit.name ? { name: vals.name, content: vals.content } : t
    );

    setWhatsappTemplates(updated);
    localStorage.setItem('whatsappTemplates', JSON.stringify(updated));
    setEditWhatsappTemplateModalVisible(false);
    setCurrentTemplateForEdit(null);
    whatsappTemplateForm.resetFields();
    message.success('Şablon güncellendi');
  };

  // Delete WhatsApp template
  const deleteWhatsappTemplate = (templateName: string) => {
    const updated = whatsappTemplates.filter(t => t.name !== templateName);
    setWhatsappTemplates(updated);
    localStorage.setItem('whatsappTemplates', JSON.stringify(updated));

    if (selectedWhatsappTemplateName === templateName) {
      setSelectedWhatsappTemplateName(undefined);
    }

    message.success('Şablon silindi');
  };

  // Edit WhatsApp template
  const editWhatsappTemplate = (template: {name: string, content: string}) => {
    setCurrentTemplateForEdit(template);
    whatsappTemplateForm.setFieldsValue(template);
    setEditWhatsappTemplateModalVisible(true);
  };

  // Verilerde değişiklik olduğunda onDataChange'i çağır
  useEffect(() => {
    if (onDataChange) {
      onDataChange(influencers.length);
    }
  }, [influencers, onDataChange]);

  // 3. useEffect ile initialCount'u tablo açılmadan sekme üstünde göster
  useEffect(() => {
    setInfluencerCount(initialCount || 0);
  }, [initialCount]);

  // Profil Resimlerini Çek fonksiyonu (progress bar ile)
  const handleFetchProfileImages = async () => {
    const usernames = tableData.map(i => i.username).filter(Boolean);
    setProgress({ total: usernames.length, current: 0, success: 0, fail: 0, running: true });
    localStorage.setItem('profileImageProgress', JSON.stringify({ total: usernames.length, current: 0, success: 0, fail: 0, running: true }));
    const token = localStorage.getItem('x_tuber_token');
    if (!token) {
      message.error('Oturum bilgileriniz bulunamadı. Lütfen tekrar giriş yapın.');
      setProgress({ total: 0, current: 0, success: 0, fail: 0, running: false });
      localStorage.removeItem('profileImageProgress');
      setTimeout(() => { window.location.href = '/login'; }, 1500);
      return;
    }
    let success = 0, fail = 0;
    for (let i = 0; i < usernames.length; i++) {
      try {
        const response = await fetch(`${API_CONFIG.X_SITE_BASE_URL}/fetch_profile_images.php`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ usernames: [usernames[i]], type: 'influencer' })
        });
        const data = await response.json();
        if (data.success && data.results && data.results[0] && data.results[0].success) {
          success++;
        } else {
          fail++;
        }
      } catch {
        fail++;
      }
      setProgress({ total: usernames.length, current: i + 1, success, fail, running: true });
      localStorage.setItem('profileImageProgress', JSON.stringify({ total: usernames.length, current: i + 1, success, fail, running: true }));
    }
    setProgress({ total: usernames.length, current: usernames.length, success, fail, running: false });
    localStorage.removeItem('profileImageProgress');
    message.success(`Profil resimleri çekildi! Başarılı: ${success}, Başarısız: ${fail}`);
    fetchInfluencers();
  };

  // Sayfa yenilendiğinde progress bar devam etsin
  useEffect(() => {
    const saved = localStorage.getItem('profileImageProgress');
    if (saved) setProgress(JSON.parse(saved));
  }, []);

  return (
    <DndProvider backend={HTML5Backend}>
      <div className={hideHeader ? "p-0" : "m-6"}>
        {/* Başlık bölümü (hideHeader true ise gizle) */}
        {!hideHeader && (
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-semibold text-indigo-600">Influencerlar</h1>
            <span className="bg-indigo-100 text-indigo-800 text-sm font-medium px-2.5 py-1 rounded">
              Toplam: {influencerCount}
            </span>
          </div>
        )}

        <div className={`bg-white dark:bg-gray-800 ${hideHeader ? 'rounded-none shadow-none border-0' : 'rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700'}`}>
          {/* Progress Bar */}
          {progress.running && (
            <div style={{ marginBottom: 16 }}>
              <Progress percent={Math.round((progress.current / progress.total) * 100)} status="active" />
              <div style={{ marginTop: 8, fontSize: 14 }}>
                {`Çekilen: ${progress.current} / ${progress.total} | Başarılı: ${progress.success} | Hatalı: ${progress.fail}`}
              </div>
            </div>
          )}
          {/* Arama ve Butonları Yan Yana Yerleştirmek İçin Flex Container */}
          <div className="flex flex-wrap items-center gap-4 mb-4 px-6 pt-6 pb-2">
            {/* Arama Kutusu */}
            <div className="relative" style={{ width: 384 }}>
              <input
                type="text"
                placeholder="Influencer ara (İsim, Mail, Kullanıcı Adı...)"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm('')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <X className="w-4 h-4" />
                </button>
              )}
            </div>

            {/* Butonlar */}
            <div className="flex flex-wrap gap-2 items-center">
              <Button
                className="flex items-center"
                onClick={() => setEmailModalVisible(true)}
              >
                <MailOutlined className="h-4 w-4 mr-2" />
                Toplu Mail Gönder
              </Button>
              <Button
                className="flex items-center"
                onClick={() => setWhatsappModalVisible(true)}
              >
                <WhatsAppOutlined className="h-4 w-4 mr-2" />
                Toplu WhatsApp
              </Button>
              <Popover
                content={
                  <div className="p-4" style={{width: '300px'}}>
                    <h3 className="text-lg font-medium mb-3">Filtreler</h3>

                    {/* Takipçi Sayısı Filtresi */}
                    <div className="mb-4">
                      <h4 className="font-medium mb-2">Takipçi Sayısı</h4>
                      <div className="space-y-2">
                        <Checkbox
                          checked={filterValues.followers?.includes('5k-10k')}
                          onChange={e => {
                            const newFollowers = [...(filterValues.followers || [])];
                            if (e.target.checked) {
                              newFollowers.push('5k-10k');
                            } else {
                              const index = newFollowers.indexOf('5k-10k');
                              if (index > -1) newFollowers.splice(index, 1);
                            }
                            setFilterValues({...filterValues, followers: newFollowers});
                          }}
                        >
                          5K - 10K
                        </Checkbox>
                        <Checkbox
                          checked={filterValues.followers?.includes('10k-50k')}
                          onChange={e => {
                            const newFollowers = [...(filterValues.followers || [])];
                            if (e.target.checked) {
                              newFollowers.push('10k-50k');
                            } else {
                              const index = newFollowers.indexOf('10k-50k');
                              if (index > -1) newFollowers.splice(index, 1);
                            }
                            setFilterValues({...filterValues, followers: newFollowers});
                          }}
                        >
                          10K - 50K
                        </Checkbox>
                        <Checkbox
                          checked={filterValues.followers?.includes('50k-100k')}
                          onChange={e => {
                            const newFollowers = [...(filterValues.followers || [])];
                            if (e.target.checked) {
                              newFollowers.push('50k-100k');
                            } else {
                              const index = newFollowers.indexOf('50k-100k');
                              if (index > -1) newFollowers.splice(index, 1);
                            }
                            setFilterValues({...filterValues, followers: newFollowers});
                          }}
                        >
                          50K - 100K
                        </Checkbox>
                        <Checkbox
                          checked={filterValues.followers?.includes('100k-500k')}
                          onChange={e => {
                            const newFollowers = [...(filterValues.followers || [])];
                            if (e.target.checked) {
                              newFollowers.push('100k-500k');
                            } else {
                              const index = newFollowers.indexOf('100k-500k');
                              if (index > -1) newFollowers.splice(index, 1);
                            }
                            setFilterValues({...filterValues, followers: newFollowers});
                          }}
                        >
                          100K - 500K
                        </Checkbox>
                        <Checkbox
                          checked={filterValues.followers?.includes('500k+')}
                          onChange={e => {
                            const newFollowers = [...(filterValues.followers || [])];
                            if (e.target.checked) {
                              newFollowers.push('500k+');
                            } else {
                              const index = newFollowers.indexOf('500k+');
                              if (index > -1) newFollowers.splice(index, 1);
                            }
                            setFilterValues({...filterValues, followers: newFollowers});
                          }}
                        >
                          500K+
                        </Checkbox>
                      </div>
                    </div>

                    {/* Telefon Numarası Filtresi */}
                    <div className="mb-4">
                      <h4 className="font-medium mb-2">Telefon Numarası</h4>
                      <div className="space-y-2">
                        <Checkbox
                          checked={filterValues.hasTelefon}
                          onChange={e => {
                            setFilterValues({...filterValues, hasTelefon: e.target.checked});
                          }}
                        >
                          Telefon Numarası Olanlar
                        </Checkbox>
                      </div>
                    </div>

                    <Divider style={{margin: '12px 0'}} />

                    {/* Filtreleri Uygula/Sıfırla */}
                    <div className="flex justify-between pt-2">
                      <Button
                        size="small"
                        onClick={() => {
                          setFilterValues({
                            category: [],
                            status: [],
                            location: [],
                            followers: [],
                            hasTelefon: false
                          });
                          message.success('Filtreler sıfırlandı');
                        }}
                      >
                        Sıfırla
                      </Button>
                      <Button
                        type="primary"
                        size="small"
                        onClick={() => {
                          message.success('Filtreler uygulandı');
                          setFilterVisible(false);
                        }}
                        style={{
                          backgroundColor: '#1890ff',
                          borderColor: '#1890ff',
                          color: 'white'
                        }}
                      >
                        Uygula
                      </Button>
                    </div>
                  </div>
                }
                trigger="click"
                open={filterVisible}
                onOpenChange={setFilterVisible}
              >
                <Button className="flex items-center">
                  <Filter className="h-4 w-4 mr-2" />
                  Filtrele
                </Button>
              </Popover>
              <Dropdown
                overlay={(
                  <Menu>
                    <Menu.Item key="fetchProfileImages" icon={<CloudDownloadOutlined />} onClick={handleFetchProfileImages}>
                      Profil Resimlerini Çek
                    </Menu.Item>
                    <Menu.Item key="updateFollowers" icon={<ReloadOutlined />} onClick={updateFollowers}>
                      Takipçi Sayılarını Güncelle
                    </Menu.Item>
                    <Menu.Item key="1" icon={<UserPlus />} onClick={() => setAddModalVisible(true)}>
                      Yeni Influencer Ekle
                    </Menu.Item>
                    <Menu.Item key="2" icon={<UploadOutlined />} onClick={() => document.getElementById('excel-import-input-influencer')?.click()}>
                      Excel'den İçe Aktar
                    </Menu.Item>
                  </Menu>
                )}
                trigger={['click']}
              >
                <Button icon={<EllipsisOutlined />} type="text" style={{ border: 'none', background: 'transparent', boxShadow: 'none' }} />
              </Dropdown>
            </div>
          </div>

          <div className="px-0">
            <Table
              rowKey="id"
              components={{
                header: {
                  cell: ({ ...restProps }) => {
                    const index = columns.findIndex(col => col.key === restProps['data-column-key']);
                    return index !== -1 ? (
                      <DraggableHeaderCell
                        index={index}
                        title={columns[index].title as string}
                        moveColumn={moveColumn}
                      />
                    ) : (
                      <th {...restProps} />
                    );
                  }
                }
              }}
              columns={columns}
              dataSource={filtered}
              loading={loading}
              pagination={{
                pageSize: 50,
                showSizeChanger: true,
                pageSizeOptions: [50, 100, 500],
                showTotal: (total, range) => `${range[0]}-${range[1]} / ${total} kayıt`,
                position: ['bottomCenter']
              }}
              className="influencers-table w-full"
              scroll={{ x: 'max-content' }}
              style={{ width: '100%' }}
            />
          </div>
        </div>
      </div>
      {/* Toplu E-posta Modal - SingleEmailModal kullanarak */}
      <SingleEmailModal
        isOpen={emailModalVisible}
        onClose={() => setEmailModalVisible(false)}
        mode="bulk"
        bulkRecipients={(() => {
          // Seçilen influencerlar veya hiç seçim yapılmadıysa filtrelenmiş tüm influencerlar
          let recipients: Influencer[] = [];

          if (selectedRowKeys.length > 0) {
            recipients = influencers.filter(inf => selectedRowKeys.includes(inf.id!));
          } else {
            recipients = influencers;
          }

          // Takipçi aralığı filtresi uygula
          if (selectedFollowerRanges.length > 0 && !selectedFollowerRanges.includes('all')) {
            recipients = recipients.filter(inf => {
              const followers = inf.followers || 0;
              return selectedFollowerRanges.some(range => {
                switch (range) {
                  case '10k_less': return followers < 10000;
                  case '10k_50k': return followers >= 10000 && followers < 50000;
                  case '50k_100k': return followers >= 50000 && followers < 100000;
                  case '100k_plus': return followers >= 100000;
                  default: return true;
                }
              });
            });
          }

          return recipients.map(inf => ({
            email: inf.email || '',
            name: inf.username || 'Influencer'
          }));
        })()}
        onBulkSend={async (data) => {
          try {
            const emails = data.recipients.map(r => r.email).filter(Boolean);
            if (emails.length === 0) {
              message.error('Gönderilecek e-posta adresi bulunamadı');
              return;
            }

            await sendEmailToInfluencers(emails, data.subject, data.content);
            message.success(`${emails.length} kişiye e-posta gönderildi!`);

            // Form temizle ve modalı kapat
            setEmailModalVisible(false);
            setSelectedRowKeys([]);
            setSelectedFollowerRanges([]);
          } catch (error) {
            console.error('Toplu e-posta gönderme hatası:', error);
            message.error('E-posta gönderilirken hata oluştu');
          }
        }}
        followerRangeFilter={selectedFollowerRanges}
      />

      <Modal
        title="Toplu WhatsApp Mesajı Gönder"
        open={whatsappModalVisible}
        onCancel={() => setWhatsappModalVisible(false)}
        footer={[
          <Button
            key="back"
            onClick={() => setWhatsappModalVisible(false)}
            style={{ borderColor: darkMode ? '#6b7280' : '#d1d5db', color: darkMode ? '#e5e7eb' : '#374151' }}
          >
            İptal
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={sendBulkWhatsApp}
            style={{ backgroundColor: '#25D366', borderColor: '#25D366', color: 'white' }}
          >
            Gönder
          </Button>
        ]}
        width={600}
        centered
        bodyStyle={{
          paddingRight: 16
        }}
        style={{ top: 20 }}
      >
        <Form form={whatsappForm} layout="vertical">
          <Form.Item label="Şablon">
            <Space>
              <Select
                style={{ width: 220 }}
                placeholder="Şablon seçin"
                value={selectedWhatsappTemplateName}
                onChange={(val) => {
                  setSelectedWhatsappTemplateName(val);
                  const t = whatsappTemplates.find(t => t.name === val);
                  if (t) {
                    whatsappForm.setFieldsValue({ content: t.content });
                  }
                }}
                dropdownRender={(menu) => (
                  <>
                    {menu}
                    <Divider style={{ margin: '8px 0' }} />
                    <div style={{ padding: '0 8px 4px' }}>
                      <Button
                        type="text"
                        icon={<UserPlus size={14} />}
                        onClick={() => setNewWhatsappTemplateModalVisible(true)}
                        block
                      >
                        Yeni Şablon Ekle
                      </Button>
                    </div>
                  </>
                )}
              >
                {whatsappTemplates.map(t => (
                  <Select.Option key={t.name} value={t.name}>
                    <div className="flex items-center justify-between">
                      <span>{t.name}</span>
                      <div className="flex items-center space-x-1">
                        <Button
                          type="text"
                          size="small"
                          icon={<Save size={14} />}
                          onClick={(e) => {
                            e.stopPropagation();
                            editWhatsappTemplate(t);
                          }}
                        />
                        <Button
                          type="text"
                          size="small"
                          danger
                          icon={<Trash2 size={14} />}
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteWhatsappTemplate(t.name);
                          }}
                        />
                      </div>
                    </div>
                  </Select.Option>
                ))}
              </Select>
              <Button
                type="link"
                onClick={() => setNewWhatsappTemplateModalVisible(true)}
              >
                Yeni Şablon
              </Button>
            </Space>
          </Form.Item>

          <Form.Item label="Takipçi Sayısı Aralığı">
            <div className="flex flex-wrap gap-2">
              <Checkbox
                checked={selectedFollowerRanges.includes('all')}
                onChange={(e) => {
                  if(e.target.checked) {
                    setSelectedFollowerRanges(['all']);
                  } else {
                    setSelectedFollowerRanges(selectedFollowerRanges.filter(r => r !== 'all'));
                  }
                }}
              >
                Tümü
              </Checkbox>
              <Checkbox
                checked={selectedFollowerRanges.includes('10k_less')}
                onChange={(e) => {
                  let newRanges = [...selectedFollowerRanges];
                  if(e.target.checked) {
                    newRanges = newRanges.filter(r => r !== 'all').concat(['10k_less']);
                  } else {
                    newRanges = newRanges.filter(r => r !== '10k_less');
                  }
                  setSelectedFollowerRanges(newRanges);
                }}
              >
                10K'dan az
              </Checkbox>
              <Checkbox
                checked={selectedFollowerRanges.includes('10k_50k')}
                onChange={(e) => {
                  let newRanges = [...selectedFollowerRanges];
                  if(e.target.checked) {
                    newRanges = newRanges.filter(r => r !== 'all').concat(['10k_50k']);
                  } else {
                    newRanges = newRanges.filter(r => r !== '10k_50k');
                  }
                  setSelectedFollowerRanges(newRanges);
                }}
              >
                10K - 50K
              </Checkbox>
              <Checkbox
                checked={selectedFollowerRanges.includes('50k_100k')}
                onChange={(e) => {
                  let newRanges = [...selectedFollowerRanges];
                  if(e.target.checked) {
                    newRanges = newRanges.filter(r => r !== 'all').concat(['50k_100k']);
                  } else {
                    newRanges = newRanges.filter(r => r !== '50k_100k');
                  }
                  setSelectedFollowerRanges(newRanges);
                }}
              >
                50K - 100K
              </Checkbox>
              <Checkbox
                checked={selectedFollowerRanges.includes('100k_plus')}
                onChange={(e) => {
                  let newRanges = [...selectedFollowerRanges];
                  if(e.target.checked) {
                    newRanges = newRanges.filter(r => r !== 'all').concat(['100k_plus']);
                  } else {
                    newRanges = newRanges.filter(r => r !== '100k_plus');
                  }
                  setSelectedFollowerRanges(newRanges);
                }}
              >
                100K+
              </Checkbox>
            </div>
          </Form.Item>

          <Form.Item
            name="content"
            label="Mesaj"
            initialValue="Merhaba {username},"
            rules={[{ required: true, message: 'Mesaj giriniz' }]}
          >
            <Input.TextArea
              rows={4}
              placeholder="Mesajınızı yazın. {username} kullanıcı adını temsil eder."
            />
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        title="Yeni WhatsApp Şablonu"
        open={newWhatsappTemplateModalVisible}
        onCancel={() => {setNewWhatsappTemplateModalVisible(false); whatsappTemplateForm.resetFields();}}
        footer={[
          <Button
            key="back"
            onClick={() => setNewWhatsappTemplateModalVisible(false)}
            style={{ borderColor: darkMode ? '#6b7280' : '#d1d5db', color: darkMode ? '#e5e7eb' : '#374151' }}
          >
            İptal
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={() => whatsappTemplateForm.submit()}
            style={{ backgroundColor: '#25D366', borderColor: '#25D366', color: 'white' }}
          >
            Kaydet
          </Button>
        ]}
        width={600}
        centered
        bodyStyle={{
          paddingRight: 16
        }}
        style={{ top: 20 }}
      >
        <Form
          form={whatsappTemplateForm}
          layout="vertical"
          onFinish={addWhatsappTemplate}
        >
          <Form.Item name="name" label="Şablon Adı" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name="content" label="Mesaj" rules={[{ required: true }]}>
            <Input.TextArea
              rows={4}
              placeholder="Mesajınızı yazın. {username} kullanıcı adını temsil eder."
            />
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        title="WhatsApp Şablonunu Düzenle"
        open={editWhatsappTemplateModalVisible}
        onCancel={() => {setEditWhatsappTemplateModalVisible(false); setCurrentTemplateForEdit(null); whatsappTemplateForm.resetFields();}}
        footer={[
          <Button
            key="back"
            onClick={() => setEditWhatsappTemplateModalVisible(false)}
            style={{ borderColor: darkMode ? '#6b7280' : '#d1d5db', color: darkMode ? '#e5e7eb' : '#374151' }}
          >
            İptal
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={() => whatsappTemplateForm.submit()}
            style={{ backgroundColor: '#25D366', borderColor: '#25D366', color: 'white' }}
          >
            Kaydet
          </Button>
        ]}
        width={600}
        centered
        bodyStyle={{
          paddingRight: 16
        }}
        style={{ top: 20 }}
      >
        <Form
          form={whatsappTemplateForm}
          layout="vertical"
          onFinish={updateWhatsappTemplate}
        >
          <Form.Item name="name" label="Şablon Adı" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name="content" label="Mesaj" rules={[{ required: true }]}>
            <Input.TextArea
              rows={4}
              placeholder="Mesajınızı yazın. {username} kullanıcı adını temsil eder."
            />
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title="Yeni Influencer Ekle"
        open={addModalVisible}
        onCancel={() => { setAddModalVisible(false); addForm.resetFields(); }}
        onOk={handleAddInfluencer}
        okText="Kaydet"
        cancelText="İptal"
        width={500}
        centered
        bodyStyle={{ padding: 24 }}
      >
        <Form form={addForm} layout="vertical">
          <Form.Item name="username" label="Kullanıcı Adı" rules={[{ required: true, message: 'Kullanıcı adı zorunlu' }]}> <Input /> </Form.Item>
          <Form.Item name="email" label="E-posta" rules={[{ required: true, type: 'email', message: 'Geçerli e-posta girin' }]}> <Input /> </Form.Item>
          <Form.Item name="followers" label="Takipçi Sayısı" rules={[{ required: true, message: 'Takipçi sayısı zorunlu' }]}> <InputNumber min={0} style={{ width: '100%' }} /> </Form.Item>
          <Form.Item name="telefon" label="Telefon"> <Input /> </Form.Item>
          <Form.Item name="sosyal_medya" label="Sosyal Medya Linki"> <Input /> </Form.Item>
        </Form>
      </Modal>

      {/* Tekil E-posta Modal */}
      {selectedInfluencerForEmail && (
        <SingleEmailModal
          isOpen={singleEmailModalVisible}
          onClose={() => {
            setSingleEmailModalVisible(false);
            setSelectedInfluencerForEmail(null);
          }}
          recipientEmail={selectedInfluencerForEmail.email || ''}
          recipientName={selectedInfluencerForEmail.username || 'Influencer'}
          defaultSubject="Tuber Ajans - İletişim"
        />
      )}
    </DndProvider>
  );
}