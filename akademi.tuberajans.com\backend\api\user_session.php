<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../classes/Auth.php';
require_once __DIR__ . '/../utils/helpers.php';

// CORS headers
header('Access-Control-Allow-Origin: https://akademi.tuberajans.com');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');
header('Content-Type: application/json');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

try {
    // Veritabanı bağlantısını al
    $db = getDB();
    
    // Auth sınıfını başlat
    $auth = new Auth($db);
    
    // Kullanıcı kontrolü
    $user = $auth->getCurrentUser();
    if (!$user) {
        sendErrorResponse('Giriş yapmanız gerekiyor', 401);
        exit;
    }

    // Veritabanından tam kullanıcı bilgilerini al
    $stmt = $db->prepare("
        SELECT
            id, username, email, role, profile_image,
            tiktok_username, tiktok_display_name, tiktok_avatar_url, tiktok_bio,
            followers_count, following_count, likes_count, video_count,
            is_verified, tiktok_linked, tiktok_open_id, tiktok_linked_at
        FROM users
        WHERE id = ?
    ");
    $stmt->execute([$user['id']]);
    $fullUser = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($fullUser) {
        // Tam kullanıcı bilgilerini kullan
        $user = array_merge($user, $fullUser);

        // TikTok bağlantı durumunu kontrol et
        if ($user['tiktok_linked'] && $user['tiktok_open_id']) {
            $user['tiktok_linked'] = 1;
        } else {
            $user['tiktok_linked'] = 0;
        }

        // Avatar URL'ini düzelt
        if (empty($user['tiktok_avatar_url']) && !empty($user['profile_image'])) {
            $user['avatar_url'] = $user['profile_image'];
        } else {
            $user['avatar_url'] = $user['tiktok_avatar_url'];
        }
    }
    
    // Hassas bilgileri temizle
    unset($user['access_token']);
    unset($user['refresh_token']);
    
    sendSuccessResponse($user, 'Kullanıcı bilgileri başarıyla alındı');
    
} catch (Exception $e) {
    writeLog('User Session API Error: ' . $e->getMessage(), 'user_session_error.log');
    sendErrorResponse('Sunucu hatası', 500);
}
?>
