<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../classes/Auth.php';
require_once __DIR__ . '/../utils/helpers.php';

// CORS headers
header('Access-Control-Allow-Origin: https://akademi.tuberajans.com');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');
header('Content-Type: application/json');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

try {
    // Veritabanı bağlantısını al
    $db = getDB();
    
    // Auth sınıfını başlat
    $auth = new Auth($db);
    
    // Kullanıcı kontrolü
    $user = $auth->getCurrentUser();
    if (!$user) {
        sendErrorResponse('Giriş yapmanız gerekiyor', 401);
        exit;
    }
    
    // Veritabanından TikTok bilgilerini al ve kullanıcı verisine ekle
    if ($user['tiktok_linked'] && $user['tiktok_open_id']) {
        $user['tiktok_linked'] = 1;
        // Veritabanından TikTok bilgilerini al
        $user['tiktok_username'] = $user['tiktok_username'] ?? null;
        $user['tiktok_display_name'] = $user['tiktok_display_name'] ?? null;
        $user['tiktok_avatar_url'] = $user['tiktok_avatar_url'] ?? null;
        $user['followers_count'] = $user['followers_count'] ?? 0;
        $user['following_count'] = $user['following_count'] ?? 0;
        $user['likes_count'] = $user['likes_count'] ?? 0;
        $user['video_count'] = $user['video_count'] ?? 0;
        $user['is_verified'] = $user['is_verified'] ?? false;

        // Session'dan da güncelle (varsa)
        if (isset($_SESSION['tiktok_avatar_url']) && $_SESSION['tiktok_avatar_url']) {
            $user['tiktok_avatar_url'] = $_SESSION['tiktok_avatar_url'];
        }
    } else {
        $user['tiktok_linked'] = 0;
    }
    
    // Hassas bilgileri temizle
    unset($user['access_token']);
    unset($user['refresh_token']);
    
    sendSuccessResponse($user, 'Kullanıcı bilgileri başarıyla alındı');
    
} catch (Exception $e) {
    writeLog('User Session API Error: ' . $e->getMessage(), 'user_session_error.log');
    sendErrorResponse('Sunucu hatası', 500);
}
?>
