"""
Chrome Session Manager - Login profilleri koruyarak Chrome çakışmasını önler
"""

import threading
import time
import subprocess
import logging
import os
from typing import Optional
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service

logger = logging.getLogger(__name__)

class ChromeSessionManager:
    """
    Chrome session'larını yöneten singleton sınıf.
    Login profilleri koruyarak thread çakışmasını önler.
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, 'initialized'):
            return
            
        self.chrome_lock = threading.Lock()
        self.current_user = None
        self.session_start_time = None
        self.max_session_duration = 600  # 10 dakika max session
        self.initialized = True
        
        # Chrome yolları - constants'tan al
        from constants import PathConfig
        self.path_config = PathConfig()
        
        logger.info("🔧 Chrome Session Manager başlatıldı")
    
    def acquire_chrome_session(self, thread_name: str, timeout: int = 300) -> bool:
        """
        Chrome session'ını thread için rezerve eder.

        Args:
            thread_name: Thread adı
            timeout: Maksimum bekleme süresi (saniye)

        Returns:
            bool: Session alınabildi mi
        """
        logger.info(f"🔒 {thread_name} Chrome session almaya çalışıyor...")

        # Eğer session timeout olmuşsa zorla temizle
        if self.check_session_timeout():
            logger.warning("⚠️ Session timeout, zorla temizleniyor...")
            self._force_release_session()

        acquired = self.chrome_lock.acquire(timeout=timeout)

        if acquired:
            self.current_user = thread_name
            self.session_start_time = time.time()
            logger.info(f"✅ {thread_name} Chrome session aldı")
            return True
        else:
            logger.error(f"❌ {thread_name} Chrome session alamadı (timeout: {timeout}s)")
            # Timeout durumunda zorla temizlemeyi dene
            self._force_release_session()
            return False
    
    def release_chrome_session(self, thread_name: str):
        """
        Chrome session'ını serbest bırakır.
        
        Args:
            thread_name: Thread adı
        """
        if self.current_user == thread_name:
            self.current_user = None
            self.session_start_time = None
            logger.info(f"🔓 {thread_name} Chrome session bıraktı")
            
            # Chrome'u temizle
            self._force_close_chrome()
            
            self.chrome_lock.release()
        else:
            logger.warning(f"⚠️ {thread_name} session sahibi değil (sahip: {self.current_user})")
    
    def _force_close_chrome(self):
        """
        Tüm Chrome işlemlerini zorla kapatır.
        """
        try:
            logger.info("🔄 Chrome işlemleri kapatılıyor...")
            
            # Chrome ve ChromeDriver işlemlerini kapat
            subprocess.run(['taskkill', '/f', '/im', 'chrome.exe'], 
                         capture_output=True, text=True, timeout=10)
            subprocess.run(['taskkill', '/f', '/im', 'chromedriver.exe'], 
                         capture_output=True, text=True, timeout=10)
            
            # Biraz bekle
            time.sleep(3)
            
            logger.info("✅ Chrome işlemleri kapatıldı")
            
        except Exception as e:
            logger.warning(f"⚠️ Chrome kapatma hatası: {e}")
    
    def create_chrome_driver(self, thread_name: str, headless: bool = False) -> Optional[webdriver.Chrome]:
        """
        Thread için Chrome driver oluşturur.
        
        Args:
            thread_name: Thread adı
            headless: Headless mod
            
        Returns:
            Chrome WebDriver instance veya None
        """
        if self.current_user != thread_name:
            logger.error(f"❌ {thread_name} Chrome session sahibi değil")
            return None
        
        try:
            logger.info(f"🚀 {thread_name} için Chrome driver oluşturuluyor...")
            
            options = Options()
            
            # Login profili kullan - AYNI PROFİL TÜM THREAD'LER İÇİN
            options.add_argument(f'--user-data-dir={self.path_config.CHROME_PROFILE_BASE}')
            options.add_argument('--profile-directory=Default')
            
            # Headless ayarı
            if headless:
                options.add_argument('--headless')
            
            # Anti-bot önlemleri
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_argument("--disable-web-security")
            options.add_argument("--disable-features=VizDisplayCompositor")
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option("useAutomationExtension", False)
            
            # User-Agent
            options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            
            # Temel ayarlar
            options.add_argument('--disable-gpu')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-notifications')
            options.add_argument('--disable-popup-blocking')
            options.add_argument('--start-maximized')
            options.add_argument('--disable-logging')
            options.add_argument('--disable-extensions')
            
            # Chrome binary path
            chrome_binary = os.getenv('CHROME_BINARY_PATH', r"C:\Program Files\Google\Chrome\Application\chrome.exe")
            if os.path.exists(chrome_binary):
                options.binary_location = chrome_binary
            
            # Service oluştur
            service = Service(self.path_config.CHROME_DRIVER_PATH)
            
            # Driver oluştur
            driver = webdriver.Chrome(service=service, options=options)
            
            # Bot algılama önlemleri
            stealth_script = """
            Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
            Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
            Object.defineProperty(navigator, 'languages', {get: () => ['tr-TR', 'tr', 'en-US', 'en']});
            delete window.chrome;
            console.debug = () => {};
            """
            driver.execute_script(stealth_script)
            
            logger.info(f"✅ {thread_name} Chrome driver oluşturuldu")
            return driver
            
        except Exception as e:
            logger.error(f"❌ {thread_name} Chrome driver oluşturma hatası: {e}")
            return None
    
    def check_session_timeout(self) -> bool:
        """
        Session timeout kontrolü yapar.
        
        Returns:
            bool: Session timeout oldu mu
        """
        if self.session_start_time is None:
            return False
            
        elapsed = time.time() - self.session_start_time
        if elapsed > self.max_session_duration:
            logger.warning(f"⏰ Chrome session timeout ({elapsed:.0f}s > {self.max_session_duration}s)")
            return True
            
        return False
    
    def _force_release_session(self):
        """
        Session'ı zorla serbest bırakır (emergency durumlar için).
        """
        try:
            logger.warning("🚨 Chrome session zorla serbest bırakılıyor...")

            # Chrome'u zorla kapat
            self._force_close_chrome()

            # Session bilgilerini temizle
            self.current_user = None
            self.session_start_time = None

            # Lock'u serbest bırak (eğer kilitliyse)
            if self.chrome_lock.locked():
                try:
                    self.chrome_lock.release()
                    logger.info("✅ Chrome lock zorla serbest bırakıldı")
                except Exception as e:
                    logger.error(f"❌ Lock serbest bırakma hatası: {e}")

        except Exception as e:
            logger.error(f"❌ Zorla serbest bırakma hatası: {e}")

    def get_session_info(self) -> dict:
        """
        Mevcut session bilgilerini döndürür.
        """
        return {
            'current_user': self.current_user,
            'session_duration': time.time() - self.session_start_time if self.session_start_time else 0,
            'max_duration': self.max_session_duration,
            'is_locked': self.chrome_lock.locked()
        }

# Singleton instance
chrome_session_manager = ChromeSessionManager()
