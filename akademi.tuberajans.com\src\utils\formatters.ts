/**
 * Say<PERSON><PERSON><PERSON> kullanıcı dostu formatta göstermek için yardımcı fonksiyonlar
 */

/**
 * Büyük sayıları K, M, B formatında gösterir
 * @param num - Formatlanacak sayı
 * @returns Formatlanmış string (örn: 1.2K, 3.4M)
 */
export const formatNumber = (num: number | string | null | undefined): string => {
  const numValue = typeof num === 'string' ? parseInt(num) : num;
  if (!numValue || isNaN(numValue)) return '0';

  if (numValue >= 1000000000) {
    return (numValue / 1000000000).toFixed(1) + 'B';
  }
  if (numValue >= 1000000) {
    return (numValue / 1000000).toFixed(1) + 'M';
  }
  if (numValue >= 1000) {
    return (numValue / 1000).toFixed(1) + 'K';
  }
  return numValue.toString();
};

/**
 * Video süresini dakika:saniye formatında gösterir
 * @param seconds - Saniye cinsinden süre
 * @returns Formatlanmış string (örn: 2:30)
 */
export const formatDuration = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

/**
 * Tarihi Türkçe formatta gösterir
 * @param dateString - ISO tarih string'i veya Unix timestamp
 * @returns Formatlanmış tarih string'i
 */
export const formatDate = (dateString: string | number | null | undefined): string => {
  if (!dateString) return 'Tarih belirtilmemiş';

  let date: Date;

  // Unix timestamp kontrolü (sayı ise)
  if (typeof dateString === 'number') {
    date = new Date(dateString * 1000); // Unix timestamp saniye cinsinden
  } else if (typeof dateString === 'string') {
    // String ise, önce sayı olup olmadığını kontrol et
    if (/^\d+$/.test(dateString)) {
      // Sadece rakamlardan oluşuyorsa Unix timestamp olarak kabul et
      const timestamp = parseInt(dateString);
      // Timestamp 10 haneli ise saniye, 13 haneli ise milisaniye
      date = new Date(timestamp > 9999999999 ? timestamp : timestamp * 1000);
    } else {
      // Normal tarih string'i
      date = new Date(dateString);
    }
  } else {
    return 'Geçersiz tarih';
  }

  // Geçerli tarih kontrolü
  if (isNaN(date.getTime())) {
    return 'Geçersiz tarih';
  }

  return date.toLocaleDateString('tr-TR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

/**
 * Yüzde değerini formatlar
 * @param value - Yüzde değeri
 * @param decimals - Ondalık basamak sayısı (varsayılan: 1)
 * @returns Formatlanmış yüzde string'i
 */
export const formatPercentage = (value: number, decimals: number = 1): string => {
  return `${value.toFixed(decimals)}%`;
};

/**
 * Engagement rate'i formatlar
 * @param rate - Engagement rate değeri
 * @returns Formatlanmış string
 */
export const formatEngagementRate = (rate: number): string => {
  if (rate >= 10) return 'Mükemmel';
  if (rate >= 6) return 'Çok İyi';
  if (rate >= 3) return 'İyi';
  if (rate >= 1) return 'Orta';
  return 'Düşük';
};
