{"name": "react-tailwind-template", "version": "0.0.0", "private": true, "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview", "start": "vite build"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/plots": "^1.2.6", "@heroicons/react": "^2.2.0", "@types/react-i18next": "^8.1.0", "@types/styled-components": "^5.1.34", "antd": "^5.13.2", "axios": "^1.8.4", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "exceljs": "^4.4.0", "lucide-react": "^0.487.0", "moment": "^2.30.1", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.4.1", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-resizable": "^3.0.5", "react-router-dom": "^6.22.0", "recharts": "^2.15.2", "styled-components": "^6.1.17", "xlsx": "^0.18.5"}, "devDependencies": {"@types/dompurify": "^3.2.0", "@types/node": "^20.17.30", "@types/react": "^18.2.43", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "8.29.0", "@typescript-eslint/parser": "8.29.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.14", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.31", "rollup-plugin-visualizer": "^5.14.0", "stylelint": "^16.18.0", "stylelint-config-standard": "^38.0.0", "tailwindcss": "^3.3.0", "typescript": "^5.3.3", "typescript-eslint": "^8.33.1", "vite": "^6.3.5"}}