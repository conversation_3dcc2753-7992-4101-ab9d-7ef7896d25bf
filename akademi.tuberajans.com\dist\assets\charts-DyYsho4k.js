import{c as ai,g as ue,r as B,a as _}from"./vendor-BKU87Gzz.js";function lh(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(r=lh(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function ee(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=lh(e))&&(n&&(n+=" "),n+=t);return n}var Ry=Array.isArray,ke=Ry,Fy=typeof ai=="object"&&ai&&ai.Object===Object&&ai,fh=Fy,Wy=fh,zy=typeof self=="object"&&self&&self.Object===Object&&self,Uy=Wy||zy||Function("return this")(),ct=Uy,qy=ct,Hy=qy.Symbol,Vn=Hy,bs=Vn,ph=Object.prototype,Gy=ph.hasOwnProperty,Ky=ph.toString,Zr=bs?bs.toStringTag:void 0;function Xy(e){var t=Gy.call(e,Zr),r=e[Zr];try{e[Zr]=void 0;var n=!0}catch{}var i=Ky.call(e);return n&&(t?e[Zr]=r:delete e[Zr]),i}var Vy=Xy,Yy=Object.prototype,Zy=Yy.toString;function Jy(e){return Zy.call(e)}var Qy=Jy,xs=Vn,em=Vy,tm=Qy,rm="[object Null]",nm="[object Undefined]",ws=xs?xs.toStringTag:void 0;function im(e){return e==null?e===void 0?nm:rm:ws&&ws in Object(e)?em(e):tm(e)}var wt=im;function am(e){return e!=null&&typeof e=="object"}var Ot=am,om=wt,um=Ot,cm="[object Symbol]";function sm(e){return typeof e=="symbol"||um(e)&&om(e)==cm}var Lr=sm,lm=ke,fm=Lr,pm=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,hm=/^\w*$/;function dm(e,t){if(lm(e))return!1;var r=typeof e;return r=="number"||r=="symbol"||r=="boolean"||e==null||fm(e)?!0:hm.test(e)||!pm.test(e)||t!=null&&e in Object(t)}var lc=dm;function vm(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var jt=vm;const Br=ue(jt);var ym=wt,mm=jt,gm="[object AsyncFunction]",bm="[object Function]",xm="[object GeneratorFunction]",wm="[object Proxy]";function Om(e){if(!mm(e))return!1;var t=ym(e);return t==bm||t==xm||t==gm||t==wm}var fc=Om;const Y=ue(fc);var Sm=ct,Am=Sm["__core-js_shared__"],_m=Am,lo=_m,Os=function(){var e=/[^.]+$/.exec(lo&&lo.keys&&lo.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function Pm(e){return!!Os&&Os in e}var $m=Pm,Tm=Function.prototype,Em=Tm.toString;function jm(e){if(e!=null){try{return Em.call(e)}catch{}try{return e+""}catch{}}return""}var hh=jm,Mm=fc,Cm=$m,Im=jt,Dm=hh,km=/[\\^$.*+?()[\]{}|]/g,Nm=/^\[object .+?Constructor\]$/,Lm=Function.prototype,Bm=Object.prototype,Rm=Lm.toString,Fm=Bm.hasOwnProperty,Wm=RegExp("^"+Rm.call(Fm).replace(km,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function zm(e){if(!Im(e)||Cm(e))return!1;var t=Mm(e)?Wm:Nm;return t.test(Dm(e))}var Um=zm;function qm(e,t){return e==null?void 0:e[t]}var Hm=qm,Gm=Um,Km=Hm;function Xm(e,t){var r=Km(e,t);return Gm(r)?r:void 0}var Qt=Xm,Vm=Qt,Ym=Vm(Object,"create"),ba=Ym,Ss=ba;function Zm(){this.__data__=Ss?Ss(null):{},this.size=0}var Jm=Zm;function Qm(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}var eg=Qm,tg=ba,rg="__lodash_hash_undefined__",ng=Object.prototype,ig=ng.hasOwnProperty;function ag(e){var t=this.__data__;if(tg){var r=t[e];return r===rg?void 0:r}return ig.call(t,e)?t[e]:void 0}var og=ag,ug=ba,cg=Object.prototype,sg=cg.hasOwnProperty;function lg(e){var t=this.__data__;return ug?t[e]!==void 0:sg.call(t,e)}var fg=lg,pg=ba,hg="__lodash_hash_undefined__";function dg(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=pg&&t===void 0?hg:t,this}var vg=dg,yg=Jm,mg=eg,gg=og,bg=fg,xg=vg;function Rr(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}Rr.prototype.clear=yg;Rr.prototype.delete=mg;Rr.prototype.get=gg;Rr.prototype.has=bg;Rr.prototype.set=xg;var wg=Rr;function Og(){this.__data__=[],this.size=0}var Sg=Og;function Ag(e,t){return e===t||e!==e&&t!==t}var pc=Ag,_g=pc;function Pg(e,t){for(var r=e.length;r--;)if(_g(e[r][0],t))return r;return-1}var xa=Pg,$g=xa,Tg=Array.prototype,Eg=Tg.splice;function jg(e){var t=this.__data__,r=$g(t,e);if(r<0)return!1;var n=t.length-1;return r==n?t.pop():Eg.call(t,r,1),--this.size,!0}var Mg=jg,Cg=xa;function Ig(e){var t=this.__data__,r=Cg(t,e);return r<0?void 0:t[r][1]}var Dg=Ig,kg=xa;function Ng(e){return kg(this.__data__,e)>-1}var Lg=Ng,Bg=xa;function Rg(e,t){var r=this.__data__,n=Bg(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this}var Fg=Rg,Wg=Sg,zg=Mg,Ug=Dg,qg=Lg,Hg=Fg;function Fr(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}Fr.prototype.clear=Wg;Fr.prototype.delete=zg;Fr.prototype.get=Ug;Fr.prototype.has=qg;Fr.prototype.set=Hg;var wa=Fr,Gg=Qt,Kg=ct,Xg=Gg(Kg,"Map"),hc=Xg,As=wg,Vg=wa,Yg=hc;function Zg(){this.size=0,this.__data__={hash:new As,map:new(Yg||Vg),string:new As}}var Jg=Zg;function Qg(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}var eb=Qg,tb=eb;function rb(e,t){var r=e.__data__;return tb(t)?r[typeof t=="string"?"string":"hash"]:r.map}var Oa=rb,nb=Oa;function ib(e){var t=nb(this,e).delete(e);return this.size-=t?1:0,t}var ab=ib,ob=Oa;function ub(e){return ob(this,e).get(e)}var cb=ub,sb=Oa;function lb(e){return sb(this,e).has(e)}var fb=lb,pb=Oa;function hb(e,t){var r=pb(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this}var db=hb,vb=Jg,yb=ab,mb=cb,gb=fb,bb=db;function Wr(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}Wr.prototype.clear=vb;Wr.prototype.delete=yb;Wr.prototype.get=mb;Wr.prototype.has=gb;Wr.prototype.set=bb;var dc=Wr,dh=dc,xb="Expected a function";function vc(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new TypeError(xb);var r=function(){var n=arguments,i=t?t.apply(this,n):n[0],a=r.cache;if(a.has(i))return a.get(i);var o=e.apply(this,n);return r.cache=a.set(i,o)||a,o};return r.cache=new(vc.Cache||dh),r}vc.Cache=dh;var vh=vc;const wb=ue(vh);var Ob=vh,Sb=500;function Ab(e){var t=Ob(e,function(n){return r.size===Sb&&r.clear(),n}),r=t.cache;return t}var _b=Ab,Pb=_b,$b=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Tb=/\\(\\)?/g,Eb=Pb(function(e){var t=[];return e.charCodeAt(0)===46&&t.push(""),e.replace($b,function(r,n,i,a){t.push(i?a.replace(Tb,"$1"):n||r)}),t}),jb=Eb;function Mb(e,t){for(var r=-1,n=e==null?0:e.length,i=Array(n);++r<n;)i[r]=t(e[r],r,e);return i}var yc=Mb,_s=Vn,Cb=yc,Ib=ke,Db=Lr,Ps=_s?_s.prototype:void 0,$s=Ps?Ps.toString:void 0;function yh(e){if(typeof e=="string")return e;if(Ib(e))return Cb(e,yh)+"";if(Db(e))return $s?$s.call(e):"";var t=e+"";return t=="0"&&1/e==-1/0?"-0":t}var kb=yh,Nb=kb;function Lb(e){return e==null?"":Nb(e)}var mh=Lb,Bb=ke,Rb=lc,Fb=jb,Wb=mh;function zb(e,t){return Bb(e)?e:Rb(e,t)?[e]:Fb(Wb(e))}var gh=zb,Ub=Lr;function qb(e){if(typeof e=="string"||Ub(e))return e;var t=e+"";return t=="0"&&1/e==-1/0?"-0":t}var Sa=qb,Hb=gh,Gb=Sa;function Kb(e,t){t=Hb(t,e);for(var r=0,n=t.length;e!=null&&r<n;)e=e[Gb(t[r++])];return r&&r==n?e:void 0}var mc=Kb,Xb=mc;function Vb(e,t,r){var n=e==null?void 0:Xb(e,t);return n===void 0?r:n}var bh=Vb;const He=ue(bh);function Yb(e){return e==null}var Zb=Yb;const Z=ue(Zb);var Jb=wt,Qb=ke,e0=Ot,t0="[object String]";function r0(e){return typeof e=="string"||!Qb(e)&&e0(e)&&Jb(e)==t0}var n0=r0;const Xt=ue(n0);var xh={exports:{}},ne={};/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var gc=Symbol.for("react.element"),bc=Symbol.for("react.portal"),Aa=Symbol.for("react.fragment"),_a=Symbol.for("react.strict_mode"),Pa=Symbol.for("react.profiler"),$a=Symbol.for("react.provider"),Ta=Symbol.for("react.context"),i0=Symbol.for("react.server_context"),Ea=Symbol.for("react.forward_ref"),ja=Symbol.for("react.suspense"),Ma=Symbol.for("react.suspense_list"),Ca=Symbol.for("react.memo"),Ia=Symbol.for("react.lazy"),a0=Symbol.for("react.offscreen"),wh;wh=Symbol.for("react.module.reference");function Xe(e){if(typeof e=="object"&&e!==null){var t=e.$$typeof;switch(t){case gc:switch(e=e.type,e){case Aa:case Pa:case _a:case ja:case Ma:return e;default:switch(e=e&&e.$$typeof,e){case i0:case Ta:case Ea:case Ia:case Ca:case $a:return e;default:return t}}case bc:return t}}}ne.ContextConsumer=Ta;ne.ContextProvider=$a;ne.Element=gc;ne.ForwardRef=Ea;ne.Fragment=Aa;ne.Lazy=Ia;ne.Memo=Ca;ne.Portal=bc;ne.Profiler=Pa;ne.StrictMode=_a;ne.Suspense=ja;ne.SuspenseList=Ma;ne.isAsyncMode=function(){return!1};ne.isConcurrentMode=function(){return!1};ne.isContextConsumer=function(e){return Xe(e)===Ta};ne.isContextProvider=function(e){return Xe(e)===$a};ne.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===gc};ne.isForwardRef=function(e){return Xe(e)===Ea};ne.isFragment=function(e){return Xe(e)===Aa};ne.isLazy=function(e){return Xe(e)===Ia};ne.isMemo=function(e){return Xe(e)===Ca};ne.isPortal=function(e){return Xe(e)===bc};ne.isProfiler=function(e){return Xe(e)===Pa};ne.isStrictMode=function(e){return Xe(e)===_a};ne.isSuspense=function(e){return Xe(e)===ja};ne.isSuspenseList=function(e){return Xe(e)===Ma};ne.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===Aa||e===Pa||e===_a||e===ja||e===Ma||e===a0||typeof e=="object"&&e!==null&&(e.$$typeof===Ia||e.$$typeof===Ca||e.$$typeof===$a||e.$$typeof===Ta||e.$$typeof===Ea||e.$$typeof===wh||e.getModuleId!==void 0)};ne.typeOf=Xe;xh.exports=ne;var o0=xh.exports,u0=wt,c0=Ot,s0="[object Number]";function l0(e){return typeof e=="number"||c0(e)&&u0(e)==s0}var Oh=l0;const f0=ue(Oh);var p0=Oh;function h0(e){return p0(e)&&e!=+e}var d0=h0;const Yn=ue(d0);var rt=function(t){return t===0?0:t>0?1:-1},zt=function(t){return Xt(t)&&t.indexOf("%")===t.length-1},R=function(t){return f0(t)&&!Yn(t)},we=function(t){return R(t)||Xt(t)},v0=0,Zn=function(t){var r=++v0;return"".concat(t||"").concat(r)},Vt=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!R(t)&&!Xt(t))return n;var a;if(zt(t)){var o=t.indexOf("%");a=r*parseFloat(t.slice(0,o))/100}else a=+t;return Yn(a)&&(a=n),i&&a>r&&(a=r),a},Pt=function(t){if(!t)return null;var r=Object.keys(t);return r&&r.length?t[r[0]]:null},y0=function(t){if(!Array.isArray(t))return!1;for(var r=t.length,n={},i=0;i<r;i++)if(!n[t[i]])n[t[i]]=!0;else return!0;return!1},et=function(t,r){return R(t)&&R(r)?function(n){return t+n*(r-t)}:function(){return r}};function xi(e,t,r){return!e||!e.length?null:e.find(function(n){return n&&(typeof t=="function"?t(n):He(n,t))===r})}var m0=function(t,r){return R(t)&&R(r)?t-r:Xt(t)&&Xt(r)?t.localeCompare(r):t instanceof Date&&r instanceof Date?t.getTime()-r.getTime():String(t).localeCompare(String(r))};function fr(e,t){for(var r in e)if({}.hasOwnProperty.call(e,r)&&(!{}.hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if({}.hasOwnProperty.call(t,n)&&!{}.hasOwnProperty.call(e,n))return!1;return!0}function Ro(e){"@babel/helpers - typeof";return Ro=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ro(e)}var g0=["viewBox","children"],b0=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],Ts=["points","pathLength"],fo={svg:g0,polygon:Ts,polyline:Ts},xc=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],wi=function(t,r){if(!t||typeof t=="function"||typeof t=="boolean")return null;var n=t;if(B.isValidElement(t)&&(n=t.props),!Br(n))return null;var i={};return Object.keys(n).forEach(function(a){xc.includes(a)&&(i[a]=r||function(o){return n[a](n,o)})}),i},x0=function(t,r,n){return function(i){return t(r,n,i),null}},Oi=function(t,r,n){if(!Br(t)||Ro(t)!=="object")return null;var i=null;return Object.keys(t).forEach(function(a){var o=t[a];xc.includes(a)&&typeof o=="function"&&(i||(i={}),i[a]=x0(o,r,n))}),i},w0=["children"],O0=["children"];function Es(e,t){if(e==null)return{};var r=S0(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function S0(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Fo(e){"@babel/helpers - typeof";return Fo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fo(e)}var js={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},dt=function(t){return typeof t=="string"?t:t?t.displayName||t.name||"Component":""},Ms=null,po=null,wc=function e(t){if(t===Ms&&Array.isArray(po))return po;var r=[];return B.Children.forEach(t,function(n){Z(n)||(o0.isFragment(n)?r=r.concat(e(n.props.children)):r.push(n))}),po=r,Ms=t,r};function Ge(e,t){var r=[],n=[];return Array.isArray(t)?n=t.map(function(i){return dt(i)}):n=[dt(t)],wc(e).forEach(function(i){var a=He(i,"type.displayName")||He(i,"type.name");n.indexOf(a)!==-1&&r.push(i)}),r}function Re(e,t){var r=Ge(e,t);return r&&r[0]}var Cs=function(t){if(!t||!t.props)return!1;var r=t.props,n=r.width,i=r.height;return!(!R(n)||n<=0||!R(i)||i<=0)},A0=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],_0=function(t){return t&&t.type&&Xt(t.type)&&A0.indexOf(t.type)>=0},P0=function(t){return t&&Fo(t)==="object"&&"clipDot"in t},$0=function(t,r,n,i){var a,o=(a=fo==null?void 0:fo[i])!==null&&a!==void 0?a:[];return r.startsWith("data-")||!Y(t)&&(i&&o.includes(r)||b0.includes(r))||n&&xc.includes(r)},J=function(t,r,n){if(!t||typeof t=="function"||typeof t=="boolean")return null;var i=t;if(B.isValidElement(t)&&(i=t.props),!Br(i))return null;var a={};return Object.keys(i).forEach(function(o){var u;$0((u=i)===null||u===void 0?void 0:u[o],o,r,n)&&(a[o]=i[o])}),a},Wo=function e(t,r){if(t===r)return!0;var n=B.Children.count(t);if(n!==B.Children.count(r))return!1;if(n===0)return!0;if(n===1)return Is(Array.isArray(t)?t[0]:t,Array.isArray(r)?r[0]:r);for(var i=0;i<n;i++){var a=t[i],o=r[i];if(Array.isArray(a)||Array.isArray(o)){if(!e(a,o))return!1}else if(!Is(a,o))return!1}return!0},Is=function(t,r){if(Z(t)&&Z(r))return!0;if(!Z(t)&&!Z(r)){var n=t.props||{},i=n.children,a=Es(n,w0),o=r.props||{},u=o.children,c=Es(o,O0);return i&&u?fr(a,c)&&Wo(i,u):!i&&!u?fr(a,c):!1}return!1},Ds=function(t,r){var n=[],i={};return wc(t).forEach(function(a,o){if(_0(a))n.push(a);else if(a){var u=dt(a.type),c=r[u]||{},s=c.handler,f=c.once;if(s&&(!f||!i[u])){var l=s(a,u,o);n.push(l),i[u]=!0}}}),n},T0=function(t){var r=t&&t.type;return r&&js[r]?js[r]:null},E0=function(t,r){return wc(r).indexOf(t)},j0=["children","width","height","viewBox","className","style","title","desc"];function zo(){return zo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},zo.apply(this,arguments)}function M0(e,t){if(e==null)return{};var r=C0(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function C0(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Uo(e){var t=e.children,r=e.width,n=e.height,i=e.viewBox,a=e.className,o=e.style,u=e.title,c=e.desc,s=M0(e,j0),f=i||{width:r,height:n,x:0,y:0},l=ee("recharts-surface",a);return _.createElement("svg",zo({},J(s,!0,"svg"),{className:l,width:r,height:n,style:o,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),_.createElement("title",null,u),_.createElement("desc",null,c),t)}var I0=["children","className"];function qo(){return qo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},qo.apply(this,arguments)}function D0(e,t){if(e==null)return{};var r=k0(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function k0(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var de=_.forwardRef(function(e,t){var r=e.children,n=e.className,i=D0(e,I0),a=ee("recharts-layer",n);return _.createElement("g",qo({className:a},J(i,!0),{ref:t}),r)}),vt=function(t,r){for(var n=arguments.length,i=new Array(n>2?n-2:0),a=2;a<n;a++)i[a-2]=arguments[a]};function N0(e,t,r){var n=-1,i=e.length;t<0&&(t=-t>i?0:i+t),r=r>i?i:r,r<0&&(r+=i),i=t>r?0:r-t>>>0,t>>>=0;for(var a=Array(i);++n<i;)a[n]=e[n+t];return a}var L0=N0,B0=L0;function R0(e,t,r){var n=e.length;return r=r===void 0?n:r,!t&&r>=n?e:B0(e,t,r)}var F0=R0,W0="\\ud800-\\udfff",z0="\\u0300-\\u036f",U0="\\ufe20-\\ufe2f",q0="\\u20d0-\\u20ff",H0=z0+U0+q0,G0="\\ufe0e\\ufe0f",K0="\\u200d",X0=RegExp("["+K0+W0+H0+G0+"]");function V0(e){return X0.test(e)}var Sh=V0;function Y0(e){return e.split("")}var Z0=Y0,Ah="\\ud800-\\udfff",J0="\\u0300-\\u036f",Q0="\\ufe20-\\ufe2f",ex="\\u20d0-\\u20ff",tx=J0+Q0+ex,rx="\\ufe0e\\ufe0f",nx="["+Ah+"]",Ho="["+tx+"]",Go="\\ud83c[\\udffb-\\udfff]",ix="(?:"+Ho+"|"+Go+")",_h="[^"+Ah+"]",Ph="(?:\\ud83c[\\udde6-\\uddff]){2}",$h="[\\ud800-\\udbff][\\udc00-\\udfff]",ax="\\u200d",Th=ix+"?",Eh="["+rx+"]?",ox="(?:"+ax+"(?:"+[_h,Ph,$h].join("|")+")"+Eh+Th+")*",ux=Eh+Th+ox,cx="(?:"+[_h+Ho+"?",Ho,Ph,$h,nx].join("|")+")",sx=RegExp(Go+"(?="+Go+")|"+cx+ux,"g");function lx(e){return e.match(sx)||[]}var fx=lx,px=Z0,hx=Sh,dx=fx;function vx(e){return hx(e)?dx(e):px(e)}var yx=vx,mx=F0,gx=Sh,bx=yx,xx=mh;function wx(e){return function(t){t=xx(t);var r=gx(t)?bx(t):void 0,n=r?r[0]:t.charAt(0),i=r?mx(r,1).join(""):t.slice(1);return n[e]()+i}}var Ox=wx,Sx=Ox,Ax=Sx("toUpperCase"),_x=Ax;const Da=ue(_x);function oe(e){return function(){return e}}const jh=Math.cos,Si=Math.sin,nt=Math.sqrt,Ai=Math.PI,ka=2*Ai,Ko=Math.PI,Xo=2*Ko,Rt=1e-6,Px=Xo-Rt;function Mh(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}function $x(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return Mh;const r=10**t;return function(n){this._+=n[0];for(let i=1,a=n.length;i<a;++i)this._+=Math.round(arguments[i]*r)/r+n[i]}}class Tx{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=t==null?Mh:$x(t)}moveTo(t,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,r){this._append`L${this._x1=+t},${this._y1=+r}`}quadraticCurveTo(t,r,n,i){this._append`Q${+t},${+r},${this._x1=+n},${this._y1=+i}`}bezierCurveTo(t,r,n,i,a,o){this._append`C${+t},${+r},${+n},${+i},${this._x1=+a},${this._y1=+o}`}arcTo(t,r,n,i,a){if(t=+t,r=+r,n=+n,i=+i,a=+a,a<0)throw new Error(`negative radius: ${a}`);let o=this._x1,u=this._y1,c=n-t,s=i-r,f=o-t,l=u-r,p=f*f+l*l;if(this._x1===null)this._append`M${this._x1=t},${this._y1=r}`;else if(p>Rt)if(!(Math.abs(l*c-s*f)>Rt)||!a)this._append`L${this._x1=t},${this._y1=r}`;else{let h=n-o,d=i-u,v=c*c+s*s,y=h*h+d*d,b=Math.sqrt(v),w=Math.sqrt(p),x=a*Math.tan((Ko-Math.acos((v+p-y)/(2*b*w)))/2),S=x/w,m=x/b;Math.abs(S-1)>Rt&&this._append`L${t+S*f},${r+S*l}`,this._append`A${a},${a},0,0,${+(l*h>f*d)},${this._x1=t+m*c},${this._y1=r+m*s}`}}arc(t,r,n,i,a,o){if(t=+t,r=+r,n=+n,o=!!o,n<0)throw new Error(`negative radius: ${n}`);let u=n*Math.cos(i),c=n*Math.sin(i),s=t+u,f=r+c,l=1^o,p=o?i-a:a-i;this._x1===null?this._append`M${s},${f}`:(Math.abs(this._x1-s)>Rt||Math.abs(this._y1-f)>Rt)&&this._append`L${s},${f}`,n&&(p<0&&(p=p%Xo+Xo),p>Px?this._append`A${n},${n},0,1,${l},${t-u},${r-c}A${n},${n},0,1,${l},${this._x1=s},${this._y1=f}`:p>Rt&&this._append`A${n},${n},0,${+(p>=Ko)},${l},${this._x1=t+n*Math.cos(a)},${this._y1=r+n*Math.sin(a)}`)}rect(t,r,n,i){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}h${n=+n}v${+i}h${-n}Z`}toString(){return this._}}function Oc(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(r==null)t=null;else{const n=Math.floor(r);if(!(n>=0))throw new RangeError(`invalid digits: ${r}`);t=n}return e},()=>new Tx(t)}function Sc(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function Ch(e){this._context=e}Ch.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t);break}}};function Na(e){return new Ch(e)}function Ih(e){return e[0]}function Dh(e){return e[1]}function kh(e,t){var r=oe(!0),n=null,i=Na,a=null,o=Oc(u);e=typeof e=="function"?e:e===void 0?Ih:oe(e),t=typeof t=="function"?t:t===void 0?Dh:oe(t);function u(c){var s,f=(c=Sc(c)).length,l,p=!1,h;for(n==null&&(a=i(h=o())),s=0;s<=f;++s)!(s<f&&r(l=c[s],s,c))===p&&((p=!p)?a.lineStart():a.lineEnd()),p&&a.point(+e(l,s,c),+t(l,s,c));if(h)return a=null,h+""||null}return u.x=function(c){return arguments.length?(e=typeof c=="function"?c:oe(+c),u):e},u.y=function(c){return arguments.length?(t=typeof c=="function"?c:oe(+c),u):t},u.defined=function(c){return arguments.length?(r=typeof c=="function"?c:oe(!!c),u):r},u.curve=function(c){return arguments.length?(i=c,n!=null&&(a=i(n)),u):i},u.context=function(c){return arguments.length?(c==null?n=a=null:a=i(n=c),u):n},u}function oi(e,t,r){var n=null,i=oe(!0),a=null,o=Na,u=null,c=Oc(s);e=typeof e=="function"?e:e===void 0?Ih:oe(+e),t=typeof t=="function"?t:oe(t===void 0?0:+t),r=typeof r=="function"?r:r===void 0?Dh:oe(+r);function s(l){var p,h,d,v=(l=Sc(l)).length,y,b=!1,w,x=new Array(v),S=new Array(v);for(a==null&&(u=o(w=c())),p=0;p<=v;++p){if(!(p<v&&i(y=l[p],p,l))===b)if(b=!b)h=p,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),d=p-1;d>=h;--d)u.point(x[d],S[d]);u.lineEnd(),u.areaEnd()}b&&(x[p]=+e(y,p,l),S[p]=+t(y,p,l),u.point(n?+n(y,p,l):x[p],r?+r(y,p,l):S[p]))}if(w)return u=null,w+""||null}function f(){return kh().defined(i).curve(o).context(a)}return s.x=function(l){return arguments.length?(e=typeof l=="function"?l:oe(+l),n=null,s):e},s.x0=function(l){return arguments.length?(e=typeof l=="function"?l:oe(+l),s):e},s.x1=function(l){return arguments.length?(n=l==null?null:typeof l=="function"?l:oe(+l),s):n},s.y=function(l){return arguments.length?(t=typeof l=="function"?l:oe(+l),r=null,s):t},s.y0=function(l){return arguments.length?(t=typeof l=="function"?l:oe(+l),s):t},s.y1=function(l){return arguments.length?(r=l==null?null:typeof l=="function"?l:oe(+l),s):r},s.lineX0=s.lineY0=function(){return f().x(e).y(t)},s.lineY1=function(){return f().x(e).y(r)},s.lineX1=function(){return f().x(n).y(t)},s.defined=function(l){return arguments.length?(i=typeof l=="function"?l:oe(!!l),s):i},s.curve=function(l){return arguments.length?(o=l,a!=null&&(u=o(a)),s):o},s.context=function(l){return arguments.length?(l==null?a=u=null:u=o(a=l),s):a},s}class Nh{constructor(t,r){this._context=t,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,r){switch(t=+t,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,r):this._context.moveTo(t,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,r,t,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,t,this._y0,t,r);break}}this._x0=t,this._y0=r}}function Ex(e){return new Nh(e,!0)}function jx(e){return new Nh(e,!1)}const Ac={draw(e,t){const r=nt(t/Ai);e.moveTo(r,0),e.arc(0,0,r,0,ka)}},Mx={draw(e,t){const r=nt(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},Lh=nt(1/3),Cx=Lh*2,Ix={draw(e,t){const r=nt(t/Cx),n=r*Lh;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},Dx={draw(e,t){const r=nt(t),n=-r/2;e.rect(n,n,r,r)}},kx=.8908130915292852,Bh=Si(Ai/10)/Si(7*Ai/10),Nx=Si(ka/10)*Bh,Lx=-jh(ka/10)*Bh,Bx={draw(e,t){const r=nt(t*kx),n=Nx*r,i=Lx*r;e.moveTo(0,-r),e.lineTo(n,i);for(let a=1;a<5;++a){const o=ka*a/5,u=jh(o),c=Si(o);e.lineTo(c*r,-u*r),e.lineTo(u*n-c*i,c*n+u*i)}e.closePath()}},ho=nt(3),Rx={draw(e,t){const r=-nt(t/(ho*3));e.moveTo(0,r*2),e.lineTo(-ho*r,-r),e.lineTo(ho*r,-r),e.closePath()}},ze=-.5,Ue=nt(3)/2,Vo=1/nt(12),Fx=(Vo/2+1)*3,Wx={draw(e,t){const r=nt(t/Fx),n=r/2,i=r*Vo,a=n,o=r*Vo+r,u=-a,c=o;e.moveTo(n,i),e.lineTo(a,o),e.lineTo(u,c),e.lineTo(ze*n-Ue*i,Ue*n+ze*i),e.lineTo(ze*a-Ue*o,Ue*a+ze*o),e.lineTo(ze*u-Ue*c,Ue*u+ze*c),e.lineTo(ze*n+Ue*i,ze*i-Ue*n),e.lineTo(ze*a+Ue*o,ze*o-Ue*a),e.lineTo(ze*u+Ue*c,ze*c-Ue*u),e.closePath()}};function zx(e,t){let r=null,n=Oc(i);e=typeof e=="function"?e:oe(e||Ac),t=typeof t=="function"?t:oe(t===void 0?64:+t);function i(){let a;if(r||(r=a=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),a)return r=null,a+""||null}return i.type=function(a){return arguments.length?(e=typeof a=="function"?a:oe(a),i):e},i.size=function(a){return arguments.length?(t=typeof a=="function"?a:oe(+a),i):t},i.context=function(a){return arguments.length?(r=a??null,i):r},i}function _i(){}function Pi(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function Rh(e){this._context=e}Rh.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:Pi(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:Pi(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function Ux(e){return new Rh(e)}function Fh(e){this._context=e}Fh.prototype={areaStart:_i,areaEnd:_i,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:Pi(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function qx(e){return new Fh(e)}function Wh(e){this._context=e}Wh.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:Pi(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function Hx(e){return new Wh(e)}function zh(e){this._context=e}zh.prototype={areaStart:_i,areaEnd:_i,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function Gx(e){return new zh(e)}function ks(e){return e<0?-1:1}function Ns(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0),u=(a*i+o*n)/(n+i);return(ks(a)+ks(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(u))||0}function Ls(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function vo(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,u=(a-n)/3;e._context.bezierCurveTo(n+u,i+u*t,a-u,o-u*r,a,o)}function $i(e){this._context=e}$i.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:vo(this,this._t0,Ls(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(e=+e,t=+t,!(e===this._x1&&t===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,vo(this,Ls(this,r=Ns(this,e,t)),r);break;default:vo(this,this._t0,r=Ns(this,e,t));break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}};function Uh(e){this._context=new qh(e)}(Uh.prototype=Object.create($i.prototype)).point=function(e,t){$i.prototype.point.call(this,t,e)};function qh(e){this._context=e}qh.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}};function Kx(e){return new $i(e)}function Xx(e){return new Uh(e)}function Hh(e){this._context=e}Hh.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),r===2)this._context.lineTo(e[1],t[1]);else for(var n=Bs(e),i=Bs(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||this._line!==0&&r===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}};function Bs(e){var t,r=e.length-1,n,i=new Array(r),a=new Array(r),o=new Array(r);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<r-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[r-1]=2,a[r-1]=7,o[r-1]=8*e[r-1]+e[r],t=1;t<r;++t)n=i[t]/a[t-1],a[t]-=n,o[t]-=n*o[t-1];for(i[r-1]=o[r-1]/a[r-1],t=r-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(a[r-1]=(e[r]+i[r-1])/2,t=0;t<r-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function Vx(e){return new Hh(e)}function La(e,t){this._context=e,this._t=t}La.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}break}}this._x=e,this._y=t}};function Yx(e){return new La(e,.5)}function Zx(e){return new La(e,0)}function Jx(e){return new La(e,1)}function vr(e,t){if((o=e.length)>1)for(var r=1,n,i,a=e[t[0]],o,u=a.length;r<o;++r)for(i=a,a=e[t[r]],n=0;n<u;++n)a[n][1]+=a[n][0]=isNaN(i[n][1])?i[n][0]:i[n][1]}function Yo(e){for(var t=e.length,r=new Array(t);--t>=0;)r[t]=t;return r}function Qx(e,t){return e[t]}function ew(e){const t=[];return t.key=e,t}function tw(){var e=oe([]),t=Yo,r=vr,n=Qx;function i(a){var o=Array.from(e.apply(this,arguments),ew),u,c=o.length,s=-1,f;for(const l of a)for(u=0,++s;u<c;++u)(o[u][s]=[0,+n(l,o[u].key,s,a)]).data=l;for(u=0,f=Sc(t(o));u<c;++u)o[f[u]].index=u;return r(o,f),o}return i.keys=function(a){return arguments.length?(e=typeof a=="function"?a:oe(Array.from(a)),i):e},i.value=function(a){return arguments.length?(n=typeof a=="function"?a:oe(+a),i):n},i.order=function(a){return arguments.length?(t=a==null?Yo:typeof a=="function"?a:oe(Array.from(a)),i):t},i.offset=function(a){return arguments.length?(r=a??vr,i):r},i}function rw(e,t){if((n=e.length)>0){for(var r,n,i=0,a=e[0].length,o;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}vr(e,t)}}function nw(e,t){if((i=e.length)>0){for(var r=0,n=e[t[0]],i,a=n.length;r<a;++r){for(var o=0,u=0;o<i;++o)u+=e[o][r][1]||0;n[r][1]+=n[r][0]=-u/2}vr(e,t)}}function iw(e,t){if(!(!((o=e.length)>0)||!((a=(i=e[t[0]]).length)>0))){for(var r=0,n=1,i,a,o;n<a;++n){for(var u=0,c=0,s=0;u<o;++u){for(var f=e[t[u]],l=f[n][1]||0,p=f[n-1][1]||0,h=(l-p)/2,d=0;d<u;++d){var v=e[t[d]],y=v[n][1]||0,b=v[n-1][1]||0;h+=y-b}c+=l,s+=h*l}i[n-1][1]+=i[n-1][0]=r,c&&(r-=s/c)}i[n-1][1]+=i[n-1][0]=r,vr(e,t)}}function dn(e){"@babel/helpers - typeof";return dn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},dn(e)}var aw=["type","size","sizeType"];function Zo(){return Zo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Zo.apply(this,arguments)}function Rs(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Fs(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Rs(Object(r),!0).forEach(function(n){ow(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Rs(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ow(e,t,r){return t=uw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function uw(e){var t=cw(e,"string");return dn(t)=="symbol"?t:t+""}function cw(e,t){if(dn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(dn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function sw(e,t){if(e==null)return{};var r=lw(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function lw(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var Gh={symbolCircle:Ac,symbolCross:Mx,symbolDiamond:Ix,symbolSquare:Dx,symbolStar:Bx,symbolTriangle:Rx,symbolWye:Wx},fw=Math.PI/180,pw=function(t){var r="symbol".concat(Da(t));return Gh[r]||Ac},hw=function(t,r,n){if(r==="area")return t;switch(n){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":{var i=18*fw;return 1.25*t*t*(Math.tan(i)-Math.tan(i*2)*Math.pow(Math.tan(i),2))}case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},dw=function(t,r){Gh["symbol".concat(Da(t))]=r},_c=function(t){var r=t.type,n=r===void 0?"circle":r,i=t.size,a=i===void 0?64:i,o=t.sizeType,u=o===void 0?"area":o,c=sw(t,aw),s=Fs(Fs({},c),{},{type:n,size:a,sizeType:u}),f=function(){var y=pw(n),b=zx().type(y).size(hw(a,u,n));return b()},l=s.className,p=s.cx,h=s.cy,d=J(s,!0);return p===+p&&h===+h&&a===+a?_.createElement("path",Zo({},d,{className:ee("recharts-symbols",l),transform:"translate(".concat(p,", ").concat(h,")"),d:f()})):null};_c.registerSymbol=dw;function yr(e){"@babel/helpers - typeof";return yr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},yr(e)}function Jo(){return Jo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Jo.apply(this,arguments)}function Ws(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function vw(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ws(Object(r),!0).forEach(function(n){vn(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ws(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function yw(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function mw(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Xh(n.key),n)}}function gw(e,t,r){return t&&mw(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function bw(e,t,r){return t=Ti(t),xw(e,Kh()?Reflect.construct(t,r||[],Ti(e).constructor):t.apply(e,r))}function xw(e,t){if(t&&(yr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return ww(e)}function ww(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Kh(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Kh=function(){return!!e})()}function Ti(e){return Ti=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ti(e)}function Ow(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Qo(e,t)}function Qo(e,t){return Qo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Qo(e,t)}function vn(e,t,r){return t=Xh(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Xh(e){var t=Sw(e,"string");return yr(t)=="symbol"?t:t+""}function Sw(e,t){if(yr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(yr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Ye=32,Pc=function(e){function t(){return yw(this,t),bw(this,t,arguments)}return Ow(t,e),gw(t,[{key:"renderIcon",value:function(n){var i=this.props.inactiveColor,a=Ye/2,o=Ye/6,u=Ye/3,c=n.inactive?i:n.color;if(n.type==="plainline")return _.createElement("line",{strokeWidth:4,fill:"none",stroke:c,strokeDasharray:n.payload.strokeDasharray,x1:0,y1:a,x2:Ye,y2:a,className:"recharts-legend-icon"});if(n.type==="line")return _.createElement("path",{strokeWidth:4,fill:"none",stroke:c,d:"M0,".concat(a,"h").concat(u,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(2*u,",").concat(a,`
            H`).concat(Ye,"M").concat(2*u,",").concat(a,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(u,",").concat(a),className:"recharts-legend-icon"});if(n.type==="rect")return _.createElement("path",{stroke:"none",fill:c,d:"M0,".concat(Ye/8,"h").concat(Ye,"v").concat(Ye*3/4,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(_.isValidElement(n.legendIcon)){var s=vw({},n);return delete s.legendIcon,_.cloneElement(n.legendIcon,s)}return _.createElement(_c,{fill:c,cx:a,cy:a,size:Ye,sizeType:"diameter",type:n.type})}},{key:"renderItems",value:function(){var n=this,i=this.props,a=i.payload,o=i.iconSize,u=i.layout,c=i.formatter,s=i.inactiveColor,f={x:0,y:0,width:Ye,height:Ye},l={display:u==="horizontal"?"inline-block":"block",marginRight:10},p={display:"inline-block",verticalAlign:"middle",marginRight:4};return a.map(function(h,d){var v=h.formatter||c,y=ee(vn(vn({"recharts-legend-item":!0},"legend-item-".concat(d),!0),"inactive",h.inactive));if(h.type==="none")return null;var b=Y(h.value)?null:h.value;vt(!Y(h.value),`The name property is also required when using a function for the dataKey of a chart's cartesian components. Ex: <Bar name="Name of my Data"/>`);var w=h.inactive?s:h.color;return _.createElement("li",Jo({className:y,style:l,key:"legend-item-".concat(d)},Oi(n.props,h,d)),_.createElement(Uo,{width:o,height:o,viewBox:f,style:p},n.renderIcon(h)),_.createElement("span",{className:"recharts-legend-item-text",style:{color:w}},v?v(b,h,d):b))})}},{key:"render",value:function(){var n=this.props,i=n.payload,a=n.layout,o=n.align;if(!i||!i.length)return null;var u={padding:0,margin:0,textAlign:a==="horizontal"?o:"left"};return _.createElement("ul",{className:"recharts-default-legend",style:u},this.renderItems())}}])}(B.PureComponent);vn(Pc,"displayName","Legend");vn(Pc,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var Aw=wa;function _w(){this.__data__=new Aw,this.size=0}var Pw=_w;function $w(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}var Tw=$w;function Ew(e){return this.__data__.get(e)}var jw=Ew;function Mw(e){return this.__data__.has(e)}var Cw=Mw,Iw=wa,Dw=hc,kw=dc,Nw=200;function Lw(e,t){var r=this.__data__;if(r instanceof Iw){var n=r.__data__;if(!Dw||n.length<Nw-1)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new kw(n)}return r.set(e,t),this.size=r.size,this}var Bw=Lw,Rw=wa,Fw=Pw,Ww=Tw,zw=jw,Uw=Cw,qw=Bw;function zr(e){var t=this.__data__=new Rw(e);this.size=t.size}zr.prototype.clear=Fw;zr.prototype.delete=Ww;zr.prototype.get=zw;zr.prototype.has=Uw;zr.prototype.set=qw;var Vh=zr,Hw="__lodash_hash_undefined__";function Gw(e){return this.__data__.set(e,Hw),this}var Kw=Gw;function Xw(e){return this.__data__.has(e)}var Vw=Xw,Yw=dc,Zw=Kw,Jw=Vw;function Ei(e){var t=-1,r=e==null?0:e.length;for(this.__data__=new Yw;++t<r;)this.add(e[t])}Ei.prototype.add=Ei.prototype.push=Zw;Ei.prototype.has=Jw;var Yh=Ei;function Qw(e,t){for(var r=-1,n=e==null?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1}var Zh=Qw;function eO(e,t){return e.has(t)}var Jh=eO,tO=Yh,rO=Zh,nO=Jh,iO=1,aO=2;function oO(e,t,r,n,i,a){var o=r&iO,u=e.length,c=t.length;if(u!=c&&!(o&&c>u))return!1;var s=a.get(e),f=a.get(t);if(s&&f)return s==t&&f==e;var l=-1,p=!0,h=r&aO?new tO:void 0;for(a.set(e,t),a.set(t,e);++l<u;){var d=e[l],v=t[l];if(n)var y=o?n(v,d,l,t,e,a):n(d,v,l,e,t,a);if(y!==void 0){if(y)continue;p=!1;break}if(h){if(!rO(t,function(b,w){if(!nO(h,w)&&(d===b||i(d,b,r,n,a)))return h.push(w)})){p=!1;break}}else if(!(d===v||i(d,v,r,n,a))){p=!1;break}}return a.delete(e),a.delete(t),p}var Qh=oO,uO=ct,cO=uO.Uint8Array,sO=cO;function lO(e){var t=-1,r=Array(e.size);return e.forEach(function(n,i){r[++t]=[i,n]}),r}var fO=lO;function pO(e){var t=-1,r=Array(e.size);return e.forEach(function(n){r[++t]=n}),r}var $c=pO,zs=Vn,Us=sO,hO=pc,dO=Qh,vO=fO,yO=$c,mO=1,gO=2,bO="[object Boolean]",xO="[object Date]",wO="[object Error]",OO="[object Map]",SO="[object Number]",AO="[object RegExp]",_O="[object Set]",PO="[object String]",$O="[object Symbol]",TO="[object ArrayBuffer]",EO="[object DataView]",qs=zs?zs.prototype:void 0,yo=qs?qs.valueOf:void 0;function jO(e,t,r,n,i,a,o){switch(r){case EO:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case TO:return!(e.byteLength!=t.byteLength||!a(new Us(e),new Us(t)));case bO:case xO:case SO:return hO(+e,+t);case wO:return e.name==t.name&&e.message==t.message;case AO:case PO:return e==t+"";case OO:var u=vO;case _O:var c=n&mO;if(u||(u=yO),e.size!=t.size&&!c)return!1;var s=o.get(e);if(s)return s==t;n|=gO,o.set(e,t);var f=dO(u(e),u(t),n,i,a,o);return o.delete(e),f;case $O:if(yo)return yo.call(e)==yo.call(t)}return!1}var MO=jO;function CO(e,t){for(var r=-1,n=t.length,i=e.length;++r<n;)e[i+r]=t[r];return e}var ed=CO,IO=ed,DO=ke;function kO(e,t,r){var n=t(e);return DO(e)?n:IO(n,r(e))}var NO=kO;function LO(e,t){for(var r=-1,n=e==null?0:e.length,i=0,a=[];++r<n;){var o=e[r];t(o,r,e)&&(a[i++]=o)}return a}var BO=LO;function RO(){return[]}var FO=RO,WO=BO,zO=FO,UO=Object.prototype,qO=UO.propertyIsEnumerable,Hs=Object.getOwnPropertySymbols,HO=Hs?function(e){return e==null?[]:(e=Object(e),WO(Hs(e),function(t){return qO.call(e,t)}))}:zO,GO=HO;function KO(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}var XO=KO,VO=wt,YO=Ot,ZO="[object Arguments]";function JO(e){return YO(e)&&VO(e)==ZO}var QO=JO,Gs=QO,e1=Ot,td=Object.prototype,t1=td.hasOwnProperty,r1=td.propertyIsEnumerable,n1=Gs(function(){return arguments}())?Gs:function(e){return e1(e)&&t1.call(e,"callee")&&!r1.call(e,"callee")},Tc=n1,ji={exports:{}};function i1(){return!1}var a1=i1;ji.exports;(function(e,t){var r=ct,n=a1,i=t&&!t.nodeType&&t,a=i&&!0&&e&&!e.nodeType&&e,o=a&&a.exports===i,u=o?r.Buffer:void 0,c=u?u.isBuffer:void 0,s=c||n;e.exports=s})(ji,ji.exports);var rd=ji.exports,o1=9007199254740991,u1=/^(?:0|[1-9]\d*)$/;function c1(e,t){var r=typeof e;return t=t??o1,!!t&&(r=="number"||r!="symbol"&&u1.test(e))&&e>-1&&e%1==0&&e<t}var Ec=c1,s1=9007199254740991;function l1(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=s1}var jc=l1,f1=wt,p1=jc,h1=Ot,d1="[object Arguments]",v1="[object Array]",y1="[object Boolean]",m1="[object Date]",g1="[object Error]",b1="[object Function]",x1="[object Map]",w1="[object Number]",O1="[object Object]",S1="[object RegExp]",A1="[object Set]",_1="[object String]",P1="[object WeakMap]",$1="[object ArrayBuffer]",T1="[object DataView]",E1="[object Float32Array]",j1="[object Float64Array]",M1="[object Int8Array]",C1="[object Int16Array]",I1="[object Int32Array]",D1="[object Uint8Array]",k1="[object Uint8ClampedArray]",N1="[object Uint16Array]",L1="[object Uint32Array]",se={};se[E1]=se[j1]=se[M1]=se[C1]=se[I1]=se[D1]=se[k1]=se[N1]=se[L1]=!0;se[d1]=se[v1]=se[$1]=se[y1]=se[T1]=se[m1]=se[g1]=se[b1]=se[x1]=se[w1]=se[O1]=se[S1]=se[A1]=se[_1]=se[P1]=!1;function B1(e){return h1(e)&&p1(e.length)&&!!se[f1(e)]}var R1=B1;function F1(e){return function(t){return e(t)}}var nd=F1,Mi={exports:{}};Mi.exports;(function(e,t){var r=fh,n=t&&!t.nodeType&&t,i=n&&!0&&e&&!e.nodeType&&e,a=i&&i.exports===n,o=a&&r.process,u=function(){try{var c=i&&i.require&&i.require("util").types;return c||o&&o.binding&&o.binding("util")}catch{}}();e.exports=u})(Mi,Mi.exports);var W1=Mi.exports,z1=R1,U1=nd,Ks=W1,Xs=Ks&&Ks.isTypedArray,q1=Xs?U1(Xs):z1,id=q1,H1=XO,G1=Tc,K1=ke,X1=rd,V1=Ec,Y1=id,Z1=Object.prototype,J1=Z1.hasOwnProperty;function Q1(e,t){var r=K1(e),n=!r&&G1(e),i=!r&&!n&&X1(e),a=!r&&!n&&!i&&Y1(e),o=r||n||i||a,u=o?H1(e.length,String):[],c=u.length;for(var s in e)(t||J1.call(e,s))&&!(o&&(s=="length"||i&&(s=="offset"||s=="parent")||a&&(s=="buffer"||s=="byteLength"||s=="byteOffset")||V1(s,c)))&&u.push(s);return u}var eS=Q1,tS=Object.prototype;function rS(e){var t=e&&e.constructor,r=typeof t=="function"&&t.prototype||tS;return e===r}var nS=rS;function iS(e,t){return function(r){return e(t(r))}}var ad=iS,aS=ad,oS=aS(Object.keys,Object),uS=oS,cS=nS,sS=uS,lS=Object.prototype,fS=lS.hasOwnProperty;function pS(e){if(!cS(e))return sS(e);var t=[];for(var r in Object(e))fS.call(e,r)&&r!="constructor"&&t.push(r);return t}var hS=pS,dS=fc,vS=jc;function yS(e){return e!=null&&vS(e.length)&&!dS(e)}var Jn=yS,mS=eS,gS=hS,bS=Jn;function xS(e){return bS(e)?mS(e):gS(e)}var Ba=xS,wS=NO,OS=GO,SS=Ba;function AS(e){return wS(e,SS,OS)}var _S=AS,Vs=_S,PS=1,$S=Object.prototype,TS=$S.hasOwnProperty;function ES(e,t,r,n,i,a){var o=r&PS,u=Vs(e),c=u.length,s=Vs(t),f=s.length;if(c!=f&&!o)return!1;for(var l=c;l--;){var p=u[l];if(!(o?p in t:TS.call(t,p)))return!1}var h=a.get(e),d=a.get(t);if(h&&d)return h==t&&d==e;var v=!0;a.set(e,t),a.set(t,e);for(var y=o;++l<c;){p=u[l];var b=e[p],w=t[p];if(n)var x=o?n(w,b,p,t,e,a):n(b,w,p,e,t,a);if(!(x===void 0?b===w||i(b,w,r,n,a):x)){v=!1;break}y||(y=p=="constructor")}if(v&&!y){var S=e.constructor,m=t.constructor;S!=m&&"constructor"in e&&"constructor"in t&&!(typeof S=="function"&&S instanceof S&&typeof m=="function"&&m instanceof m)&&(v=!1)}return a.delete(e),a.delete(t),v}var jS=ES,MS=Qt,CS=ct,IS=MS(CS,"DataView"),DS=IS,kS=Qt,NS=ct,LS=kS(NS,"Promise"),BS=LS,RS=Qt,FS=ct,WS=RS(FS,"Set"),od=WS,zS=Qt,US=ct,qS=zS(US,"WeakMap"),HS=qS,eu=DS,tu=hc,ru=BS,nu=od,iu=HS,ud=wt,Ur=hh,Ys="[object Map]",GS="[object Object]",Zs="[object Promise]",Js="[object Set]",Qs="[object WeakMap]",el="[object DataView]",KS=Ur(eu),XS=Ur(tu),VS=Ur(ru),YS=Ur(nu),ZS=Ur(iu),Ft=ud;(eu&&Ft(new eu(new ArrayBuffer(1)))!=el||tu&&Ft(new tu)!=Ys||ru&&Ft(ru.resolve())!=Zs||nu&&Ft(new nu)!=Js||iu&&Ft(new iu)!=Qs)&&(Ft=function(e){var t=ud(e),r=t==GS?e.constructor:void 0,n=r?Ur(r):"";if(n)switch(n){case KS:return el;case XS:return Ys;case VS:return Zs;case YS:return Js;case ZS:return Qs}return t});var JS=Ft,mo=Vh,QS=Qh,eA=MO,tA=jS,tl=JS,rl=ke,nl=rd,rA=id,nA=1,il="[object Arguments]",al="[object Array]",ui="[object Object]",iA=Object.prototype,ol=iA.hasOwnProperty;function aA(e,t,r,n,i,a){var o=rl(e),u=rl(t),c=o?al:tl(e),s=u?al:tl(t);c=c==il?ui:c,s=s==il?ui:s;var f=c==ui,l=s==ui,p=c==s;if(p&&nl(e)){if(!nl(t))return!1;o=!0,f=!1}if(p&&!f)return a||(a=new mo),o||rA(e)?QS(e,t,r,n,i,a):eA(e,t,c,r,n,i,a);if(!(r&nA)){var h=f&&ol.call(e,"__wrapped__"),d=l&&ol.call(t,"__wrapped__");if(h||d){var v=h?e.value():e,y=d?t.value():t;return a||(a=new mo),i(v,y,r,n,a)}}return p?(a||(a=new mo),tA(e,t,r,n,i,a)):!1}var oA=aA,uA=oA,ul=Ot;function cd(e,t,r,n,i){return e===t?!0:e==null||t==null||!ul(e)&&!ul(t)?e!==e&&t!==t:uA(e,t,r,n,cd,i)}var Mc=cd,cA=Vh,sA=Mc,lA=1,fA=2;function pA(e,t,r,n){var i=r.length,a=i,o=!n;if(e==null)return!a;for(e=Object(e);i--;){var u=r[i];if(o&&u[2]?u[1]!==e[u[0]]:!(u[0]in e))return!1}for(;++i<a;){u=r[i];var c=u[0],s=e[c],f=u[1];if(o&&u[2]){if(s===void 0&&!(c in e))return!1}else{var l=new cA;if(n)var p=n(s,f,c,e,t,l);if(!(p===void 0?sA(f,s,lA|fA,n,l):p))return!1}}return!0}var hA=pA,dA=jt;function vA(e){return e===e&&!dA(e)}var sd=vA,yA=sd,mA=Ba;function gA(e){for(var t=mA(e),r=t.length;r--;){var n=t[r],i=e[n];t[r]=[n,i,yA(i)]}return t}var bA=gA;function xA(e,t){return function(r){return r==null?!1:r[e]===t&&(t!==void 0||e in Object(r))}}var ld=xA,wA=hA,OA=bA,SA=ld;function AA(e){var t=OA(e);return t.length==1&&t[0][2]?SA(t[0][0],t[0][1]):function(r){return r===e||wA(r,e,t)}}var _A=AA;function PA(e,t){return e!=null&&t in Object(e)}var $A=PA,TA=gh,EA=Tc,jA=ke,MA=Ec,CA=jc,IA=Sa;function DA(e,t,r){t=TA(t,e);for(var n=-1,i=t.length,a=!1;++n<i;){var o=IA(t[n]);if(!(a=e!=null&&r(e,o)))break;e=e[o]}return a||++n!=i?a:(i=e==null?0:e.length,!!i&&CA(i)&&MA(o,i)&&(jA(e)||EA(e)))}var kA=DA,NA=$A,LA=kA;function BA(e,t){return e!=null&&LA(e,t,NA)}var RA=BA,FA=Mc,WA=bh,zA=RA,UA=lc,qA=sd,HA=ld,GA=Sa,KA=1,XA=2;function VA(e,t){return UA(e)&&qA(t)?HA(GA(e),t):function(r){var n=WA(r,e);return n===void 0&&n===t?zA(r,e):FA(t,n,KA|XA)}}var YA=VA;function ZA(e){return e}var qr=ZA;function JA(e){return function(t){return t==null?void 0:t[e]}}var QA=JA,e_=mc;function t_(e){return function(t){return e_(t,e)}}var r_=t_,n_=QA,i_=r_,a_=lc,o_=Sa;function u_(e){return a_(e)?n_(o_(e)):i_(e)}var c_=u_,s_=_A,l_=YA,f_=qr,p_=ke,h_=c_;function d_(e){return typeof e=="function"?e:e==null?f_:typeof e=="object"?p_(e)?l_(e[0],e[1]):s_(e):h_(e)}var Mt=d_;function v_(e,t,r,n){for(var i=e.length,a=r+(n?1:-1);n?a--:++a<i;)if(t(e[a],a,e))return a;return-1}var fd=v_;function y_(e){return e!==e}var m_=y_;function g_(e,t,r){for(var n=r-1,i=e.length;++n<i;)if(e[n]===t)return n;return-1}var b_=g_,x_=fd,w_=m_,O_=b_;function S_(e,t,r){return t===t?O_(e,t,r):x_(e,w_,r)}var A_=S_,__=A_;function P_(e,t){var r=e==null?0:e.length;return!!r&&__(e,t,0)>-1}var $_=P_;function T_(e,t,r){for(var n=-1,i=e==null?0:e.length;++n<i;)if(r(t,e[n]))return!0;return!1}var E_=T_;function j_(){}var M_=j_,go=od,C_=M_,I_=$c,D_=1/0,k_=go&&1/I_(new go([,-0]))[1]==D_?function(e){return new go(e)}:C_,N_=k_,L_=Yh,B_=$_,R_=E_,F_=Jh,W_=N_,z_=$c,U_=200;function q_(e,t,r){var n=-1,i=B_,a=e.length,o=!0,u=[],c=u;if(r)o=!1,i=R_;else if(a>=U_){var s=t?null:W_(e);if(s)return z_(s);o=!1,i=F_,c=new L_}else c=t?[]:u;e:for(;++n<a;){var f=e[n],l=t?t(f):f;if(f=r||f!==0?f:0,o&&l===l){for(var p=c.length;p--;)if(c[p]===l)continue e;t&&c.push(l),u.push(f)}else i(c,l,r)||(c!==u&&c.push(l),u.push(f))}return u}var H_=q_,G_=Mt,K_=H_;function X_(e,t){return e&&e.length?K_(e,G_(t)):[]}var V_=X_;const cl=ue(V_);function pd(e,t,r){return t===!0?cl(e,r):Y(t)?cl(e,t):e}function mr(e){"@babel/helpers - typeof";return mr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},mr(e)}var Y_=["ref"];function sl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function st(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?sl(Object(r),!0).forEach(function(n){Ra(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sl(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Z_(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ll(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,dd(n.key),n)}}function J_(e,t,r){return t&&ll(e.prototype,t),r&&ll(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Q_(e,t,r){return t=Ci(t),eP(e,hd()?Reflect.construct(t,r||[],Ci(e).constructor):t.apply(e,r))}function eP(e,t){if(t&&(mr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return tP(e)}function tP(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function hd(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(hd=function(){return!!e})()}function Ci(e){return Ci=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ci(e)}function rP(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&au(e,t)}function au(e,t){return au=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},au(e,t)}function Ra(e,t,r){return t=dd(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dd(e){var t=nP(e,"string");return mr(t)=="symbol"?t:t+""}function nP(e,t){if(mr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(mr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function iP(e,t){if(e==null)return{};var r=aP(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function aP(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function oP(e){return e.value}function uP(e,t){if(_.isValidElement(e))return _.cloneElement(e,t);if(typeof e=="function")return _.createElement(e,t);t.ref;var r=iP(t,Y_);return _.createElement(Pc,r)}var fl=1,pr=function(e){function t(){var r;Z_(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=Q_(this,t,[].concat(i)),Ra(r,"lastBoundingBox",{width:-1,height:-1}),r}return rP(t,e),J_(t,[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();return n.height=this.wrapperNode.offsetHeight,n.width=this.wrapperNode.offsetWidth,n}return null}},{key:"updateBBox",value:function(){var n=this.props.onBBoxUpdate,i=this.getBBox();i?(Math.abs(i.width-this.lastBoundingBox.width)>fl||Math.abs(i.height-this.lastBoundingBox.height)>fl)&&(this.lastBoundingBox.width=i.width,this.lastBoundingBox.height=i.height,n&&n(i)):(this.lastBoundingBox.width!==-1||this.lastBoundingBox.height!==-1)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,n&&n(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?st({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(n){var i=this.props,a=i.layout,o=i.align,u=i.verticalAlign,c=i.margin,s=i.chartWidth,f=i.chartHeight,l,p;if(!n||(n.left===void 0||n.left===null)&&(n.right===void 0||n.right===null))if(o==="center"&&a==="vertical"){var h=this.getBBoxSnapshot();l={left:((s||0)-h.width)/2}}else l=o==="right"?{right:c&&c.right||0}:{left:c&&c.left||0};if(!n||(n.top===void 0||n.top===null)&&(n.bottom===void 0||n.bottom===null))if(u==="middle"){var d=this.getBBoxSnapshot();p={top:((f||0)-d.height)/2}}else p=u==="bottom"?{bottom:c&&c.bottom||0}:{top:c&&c.top||0};return st(st({},l),p)}},{key:"render",value:function(){var n=this,i=this.props,a=i.content,o=i.width,u=i.height,c=i.wrapperStyle,s=i.payloadUniqBy,f=i.payload,l=st(st({position:"absolute",width:o||"auto",height:u||"auto"},this.getDefaultPosition(c)),c);return _.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(h){n.wrapperNode=h}},uP(a,st(st({},this.props),{},{payload:pd(f,s,oP)})))}}],[{key:"getWithHeight",value:function(n,i){var a=st(st({},this.defaultProps),n.props),o=a.layout;return o==="vertical"&&R(n.props.height)?{height:n.props.height}:o==="horizontal"?{width:n.props.width||i}:null}}])}(B.PureComponent);Ra(pr,"displayName","Legend");Ra(pr,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var pl=Vn,cP=Tc,sP=ke,hl=pl?pl.isConcatSpreadable:void 0;function lP(e){return sP(e)||cP(e)||!!(hl&&e&&e[hl])}var fP=lP,pP=ed,hP=fP;function vd(e,t,r,n,i){var a=-1,o=e.length;for(r||(r=hP),i||(i=[]);++a<o;){var u=e[a];t>0&&r(u)?t>1?vd(u,t-1,r,n,i):pP(i,u):n||(i[i.length]=u)}return i}var yd=vd;function dP(e){return function(t,r,n){for(var i=-1,a=Object(t),o=n(t),u=o.length;u--;){var c=o[e?u:++i];if(r(a[c],c,a)===!1)break}return t}}var vP=dP,yP=vP,mP=yP(),gP=mP,bP=gP,xP=Ba;function wP(e,t){return e&&bP(e,t,xP)}var md=wP,OP=Jn;function SP(e,t){return function(r,n){if(r==null)return r;if(!OP(r))return e(r,n);for(var i=r.length,a=t?i:-1,o=Object(r);(t?a--:++a<i)&&n(o[a],a,o)!==!1;);return r}}var AP=SP,_P=md,PP=AP,$P=PP(_P),Cc=$P,TP=Cc,EP=Jn;function jP(e,t){var r=-1,n=EP(e)?Array(e.length):[];return TP(e,function(i,a,o){n[++r]=t(i,a,o)}),n}var gd=jP;function MP(e,t){var r=e.length;for(e.sort(t);r--;)e[r]=e[r].value;return e}var CP=MP,dl=Lr;function IP(e,t){if(e!==t){var r=e!==void 0,n=e===null,i=e===e,a=dl(e),o=t!==void 0,u=t===null,c=t===t,s=dl(t);if(!u&&!s&&!a&&e>t||a&&o&&c&&!u&&!s||n&&o&&c||!r&&c||!i)return 1;if(!n&&!a&&!s&&e<t||s&&r&&i&&!n&&!a||u&&r&&i||!o&&i||!c)return-1}return 0}var DP=IP,kP=DP;function NP(e,t,r){for(var n=-1,i=e.criteria,a=t.criteria,o=i.length,u=r.length;++n<o;){var c=kP(i[n],a[n]);if(c){if(n>=u)return c;var s=r[n];return c*(s=="desc"?-1:1)}}return e.index-t.index}var LP=NP,bo=yc,BP=mc,RP=Mt,FP=gd,WP=CP,zP=nd,UP=LP,qP=qr,HP=ke;function GP(e,t,r){t.length?t=bo(t,function(a){return HP(a)?function(o){return BP(o,a.length===1?a[0]:a)}:a}):t=[qP];var n=-1;t=bo(t,zP(RP));var i=FP(e,function(a,o,u){var c=bo(t,function(s){return s(a)});return{criteria:c,index:++n,value:a}});return WP(i,function(a,o){return UP(a,o,r)})}var KP=GP;function XP(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}var VP=XP,YP=VP,vl=Math.max;function ZP(e,t,r){return t=vl(t===void 0?e.length-1:t,0),function(){for(var n=arguments,i=-1,a=vl(n.length-t,0),o=Array(a);++i<a;)o[i]=n[t+i];i=-1;for(var u=Array(t+1);++i<t;)u[i]=n[i];return u[t]=r(o),YP(e,this,u)}}var JP=ZP;function QP(e){return function(){return e}}var e$=QP,t$=Qt,r$=function(){try{var e=t$(Object,"defineProperty");return e({},"",{}),e}catch{}}(),bd=r$,n$=e$,yl=bd,i$=qr,a$=yl?function(e,t){return yl(e,"toString",{configurable:!0,enumerable:!1,value:n$(t),writable:!0})}:i$,o$=a$,u$=800,c$=16,s$=Date.now;function l$(e){var t=0,r=0;return function(){var n=s$(),i=c$-(n-r);if(r=n,i>0){if(++t>=u$)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}var f$=l$,p$=o$,h$=f$,d$=h$(p$),v$=d$,y$=qr,m$=JP,g$=v$;function b$(e,t){return g$(m$(e,t,y$),e+"")}var x$=b$,w$=pc,O$=Jn,S$=Ec,A$=jt;function _$(e,t,r){if(!A$(r))return!1;var n=typeof t;return(n=="number"?O$(r)&&S$(t,r.length):n=="string"&&t in r)?w$(r[t],e):!1}var Fa=_$,P$=yd,$$=KP,T$=x$,ml=Fa,E$=T$(function(e,t){if(e==null)return[];var r=t.length;return r>1&&ml(e,t[0],t[1])?t=[]:r>2&&ml(t[0],t[1],t[2])&&(t=[t[0]]),$$(e,P$(t,1),[])}),j$=E$;const Ic=ue(j$);function yn(e){"@babel/helpers - typeof";return yn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},yn(e)}function ou(){return ou=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ou.apply(this,arguments)}function M$(e,t){return k$(e)||D$(e,t)||I$(e,t)||C$()}function C$(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function I$(e,t){if(e){if(typeof e=="string")return gl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return gl(e,t)}}function gl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function D$(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function k$(e){if(Array.isArray(e))return e}function bl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function xo(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?bl(Object(r),!0).forEach(function(n){N$(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):bl(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function N$(e,t,r){return t=L$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function L$(e){var t=B$(e,"string");return yn(t)=="symbol"?t:t+""}function B$(e,t){if(yn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(yn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function R$(e){return Array.isArray(e)&&we(e[0])&&we(e[1])?e.join(" ~ "):e}var F$=function(t){var r=t.separator,n=r===void 0?" : ":r,i=t.contentStyle,a=i===void 0?{}:i,o=t.itemStyle,u=o===void 0?{}:o,c=t.labelStyle,s=c===void 0?{}:c,f=t.payload,l=t.formatter,p=t.itemSorter,h=t.wrapperClassName,d=t.labelClassName,v=t.label,y=t.labelFormatter,b=t.accessibilityLayer,w=b===void 0?!1:b,x=function(){if(f&&f.length){var T={padding:0,margin:0},I=(p?Ic(f,p):f).map(function(C,M){if(C.type==="none")return null;var D=xo({display:"block",paddingTop:4,paddingBottom:4,color:C.color||"#000"},u),k=C.formatter||l||R$,L=C.value,F=C.name,q=L,G=F;if(k&&q!=null&&G!=null){var z=k(L,F,C,M,f);if(Array.isArray(z)){var K=M$(z,2);q=K[0],G=K[1]}else q=z}return _.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(M),style:D},we(G)?_.createElement("span",{className:"recharts-tooltip-item-name"},G):null,we(G)?_.createElement("span",{className:"recharts-tooltip-item-separator"},n):null,_.createElement("span",{className:"recharts-tooltip-item-value"},q),_.createElement("span",{className:"recharts-tooltip-item-unit"},C.unit||""))});return _.createElement("ul",{className:"recharts-tooltip-item-list",style:T},I)}return null},S=xo({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},a),m=xo({margin:0},s),g=!Z(v),O=g?v:"",A=ee("recharts-default-tooltip",h),P=ee("recharts-tooltip-label",d);g&&y&&f!==void 0&&f!==null&&(O=y(v,f));var j=w?{role:"status","aria-live":"assertive"}:{};return _.createElement("div",ou({className:A,style:S},j),_.createElement("p",{className:P,style:m},_.isValidElement(O)?O:"".concat(O)),x())};function mn(e){"@babel/helpers - typeof";return mn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},mn(e)}function ci(e,t,r){return t=W$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function W$(e){var t=z$(e,"string");return mn(t)=="symbol"?t:t+""}function z$(e,t){if(mn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(mn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Jr="recharts-tooltip-wrapper",U$={visibility:"hidden"};function q$(e){var t=e.coordinate,r=e.translateX,n=e.translateY;return ee(Jr,ci(ci(ci(ci({},"".concat(Jr,"-right"),R(r)&&t&&R(t.x)&&r>=t.x),"".concat(Jr,"-left"),R(r)&&t&&R(t.x)&&r<t.x),"".concat(Jr,"-bottom"),R(n)&&t&&R(t.y)&&n>=t.y),"".concat(Jr,"-top"),R(n)&&t&&R(t.y)&&n<t.y))}function xl(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.key,i=e.offsetTopLeft,a=e.position,o=e.reverseDirection,u=e.tooltipDimension,c=e.viewBox,s=e.viewBoxDimension;if(a&&R(a[n]))return a[n];var f=r[n]-u-i,l=r[n]+i;if(t[n])return o[n]?f:l;if(o[n]){var p=f,h=c[n];return p<h?Math.max(l,c[n]):Math.max(f,c[n])}var d=l+u,v=c[n]+s;return d>v?Math.max(f,c[n]):Math.max(l,c[n])}function H$(e){var t=e.translateX,r=e.translateY,n=e.useTranslate3d;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}function G$(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.offsetTopLeft,i=e.position,a=e.reverseDirection,o=e.tooltipBox,u=e.useTranslate3d,c=e.viewBox,s,f,l;return o.height>0&&o.width>0&&r?(f=xl({allowEscapeViewBox:t,coordinate:r,key:"x",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.width,viewBox:c,viewBoxDimension:c.width}),l=xl({allowEscapeViewBox:t,coordinate:r,key:"y",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.height,viewBox:c,viewBoxDimension:c.height}),s=H$({translateX:f,translateY:l,useTranslate3d:u})):s=U$,{cssProperties:s,cssClasses:q$({translateX:f,translateY:l,coordinate:r})}}function gr(e){"@babel/helpers - typeof";return gr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},gr(e)}function wl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ol(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?wl(Object(r),!0).forEach(function(n){cu(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):wl(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function K$(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function X$(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,wd(n.key),n)}}function V$(e,t,r){return t&&X$(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Y$(e,t,r){return t=Ii(t),Z$(e,xd()?Reflect.construct(t,r||[],Ii(e).constructor):t.apply(e,r))}function Z$(e,t){if(t&&(gr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return J$(e)}function J$(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function xd(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(xd=function(){return!!e})()}function Ii(e){return Ii=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ii(e)}function Q$(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&uu(e,t)}function uu(e,t){return uu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},uu(e,t)}function cu(e,t,r){return t=wd(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function wd(e){var t=eT(e,"string");return gr(t)=="symbol"?t:t+""}function eT(e,t){if(gr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(gr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Sl=1,tT=function(e){function t(){var r;K$(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=Y$(this,t,[].concat(i)),cu(r,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),cu(r,"handleKeyDown",function(o){if(o.key==="Escape"){var u,c,s,f;r.setState({dismissed:!0,dismissedAtCoordinate:{x:(u=(c=r.props.coordinate)===null||c===void 0?void 0:c.x)!==null&&u!==void 0?u:0,y:(s=(f=r.props.coordinate)===null||f===void 0?void 0:f.y)!==null&&s!==void 0?s:0}})}}),r}return Q$(t,e),V$(t,[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();(Math.abs(n.width-this.state.lastBoundingBox.width)>Sl||Math.abs(n.height-this.state.lastBoundingBox.height)>Sl)&&this.setState({lastBoundingBox:{width:n.width,height:n.height}})}else(this.state.lastBoundingBox.width!==-1||this.state.lastBoundingBox.height!==-1)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var n,i;this.props.active&&this.updateBBox(),this.state.dismissed&&(((n=this.props.coordinate)===null||n===void 0?void 0:n.x)!==this.state.dismissedAtCoordinate.x||((i=this.props.coordinate)===null||i===void 0?void 0:i.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,s=i.children,f=i.coordinate,l=i.hasPayload,p=i.isAnimationActive,h=i.offset,d=i.position,v=i.reverseDirection,y=i.useTranslate3d,b=i.viewBox,w=i.wrapperStyle,x=G$({allowEscapeViewBox:o,coordinate:f,offsetTopLeft:h,position:d,reverseDirection:v,tooltipBox:this.state.lastBoundingBox,useTranslate3d:y,viewBox:b}),S=x.cssClasses,m=x.cssProperties,g=Ol(Ol({transition:p&&a?"transform ".concat(u,"ms ").concat(c):void 0},m),{},{pointerEvents:"none",visibility:!this.state.dismissed&&a&&l?"visible":"hidden",position:"absolute",top:0,left:0},w);return _.createElement("div",{tabIndex:-1,className:S,style:g,ref:function(A){n.wrapperNode=A}},s)}}])}(B.PureComponent),rT=function(){return!(typeof window<"u"&&window.document&&window.document.createElement&&window.setTimeout)},Hr={isSsr:rT()};function br(e){"@babel/helpers - typeof";return br=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},br(e)}function Al(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function _l(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Al(Object(r),!0).forEach(function(n){Dc(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Al(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function nT(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function iT(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Sd(n.key),n)}}function aT(e,t,r){return t&&iT(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function oT(e,t,r){return t=Di(t),uT(e,Od()?Reflect.construct(t,r||[],Di(e).constructor):t.apply(e,r))}function uT(e,t){if(t&&(br(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return cT(e)}function cT(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Od(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Od=function(){return!!e})()}function Di(e){return Di=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Di(e)}function sT(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&su(e,t)}function su(e,t){return su=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},su(e,t)}function Dc(e,t,r){return t=Sd(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Sd(e){var t=lT(e,"string");return br(t)=="symbol"?t:t+""}function lT(e,t){if(br(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(br(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function fT(e){return e.dataKey}function pT(e,t){return _.isValidElement(e)?_.cloneElement(e,t):typeof e=="function"?_.createElement(e,t):_.createElement(F$,t)}var lt=function(e){function t(){return nT(this,t),oT(this,t,arguments)}return sT(t,e),aT(t,[{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,s=i.content,f=i.coordinate,l=i.filterNull,p=i.isAnimationActive,h=i.offset,d=i.payload,v=i.payloadUniqBy,y=i.position,b=i.reverseDirection,w=i.useTranslate3d,x=i.viewBox,S=i.wrapperStyle,m=d??[];l&&m.length&&(m=pd(d.filter(function(O){return O.value!=null&&(O.hide!==!0||n.props.includeHidden)}),v,fT));var g=m.length>0;return _.createElement(tT,{allowEscapeViewBox:o,animationDuration:u,animationEasing:c,isAnimationActive:p,active:a,coordinate:f,hasPayload:g,offset:h,position:y,reverseDirection:b,useTranslate3d:w,viewBox:x,wrapperStyle:S},pT(s,_l(_l({},this.props),{},{payload:m})))}}])}(B.PureComponent);Dc(lt,"displayName","Tooltip");Dc(lt,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!Hr.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var hT=ct,dT=function(){return hT.Date.now()},vT=dT,yT=/\s/;function mT(e){for(var t=e.length;t--&&yT.test(e.charAt(t)););return t}var gT=mT,bT=gT,xT=/^\s+/;function wT(e){return e&&e.slice(0,bT(e)+1).replace(xT,"")}var OT=wT,ST=OT,Pl=jt,AT=Lr,$l=NaN,_T=/^[-+]0x[0-9a-f]+$/i,PT=/^0b[01]+$/i,$T=/^0o[0-7]+$/i,TT=parseInt;function ET(e){if(typeof e=="number")return e;if(AT(e))return $l;if(Pl(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=Pl(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=ST(e);var r=PT.test(e);return r||$T.test(e)?TT(e.slice(2),r?2:8):_T.test(e)?$l:+e}var Ad=ET,jT=jt,wo=vT,Tl=Ad,MT="Expected a function",CT=Math.max,IT=Math.min;function DT(e,t,r){var n,i,a,o,u,c,s=0,f=!1,l=!1,p=!0;if(typeof e!="function")throw new TypeError(MT);t=Tl(t)||0,jT(r)&&(f=!!r.leading,l="maxWait"in r,a=l?CT(Tl(r.maxWait)||0,t):a,p="trailing"in r?!!r.trailing:p);function h(g){var O=n,A=i;return n=i=void 0,s=g,o=e.apply(A,O),o}function d(g){return s=g,u=setTimeout(b,t),f?h(g):o}function v(g){var O=g-c,A=g-s,P=t-O;return l?IT(P,a-A):P}function y(g){var O=g-c,A=g-s;return c===void 0||O>=t||O<0||l&&A>=a}function b(){var g=wo();if(y(g))return w(g);u=setTimeout(b,v(g))}function w(g){return u=void 0,p&&n?h(g):(n=i=void 0,o)}function x(){u!==void 0&&clearTimeout(u),s=0,n=c=i=u=void 0}function S(){return u===void 0?o:w(wo())}function m(){var g=wo(),O=y(g);if(n=arguments,i=this,c=g,O){if(u===void 0)return d(c);if(l)return clearTimeout(u),u=setTimeout(b,t),h(c)}return u===void 0&&(u=setTimeout(b,t)),o}return m.cancel=x,m.flush=S,m}var kT=DT,NT=kT,LT=jt,BT="Expected a function";function RT(e,t,r){var n=!0,i=!0;if(typeof e!="function")throw new TypeError(BT);return LT(r)&&(n="leading"in r?!!r.leading:n,i="trailing"in r?!!r.trailing:i),NT(e,t,{leading:n,maxWait:t,trailing:i})}var FT=RT;const _d=ue(FT);function gn(e){"@babel/helpers - typeof";return gn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},gn(e)}function El(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function si(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?El(Object(r),!0).forEach(function(n){WT(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):El(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function WT(e,t,r){return t=zT(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zT(e){var t=UT(e,"string");return gn(t)=="symbol"?t:t+""}function UT(e,t){if(gn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(gn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function qT(e,t){return XT(e)||KT(e,t)||GT(e,t)||HT()}function HT(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function GT(e,t){if(e){if(typeof e=="string")return jl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return jl(e,t)}}function jl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function KT(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function XT(e){if(Array.isArray(e))return e}var _3=B.forwardRef(function(e,t){var r=e.aspect,n=e.initialDimension,i=n===void 0?{width:-1,height:-1}:n,a=e.width,o=a===void 0?"100%":a,u=e.height,c=u===void 0?"100%":u,s=e.minWidth,f=s===void 0?0:s,l=e.minHeight,p=e.maxHeight,h=e.children,d=e.debounce,v=d===void 0?0:d,y=e.id,b=e.className,w=e.onResize,x=e.style,S=x===void 0?{}:x,m=B.useRef(null),g=B.useRef();g.current=w,B.useImperativeHandle(t,function(){return Object.defineProperty(m.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),m.current},configurable:!0})});var O=B.useState({containerWidth:i.width,containerHeight:i.height}),A=qT(O,2),P=A[0],j=A[1],$=B.useCallback(function(I,C){j(function(M){var D=Math.round(I),k=Math.round(C);return M.containerWidth===D&&M.containerHeight===k?M:{containerWidth:D,containerHeight:k}})},[]);B.useEffect(function(){var I=function(F){var q,G=F[0].contentRect,z=G.width,K=G.height;$(z,K),(q=g.current)===null||q===void 0||q.call(g,z,K)};v>0&&(I=_d(I,v,{trailing:!0,leading:!1}));var C=new ResizeObserver(I),M=m.current.getBoundingClientRect(),D=M.width,k=M.height;return $(D,k),C.observe(m.current),function(){C.disconnect()}},[$,v]);var T=B.useMemo(function(){var I=P.containerWidth,C=P.containerHeight;if(I<0||C<0)return null;vt(zt(o)||zt(c),`The width(%s) and height(%s) are both fixed numbers,
       maybe you don't need to use a ResponsiveContainer.`,o,c),vt(!r||r>0,"The aspect(%s) must be greater than zero.",r);var M=zt(o)?I:o,D=zt(c)?C:c;r&&r>0&&(M?D=M/r:D&&(M=D*r),p&&D>p&&(D=p)),vt(M>0||D>0,`The width(%s) and height(%s) of chart should be greater than 0,
       please check the style of container, or the props width(%s) and height(%s),
       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the
       height and width.`,M,D,o,c,f,l,r);var k=!Array.isArray(h)&&dt(h.type).endsWith("Chart");return _.Children.map(h,function(L){return _.isValidElement(L)?B.cloneElement(L,si({width:M,height:D},k?{style:si({height:"100%",width:"100%",maxHeight:D,maxWidth:M},L.props.style)}:{})):L})},[r,h,c,p,l,f,P,o]);return _.createElement("div",{id:y?"".concat(y):void 0,className:ee("recharts-responsive-container",b),style:si(si({},S),{},{width:o,height:c,minWidth:f,minHeight:l,maxHeight:p}),ref:m},T)}),Pd=function(t){return null};Pd.displayName="Cell";function bn(e){"@babel/helpers - typeof";return bn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},bn(e)}function Ml(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function lu(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ml(Object(r),!0).forEach(function(n){VT(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ml(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function VT(e,t,r){return t=YT(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function YT(e){var t=ZT(e,"string");return bn(t)=="symbol"?t:t+""}function ZT(e,t){if(bn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(bn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var ir={widthCache:{},cacheCount:0},JT=2e3,QT={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},Cl="recharts_measurement_span";function eE(e){var t=lu({},e);return Object.keys(t).forEach(function(r){t[r]||delete t[r]}),t}var sn=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(t==null||Hr.isSsr)return{width:0,height:0};var n=eE(r),i=JSON.stringify({text:t,copyStyle:n});if(ir.widthCache[i])return ir.widthCache[i];try{var a=document.getElementById(Cl);a||(a=document.createElement("span"),a.setAttribute("id",Cl),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=lu(lu({},QT),n);Object.assign(a.style,o),a.textContent="".concat(t);var u=a.getBoundingClientRect(),c={width:u.width,height:u.height};return ir.widthCache[i]=c,++ir.cacheCount>JT&&(ir.cacheCount=0,ir.widthCache={}),c}catch{return{width:0,height:0}}},tE=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}};function xn(e){"@babel/helpers - typeof";return xn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xn(e)}function ki(e,t){return aE(e)||iE(e,t)||nE(e,t)||rE()}function rE(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function nE(e,t){if(e){if(typeof e=="string")return Il(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Il(e,t)}}function Il(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function iE(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function aE(e){if(Array.isArray(e))return e}function oE(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Dl(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,cE(n.key),n)}}function uE(e,t,r){return t&&Dl(e.prototype,t),r&&Dl(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function cE(e){var t=sE(e,"string");return xn(t)=="symbol"?t:t+""}function sE(e,t){if(xn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(xn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var kl=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,Nl=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,lE=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,fE=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,$d={cm:96/2.54,mm:96/25.4,pt:96/72,pc:96/6,in:96,Q:96/(2.54*40),px:1},pE=Object.keys($d),cr="NaN";function hE(e,t){return e*$d[t]}var li=function(){function e(t,r){oE(this,e),this.num=t,this.unit=r,this.num=t,this.unit=r,Number.isNaN(t)&&(this.unit=""),r!==""&&!lE.test(r)&&(this.num=NaN,this.unit=""),pE.includes(r)&&(this.num=hE(t,r),this.unit="px")}return uE(e,[{key:"add",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num+r.num,this.unit)}},{key:"subtract",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num-r.num,this.unit)}},{key:"multiply",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num*r.num,this.unit||r.unit)}},{key:"divide",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num/r.num,this.unit||r.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],[{key:"parse",value:function(r){var n,i=(n=fE.exec(r))!==null&&n!==void 0?n:[],a=ki(i,3),o=a[1],u=a[2];return new e(parseFloat(o),u??"")}}])}();function Td(e){if(e.includes(cr))return cr;for(var t=e;t.includes("*")||t.includes("/");){var r,n=(r=kl.exec(t))!==null&&r!==void 0?r:[],i=ki(n,4),a=i[1],o=i[2],u=i[3],c=li.parse(a??""),s=li.parse(u??""),f=o==="*"?c.multiply(s):c.divide(s);if(f.isNaN())return cr;t=t.replace(kl,f.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var l,p=(l=Nl.exec(t))!==null&&l!==void 0?l:[],h=ki(p,4),d=h[1],v=h[2],y=h[3],b=li.parse(d??""),w=li.parse(y??""),x=v==="+"?b.add(w):b.subtract(w);if(x.isNaN())return cr;t=t.replace(Nl,x.toString())}return t}var Ll=/\(([^()]*)\)/;function dE(e){for(var t=e;t.includes("(");){var r=Ll.exec(t),n=ki(r,2),i=n[1];t=t.replace(Ll,Td(i))}return t}function vE(e){var t=e.replace(/\s+/g,"");return t=dE(t),t=Td(t),t}function yE(e){try{return vE(e)}catch{return cr}}function Oo(e){var t=yE(e.slice(5,-1));return t===cr?"":t}var mE=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],gE=["dx","dy","angle","className","breakAll"];function fu(){return fu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},fu.apply(this,arguments)}function Bl(e,t){if(e==null)return{};var r=bE(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function bE(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Rl(e,t){return SE(e)||OE(e,t)||wE(e,t)||xE()}function xE(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function wE(e,t){if(e){if(typeof e=="string")return Fl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Fl(e,t)}}function Fl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function OE(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function SE(e){if(Array.isArray(e))return e}var Ed=/[ \f\n\r\t\v\u2028\u2029]+/,jd=function(t){var r=t.children,n=t.breakAll,i=t.style;try{var a=[];Z(r)||(n?a=r.toString().split(""):a=r.toString().split(Ed));var o=a.map(function(c){return{word:c,width:sn(c,i).width}}),u=n?0:sn(" ",i).width;return{wordsWithComputedWidth:o,spaceWidth:u}}catch{return null}},AE=function(t,r,n,i,a){var o=t.maxLines,u=t.children,c=t.style,s=t.breakAll,f=R(o),l=u,p=function(){var M=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return M.reduce(function(D,k){var L=k.word,F=k.width,q=D[D.length-1];if(q&&(i==null||a||q.width+F+n<Number(i)))q.words.push(L),q.width+=F+n;else{var G={words:[L],width:F};D.push(G)}return D},[])},h=p(r),d=function(M){return M.reduce(function(D,k){return D.width>k.width?D:k})};if(!f)return h;for(var v="…",y=function(M){var D=l.slice(0,M),k=jd({breakAll:s,style:c,children:D+v}).wordsWithComputedWidth,L=p(k),F=L.length>o||d(L).width>Number(i);return[F,L]},b=0,w=l.length-1,x=0,S;b<=w&&x<=l.length-1;){var m=Math.floor((b+w)/2),g=m-1,O=y(g),A=Rl(O,2),P=A[0],j=A[1],$=y(m),T=Rl($,1),I=T[0];if(!P&&!I&&(b=m+1),P&&I&&(w=m-1),!P&&I){S=j;break}x++}return S||h},Wl=function(t){var r=Z(t)?[]:t.toString().split(Ed);return[{words:r}]},_E=function(t){var r=t.width,n=t.scaleToFit,i=t.children,a=t.style,o=t.breakAll,u=t.maxLines;if((r||n)&&!Hr.isSsr){var c,s,f=jd({breakAll:o,children:i,style:a});if(f){var l=f.wordsWithComputedWidth,p=f.spaceWidth;c=l,s=p}else return Wl(i);return AE({breakAll:o,children:i,maxLines:u,style:a},c,s,r,n)}return Wl(i)},zl="#808080",Ni=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.lineHeight,u=o===void 0?"1em":o,c=t.capHeight,s=c===void 0?"0.71em":c,f=t.scaleToFit,l=f===void 0?!1:f,p=t.textAnchor,h=p===void 0?"start":p,d=t.verticalAnchor,v=d===void 0?"end":d,y=t.fill,b=y===void 0?zl:y,w=Bl(t,mE),x=B.useMemo(function(){return _E({breakAll:w.breakAll,children:w.children,maxLines:w.maxLines,scaleToFit:l,style:w.style,width:w.width})},[w.breakAll,w.children,w.maxLines,l,w.style,w.width]),S=w.dx,m=w.dy,g=w.angle,O=w.className,A=w.breakAll,P=Bl(w,gE);if(!we(n)||!we(a))return null;var j=n+(R(S)?S:0),$=a+(R(m)?m:0),T;switch(v){case"start":T=Oo("calc(".concat(s,")"));break;case"middle":T=Oo("calc(".concat((x.length-1)/2," * -").concat(u," + (").concat(s," / 2))"));break;default:T=Oo("calc(".concat(x.length-1," * -").concat(u,")"));break}var I=[];if(l){var C=x[0].width,M=w.width;I.push("scale(".concat((R(M)?M/C:1)/C,")"))}return g&&I.push("rotate(".concat(g,", ").concat(j,", ").concat($,")")),I.length&&(P.transform=I.join(" ")),_.createElement("text",fu({},J(P,!0),{x:j,y:$,className:ee("recharts-text",O),textAnchor:h,fill:b.includes("url")?zl:b}),x.map(function(D,k){var L=D.words.join(A?"":" ");return _.createElement("tspan",{x:j,dy:k===0?T:u,key:"".concat(L,"-").concat(k)},L)}))};function Tt(e,t){return e==null||t==null?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function PE(e,t){return e==null||t==null?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function kc(e){let t,r,n;e.length!==2?(t=Tt,r=(u,c)=>Tt(e(u),c),n=(u,c)=>e(u)-c):(t=e===Tt||e===PE?e:$E,r=e,n=e);function i(u,c,s=0,f=u.length){if(s<f){if(t(c,c)!==0)return f;do{const l=s+f>>>1;r(u[l],c)<0?s=l+1:f=l}while(s<f)}return s}function a(u,c,s=0,f=u.length){if(s<f){if(t(c,c)!==0)return f;do{const l=s+f>>>1;r(u[l],c)<=0?s=l+1:f=l}while(s<f)}return s}function o(u,c,s=0,f=u.length){const l=i(u,c,s,f-1);return l>s&&n(u[l-1],c)>-n(u[l],c)?l-1:l}return{left:i,center:o,right:a}}function $E(){return 0}function Md(e){return e===null?NaN:+e}function*TE(e,t){for(let r of e)r!=null&&(r=+r)>=r&&(yield r)}const EE=kc(Tt),Qn=EE.right;kc(Md).center;class Ul extends Map{constructor(t,r=CE){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:r}}),t!=null)for(const[n,i]of t)this.set(n,i)}get(t){return super.get(ql(this,t))}has(t){return super.has(ql(this,t))}set(t,r){return super.set(jE(this,t),r)}delete(t){return super.delete(ME(this,t))}}function ql({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):r}function jE({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}function ME({_intern:e,_key:t},r){const n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}function CE(e){return e!==null&&typeof e=="object"?e.valueOf():e}function IE(e=Tt){if(e===Tt)return Cd;if(typeof e!="function")throw new TypeError("compare is not a function");return(t,r)=>{const n=e(t,r);return n||n===0?n:(e(r,r)===0)-(e(t,t)===0)}}function Cd(e,t){return(e==null||!(e>=e))-(t==null||!(t>=t))||(e<t?-1:e>t?1:0)}const DE=Math.sqrt(50),kE=Math.sqrt(10),NE=Math.sqrt(2);function Li(e,t,r){const n=(t-e)/Math.max(0,r),i=Math.floor(Math.log10(n)),a=n/Math.pow(10,i),o=a>=DE?10:a>=kE?5:a>=NE?2:1;let u,c,s;return i<0?(s=Math.pow(10,-i)/o,u=Math.round(e*s),c=Math.round(t*s),u/s<e&&++u,c/s>t&&--c,s=-s):(s=Math.pow(10,i)*o,u=Math.round(e/s),c=Math.round(t/s),u*s<e&&++u,c*s>t&&--c),c<u&&.5<=r&&r<2?Li(e,t,r*2):[u,c,s]}function pu(e,t,r){if(t=+t,e=+e,r=+r,!(r>0))return[];if(e===t)return[e];const n=t<e,[i,a,o]=n?Li(t,e,r):Li(e,t,r);if(!(a>=i))return[];const u=a-i+1,c=new Array(u);if(n)if(o<0)for(let s=0;s<u;++s)c[s]=(a-s)/-o;else for(let s=0;s<u;++s)c[s]=(a-s)*o;else if(o<0)for(let s=0;s<u;++s)c[s]=(i+s)/-o;else for(let s=0;s<u;++s)c[s]=(i+s)*o;return c}function hu(e,t,r){return t=+t,e=+e,r=+r,Li(e,t,r)[2]}function du(e,t,r){t=+t,e=+e,r=+r;const n=t<e,i=n?hu(t,e,r):hu(e,t,r);return(n?-1:1)*(i<0?1/-i:i)}function Hl(e,t){let r;for(const n of e)n!=null&&(r<n||r===void 0&&n>=n)&&(r=n);return r}function Gl(e,t){let r;for(const n of e)n!=null&&(r>n||r===void 0&&n>=n)&&(r=n);return r}function Id(e,t,r=0,n=1/0,i){if(t=Math.floor(t),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(e.length-1,n)),!(r<=t&&t<=n))return e;for(i=i===void 0?Cd:IE(i);n>r;){if(n-r>600){const c=n-r+1,s=t-r+1,f=Math.log(c),l=.5*Math.exp(2*f/3),p=.5*Math.sqrt(f*l*(c-l)/c)*(s-c/2<0?-1:1),h=Math.max(r,Math.floor(t-s*l/c+p)),d=Math.min(n,Math.floor(t+(c-s)*l/c+p));Id(e,t,h,d,i)}const a=e[t];let o=r,u=n;for(Qr(e,r,t),i(e[n],a)>0&&Qr(e,r,n);o<u;){for(Qr(e,o,u),++o,--u;i(e[o],a)<0;)++o;for(;i(e[u],a)>0;)--u}i(e[r],a)===0?Qr(e,r,u):(++u,Qr(e,u,n)),u<=t&&(r=u+1),t<=u&&(n=u-1)}return e}function Qr(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function LE(e,t,r){if(e=Float64Array.from(TE(e)),!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return Gl(e);if(t>=1)return Hl(e);var n,i=(n-1)*t,a=Math.floor(i),o=Hl(Id(e,a).subarray(0,a+1)),u=Gl(e.subarray(a+1));return o+(u-o)*(i-a)}}function BE(e,t,r=Md){if(!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e),u=+r(e[a+1],a+1,e);return o+(u-o)*(i-a)}}function RE(e,t,r){e=+e,t=+t,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=Math.max(0,Math.ceil((t-e)/r))|0,a=new Array(i);++n<i;)a[n]=e+n*r;return a}function Ve(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e);break}return this}function St(e,t){switch(arguments.length){case 0:break;case 1:{typeof e=="function"?this.interpolator(e):this.range(e);break}default:{this.domain(e),typeof t=="function"?this.interpolator(t):this.range(t);break}}return this}const vu=Symbol("implicit");function Nc(){var e=new Ul,t=[],r=[],n=vu;function i(a){let o=e.get(a);if(o===void 0){if(n!==vu)return n;e.set(a,o=t.push(a)-1)}return r[o%r.length]}return i.domain=function(a){if(!arguments.length)return t.slice();t=[],e=new Ul;for(const o of a)e.has(o)||e.set(o,t.push(o)-1);return i},i.range=function(a){return arguments.length?(r=Array.from(a),i):r.slice()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return Nc(t,r).unknown(n)},Ve.apply(i,arguments),i}function wn(){var e=Nc().unknown(void 0),t=e.domain,r=e.range,n=0,i=1,a,o,u=!1,c=0,s=0,f=.5;delete e.unknown;function l(){var p=t().length,h=i<n,d=h?i:n,v=h?n:i;a=(v-d)/Math.max(1,p-c+s*2),u&&(a=Math.floor(a)),d+=(v-d-a*(p-c))*f,o=a*(1-c),u&&(d=Math.round(d),o=Math.round(o));var y=RE(p).map(function(b){return d+a*b});return r(h?y.reverse():y)}return e.domain=function(p){return arguments.length?(t(p),l()):t()},e.range=function(p){return arguments.length?([n,i]=p,n=+n,i=+i,l()):[n,i]},e.rangeRound=function(p){return[n,i]=p,n=+n,i=+i,u=!0,l()},e.bandwidth=function(){return o},e.step=function(){return a},e.round=function(p){return arguments.length?(u=!!p,l()):u},e.padding=function(p){return arguments.length?(c=Math.min(1,s=+p),l()):c},e.paddingInner=function(p){return arguments.length?(c=Math.min(1,p),l()):c},e.paddingOuter=function(p){return arguments.length?(s=+p,l()):s},e.align=function(p){return arguments.length?(f=Math.max(0,Math.min(1,p)),l()):f},e.copy=function(){return wn(t(),[n,i]).round(u).paddingInner(c).paddingOuter(s).align(f)},Ve.apply(l(),arguments)}function Dd(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return Dd(t())},e}function ln(){return Dd(wn.apply(null,arguments).paddingInner(1))}function Lc(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function kd(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function ei(){}var On=.7,Bi=1/On,hr="\\s*([+-]?\\d+)\\s*",Sn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",at="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",FE=/^#([0-9a-f]{3,8})$/,WE=new RegExp(`^rgb\\(${hr},${hr},${hr}\\)$`),zE=new RegExp(`^rgb\\(${at},${at},${at}\\)$`),UE=new RegExp(`^rgba\\(${hr},${hr},${hr},${Sn}\\)$`),qE=new RegExp(`^rgba\\(${at},${at},${at},${Sn}\\)$`),HE=new RegExp(`^hsl\\(${Sn},${at},${at}\\)$`),GE=new RegExp(`^hsla\\(${Sn},${at},${at},${Sn}\\)$`),Kl={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};Lc(ei,An,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Xl,formatHex:Xl,formatHex8:KE,formatHsl:XE,formatRgb:Vl,toString:Vl});function Xl(){return this.rgb().formatHex()}function KE(){return this.rgb().formatHex8()}function XE(){return Nd(this).formatHsl()}function Vl(){return this.rgb().formatRgb()}function An(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=FE.exec(e))?(r=t[1].length,t=parseInt(t[1],16),r===6?Yl(t):r===3?new De(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):r===8?fi(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):r===4?fi(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=WE.exec(e))?new De(t[1],t[2],t[3],1):(t=zE.exec(e))?new De(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=UE.exec(e))?fi(t[1],t[2],t[3],t[4]):(t=qE.exec(e))?fi(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=HE.exec(e))?Ql(t[1],t[2]/100,t[3]/100,1):(t=GE.exec(e))?Ql(t[1],t[2]/100,t[3]/100,t[4]):Kl.hasOwnProperty(e)?Yl(Kl[e]):e==="transparent"?new De(NaN,NaN,NaN,0):null}function Yl(e){return new De(e>>16&255,e>>8&255,e&255,1)}function fi(e,t,r,n){return n<=0&&(e=t=r=NaN),new De(e,t,r,n)}function VE(e){return e instanceof ei||(e=An(e)),e?(e=e.rgb(),new De(e.r,e.g,e.b,e.opacity)):new De}function yu(e,t,r,n){return arguments.length===1?VE(e):new De(e,t,r,n??1)}function De(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}Lc(De,yu,kd(ei,{brighter(e){return e=e==null?Bi:Math.pow(Bi,e),new De(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?On:Math.pow(On,e),new De(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new De(Gt(this.r),Gt(this.g),Gt(this.b),Ri(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Zl,formatHex:Zl,formatHex8:YE,formatRgb:Jl,toString:Jl}));function Zl(){return`#${Ut(this.r)}${Ut(this.g)}${Ut(this.b)}`}function YE(){return`#${Ut(this.r)}${Ut(this.g)}${Ut(this.b)}${Ut((isNaN(this.opacity)?1:this.opacity)*255)}`}function Jl(){const e=Ri(this.opacity);return`${e===1?"rgb(":"rgba("}${Gt(this.r)}, ${Gt(this.g)}, ${Gt(this.b)}${e===1?")":`, ${e})`}`}function Ri(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function Gt(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Ut(e){return e=Gt(e),(e<16?"0":"")+e.toString(16)}function Ql(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new tt(e,t,r,n)}function Nd(e){if(e instanceof tt)return new tt(e.h,e.s,e.l,e.opacity);if(e instanceof ei||(e=An(e)),!e)return new tt;if(e instanceof tt)return e;e=e.rgb();var t=e.r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,u=a-i,c=(a+i)/2;return u?(t===a?o=(r-n)/u+(r<n)*6:r===a?o=(n-t)/u+2:o=(t-r)/u+4,u/=c<.5?a+i:2-a-i,o*=60):u=c>0&&c<1?0:o,new tt(o,u,c,e.opacity)}function ZE(e,t,r,n){return arguments.length===1?Nd(e):new tt(e,t,r,n??1)}function tt(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}Lc(tt,ZE,kd(ei,{brighter(e){return e=e==null?Bi:Math.pow(Bi,e),new tt(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?On:Math.pow(On,e),new tt(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new De(So(e>=240?e-240:e+120,i,n),So(e,i,n),So(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new tt(ef(this.h),pi(this.s),pi(this.l),Ri(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=Ri(this.opacity);return`${e===1?"hsl(":"hsla("}${ef(this.h)}, ${pi(this.s)*100}%, ${pi(this.l)*100}%${e===1?")":`, ${e})`}`}}));function ef(e){return e=(e||0)%360,e<0?e+360:e}function pi(e){return Math.max(0,Math.min(1,e||0))}function So(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}const Bc=e=>()=>e;function JE(e,t){return function(r){return e+r*t}}function QE(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(n){return Math.pow(e+n*t,r)}}function ej(e){return(e=+e)==1?Ld:function(t,r){return r-t?QE(t,r,e):Bc(isNaN(t)?r:t)}}function Ld(e,t){var r=t-e;return r?JE(e,r):Bc(isNaN(e)?t:e)}const tf=function e(t){var r=ej(t);function n(i,a){var o=r((i=yu(i)).r,(a=yu(a)).r),u=r(i.g,a.g),c=r(i.b,a.b),s=Ld(i.opacity,a.opacity);return function(f){return i.r=o(f),i.g=u(f),i.b=c(f),i.opacity=s(f),i+""}}return n.gamma=e,n}(1);function tj(e,t){t||(t=[]);var r=e?Math.min(t.length,e.length):0,n=t.slice(),i;return function(a){for(i=0;i<r;++i)n[i]=e[i]*(1-a)+t[i]*a;return n}}function rj(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}function nj(e,t){var r=t?t.length:0,n=e?Math.min(r,e.length):0,i=new Array(n),a=new Array(r),o;for(o=0;o<n;++o)i[o]=Gr(e[o],t[o]);for(;o<r;++o)a[o]=t[o];return function(u){for(o=0;o<n;++o)a[o]=i[o](u);return a}}function ij(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}function Fi(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}function aj(e,t){var r={},n={},i;(e===null||typeof e!="object")&&(e={}),(t===null||typeof t!="object")&&(t={});for(i in t)i in e?r[i]=Gr(e[i],t[i]):n[i]=t[i];return function(a){for(i in r)n[i]=r[i](a);return n}}var mu=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Ao=new RegExp(mu.source,"g");function oj(e){return function(){return e}}function uj(e){return function(t){return e(t)+""}}function cj(e,t){var r=mu.lastIndex=Ao.lastIndex=0,n,i,a,o=-1,u=[],c=[];for(e=e+"",t=t+"";(n=mu.exec(e))&&(i=Ao.exec(t));)(a=i.index)>r&&(a=t.slice(r,a),u[o]?u[o]+=a:u[++o]=a),(n=n[0])===(i=i[0])?u[o]?u[o]+=i:u[++o]=i:(u[++o]=null,c.push({i:o,x:Fi(n,i)})),r=Ao.lastIndex;return r<t.length&&(a=t.slice(r),u[o]?u[o]+=a:u[++o]=a),u.length<2?c[0]?uj(c[0].x):oj(t):(t=c.length,function(s){for(var f=0,l;f<t;++f)u[(l=c[f]).i]=l.x(s);return u.join("")})}function Gr(e,t){var r=typeof t,n;return t==null||r==="boolean"?Bc(t):(r==="number"?Fi:r==="string"?(n=An(t))?(t=n,tf):cj:t instanceof An?tf:t instanceof Date?ij:rj(t)?tj:Array.isArray(t)?nj:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?aj:Fi)(e,t)}function Rc(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function sj(e,t){t===void 0&&(t=e,e=Gr);for(var r=0,n=t.length-1,i=t[0],a=new Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(o){var u=Math.max(0,Math.min(n-1,Math.floor(o*=n)));return a[u](o-u)}}function lj(e){return function(){return e}}function Wi(e){return+e}var rf=[0,1];function Me(e){return e}function gu(e,t){return(t-=e=+e)?function(r){return(r-e)/t}:lj(isNaN(t)?NaN:.5)}function fj(e,t){var r;return e>t&&(r=e,e=t,t=r),function(n){return Math.max(e,Math.min(t,n))}}function pj(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=gu(i,n),a=r(o,a)):(n=gu(n,i),a=r(a,o)),function(u){return a(n(u))}}function hj(e,t,r){var n=Math.min(e.length,t.length)-1,i=new Array(n),a=new Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=gu(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(u){var c=Qn(e,u,1,n)-1;return a[c](i[c](u))}}function ti(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function Wa(){var e=rf,t=rf,r=Gr,n,i,a,o=Me,u,c,s;function f(){var p=Math.min(e.length,t.length);return o!==Me&&(o=fj(e[0],e[p-1])),u=p>2?hj:pj,c=s=null,l}function l(p){return p==null||isNaN(p=+p)?a:(c||(c=u(e.map(n),t,r)))(n(o(p)))}return l.invert=function(p){return o(i((s||(s=u(t,e.map(n),Fi)))(p)))},l.domain=function(p){return arguments.length?(e=Array.from(p,Wi),f()):e.slice()},l.range=function(p){return arguments.length?(t=Array.from(p),f()):t.slice()},l.rangeRound=function(p){return t=Array.from(p),r=Rc,f()},l.clamp=function(p){return arguments.length?(o=p?!0:Me,f()):o!==Me},l.interpolate=function(p){return arguments.length?(r=p,f()):r},l.unknown=function(p){return arguments.length?(a=p,l):a},function(p,h){return n=p,i=h,f()}}function Fc(){return Wa()(Me,Me)}function dj(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)}function zi(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function xr(e){return e=zi(Math.abs(e)),e?e[1]:NaN}function vj(e,t){return function(r,n){for(var i=r.length,a=[],o=0,u=e[0],c=0;i>0&&u>0&&(c+u+1>n&&(u=Math.max(1,n-c)),a.push(r.substring(i-=u,i+u)),!((c+=u+1)>n));)u=e[o=(o+1)%e.length];return a.reverse().join(t)}}function yj(e){return function(t){return t.replace(/[0-9]/g,function(r){return e[+r]})}}var mj=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function _n(e){if(!(t=mj.exec(e)))throw new Error("invalid format: "+e);var t;return new Wc({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}_n.prototype=Wc.prototype;function Wc(e){this.fill=e.fill===void 0?" ":e.fill+"",this.align=e.align===void 0?">":e.align+"",this.sign=e.sign===void 0?"-":e.sign+"",this.symbol=e.symbol===void 0?"":e.symbol+"",this.zero=!!e.zero,this.width=e.width===void 0?void 0:+e.width,this.comma=!!e.comma,this.precision=e.precision===void 0?void 0:+e.precision,this.trim=!!e.trim,this.type=e.type===void 0?"":e.type+""}Wc.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function gj(e){e:for(var t=e.length,r=1,n=-1,i;r<t;++r)switch(e[r]){case".":n=i=r;break;case"0":n===0&&(n=r),i=r;break;default:if(!+e[r])break e;n>0&&(n=0);break}return n>0?e.slice(0,n)+e.slice(i+1):e}var Bd;function bj(e,t){var r=zi(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(Bd=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,o=n.length;return a===o?n:a>o?n+new Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+new Array(1-a).join("0")+zi(e,Math.max(0,t+a-1))[0]}function nf(e,t){var r=zi(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+new Array(i-n.length+2).join("0")}const af={"%":(e,t)=>(e*100).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:dj,e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>nf(e*100,t),r:nf,s:bj,X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function of(e){return e}var uf=Array.prototype.map,cf=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function xj(e){var t=e.grouping===void 0||e.thousands===void 0?of:vj(uf.call(e.grouping,Number),e.thousands+""),r=e.currency===void 0?"":e.currency[0]+"",n=e.currency===void 0?"":e.currency[1]+"",i=e.decimal===void 0?".":e.decimal+"",a=e.numerals===void 0?of:yj(uf.call(e.numerals,String)),o=e.percent===void 0?"%":e.percent+"",u=e.minus===void 0?"−":e.minus+"",c=e.nan===void 0?"NaN":e.nan+"";function s(l){l=_n(l);var p=l.fill,h=l.align,d=l.sign,v=l.symbol,y=l.zero,b=l.width,w=l.comma,x=l.precision,S=l.trim,m=l.type;m==="n"?(w=!0,m="g"):af[m]||(x===void 0&&(x=12),S=!0,m="g"),(y||p==="0"&&h==="=")&&(y=!0,p="0",h="=");var g=v==="$"?r:v==="#"&&/[boxX]/.test(m)?"0"+m.toLowerCase():"",O=v==="$"?n:/[%p]/.test(m)?o:"",A=af[m],P=/[defgprs%]/.test(m);x=x===void 0?6:/[gprs]/.test(m)?Math.max(1,Math.min(21,x)):Math.max(0,Math.min(20,x));function j($){var T=g,I=O,C,M,D;if(m==="c")I=A($)+I,$="";else{$=+$;var k=$<0||1/$<0;if($=isNaN($)?c:A(Math.abs($),x),S&&($=gj($)),k&&+$==0&&d!=="+"&&(k=!1),T=(k?d==="("?d:u:d==="-"||d==="("?"":d)+T,I=(m==="s"?cf[8+Bd/3]:"")+I+(k&&d==="("?")":""),P){for(C=-1,M=$.length;++C<M;)if(D=$.charCodeAt(C),48>D||D>57){I=(D===46?i+$.slice(C+1):$.slice(C))+I,$=$.slice(0,C);break}}}w&&!y&&($=t($,1/0));var L=T.length+$.length+I.length,F=L<b?new Array(b-L+1).join(p):"";switch(w&&y&&($=t(F+$,F.length?b-I.length:1/0),F=""),h){case"<":$=T+$+I+F;break;case"=":$=T+F+$+I;break;case"^":$=F.slice(0,L=F.length>>1)+T+$+I+F.slice(L);break;default:$=F+T+$+I;break}return a($)}return j.toString=function(){return l+""},j}function f(l,p){var h=s((l=_n(l),l.type="f",l)),d=Math.max(-8,Math.min(8,Math.floor(xr(p)/3)))*3,v=Math.pow(10,-d),y=cf[8+d/3];return function(b){return h(v*b)+y}}return{format:s,formatPrefix:f}}var hi,zc,Rd;wj({thousands:",",grouping:[3],currency:["$",""]});function wj(e){return hi=xj(e),zc=hi.format,Rd=hi.formatPrefix,hi}function Oj(e){return Math.max(0,-xr(Math.abs(e)))}function Sj(e,t){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(xr(t)/3)))*3-xr(Math.abs(e)))}function Aj(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,xr(t)-xr(e))+1}function Fd(e,t,r,n){var i=du(e,t,r),a;switch(n=_n(n??",f"),n.type){case"s":{var o=Math.max(Math.abs(e),Math.abs(t));return n.precision==null&&!isNaN(a=Sj(i,o))&&(n.precision=a),Rd(n,o)}case"":case"e":case"g":case"p":case"r":{n.precision==null&&!isNaN(a=Aj(i,Math.max(Math.abs(e),Math.abs(t))))&&(n.precision=a-(n.type==="e"));break}case"f":case"%":{n.precision==null&&!isNaN(a=Oj(i))&&(n.precision=a-(n.type==="%")*2);break}}return zc(n)}function Ct(e){var t=e.domain;return e.ticks=function(r){var n=t();return pu(n[0],n[n.length-1],r??10)},e.tickFormat=function(r,n){var i=t();return Fd(i[0],i[i.length-1],r??10,n)},e.nice=function(r){r==null&&(r=10);var n=t(),i=0,a=n.length-1,o=n[i],u=n[a],c,s,f=10;for(u<o&&(s=o,o=u,u=s,s=i,i=a,a=s);f-- >0;){if(s=hu(o,u,r),s===c)return n[i]=o,n[a]=u,t(n);if(s>0)o=Math.floor(o/s)*s,u=Math.ceil(u/s)*s;else if(s<0)o=Math.ceil(o*s)/s,u=Math.floor(u*s)/s;else break;c=s}return e},e}function Ui(){var e=Fc();return e.copy=function(){return ti(e,Ui())},Ve.apply(e,arguments),Ct(e)}function Wd(e){var t;function r(n){return n==null||isNaN(n=+n)?t:n}return r.invert=r,r.domain=r.range=function(n){return arguments.length?(e=Array.from(n,Wi),r):e.slice()},r.unknown=function(n){return arguments.length?(t=n,r):t},r.copy=function(){return Wd(e).unknown(t)},e=arguments.length?Array.from(e,Wi):[0,1],Ct(r)}function zd(e,t){e=e.slice();var r=0,n=e.length-1,i=e[r],a=e[n],o;return a<i&&(o=r,r=n,n=o,o=i,i=a,a=o),e[r]=t.floor(i),e[n]=t.ceil(a),e}function sf(e){return Math.log(e)}function lf(e){return Math.exp(e)}function _j(e){return-Math.log(-e)}function Pj(e){return-Math.exp(-e)}function $j(e){return isFinite(e)?+("1e"+e):e<0?0:e}function Tj(e){return e===10?$j:e===Math.E?Math.exp:t=>Math.pow(e,t)}function Ej(e){return e===Math.E?Math.log:e===10&&Math.log10||e===2&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}function ff(e){return(t,r)=>-e(-t,r)}function Uc(e){const t=e(sf,lf),r=t.domain;let n=10,i,a;function o(){return i=Ej(n),a=Tj(n),r()[0]<0?(i=ff(i),a=ff(a),e(_j,Pj)):e(sf,lf),t}return t.base=function(u){return arguments.length?(n=+u,o()):n},t.domain=function(u){return arguments.length?(r(u),o()):r()},t.ticks=u=>{const c=r();let s=c[0],f=c[c.length-1];const l=f<s;l&&([s,f]=[f,s]);let p=i(s),h=i(f),d,v;const y=u==null?10:+u;let b=[];if(!(n%1)&&h-p<y){if(p=Math.floor(p),h=Math.ceil(h),s>0){for(;p<=h;++p)for(d=1;d<n;++d)if(v=p<0?d/a(-p):d*a(p),!(v<s)){if(v>f)break;b.push(v)}}else for(;p<=h;++p)for(d=n-1;d>=1;--d)if(v=p>0?d/a(-p):d*a(p),!(v<s)){if(v>f)break;b.push(v)}b.length*2<y&&(b=pu(s,f,y))}else b=pu(p,h,Math.min(h-p,y)).map(a);return l?b.reverse():b},t.tickFormat=(u,c)=>{if(u==null&&(u=10),c==null&&(c=n===10?"s":","),typeof c!="function"&&(!(n%1)&&(c=_n(c)).precision==null&&(c.trim=!0),c=zc(c)),u===1/0)return c;const s=Math.max(1,n*u/t.ticks().length);return f=>{let l=f/a(Math.round(i(f)));return l*n<n-.5&&(l*=n),l<=s?c(f):""}},t.nice=()=>r(zd(r(),{floor:u=>a(Math.floor(i(u))),ceil:u=>a(Math.ceil(i(u)))})),t}function Ud(){const e=Uc(Wa()).domain([1,10]);return e.copy=()=>ti(e,Ud()).base(e.base()),Ve.apply(e,arguments),e}function pf(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function hf(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function qc(e){var t=1,r=e(pf(t),hf(t));return r.constant=function(n){return arguments.length?e(pf(t=+n),hf(t)):t},Ct(r)}function qd(){var e=qc(Wa());return e.copy=function(){return ti(e,qd()).constant(e.constant())},Ve.apply(e,arguments)}function df(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function jj(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function Mj(e){return e<0?-e*e:e*e}function Hc(e){var t=e(Me,Me),r=1;function n(){return r===1?e(Me,Me):r===.5?e(jj,Mj):e(df(r),df(1/r))}return t.exponent=function(i){return arguments.length?(r=+i,n()):r},Ct(t)}function Gc(){var e=Hc(Wa());return e.copy=function(){return ti(e,Gc()).exponent(e.exponent())},Ve.apply(e,arguments),e}function Cj(){return Gc.apply(null,arguments).exponent(.5)}function vf(e){return Math.sign(e)*e*e}function Ij(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}function Hd(){var e=Fc(),t=[0,1],r=!1,n;function i(a){var o=Ij(e(a));return isNaN(o)?n:r?Math.round(o):o}return i.invert=function(a){return e.invert(vf(a))},i.domain=function(a){return arguments.length?(e.domain(a),i):e.domain()},i.range=function(a){return arguments.length?(e.range((t=Array.from(a,Wi)).map(vf)),i):t.slice()},i.rangeRound=function(a){return i.range(a).round(!0)},i.round=function(a){return arguments.length?(r=!!a,i):r},i.clamp=function(a){return arguments.length?(e.clamp(a),i):e.clamp()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return Hd(e.domain(),t).round(r).clamp(e.clamp()).unknown(n)},Ve.apply(i,arguments),Ct(i)}function Gd(){var e=[],t=[],r=[],n;function i(){var o=0,u=Math.max(1,t.length);for(r=new Array(u-1);++o<u;)r[o-1]=BE(e,o/u);return a}function a(o){return o==null||isNaN(o=+o)?n:t[Qn(r,o)]}return a.invertExtent=function(o){var u=t.indexOf(o);return u<0?[NaN,NaN]:[u>0?r[u-1]:e[0],u<r.length?r[u]:e[e.length-1]]},a.domain=function(o){if(!arguments.length)return e.slice();e=[];for(let u of o)u!=null&&!isNaN(u=+u)&&e.push(u);return e.sort(Tt),i()},a.range=function(o){return arguments.length?(t=Array.from(o),i()):t.slice()},a.unknown=function(o){return arguments.length?(n=o,a):n},a.quantiles=function(){return r.slice()},a.copy=function(){return Gd().domain(e).range(t).unknown(n)},Ve.apply(a,arguments)}function Kd(){var e=0,t=1,r=1,n=[.5],i=[0,1],a;function o(c){return c!=null&&c<=c?i[Qn(n,c,0,r)]:a}function u(){var c=-1;for(n=new Array(r);++c<r;)n[c]=((c+1)*t-(c-r)*e)/(r+1);return o}return o.domain=function(c){return arguments.length?([e,t]=c,e=+e,t=+t,u()):[e,t]},o.range=function(c){return arguments.length?(r=(i=Array.from(c)).length-1,u()):i.slice()},o.invertExtent=function(c){var s=i.indexOf(c);return s<0?[NaN,NaN]:s<1?[e,n[0]]:s>=r?[n[r-1],t]:[n[s-1],n[s]]},o.unknown=function(c){return arguments.length&&(a=c),o},o.thresholds=function(){return n.slice()},o.copy=function(){return Kd().domain([e,t]).range(i).unknown(a)},Ve.apply(Ct(o),arguments)}function Xd(){var e=[.5],t=[0,1],r,n=1;function i(a){return a!=null&&a<=a?t[Qn(e,a,0,n)]:r}return i.domain=function(a){return arguments.length?(e=Array.from(a),n=Math.min(e.length,t.length-1),i):e.slice()},i.range=function(a){return arguments.length?(t=Array.from(a),n=Math.min(e.length,t.length-1),i):t.slice()},i.invertExtent=function(a){var o=t.indexOf(a);return[e[o-1],e[o]]},i.unknown=function(a){return arguments.length?(r=a,i):r},i.copy=function(){return Xd().domain(e).range(t).unknown(r)},Ve.apply(i,arguments)}const _o=new Date,Po=new Date;function Oe(e,t,r,n){function i(a){return e(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=a=>(e(a=new Date(+a)),a),i.ceil=a=>(e(a=new Date(a-1)),t(a,1),e(a),a),i.round=a=>{const o=i(a),u=i.ceil(a);return a-o<u-a?o:u},i.offset=(a,o)=>(t(a=new Date(+a),o==null?1:Math.floor(o)),a),i.range=(a,o,u)=>{const c=[];if(a=i.ceil(a),u=u==null?1:Math.floor(u),!(a<o)||!(u>0))return c;let s;do c.push(s=new Date(+a)),t(a,u),e(a);while(s<a&&a<o);return c},i.filter=a=>Oe(o=>{if(o>=o)for(;e(o),!a(o);)o.setTime(o-1)},(o,u)=>{if(o>=o)if(u<0)for(;++u<=0;)for(;t(o,-1),!a(o););else for(;--u>=0;)for(;t(o,1),!a(o););}),r&&(i.count=(a,o)=>(_o.setTime(+a),Po.setTime(+o),e(_o),e(Po),Math.floor(r(_o,Po))),i.every=a=>(a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(n?o=>n(o)%a===0:o=>i.count(0,o)%a===0):i)),i}const qi=Oe(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);qi.every=e=>(e=Math.floor(e),!isFinite(e)||!(e>0)?null:e>1?Oe(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):qi);qi.range;const ft=1e3,qe=ft*60,pt=qe*60,mt=pt*24,Kc=mt*7,yf=mt*30,$o=mt*365,qt=Oe(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+t*ft)},(e,t)=>(t-e)/ft,e=>e.getUTCSeconds());qt.range;const Xc=Oe(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*ft)},(e,t)=>{e.setTime(+e+t*qe)},(e,t)=>(t-e)/qe,e=>e.getMinutes());Xc.range;const Vc=Oe(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+t*qe)},(e,t)=>(t-e)/qe,e=>e.getUTCMinutes());Vc.range;const Yc=Oe(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*ft-e.getMinutes()*qe)},(e,t)=>{e.setTime(+e+t*pt)},(e,t)=>(t-e)/pt,e=>e.getHours());Yc.range;const Zc=Oe(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+t*pt)},(e,t)=>(t-e)/pt,e=>e.getUTCHours());Zc.range;const ri=Oe(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*qe)/mt,e=>e.getDate()-1);ri.range;const za=Oe(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/mt,e=>e.getUTCDate()-1);za.range;const Vd=Oe(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/mt,e=>Math.floor(e/mt));Vd.range;function er(e){return Oe(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(t,r)=>{t.setDate(t.getDate()+r*7)},(t,r)=>(r-t-(r.getTimezoneOffset()-t.getTimezoneOffset())*qe)/Kc)}const Ua=er(0),Hi=er(1),Dj=er(2),kj=er(3),wr=er(4),Nj=er(5),Lj=er(6);Ua.range;Hi.range;Dj.range;kj.range;wr.range;Nj.range;Lj.range;function tr(e){return Oe(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCDate(t.getUTCDate()+r*7)},(t,r)=>(r-t)/Kc)}const qa=tr(0),Gi=tr(1),Bj=tr(2),Rj=tr(3),Or=tr(4),Fj=tr(5),Wj=tr(6);qa.range;Gi.range;Bj.range;Rj.range;Or.range;Fj.range;Wj.range;const Jc=Oe(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());Jc.range;const Qc=Oe(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());Qc.range;const gt=Oe(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());gt.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:Oe(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)});gt.range;const bt=Oe(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());bt.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:Oe(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)});bt.range;function Yd(e,t,r,n,i,a){const o=[[qt,1,ft],[qt,5,5*ft],[qt,15,15*ft],[qt,30,30*ft],[a,1,qe],[a,5,5*qe],[a,15,15*qe],[a,30,30*qe],[i,1,pt],[i,3,3*pt],[i,6,6*pt],[i,12,12*pt],[n,1,mt],[n,2,2*mt],[r,1,Kc],[t,1,yf],[t,3,3*yf],[e,1,$o]];function u(s,f,l){const p=f<s;p&&([s,f]=[f,s]);const h=l&&typeof l.range=="function"?l:c(s,f,l),d=h?h.range(s,+f+1):[];return p?d.reverse():d}function c(s,f,l){const p=Math.abs(f-s)/l,h=kc(([,,y])=>y).right(o,p);if(h===o.length)return e.every(du(s/$o,f/$o,l));if(h===0)return qi.every(Math.max(du(s,f,l),1));const[d,v]=o[p/o[h-1][2]<o[h][2]/p?h-1:h];return d.every(v)}return[u,c]}const[zj,Uj]=Yd(bt,Qc,qa,Vd,Zc,Vc),[qj,Hj]=Yd(gt,Jc,Ua,ri,Yc,Xc);function To(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function Eo(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function en(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}function Gj(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,u=e.months,c=e.shortMonths,s=tn(i),f=rn(i),l=tn(a),p=rn(a),h=tn(o),d=rn(o),v=tn(u),y=rn(u),b=tn(c),w=rn(c),x={a:k,A:L,b:F,B:q,c:null,d:Of,e:Of,f:vM,g:_M,G:$M,H:pM,I:hM,j:dM,L:Zd,m:yM,M:mM,p:G,q:z,Q:_f,s:Pf,S:gM,u:bM,U:xM,V:wM,w:OM,W:SM,x:null,X:null,y:AM,Y:PM,Z:TM,"%":Af},S={a:K,A:ce,b:ve,B:Ne,c:null,d:Sf,e:Sf,f:CM,g:zM,G:qM,H:EM,I:jM,j:MM,L:Qd,m:IM,M:DM,p:kt,q:Ce,Q:_f,s:Pf,S:kM,u:NM,U:LM,V:BM,w:RM,W:FM,x:null,X:null,y:WM,Y:UM,Z:HM,"%":Af},m={a:j,A:$,b:T,B:I,c:C,d:xf,e:xf,f:cM,g:bf,G:gf,H:wf,I:wf,j:iM,L:uM,m:nM,M:aM,p:P,q:rM,Q:lM,s:fM,S:oM,u:Zj,U:Jj,V:Qj,w:Yj,W:eM,x:M,X:D,y:bf,Y:gf,Z:tM,"%":sM};x.x=g(r,x),x.X=g(n,x),x.c=g(t,x),S.x=g(r,S),S.X=g(n,S),S.c=g(t,S);function g(W,X){return function(V){var N=[],pe=-1,Q=0,ge=W.length,be,Ie,At;for(V instanceof Date||(V=new Date(+V));++pe<ge;)W.charCodeAt(pe)===37&&(N.push(W.slice(Q,pe)),(Ie=mf[be=W.charAt(++pe)])!=null?be=W.charAt(++pe):Ie=be==="e"?" ":"0",(At=X[be])&&(be=At(V,Ie)),N.push(be),Q=pe+1);return N.push(W.slice(Q,pe)),N.join("")}}function O(W,X){return function(V){var N=en(1900,void 0,1),pe=A(N,W,V+="",0),Q,ge;if(pe!=V.length)return null;if("Q"in N)return new Date(N.Q);if("s"in N)return new Date(N.s*1e3+("L"in N?N.L:0));if(X&&!("Z"in N)&&(N.Z=0),"p"in N&&(N.H=N.H%12+N.p*12),N.m===void 0&&(N.m="q"in N?N.q:0),"V"in N){if(N.V<1||N.V>53)return null;"w"in N||(N.w=1),"Z"in N?(Q=Eo(en(N.y,0,1)),ge=Q.getUTCDay(),Q=ge>4||ge===0?Gi.ceil(Q):Gi(Q),Q=za.offset(Q,(N.V-1)*7),N.y=Q.getUTCFullYear(),N.m=Q.getUTCMonth(),N.d=Q.getUTCDate()+(N.w+6)%7):(Q=To(en(N.y,0,1)),ge=Q.getDay(),Q=ge>4||ge===0?Hi.ceil(Q):Hi(Q),Q=ri.offset(Q,(N.V-1)*7),N.y=Q.getFullYear(),N.m=Q.getMonth(),N.d=Q.getDate()+(N.w+6)%7)}else("W"in N||"U"in N)&&("w"in N||(N.w="u"in N?N.u%7:"W"in N?1:0),ge="Z"in N?Eo(en(N.y,0,1)).getUTCDay():To(en(N.y,0,1)).getDay(),N.m=0,N.d="W"in N?(N.w+6)%7+N.W*7-(ge+5)%7:N.w+N.U*7-(ge+6)%7);return"Z"in N?(N.H+=N.Z/100|0,N.M+=N.Z%100,Eo(N)):To(N)}}function A(W,X,V,N){for(var pe=0,Q=X.length,ge=V.length,be,Ie;pe<Q;){if(N>=ge)return-1;if(be=X.charCodeAt(pe++),be===37){if(be=X.charAt(pe++),Ie=m[be in mf?X.charAt(pe++):be],!Ie||(N=Ie(W,V,N))<0)return-1}else if(be!=V.charCodeAt(N++))return-1}return N}function P(W,X,V){var N=s.exec(X.slice(V));return N?(W.p=f.get(N[0].toLowerCase()),V+N[0].length):-1}function j(W,X,V){var N=h.exec(X.slice(V));return N?(W.w=d.get(N[0].toLowerCase()),V+N[0].length):-1}function $(W,X,V){var N=l.exec(X.slice(V));return N?(W.w=p.get(N[0].toLowerCase()),V+N[0].length):-1}function T(W,X,V){var N=b.exec(X.slice(V));return N?(W.m=w.get(N[0].toLowerCase()),V+N[0].length):-1}function I(W,X,V){var N=v.exec(X.slice(V));return N?(W.m=y.get(N[0].toLowerCase()),V+N[0].length):-1}function C(W,X,V){return A(W,t,X,V)}function M(W,X,V){return A(W,r,X,V)}function D(W,X,V){return A(W,n,X,V)}function k(W){return o[W.getDay()]}function L(W){return a[W.getDay()]}function F(W){return c[W.getMonth()]}function q(W){return u[W.getMonth()]}function G(W){return i[+(W.getHours()>=12)]}function z(W){return 1+~~(W.getMonth()/3)}function K(W){return o[W.getUTCDay()]}function ce(W){return a[W.getUTCDay()]}function ve(W){return c[W.getUTCMonth()]}function Ne(W){return u[W.getUTCMonth()]}function kt(W){return i[+(W.getUTCHours()>=12)]}function Ce(W){return 1+~~(W.getUTCMonth()/3)}return{format:function(W){var X=g(W+="",x);return X.toString=function(){return W},X},parse:function(W){var X=O(W+="",!1);return X.toString=function(){return W},X},utcFormat:function(W){var X=g(W+="",S);return X.toString=function(){return W},X},utcParse:function(W){var X=O(W+="",!0);return X.toString=function(){return W},X}}}var mf={"-":"",_:" ",0:"0"},Ae=/^\s*\d+/,Kj=/^%/,Xj=/[\\^$*+?|[\]().{}]/g;function te(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?new Array(r-a+1).join(t)+i:i)}function Vj(e){return e.replace(Xj,"\\$&")}function tn(e){return new RegExp("^(?:"+e.map(Vj).join("|")+")","i")}function rn(e){return new Map(e.map((t,r)=>[t.toLowerCase(),r]))}function Yj(e,t,r){var n=Ae.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function Zj(e,t,r){var n=Ae.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function Jj(e,t,r){var n=Ae.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function Qj(e,t,r){var n=Ae.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function eM(e,t,r){var n=Ae.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function gf(e,t,r){var n=Ae.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function bf(e,t,r){var n=Ae.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function tM(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function rM(e,t,r){var n=Ae.exec(t.slice(r,r+1));return n?(e.q=n[0]*3-3,r+n[0].length):-1}function nM(e,t,r){var n=Ae.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function xf(e,t,r){var n=Ae.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function iM(e,t,r){var n=Ae.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function wf(e,t,r){var n=Ae.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function aM(e,t,r){var n=Ae.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function oM(e,t,r){var n=Ae.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function uM(e,t,r){var n=Ae.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function cM(e,t,r){var n=Ae.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function sM(e,t,r){var n=Kj.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function lM(e,t,r){var n=Ae.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function fM(e,t,r){var n=Ae.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function Of(e,t){return te(e.getDate(),t,2)}function pM(e,t){return te(e.getHours(),t,2)}function hM(e,t){return te(e.getHours()%12||12,t,2)}function dM(e,t){return te(1+ri.count(gt(e),e),t,3)}function Zd(e,t){return te(e.getMilliseconds(),t,3)}function vM(e,t){return Zd(e,t)+"000"}function yM(e,t){return te(e.getMonth()+1,t,2)}function mM(e,t){return te(e.getMinutes(),t,2)}function gM(e,t){return te(e.getSeconds(),t,2)}function bM(e){var t=e.getDay();return t===0?7:t}function xM(e,t){return te(Ua.count(gt(e)-1,e),t,2)}function Jd(e){var t=e.getDay();return t>=4||t===0?wr(e):wr.ceil(e)}function wM(e,t){return e=Jd(e),te(wr.count(gt(e),e)+(gt(e).getDay()===4),t,2)}function OM(e){return e.getDay()}function SM(e,t){return te(Hi.count(gt(e)-1,e),t,2)}function AM(e,t){return te(e.getFullYear()%100,t,2)}function _M(e,t){return e=Jd(e),te(e.getFullYear()%100,t,2)}function PM(e,t){return te(e.getFullYear()%1e4,t,4)}function $M(e,t){var r=e.getDay();return e=r>=4||r===0?wr(e):wr.ceil(e),te(e.getFullYear()%1e4,t,4)}function TM(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+te(t/60|0,"0",2)+te(t%60,"0",2)}function Sf(e,t){return te(e.getUTCDate(),t,2)}function EM(e,t){return te(e.getUTCHours(),t,2)}function jM(e,t){return te(e.getUTCHours()%12||12,t,2)}function MM(e,t){return te(1+za.count(bt(e),e),t,3)}function Qd(e,t){return te(e.getUTCMilliseconds(),t,3)}function CM(e,t){return Qd(e,t)+"000"}function IM(e,t){return te(e.getUTCMonth()+1,t,2)}function DM(e,t){return te(e.getUTCMinutes(),t,2)}function kM(e,t){return te(e.getUTCSeconds(),t,2)}function NM(e){var t=e.getUTCDay();return t===0?7:t}function LM(e,t){return te(qa.count(bt(e)-1,e),t,2)}function ev(e){var t=e.getUTCDay();return t>=4||t===0?Or(e):Or.ceil(e)}function BM(e,t){return e=ev(e),te(Or.count(bt(e),e)+(bt(e).getUTCDay()===4),t,2)}function RM(e){return e.getUTCDay()}function FM(e,t){return te(Gi.count(bt(e)-1,e),t,2)}function WM(e,t){return te(e.getUTCFullYear()%100,t,2)}function zM(e,t){return e=ev(e),te(e.getUTCFullYear()%100,t,2)}function UM(e,t){return te(e.getUTCFullYear()%1e4,t,4)}function qM(e,t){var r=e.getUTCDay();return e=r>=4||r===0?Or(e):Or.ceil(e),te(e.getUTCFullYear()%1e4,t,4)}function HM(){return"+0000"}function Af(){return"%"}function _f(e){return+e}function Pf(e){return Math.floor(+e/1e3)}var ar,tv,rv;GM({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function GM(e){return ar=Gj(e),tv=ar.format,ar.parse,rv=ar.utcFormat,ar.utcParse,ar}function KM(e){return new Date(e)}function XM(e){return e instanceof Date?+e:+new Date(+e)}function es(e,t,r,n,i,a,o,u,c,s){var f=Fc(),l=f.invert,p=f.domain,h=s(".%L"),d=s(":%S"),v=s("%I:%M"),y=s("%I %p"),b=s("%a %d"),w=s("%b %d"),x=s("%B"),S=s("%Y");function m(g){return(c(g)<g?h:u(g)<g?d:o(g)<g?v:a(g)<g?y:n(g)<g?i(g)<g?b:w:r(g)<g?x:S)(g)}return f.invert=function(g){return new Date(l(g))},f.domain=function(g){return arguments.length?p(Array.from(g,XM)):p().map(KM)},f.ticks=function(g){var O=p();return e(O[0],O[O.length-1],g??10)},f.tickFormat=function(g,O){return O==null?m:s(O)},f.nice=function(g){var O=p();return(!g||typeof g.range!="function")&&(g=t(O[0],O[O.length-1],g??10)),g?p(zd(O,g)):f},f.copy=function(){return ti(f,es(e,t,r,n,i,a,o,u,c,s))},f}function VM(){return Ve.apply(es(qj,Hj,gt,Jc,Ua,ri,Yc,Xc,qt,tv).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function YM(){return Ve.apply(es(zj,Uj,bt,Qc,qa,za,Zc,Vc,qt,rv).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function Ha(){var e=0,t=1,r,n,i,a,o=Me,u=!1,c;function s(l){return l==null||isNaN(l=+l)?c:o(i===0?.5:(l=(a(l)-r)*i,u?Math.max(0,Math.min(1,l)):l))}s.domain=function(l){return arguments.length?([e,t]=l,r=a(e=+e),n=a(t=+t),i=r===n?0:1/(n-r),s):[e,t]},s.clamp=function(l){return arguments.length?(u=!!l,s):u},s.interpolator=function(l){return arguments.length?(o=l,s):o};function f(l){return function(p){var h,d;return arguments.length?([h,d]=p,o=l(h,d),s):[o(0),o(1)]}}return s.range=f(Gr),s.rangeRound=f(Rc),s.unknown=function(l){return arguments.length?(c=l,s):c},function(l){return a=l,r=l(e),n=l(t),i=r===n?0:1/(n-r),s}}function It(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function nv(){var e=Ct(Ha()(Me));return e.copy=function(){return It(e,nv())},St.apply(e,arguments)}function iv(){var e=Uc(Ha()).domain([1,10]);return e.copy=function(){return It(e,iv()).base(e.base())},St.apply(e,arguments)}function av(){var e=qc(Ha());return e.copy=function(){return It(e,av()).constant(e.constant())},St.apply(e,arguments)}function ts(){var e=Hc(Ha());return e.copy=function(){return It(e,ts()).exponent(e.exponent())},St.apply(e,arguments)}function ZM(){return ts.apply(null,arguments).exponent(.5)}function ov(){var e=[],t=Me;function r(n){if(n!=null&&!isNaN(n=+n))return t((Qn(e,n,1)-1)/(e.length-1))}return r.domain=function(n){if(!arguments.length)return e.slice();e=[];for(let i of n)i!=null&&!isNaN(i=+i)&&e.push(i);return e.sort(Tt),r},r.interpolator=function(n){return arguments.length?(t=n,r):t},r.range=function(){return e.map((n,i)=>t(i/(e.length-1)))},r.quantiles=function(n){return Array.from({length:n+1},(i,a)=>LE(e,a/n))},r.copy=function(){return ov(t).domain(e)},St.apply(r,arguments)}function Ga(){var e=0,t=.5,r=1,n=1,i,a,o,u,c,s=Me,f,l=!1,p;function h(v){return isNaN(v=+v)?p:(v=.5+((v=+f(v))-a)*(n*v<n*a?u:c),s(l?Math.max(0,Math.min(1,v)):v))}h.domain=function(v){return arguments.length?([e,t,r]=v,i=f(e=+e),a=f(t=+t),o=f(r=+r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,h):[e,t,r]},h.clamp=function(v){return arguments.length?(l=!!v,h):l},h.interpolator=function(v){return arguments.length?(s=v,h):s};function d(v){return function(y){var b,w,x;return arguments.length?([b,w,x]=y,s=sj(v,[b,w,x]),h):[s(0),s(.5),s(1)]}}return h.range=d(Gr),h.rangeRound=d(Rc),h.unknown=function(v){return arguments.length?(p=v,h):p},function(v){return f=v,i=v(e),a=v(t),o=v(r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,h}}function uv(){var e=Ct(Ga()(Me));return e.copy=function(){return It(e,uv())},St.apply(e,arguments)}function cv(){var e=Uc(Ga()).domain([.1,1,10]);return e.copy=function(){return It(e,cv()).base(e.base())},St.apply(e,arguments)}function sv(){var e=qc(Ga());return e.copy=function(){return It(e,sv()).constant(e.constant())},St.apply(e,arguments)}function rs(){var e=Hc(Ga());return e.copy=function(){return It(e,rs()).exponent(e.exponent())},St.apply(e,arguments)}function JM(){return rs.apply(null,arguments).exponent(.5)}const $f=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:wn,scaleDiverging:uv,scaleDivergingLog:cv,scaleDivergingPow:rs,scaleDivergingSqrt:JM,scaleDivergingSymlog:sv,scaleIdentity:Wd,scaleImplicit:vu,scaleLinear:Ui,scaleLog:Ud,scaleOrdinal:Nc,scalePoint:ln,scalePow:Gc,scaleQuantile:Gd,scaleQuantize:Kd,scaleRadial:Hd,scaleSequential:nv,scaleSequentialLog:iv,scaleSequentialPow:ts,scaleSequentialQuantile:ov,scaleSequentialSqrt:ZM,scaleSequentialSymlog:av,scaleSqrt:Cj,scaleSymlog:qd,scaleThreshold:Xd,scaleTime:VM,scaleUtc:YM,tickFormat:Fd},Symbol.toStringTag,{value:"Module"}));var QM=Lr;function eC(e,t,r){for(var n=-1,i=e.length;++n<i;){var a=e[n],o=t(a);if(o!=null&&(u===void 0?o===o&&!QM(o):r(o,u)))var u=o,c=a}return c}var lv=eC;function tC(e,t){return e>t}var rC=tC,nC=lv,iC=rC,aC=qr;function oC(e){return e&&e.length?nC(e,aC,iC):void 0}var uC=oC;const Ka=ue(uC);function cC(e,t){return e<t}var sC=cC,lC=lv,fC=sC,pC=qr;function hC(e){return e&&e.length?lC(e,pC,fC):void 0}var dC=hC;const Xa=ue(dC);var vC=yc,yC=Mt,mC=gd,gC=ke;function bC(e,t){var r=gC(e)?vC:mC;return r(e,yC(t))}var xC=bC,wC=yd,OC=xC;function SC(e,t){return wC(OC(e,t),1)}var AC=SC;const _C=ue(AC);var PC=Mc;function $C(e,t){return PC(e,t)}var TC=$C;const Va=ue(TC);var Kr=1e9,EC={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},is,fe=!0,Ke="[DecimalError] ",Kt=Ke+"Invalid argument: ",ns=Ke+"Exponent out of range: ",Xr=Math.floor,Wt=Math.pow,jC=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,Fe,Se=1e7,le=7,fv=9007199254740991,Ki=Xr(fv/le),U={};U.absoluteValue=U.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e};U.comparedTo=U.cmp=function(e){var t,r,n,i,a=this;if(e=new a.constructor(e),a.s!==e.s)return a.s||-e.s;if(a.e!==e.e)return a.e>e.e^a.s<0?1:-1;for(n=a.d.length,i=e.d.length,t=0,r=n<i?n:i;t<r;++t)if(a.d[t]!==e.d[t])return a.d[t]>e.d[t]^a.s<0?1:-1;return n===i?0:n>i^a.s<0?1:-1};U.decimalPlaces=U.dp=function(){var e=this,t=e.d.length-1,r=(t-e.e)*le;if(t=e.d[t],t)for(;t%10==0;t/=10)r--;return r<0?0:r};U.dividedBy=U.div=function(e){return yt(this,new this.constructor(e))};U.dividedToIntegerBy=U.idiv=function(e){var t=this,r=t.constructor;return ae(yt(t,new r(e),0,1),r.precision)};U.equals=U.eq=function(e){return!this.cmp(e)};U.exponent=function(){return me(this)};U.greaterThan=U.gt=function(e){return this.cmp(e)>0};U.greaterThanOrEqualTo=U.gte=function(e){return this.cmp(e)>=0};U.isInteger=U.isint=function(){return this.e>this.d.length-2};U.isNegative=U.isneg=function(){return this.s<0};U.isPositive=U.ispos=function(){return this.s>0};U.isZero=function(){return this.s===0};U.lessThan=U.lt=function(e){return this.cmp(e)<0};U.lessThanOrEqualTo=U.lte=function(e){return this.cmp(e)<1};U.logarithm=U.log=function(e){var t,r=this,n=r.constructor,i=n.precision,a=i+5;if(e===void 0)e=new n(10);else if(e=new n(e),e.s<1||e.eq(Fe))throw Error(Ke+"NaN");if(r.s<1)throw Error(Ke+(r.s?"NaN":"-Infinity"));return r.eq(Fe)?new n(0):(fe=!1,t=yt(Pn(r,a),Pn(e,a),a),fe=!0,ae(t,i))};U.minus=U.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?dv(t,e):pv(t,(e.s=-e.s,e))};U.modulo=U.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(e=new n(e),!e.s)throw Error(Ke+"NaN");return r.s?(fe=!1,t=yt(r,e,0,1).times(e),fe=!0,r.minus(t)):ae(new n(r),i)};U.naturalExponential=U.exp=function(){return hv(this)};U.naturalLogarithm=U.ln=function(){return Pn(this)};U.negated=U.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e};U.plus=U.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?pv(t,e):dv(t,(e.s=-e.s,e))};U.precision=U.sd=function(e){var t,r,n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(Kt+e);if(t=me(i)+1,n=i.d.length-1,r=n*le+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r};U.squareRoot=U.sqrt=function(){var e,t,r,n,i,a,o,u=this,c=u.constructor;if(u.s<1){if(!u.s)return new c(0);throw Error(Ke+"NaN")}for(e=me(u),fe=!1,i=Math.sqrt(+u),i==0||i==1/0?(t=it(u.d),(t.length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=Xr((e+1)/2)-(e<0||e%2),i==1/0?t="5e"+e:(t=i.toExponential(),t=t.slice(0,t.indexOf("e")+1)+e),n=new c(t)):n=new c(i.toString()),r=c.precision,i=o=r+3;;)if(a=n,n=a.plus(yt(u,a,o+2)).times(.5),it(a.d).slice(0,o)===(t=it(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&t=="4999"){if(ae(a,r+1,0),a.times(a).eq(u)){n=a;break}}else if(t!="9999")break;o+=4}return fe=!0,ae(n,r)};U.times=U.mul=function(e){var t,r,n,i,a,o,u,c,s,f=this,l=f.constructor,p=f.d,h=(e=new l(e)).d;if(!f.s||!e.s)return new l(0);for(e.s*=f.s,r=f.e+e.e,c=p.length,s=h.length,c<s&&(a=p,p=h,h=a,o=c,c=s,s=o),a=[],o=c+s,n=o;n--;)a.push(0);for(n=s;--n>=0;){for(t=0,i=c+n;i>n;)u=a[i]+h[n]*p[i-n-1]+t,a[i--]=u%Se|0,t=u/Se|0;a[i]=(a[i]+t)%Se|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,fe?ae(e,l.precision):e};U.toDecimalPlaces=U.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(ut(e,0,Kr),t===void 0?t=n.rounding:ut(t,0,8),ae(r,e+me(r)+1,t))};U.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=Yt(n,!0):(ut(e,0,Kr),t===void 0?t=i.rounding:ut(t,0,8),n=ae(new i(n),e+1,t),r=Yt(n,!0,e+1)),r};U.toFixed=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?Yt(i):(ut(e,0,Kr),t===void 0?t=a.rounding:ut(t,0,8),n=ae(new a(i),e+me(i)+1,t),r=Yt(n.abs(),!1,e+me(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};U.toInteger=U.toint=function(){var e=this,t=e.constructor;return ae(new t(e),me(e)+1,t.rounding)};U.toNumber=function(){return+this};U.toPower=U.pow=function(e){var t,r,n,i,a,o,u=this,c=u.constructor,s=12,f=+(e=new c(e));if(!e.s)return new c(Fe);if(u=new c(u),!u.s){if(e.s<1)throw Error(Ke+"Infinity");return u}if(u.eq(Fe))return u;if(n=c.precision,e.eq(Fe))return ae(u,n);if(t=e.e,r=e.d.length-1,o=t>=r,a=u.s,o){if((r=f<0?-f:f)<=fv){for(i=new c(Fe),t=Math.ceil(n/le+4),fe=!1;r%2&&(i=i.times(u),Ef(i.d,t)),r=Xr(r/2),r!==0;)u=u.times(u),Ef(u.d,t);return fe=!0,e.s<0?new c(Fe).div(i):ae(i,n)}}else if(a<0)throw Error(Ke+"NaN");return a=a<0&&e.d[Math.max(t,r)]&1?-1:1,u.s=1,fe=!1,i=e.times(Pn(u,n+s)),fe=!0,i=hv(i),i.s=a,i};U.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?(r=me(i),n=Yt(i,r<=a.toExpNeg||r>=a.toExpPos)):(ut(e,1,Kr),t===void 0?t=a.rounding:ut(t,0,8),i=ae(new a(i),e,t),r=me(i),n=Yt(i,e<=r||r<=a.toExpNeg,e)),n};U.toSignificantDigits=U.tosd=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(ut(e,1,Kr),t===void 0?t=n.rounding:ut(t,0,8)),ae(new n(r),e,t)};U.toString=U.valueOf=U.val=U.toJSON=U[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=me(e),r=e.constructor;return Yt(e,t<=r.toExpNeg||t>=r.toExpPos)};function pv(e,t){var r,n,i,a,o,u,c,s,f=e.constructor,l=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),fe?ae(t,l):t;if(c=e.d,s=t.d,o=e.e,i=t.e,c=c.slice(),a=o-i,a){for(a<0?(n=c,a=-a,u=s.length):(n=s,i=o,u=c.length),o=Math.ceil(l/le),u=o>u?o+1:u+1,a>u&&(a=u,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for(u=c.length,a=s.length,u-a<0&&(a=u,n=s,s=c,c=n),r=0;a;)r=(c[--a]=c[a]+s[a]+r)/Se|0,c[a]%=Se;for(r&&(c.unshift(r),++i),u=c.length;c[--u]==0;)c.pop();return t.d=c,t.e=i,fe?ae(t,l):t}function ut(e,t,r){if(e!==~~e||e<t||e>r)throw Error(Kt+e)}function it(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)n=e[t]+"",r=le-n.length,r&&(a+=_t(r)),a+=n;o=e[t],n=o+"",r=le-n.length,r&&(a+=_t(r))}else if(o===0)return"0";for(;o%10===0;)o/=10;return a+o}var yt=function(){function e(n,i){var a,o=0,u=n.length;for(n=n.slice();u--;)a=n[u]*i+o,n[u]=a%Se|0,o=a/Se|0;return o&&n.unshift(o),n}function t(n,i,a,o){var u,c;if(a!=o)c=a>o?1:-1;else for(u=c=0;u<a;u++)if(n[u]!=i[u]){c=n[u]>i[u]?1:-1;break}return c}function r(n,i,a){for(var o=0;a--;)n[a]-=o,o=n[a]<i[a]?1:0,n[a]=o*Se+n[a]-i[a];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,a,o){var u,c,s,f,l,p,h,d,v,y,b,w,x,S,m,g,O,A,P=n.constructor,j=n.s==i.s?1:-1,$=n.d,T=i.d;if(!n.s)return new P(n);if(!i.s)throw Error(Ke+"Division by zero");for(c=n.e-i.e,O=T.length,m=$.length,h=new P(j),d=h.d=[],s=0;T[s]==($[s]||0);)++s;if(T[s]>($[s]||0)&&--c,a==null?w=a=P.precision:o?w=a+(me(n)-me(i))+1:w=a,w<0)return new P(0);if(w=w/le+2|0,s=0,O==1)for(f=0,T=T[0],w++;(s<m||f)&&w--;s++)x=f*Se+($[s]||0),d[s]=x/T|0,f=x%T|0;else{for(f=Se/(T[0]+1)|0,f>1&&(T=e(T,f),$=e($,f),O=T.length,m=$.length),S=O,v=$.slice(0,O),y=v.length;y<O;)v[y++]=0;A=T.slice(),A.unshift(0),g=T[0],T[1]>=Se/2&&++g;do f=0,u=t(T,v,O,y),u<0?(b=v[0],O!=y&&(b=b*Se+(v[1]||0)),f=b/g|0,f>1?(f>=Se&&(f=Se-1),l=e(T,f),p=l.length,y=v.length,u=t(l,v,p,y),u==1&&(f--,r(l,O<p?A:T,p))):(f==0&&(u=f=1),l=T.slice()),p=l.length,p<y&&l.unshift(0),r(v,l,y),u==-1&&(y=v.length,u=t(T,v,O,y),u<1&&(f++,r(v,O<y?A:T,y))),y=v.length):u===0&&(f++,v=[0]),d[s++]=f,u&&v[0]?v[y++]=$[S]||0:(v=[$[S]],y=1);while((S++<m||v[0]!==void 0)&&w--)}return d[0]||d.shift(),h.e=c,ae(h,o?a+me(h)+1:a)}}();function hv(e,t){var r,n,i,a,o,u,c=0,s=0,f=e.constructor,l=f.precision;if(me(e)>16)throw Error(ns+me(e));if(!e.s)return new f(Fe);for(fe=!1,u=l,o=new f(.03125);e.abs().gte(.1);)e=e.times(o),s+=5;for(n=Math.log(Wt(2,s))/Math.LN10*2+5|0,u+=n,r=i=a=new f(Fe),f.precision=u;;){if(i=ae(i.times(e),u),r=r.times(++c),o=a.plus(yt(i,r,u)),it(o.d).slice(0,u)===it(a.d).slice(0,u)){for(;s--;)a=ae(a.times(a),u);return f.precision=l,t==null?(fe=!0,ae(a,l)):a}a=o}}function me(e){for(var t=e.e*le,r=e.d[0];r>=10;r/=10)t++;return t}function jo(e,t,r){if(t>e.LN10.sd())throw fe=!0,r&&(e.precision=r),Error(Ke+"LN10 precision limit exceeded");return ae(new e(e.LN10),t)}function _t(e){for(var t="";e--;)t+="0";return t}function Pn(e,t){var r,n,i,a,o,u,c,s,f,l=1,p=10,h=e,d=h.d,v=h.constructor,y=v.precision;if(h.s<1)throw Error(Ke+(h.s?"NaN":"-Infinity"));if(h.eq(Fe))return new v(0);if(t==null?(fe=!1,s=y):s=t,h.eq(10))return t==null&&(fe=!0),jo(v,s);if(s+=p,v.precision=s,r=it(d),n=r.charAt(0),a=me(h),Math.abs(a)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)h=h.times(e),r=it(h.d),n=r.charAt(0),l++;a=me(h),n>1?(h=new v("0."+r),a++):h=new v(n+"."+r.slice(1))}else return c=jo(v,s+2,y).times(a+""),h=Pn(new v(n+"."+r.slice(1)),s-p).plus(c),v.precision=y,t==null?(fe=!0,ae(h,y)):h;for(u=o=h=yt(h.minus(Fe),h.plus(Fe),s),f=ae(h.times(h),s),i=3;;){if(o=ae(o.times(f),s),c=u.plus(yt(o,new v(i),s)),it(c.d).slice(0,s)===it(u.d).slice(0,s))return u=u.times(2),a!==0&&(u=u.plus(jo(v,s+2,y).times(a+""))),u=yt(u,new v(l),s),v.precision=y,t==null?(fe=!0,ae(u,y)):u;u=c,i+=2}}function Tf(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;)++n;for(i=t.length;t.charCodeAt(i-1)===48;)--i;if(t=t.slice(n,i),t){if(i-=n,r=r-n-1,e.e=Xr(r/le),e.d=[],n=(r+1)%le,r<0&&(n+=le),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=le;n<i;)e.d.push(+t.slice(n,n+=le));t=t.slice(n),n=le-t.length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),fe&&(e.e>Ki||e.e<-Ki))throw Error(ns+r)}else e.s=0,e.e=0,e.d=[0];return e}function ae(e,t,r){var n,i,a,o,u,c,s,f,l=e.d;for(o=1,a=l[0];a>=10;a/=10)o++;if(n=t-o,n<0)n+=le,i=t,s=l[f=0];else{if(f=Math.ceil((n+1)/le),a=l.length,f>=a)return e;for(s=a=l[f],o=1;a>=10;a/=10)o++;n%=le,i=n-le+o}if(r!==void 0&&(a=Wt(10,o-i-1),u=s/a%10|0,c=t<0||l[f+1]!==void 0||s%a,c=r<4?(u||c)&&(r==0||r==(e.s<0?3:2)):u>5||u==5&&(r==4||c||r==6&&(n>0?i>0?s/Wt(10,o-i):0:l[f-1])%10&1||r==(e.s<0?8:7))),t<1||!l[0])return c?(a=me(e),l.length=1,t=t-a-1,l[0]=Wt(10,(le-t%le)%le),e.e=Xr(-t/le)||0):(l.length=1,l[0]=e.e=e.s=0),e;if(n==0?(l.length=f,a=1,f--):(l.length=f+1,a=Wt(10,le-n),l[f]=i>0?(s/Wt(10,o-i)%Wt(10,i)|0)*a:0),c)for(;;)if(f==0){(l[0]+=a)==Se&&(l[0]=1,++e.e);break}else{if(l[f]+=a,l[f]!=Se)break;l[f--]=0,a=1}for(n=l.length;l[--n]===0;)l.pop();if(fe&&(e.e>Ki||e.e<-Ki))throw Error(ns+me(e));return e}function dv(e,t){var r,n,i,a,o,u,c,s,f,l,p=e.constructor,h=p.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new p(e),fe?ae(t,h):t;if(c=e.d,l=t.d,n=t.e,s=e.e,c=c.slice(),o=s-n,o){for(f=o<0,f?(r=c,o=-o,u=l.length):(r=l,n=s,u=c.length),i=Math.max(Math.ceil(h/le),u)+2,o>i&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for(i=c.length,u=l.length,f=i<u,f&&(u=i),i=0;i<u;i++)if(c[i]!=l[i]){f=c[i]<l[i];break}o=0}for(f&&(r=c,c=l,l=r,t.s=-t.s),u=c.length,i=l.length-u;i>0;--i)c[u++]=0;for(i=l.length;i>o;){if(c[--i]<l[i]){for(a=i;a&&c[--a]===0;)c[a]=Se-1;--c[a],c[i]+=Se}c[i]-=l[i]}for(;c[--u]===0;)c.pop();for(;c[0]===0;c.shift())--n;return c[0]?(t.d=c,t.e=n,fe?ae(t,h):t):new p(0)}function Yt(e,t,r){var n,i=me(e),a=it(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+_t(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+_t(-i-1)+a,r&&(n=r-o)>0&&(a+=_t(n))):i>=o?(a+=_t(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+_t(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=_t(n))),e.s<0?"-"+a:a}function Ef(e,t){if(e.length>t)return e.length=t,!0}function vv(e){var t,r,n;function i(a){var o=this;if(!(o instanceof i))return new i(a);if(o.constructor=i,a instanceof i){o.s=a.s,o.e=a.e,o.d=(a=a.d)?a.slice():a;return}if(typeof a=="number"){if(a*0!==0)throw Error(Kt+a);if(a>0)o.s=1;else if(a<0)a=-a,o.s=-1;else{o.s=0,o.e=0,o.d=[0];return}if(a===~~a&&a<1e7){o.e=0,o.d=[a];return}return Tf(o,a.toString())}else if(typeof a!="string")throw Error(Kt+a);if(a.charCodeAt(0)===45?(a=a.slice(1),o.s=-1):o.s=1,jC.test(a))Tf(o,a);else throw Error(Kt+a)}if(i.prototype=U,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=vv,i.config=i.set=MC,e===void 0&&(e={}),e)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function MC(e){if(!e||typeof e!="object")throw Error(Ke+"Object expected");var t,r,n,i=["precision",1,Kr,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if((n=e[r=i[t]])!==void 0)if(Xr(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(Kt+r+": "+n);if((n=e[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(Kt+r+": "+n);return this}var is=vv(EC);Fe=new is(1);const ie=is;function CC(e){return NC(e)||kC(e)||DC(e)||IC()}function IC(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function DC(e,t){if(e){if(typeof e=="string")return bu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return bu(e,t)}}function kC(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function NC(e){if(Array.isArray(e))return bu(e)}function bu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var LC=function(t){return t},yv={},mv=function(t){return t===yv},jf=function(t){return function r(){return arguments.length===0||arguments.length===1&&mv(arguments.length<=0?void 0:arguments[0])?r:t.apply(void 0,arguments)}},BC=function e(t,r){return t===1?r:jf(function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];var o=i.filter(function(u){return u!==yv}).length;return o>=t?r.apply(void 0,i):e(t-o,jf(function(){for(var u=arguments.length,c=new Array(u),s=0;s<u;s++)c[s]=arguments[s];var f=i.map(function(l){return mv(l)?c.shift():l});return r.apply(void 0,CC(f).concat(c))}))})},Ya=function(t){return BC(t.length,t)},xu=function(t,r){for(var n=[],i=t;i<r;++i)n[i-t]=i;return n},RC=Ya(function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map(function(r){return t[r]}).map(e)}),FC=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];if(!r.length)return LC;var i=r.reverse(),a=i[0],o=i.slice(1);return function(){return o.reduce(function(u,c){return c(u)},a.apply(void 0,arguments))}},wu=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},gv=function(t){var r=null,n=null;return function(){for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return r&&a.every(function(u,c){return u===r[c]})||(r=a,n=t.apply(void 0,a)),n}};function WC(e){var t;return e===0?t=1:t=Math.floor(new ie(e).abs().log(10).toNumber())+1,t}function zC(e,t,r){for(var n=new ie(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}var UC=Ya(function(e,t,r){var n=+e,i=+t;return n+r*(i-n)}),qC=Ya(function(e,t,r){var n=t-+e;return n=n||1/0,(r-e)/n}),HC=Ya(function(e,t,r){var n=t-+e;return n=n||1/0,Math.max(0,Math.min(1,(r-e)/n))});const Za={rangeStep:zC,getDigitCount:WC,interpolateNumber:UC,uninterpolateNumber:qC,uninterpolateTruncation:HC};function Ou(e){return XC(e)||KC(e)||bv(e)||GC()}function GC(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function KC(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function XC(e){if(Array.isArray(e))return Su(e)}function $n(e,t){return ZC(e)||YC(e,t)||bv(e,t)||VC()}function VC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function bv(e,t){if(e){if(typeof e=="string")return Su(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Su(e,t)}}function Su(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function YC(e,t){if(!(typeof Symbol>"u"||!(Symbol.iterator in Object(e)))){var r=[],n=!0,i=!1,a=void 0;try{for(var o=e[Symbol.iterator](),u;!(n=(u=o.next()).done)&&(r.push(u.value),!(t&&r.length===t));n=!0);}catch(c){i=!0,a=c}finally{try{!n&&o.return!=null&&o.return()}finally{if(i)throw a}}return r}}function ZC(e){if(Array.isArray(e))return e}function xv(e){var t=$n(e,2),r=t[0],n=t[1],i=r,a=n;return r>n&&(i=n,a=r),[i,a]}function wv(e,t,r){if(e.lte(0))return new ie(0);var n=Za.getDigitCount(e.toNumber()),i=new ie(10).pow(n),a=e.div(i),o=n!==1?.05:.1,u=new ie(Math.ceil(a.div(o).toNumber())).add(r).mul(o),c=u.mul(i);return t?c:new ie(Math.ceil(c))}function JC(e,t,r){var n=1,i=new ie(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new ie(10).pow(Za.getDigitCount(e)-1),i=new ie(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new ie(Math.floor(e)))}else e===0?i=new ie(Math.floor((t-1)/2)):r||(i=new ie(Math.floor(e)));var o=Math.floor((t-1)/2),u=FC(RC(function(c){return i.add(new ie(c-o).mul(n)).toNumber()}),xu);return u(0,t)}function Ov(e,t,r,n){var i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new ie(0),tickMin:new ie(0),tickMax:new ie(0)};var a=wv(new ie(t).sub(e).div(r-1),n,i),o;e<=0&&t>=0?o=new ie(0):(o=new ie(e).add(t).div(2),o=o.sub(new ie(o).mod(a)));var u=Math.ceil(o.sub(e).div(a).toNumber()),c=Math.ceil(new ie(t).sub(o).div(a).toNumber()),s=u+c+1;return s>r?Ov(e,t,r,n,i+1):(s<r&&(c=t>0?c+(r-s):c,u=t>0?u:u+(r-s)),{step:a,tickMin:o.sub(new ie(u).mul(a)),tickMax:o.add(new ie(c).mul(a))})}function QC(e){var t=$n(e,2),r=t[0],n=t[1],i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:6,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=Math.max(i,2),u=xv([r,n]),c=$n(u,2),s=c[0],f=c[1];if(s===-1/0||f===1/0){var l=f===1/0?[s].concat(Ou(xu(0,i-1).map(function(){return 1/0}))):[].concat(Ou(xu(0,i-1).map(function(){return-1/0})),[f]);return r>n?wu(l):l}if(s===f)return JC(s,i,a);var p=Ov(s,f,o,a),h=p.step,d=p.tickMin,v=p.tickMax,y=Za.rangeStep(d,v.add(new ie(.1).mul(h)),h);return r>n?wu(y):y}function eI(e,t){var r=$n(e,2),n=r[0],i=r[1],a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=xv([n,i]),u=$n(o,2),c=u[0],s=u[1];if(c===-1/0||s===1/0)return[n,i];if(c===s)return[c];var f=Math.max(t,2),l=wv(new ie(s).sub(c).div(f-1),a,0),p=[].concat(Ou(Za.rangeStep(new ie(c),new ie(s).sub(new ie(.99).mul(l)),l)),[s]);return n>i?wu(p):p}var tI=gv(QC),rI=gv(eI),nI="Invariant failed";function Zt(e,t){throw new Error(nI)}var iI=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function Sr(e){"@babel/helpers - typeof";return Sr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Sr(e)}function Xi(){return Xi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Xi.apply(this,arguments)}function aI(e,t){return sI(e)||cI(e,t)||uI(e,t)||oI()}function oI(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function uI(e,t){if(e){if(typeof e=="string")return Mf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Mf(e,t)}}function Mf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function cI(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function sI(e){if(Array.isArray(e))return e}function lI(e,t){if(e==null)return{};var r=fI(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function fI(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function pI(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function hI(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,_v(n.key),n)}}function dI(e,t,r){return t&&hI(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function vI(e,t,r){return t=Vi(t),yI(e,Sv()?Reflect.construct(t,r||[],Vi(e).constructor):t.apply(e,r))}function yI(e,t){if(t&&(Sr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return mI(e)}function mI(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Sv(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Sv=function(){return!!e})()}function Vi(e){return Vi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Vi(e)}function gI(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Au(e,t)}function Au(e,t){return Au=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Au(e,t)}function Av(e,t,r){return t=_v(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _v(e){var t=bI(e,"string");return Sr(t)=="symbol"?t:t+""}function bI(e,t){if(Sr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Sr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var ni=function(e){function t(){return pI(this,t),vI(this,t,arguments)}return gI(t,e),dI(t,[{key:"render",value:function(){var n=this.props,i=n.offset,a=n.layout,o=n.width,u=n.dataKey,c=n.data,s=n.dataPointFormatter,f=n.xAxis,l=n.yAxis,p=lI(n,iI),h=J(p,!1);this.props.direction==="x"&&f.type!=="number"&&Zt();var d=c.map(function(v){var y=s(v,u),b=y.x,w=y.y,x=y.value,S=y.errorVal;if(!S)return null;var m=[],g,O;if(Array.isArray(S)){var A=aI(S,2);g=A[0],O=A[1]}else g=O=S;if(a==="vertical"){var P=f.scale,j=w+i,$=j+o,T=j-o,I=P(x-g),C=P(x+O);m.push({x1:C,y1:$,x2:C,y2:T}),m.push({x1:I,y1:j,x2:C,y2:j}),m.push({x1:I,y1:$,x2:I,y2:T})}else if(a==="horizontal"){var M=l.scale,D=b+i,k=D-o,L=D+o,F=M(x-g),q=M(x+O);m.push({x1:k,y1:q,x2:L,y2:q}),m.push({x1:D,y1:F,x2:D,y2:q}),m.push({x1:k,y1:F,x2:L,y2:F})}return _.createElement(de,Xi({className:"recharts-errorBar",key:"bar-".concat(m.map(function(G){return"".concat(G.x1,"-").concat(G.x2,"-").concat(G.y1,"-").concat(G.y2)}))},h),m.map(function(G){return _.createElement("line",Xi({},G,{key:"line-".concat(G.x1,"-").concat(G.x2,"-").concat(G.y1,"-").concat(G.y2)}))}))});return _.createElement(de,{className:"recharts-errorBars"},d)}}])}(_.Component);Av(ni,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"});Av(ni,"displayName","ErrorBar");function Tn(e){"@babel/helpers - typeof";return Tn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Tn(e)}function Cf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Bt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Cf(Object(r),!0).forEach(function(n){xI(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Cf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function xI(e,t,r){return t=wI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function wI(e){var t=OI(e,"string");return Tn(t)=="symbol"?t:t+""}function OI(e,t){if(Tn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Tn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Pv=function(t){var r=t.children,n=t.formattedGraphicalItems,i=t.legendWidth,a=t.legendContent,o=Re(r,pr);if(!o)return null;var u=pr.defaultProps,c=u!==void 0?Bt(Bt({},u),o.props):{},s;return o.props&&o.props.payload?s=o.props&&o.props.payload:a==="children"?s=(n||[]).reduce(function(f,l){var p=l.item,h=l.props,d=h.sectors||h.data||[];return f.concat(d.map(function(v){return{type:o.props.iconType||p.props.legendType,value:v.name,color:v.fill,payload:v}}))},[]):s=(n||[]).map(function(f){var l=f.item,p=l.type.defaultProps,h=p!==void 0?Bt(Bt({},p),l.props):{},d=h.dataKey,v=h.name,y=h.legendType,b=h.hide;return{inactive:b,dataKey:d,type:c.iconType||y||"square",color:as(l),value:v||d,payload:h}}),Bt(Bt(Bt({},c),pr.getWithHeight(o,i)),{},{payload:s,item:o})};function En(e){"@babel/helpers - typeof";return En=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},En(e)}function If(e){return PI(e)||_I(e)||AI(e)||SI()}function SI(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function AI(e,t){if(e){if(typeof e=="string")return _u(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return _u(e,t)}}function _I(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function PI(e){if(Array.isArray(e))return _u(e)}function _u(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Df(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function he(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Df(Object(r),!0).forEach(function(n){dr(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Df(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function dr(e,t,r){return t=$I(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $I(e){var t=TI(e,"string");return En(t)=="symbol"?t:t+""}function TI(e,t){if(En(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(En(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function We(e,t,r){return Z(e)||Z(t)?r:we(t)?He(e,t,r):Y(t)?t(e):r}function fn(e,t,r,n){var i=_C(e,function(u){return We(u,t)});if(r==="number"){var a=i.filter(function(u){return R(u)||parseFloat(u)});return a.length?[Xa(a),Ka(a)]:[1/0,-1/0]}var o=n?i.filter(function(u){return!Z(u)}):i;return o.map(function(u){return we(u)||u instanceof Date?u:""})}var EI=function(t){var r,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],i=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0,o=-1,u=(r=n==null?void 0:n.length)!==null&&r!==void 0?r:0;if(u<=1)return 0;if(a&&a.axisType==="angleAxis"&&Math.abs(Math.abs(a.range[1]-a.range[0])-360)<=1e-6)for(var c=a.range,s=0;s<u;s++){var f=s>0?i[s-1].coordinate:i[u-1].coordinate,l=i[s].coordinate,p=s>=u-1?i[0].coordinate:i[s+1].coordinate,h=void 0;if(rt(l-f)!==rt(p-l)){var d=[];if(rt(p-l)===rt(c[1]-c[0])){h=p;var v=l+c[1]-c[0];d[0]=Math.min(v,(v+f)/2),d[1]=Math.max(v,(v+f)/2)}else{h=f;var y=p+c[1]-c[0];d[0]=Math.min(l,(y+l)/2),d[1]=Math.max(l,(y+l)/2)}var b=[Math.min(l,(h+l)/2),Math.max(l,(h+l)/2)];if(t>b[0]&&t<=b[1]||t>=d[0]&&t<=d[1]){o=i[s].index;break}}else{var w=Math.min(f,p),x=Math.max(f,p);if(t>(w+l)/2&&t<=(x+l)/2){o=i[s].index;break}}}else for(var S=0;S<u;S++)if(S===0&&t<=(n[S].coordinate+n[S+1].coordinate)/2||S>0&&S<u-1&&t>(n[S].coordinate+n[S-1].coordinate)/2&&t<=(n[S].coordinate+n[S+1].coordinate)/2||S===u-1&&t>(n[S].coordinate+n[S-1].coordinate)/2){o=n[S].index;break}return o},as=function(t){var r,n=t,i=n.type.displayName,a=(r=t.type)!==null&&r!==void 0&&r.defaultProps?he(he({},t.type.defaultProps),t.props):t.props,o=a.stroke,u=a.fill,c;switch(i){case"Line":c=o;break;case"Area":case"Radar":c=o&&o!=="none"?o:u;break;default:c=u;break}return c},jI=function(t){var r=t.barSize,n=t.totalSize,i=t.stackGroups,a=i===void 0?{}:i;if(!a)return{};for(var o={},u=Object.keys(a),c=0,s=u.length;c<s;c++)for(var f=a[u[c]].stackGroups,l=Object.keys(f),p=0,h=l.length;p<h;p++){var d=f[l[p]],v=d.items,y=d.cateAxisId,b=v.filter(function(O){return dt(O.type).indexOf("Bar")>=0});if(b&&b.length){var w=b[0].type.defaultProps,x=w!==void 0?he(he({},w),b[0].props):b[0].props,S=x.barSize,m=x[y];o[m]||(o[m]=[]);var g=Z(S)?r:S;o[m].push({item:b[0],stackList:b.slice(1),barSize:Z(g)?void 0:Vt(g,n,0)})}}return o},MI=function(t){var r=t.barGap,n=t.barCategoryGap,i=t.bandSize,a=t.sizeList,o=a===void 0?[]:a,u=t.maxBarSize,c=o.length;if(c<1)return null;var s=Vt(r,i,0,!0),f,l=[];if(o[0].barSize===+o[0].barSize){var p=!1,h=i/c,d=o.reduce(function(S,m){return S+m.barSize||0},0);d+=(c-1)*s,d>=i&&(d-=(c-1)*s,s=0),d>=i&&h>0&&(p=!0,h*=.9,d=c*h);var v=(i-d)/2>>0,y={offset:v-s,size:0};f=o.reduce(function(S,m){var g={item:m.item,position:{offset:y.offset+y.size+s,size:p?h:m.barSize}},O=[].concat(If(S),[g]);return y=O[O.length-1].position,m.stackList&&m.stackList.length&&m.stackList.forEach(function(A){O.push({item:A,position:y})}),O},l)}else{var b=Vt(n,i,0,!0);i-2*b-(c-1)*s<=0&&(s=0);var w=(i-2*b-(c-1)*s)/c;w>1&&(w>>=0);var x=u===+u?Math.min(w,u):w;f=o.reduce(function(S,m,g){var O=[].concat(If(S),[{item:m.item,position:{offset:b+(w+s)*g+(w-x)/2,size:x}}]);return m.stackList&&m.stackList.length&&m.stackList.forEach(function(A){O.push({item:A,position:O[O.length-1].position})}),O},l)}return f},CI=function(t,r,n,i){var a=n.children,o=n.width,u=n.margin,c=o-(u.left||0)-(u.right||0),s=Pv({children:a,legendWidth:c});if(s){var f=i||{},l=f.width,p=f.height,h=s.align,d=s.verticalAlign,v=s.layout;if((v==="vertical"||v==="horizontal"&&d==="middle")&&h!=="center"&&R(t[h]))return he(he({},t),{},dr({},h,t[h]+(l||0)));if((v==="horizontal"||v==="vertical"&&h==="center")&&d!=="middle"&&R(t[d]))return he(he({},t),{},dr({},d,t[d]+(p||0)))}return t},II=function(t,r,n){return Z(r)?!0:t==="horizontal"?r==="yAxis":t==="vertical"||n==="x"?r==="xAxis":n==="y"?r==="yAxis":!0},$v=function(t,r,n,i,a){var o=r.props.children,u=Ge(o,ni).filter(function(s){return II(i,a,s.props.direction)});if(u&&u.length){var c=u.map(function(s){return s.props.dataKey});return t.reduce(function(s,f){var l=We(f,n);if(Z(l))return s;var p=Array.isArray(l)?[Xa(l),Ka(l)]:[l,l],h=c.reduce(function(d,v){var y=We(f,v,0),b=p[0]-Math.abs(Array.isArray(y)?y[0]:y),w=p[1]+Math.abs(Array.isArray(y)?y[1]:y);return[Math.min(b,d[0]),Math.max(w,d[1])]},[1/0,-1/0]);return[Math.min(h[0],s[0]),Math.max(h[1],s[1])]},[1/0,-1/0])}return null},DI=function(t,r,n,i,a){var o=r.map(function(u){return $v(t,u,n,a,i)}).filter(function(u){return!Z(u)});return o&&o.length?o.reduce(function(u,c){return[Math.min(u[0],c[0]),Math.max(u[1],c[1])]},[1/0,-1/0]):null},Tv=function(t,r,n,i,a){var o=r.map(function(c){var s=c.props.dataKey;return n==="number"&&s&&$v(t,c,s,i)||fn(t,s,n,a)});if(n==="number")return o.reduce(function(c,s){return[Math.min(c[0],s[0]),Math.max(c[1],s[1])]},[1/0,-1/0]);var u={};return o.reduce(function(c,s){for(var f=0,l=s.length;f<l;f++)u[s[f]]||(u[s[f]]=!0,c.push(s[f]));return c},[])},Ev=function(t,r){return t==="horizontal"&&r==="xAxis"||t==="vertical"&&r==="yAxis"||t==="centric"&&r==="angleAxis"||t==="radial"&&r==="radiusAxis"},jv=function(t,r,n,i){if(i)return t.map(function(c){return c.coordinate});var a,o,u=t.map(function(c){return c.coordinate===r&&(a=!0),c.coordinate===n&&(o=!0),c.coordinate});return a||u.push(r),o||u.push(n),u},ht=function(t,r,n){if(!t)return null;var i=t.scale,a=t.duplicateDomain,o=t.type,u=t.range,c=t.realScaleType==="scaleBand"?i.bandwidth()/2:2,s=(r||n)&&o==="category"&&i.bandwidth?i.bandwidth()/c:0;if(s=t.axisType==="angleAxis"&&(u==null?void 0:u.length)>=2?rt(u[0]-u[1])*2*s:s,r&&(t.ticks||t.niceTicks)){var f=(t.ticks||t.niceTicks).map(function(l){var p=a?a.indexOf(l):l;return{coordinate:i(p)+s,value:l,offset:s}});return f.filter(function(l){return!Yn(l.coordinate)})}return t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(l,p){return{coordinate:i(l)+s,value:l,index:p,offset:s}}):i.ticks&&!n?i.ticks(t.tickCount).map(function(l){return{coordinate:i(l)+s,value:l,offset:s}}):i.domain().map(function(l,p){return{coordinate:i(l)+s,value:a?a[l]:l,index:p,offset:s}})},Mo=new WeakMap,di=function(t,r){if(typeof r!="function")return t;Mo.has(t)||Mo.set(t,new WeakMap);var n=Mo.get(t);if(n.has(r))return n.get(r);var i=function(){t.apply(void 0,arguments),r.apply(void 0,arguments)};return n.set(r,i),i},kI=function(t,r,n){var i=t.scale,a=t.type,o=t.layout,u=t.axisType;if(i==="auto")return o==="radial"&&u==="radiusAxis"?{scale:wn(),realScaleType:"band"}:o==="radial"&&u==="angleAxis"?{scale:Ui(),realScaleType:"linear"}:a==="category"&&r&&(r.indexOf("LineChart")>=0||r.indexOf("AreaChart")>=0||r.indexOf("ComposedChart")>=0&&!n)?{scale:ln(),realScaleType:"point"}:a==="category"?{scale:wn(),realScaleType:"band"}:{scale:Ui(),realScaleType:"linear"};if(Xt(i)){var c="scale".concat(Da(i));return{scale:($f[c]||ln)(),realScaleType:$f[c]?c:"point"}}return Y(i)?{scale:i}:{scale:ln(),realScaleType:"point"}},kf=1e-4,NI=function(t){var r=t.domain();if(!(!r||r.length<=2)){var n=r.length,i=t.range(),a=Math.min(i[0],i[1])-kf,o=Math.max(i[0],i[1])+kf,u=t(r[0]),c=t(r[n-1]);(u<a||u>o||c<a||c>o)&&t.domain([r[0],r[n-1]])}},LI=function(t,r){if(!t)return null;for(var n=0,i=t.length;n<i;n++)if(t[n].item===r)return t[n].position;return null},BI=function(t,r){if(!r||r.length!==2||!R(r[0])||!R(r[1]))return t;var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]),a=[t[0],t[1]];return(!R(t[0])||t[0]<n)&&(a[0]=n),(!R(t[1])||t[1]>i)&&(a[1]=i),a[0]>i&&(a[0]=i),a[1]<n&&(a[1]=n),a},RI=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0,u=0;u<r;++u){var c=Yn(t[u][n][1])?t[u][n][0]:t[u][n][1];c>=0?(t[u][n][0]=a,t[u][n][1]=a+c,a=t[u][n][1]):(t[u][n][0]=o,t[u][n][1]=o+c,o=t[u][n][1])}},FI=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0;o<r;++o){var u=Yn(t[o][n][1])?t[o][n][0]:t[o][n][1];u>=0?(t[o][n][0]=a,t[o][n][1]=a+u,a=t[o][n][1]):(t[o][n][0]=0,t[o][n][1]=0)}},WI={sign:RI,expand:rw,none:vr,silhouette:nw,wiggle:iw,positive:FI},zI=function(t,r,n){var i=r.map(function(u){return u.props.dataKey}),a=WI[n],o=tw().keys(i).value(function(u,c){return+We(u,c,0)}).order(Yo).offset(a);return o(t)},UI=function(t,r,n,i,a,o){if(!t)return null;var u=o?r.reverse():r,c={},s=u.reduce(function(l,p){var h,d=(h=p.type)!==null&&h!==void 0&&h.defaultProps?he(he({},p.type.defaultProps),p.props):p.props,v=d.stackId,y=d.hide;if(y)return l;var b=d[n],w=l[b]||{hasStack:!1,stackGroups:{}};if(we(v)){var x=w.stackGroups[v]||{numericAxisId:n,cateAxisId:i,items:[]};x.items.push(p),w.hasStack=!0,w.stackGroups[v]=x}else w.stackGroups[Zn("_stackId_")]={numericAxisId:n,cateAxisId:i,items:[p]};return he(he({},l),{},dr({},b,w))},c),f={};return Object.keys(s).reduce(function(l,p){var h=s[p];if(h.hasStack){var d={};h.stackGroups=Object.keys(h.stackGroups).reduce(function(v,y){var b=h.stackGroups[y];return he(he({},v),{},dr({},y,{numericAxisId:n,cateAxisId:i,items:b.items,stackedData:zI(t,b.items,a)}))},d)}return he(he({},l),{},dr({},p,h))},f)},qI=function(t,r){var n=r.realScaleType,i=r.type,a=r.tickCount,o=r.originalDomain,u=r.allowDecimals,c=n||r.scale;if(c!=="auto"&&c!=="linear")return null;if(a&&i==="number"&&o&&(o[0]==="auto"||o[1]==="auto")){var s=t.domain();if(!s.length)return null;var f=tI(s,a,u);return t.domain([Xa(f),Ka(f)]),{niceTicks:f}}if(a&&i==="number"){var l=t.domain(),p=rI(l,a,u);return{niceTicks:p}}return null};function Nf(e){var t=e.axis,r=e.ticks,n=e.bandSize,i=e.entry,a=e.index,o=e.dataKey;if(t.type==="category"){if(!t.allowDuplicatedCategory&&t.dataKey&&!Z(i[t.dataKey])){var u=xi(r,"value",i[t.dataKey]);if(u)return u.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var c=We(i,Z(o)?t.dataKey:o);return Z(c)?null:t.scale(c)}var Lf=function(t){var r=t.axis,n=t.ticks,i=t.offset,a=t.bandSize,o=t.entry,u=t.index;if(r.type==="category")return n[u]?n[u].coordinate+i:null;var c=We(o,r.dataKey,r.domain[u]);return Z(c)?null:r.scale(c)-a/2+i},HI=function(t){var r=t.numericAxis,n=r.scale.domain();if(r.type==="number"){var i=Math.min(n[0],n[1]),a=Math.max(n[0],n[1]);return i<=0&&a>=0?0:a<0?a:i}return n[0]},GI=function(t,r){var n,i=(n=t.type)!==null&&n!==void 0&&n.defaultProps?he(he({},t.type.defaultProps),t.props):t.props,a=i.stackId;if(we(a)){var o=r[a];if(o){var u=o.items.indexOf(t);return u>=0?o.stackedData[u]:null}}return null},KI=function(t){return t.reduce(function(r,n){return[Xa(n.concat([r[0]]).filter(R)),Ka(n.concat([r[1]]).filter(R))]},[1/0,-1/0])},Mv=function(t,r,n){return Object.keys(t).reduce(function(i,a){var o=t[a],u=o.stackedData,c=u.reduce(function(s,f){var l=KI(f.slice(r,n+1));return[Math.min(s[0],l[0]),Math.max(s[1],l[1])]},[1/0,-1/0]);return[Math.min(c[0],i[0]),Math.max(c[1],i[1])]},[1/0,-1/0]).map(function(i){return i===1/0||i===-1/0?0:i})},Bf=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Rf=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Pu=function(t,r,n){if(Y(t))return t(r,n);if(!Array.isArray(t))return r;var i=[];if(R(t[0]))i[0]=n?t[0]:Math.min(t[0],r[0]);else if(Bf.test(t[0])){var a=+Bf.exec(t[0])[1];i[0]=r[0]-a}else Y(t[0])?i[0]=t[0](r[0]):i[0]=r[0];if(R(t[1]))i[1]=n?t[1]:Math.max(t[1],r[1]);else if(Rf.test(t[1])){var o=+Rf.exec(t[1])[1];i[1]=r[1]+o}else Y(t[1])?i[1]=t[1](r[1]):i[1]=r[1];return i},Yi=function(t,r,n){if(t&&t.scale&&t.scale.bandwidth){var i=t.scale.bandwidth();if(!n||i>0)return i}if(t&&r&&r.length>=2){for(var a=Ic(r,function(l){return l.coordinate}),o=1/0,u=1,c=a.length;u<c;u++){var s=a[u],f=a[u-1];o=Math.min((s.coordinate||0)-(f.coordinate||0),o)}return o===1/0?0:o}return n?void 0:0},Ff=function(t,r,n){return!t||!t.length||Va(t,He(n,"type.defaultProps.domain"))?r:t},Cv=function(t,r){var n=t.type.defaultProps?he(he({},t.type.defaultProps),t.props):t.props,i=n.dataKey,a=n.name,o=n.unit,u=n.formatter,c=n.tooltipType,s=n.chartType,f=n.hide;return he(he({},J(t,!1)),{},{dataKey:i,unit:o,formatter:u,name:a||i,color:as(t),value:We(r,i),type:c,payload:r,chartType:s,hide:f})};function jn(e){"@babel/helpers - typeof";return jn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},jn(e)}function Wf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function zf(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Wf(Object(r),!0).forEach(function(n){XI(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Wf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function XI(e,t,r){return t=VI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function VI(e){var t=YI(e,"string");return jn(t)=="symbol"?t:t+""}function YI(e,t){if(jn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(jn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Zi=Math.PI/180,ZI=function(t){return t*180/Math.PI},Pe=function(t,r,n,i){return{x:t+Math.cos(-Zi*i)*n,y:r+Math.sin(-Zi*i)*n}},JI=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return Math.sqrt(Math.pow(n-a,2)+Math.pow(i-o,2))},QI=function(t,r){var n=t.x,i=t.y,a=r.cx,o=r.cy,u=JI({x:n,y:i},{x:a,y:o});if(u<=0)return{radius:u};var c=(n-a)/u,s=Math.acos(c);return i>o&&(s=2*Math.PI-s),{radius:u,angle:ZI(s),angleInRadian:s}},e2=function(t){var r=t.startAngle,n=t.endAngle,i=Math.floor(r/360),a=Math.floor(n/360),o=Math.min(i,a);return{startAngle:r-o*360,endAngle:n-o*360}},t2=function(t,r){var n=r.startAngle,i=r.endAngle,a=Math.floor(n/360),o=Math.floor(i/360),u=Math.min(a,o);return t+u*360},Uf=function(t,r){var n=t.x,i=t.y,a=QI({x:n,y:i},r),o=a.radius,u=a.angle,c=r.innerRadius,s=r.outerRadius;if(o<c||o>s)return!1;if(o===0)return!0;var f=e2(r),l=f.startAngle,p=f.endAngle,h=u,d;if(l<=p){for(;h>p;)h-=360;for(;h<l;)h+=360;d=h>=l&&h<=p}else{for(;h>l;)h-=360;for(;h<p;)h+=360;d=h>=p&&h<=l}return d?zf(zf({},r),{},{radius:o,angle:t2(h,r)}):null};function Mn(e){"@babel/helpers - typeof";return Mn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mn(e)}var r2=["offset"];function n2(e){return u2(e)||o2(e)||a2(e)||i2()}function i2(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function a2(e,t){if(e){if(typeof e=="string")return $u(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return $u(e,t)}}function o2(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function u2(e){if(Array.isArray(e))return $u(e)}function $u(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function c2(e,t){if(e==null)return{};var r=s2(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function s2(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function qf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function xe(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?qf(Object(r),!0).forEach(function(n){l2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):qf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function l2(e,t,r){return t=f2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function f2(e){var t=p2(e,"string");return Mn(t)=="symbol"?t:t+""}function p2(e,t){if(Mn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Mn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Cn(){return Cn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Cn.apply(this,arguments)}var h2=function(t){var r=t.value,n=t.formatter,i=Z(t.children)?r:t.children;return Y(n)?n(i):i},d2=function(t,r){var n=rt(r-t),i=Math.min(Math.abs(r-t),360);return n*i},v2=function(t,r,n){var i=t.position,a=t.viewBox,o=t.offset,u=t.className,c=a,s=c.cx,f=c.cy,l=c.innerRadius,p=c.outerRadius,h=c.startAngle,d=c.endAngle,v=c.clockWise,y=(l+p)/2,b=d2(h,d),w=b>=0?1:-1,x,S;i==="insideStart"?(x=h+w*o,S=v):i==="insideEnd"?(x=d-w*o,S=!v):i==="end"&&(x=d+w*o,S=v),S=b<=0?S:!S;var m=Pe(s,f,y,x),g=Pe(s,f,y,x+(S?1:-1)*359),O="M".concat(m.x,",").concat(m.y,`
    A`).concat(y,",").concat(y,",0,1,").concat(S?0:1,`,
    `).concat(g.x,",").concat(g.y),A=Z(t.id)?Zn("recharts-radial-line-"):t.id;return _.createElement("text",Cn({},n,{dominantBaseline:"central",className:ee("recharts-radial-bar-label",u)}),_.createElement("defs",null,_.createElement("path",{id:A,d:O})),_.createElement("textPath",{xlinkHref:"#".concat(A)},r))},y2=function(t){var r=t.viewBox,n=t.offset,i=t.position,a=r,o=a.cx,u=a.cy,c=a.innerRadius,s=a.outerRadius,f=a.startAngle,l=a.endAngle,p=(f+l)/2;if(i==="outside"){var h=Pe(o,u,s+n,p),d=h.x,v=h.y;return{x:d,y:v,textAnchor:d>=o?"start":"end",verticalAnchor:"middle"}}if(i==="center")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"middle"};if(i==="centerTop")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"start"};if(i==="centerBottom")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"end"};var y=(c+s)/2,b=Pe(o,u,y,p),w=b.x,x=b.y;return{x:w,y:x,textAnchor:"middle",verticalAnchor:"middle"}},m2=function(t){var r=t.viewBox,n=t.parentViewBox,i=t.offset,a=t.position,o=r,u=o.x,c=o.y,s=o.width,f=o.height,l=f>=0?1:-1,p=l*i,h=l>0?"end":"start",d=l>0?"start":"end",v=s>=0?1:-1,y=v*i,b=v>0?"end":"start",w=v>0?"start":"end";if(a==="top"){var x={x:u+s/2,y:c-l*i,textAnchor:"middle",verticalAnchor:h};return xe(xe({},x),n?{height:Math.max(c-n.y,0),width:s}:{})}if(a==="bottom"){var S={x:u+s/2,y:c+f+p,textAnchor:"middle",verticalAnchor:d};return xe(xe({},S),n?{height:Math.max(n.y+n.height-(c+f),0),width:s}:{})}if(a==="left"){var m={x:u-y,y:c+f/2,textAnchor:b,verticalAnchor:"middle"};return xe(xe({},m),n?{width:Math.max(m.x-n.x,0),height:f}:{})}if(a==="right"){var g={x:u+s+y,y:c+f/2,textAnchor:w,verticalAnchor:"middle"};return xe(xe({},g),n?{width:Math.max(n.x+n.width-g.x,0),height:f}:{})}var O=n?{width:s,height:f}:{};return a==="insideLeft"?xe({x:u+y,y:c+f/2,textAnchor:w,verticalAnchor:"middle"},O):a==="insideRight"?xe({x:u+s-y,y:c+f/2,textAnchor:b,verticalAnchor:"middle"},O):a==="insideTop"?xe({x:u+s/2,y:c+p,textAnchor:"middle",verticalAnchor:d},O):a==="insideBottom"?xe({x:u+s/2,y:c+f-p,textAnchor:"middle",verticalAnchor:h},O):a==="insideTopLeft"?xe({x:u+y,y:c+p,textAnchor:w,verticalAnchor:d},O):a==="insideTopRight"?xe({x:u+s-y,y:c+p,textAnchor:b,verticalAnchor:d},O):a==="insideBottomLeft"?xe({x:u+y,y:c+f-p,textAnchor:w,verticalAnchor:h},O):a==="insideBottomRight"?xe({x:u+s-y,y:c+f-p,textAnchor:b,verticalAnchor:h},O):Br(a)&&(R(a.x)||zt(a.x))&&(R(a.y)||zt(a.y))?xe({x:u+Vt(a.x,s),y:c+Vt(a.y,f),textAnchor:"end",verticalAnchor:"end"},O):xe({x:u+s/2,y:c+f/2,textAnchor:"middle",verticalAnchor:"middle"},O)},g2=function(t){return"cx"in t&&R(t.cx)};function Te(e){var t=e.offset,r=t===void 0?5:t,n=c2(e,r2),i=xe({offset:r},n),a=i.viewBox,o=i.position,u=i.value,c=i.children,s=i.content,f=i.className,l=f===void 0?"":f,p=i.textBreakAll;if(!a||Z(u)&&Z(c)&&!B.isValidElement(s)&&!Y(s))return null;if(B.isValidElement(s))return B.cloneElement(s,i);var h;if(Y(s)){if(h=B.createElement(s,i),B.isValidElement(h))return h}else h=h2(i);var d=g2(a),v=J(i,!0);if(d&&(o==="insideStart"||o==="insideEnd"||o==="end"))return v2(i,h,v);var y=d?y2(i):m2(i);return _.createElement(Ni,Cn({className:ee("recharts-label",l)},v,y,{breakAll:p}),h)}Te.displayName="Label";var Iv=function(t){var r=t.cx,n=t.cy,i=t.angle,a=t.startAngle,o=t.endAngle,u=t.r,c=t.radius,s=t.innerRadius,f=t.outerRadius,l=t.x,p=t.y,h=t.top,d=t.left,v=t.width,y=t.height,b=t.clockWise,w=t.labelViewBox;if(w)return w;if(R(v)&&R(y)){if(R(l)&&R(p))return{x:l,y:p,width:v,height:y};if(R(h)&&R(d))return{x:h,y:d,width:v,height:y}}return R(l)&&R(p)?{x:l,y:p,width:0,height:0}:R(r)&&R(n)?{cx:r,cy:n,startAngle:a||i||0,endAngle:o||i||0,innerRadius:s||0,outerRadius:f||c||u||0,clockWise:b}:t.viewBox?t.viewBox:{}},b2=function(t,r){return t?t===!0?_.createElement(Te,{key:"label-implicit",viewBox:r}):we(t)?_.createElement(Te,{key:"label-implicit",viewBox:r,value:t}):B.isValidElement(t)?t.type===Te?B.cloneElement(t,{key:"label-implicit",viewBox:r}):_.createElement(Te,{key:"label-implicit",content:t,viewBox:r}):Y(t)?_.createElement(Te,{key:"label-implicit",content:t,viewBox:r}):Br(t)?_.createElement(Te,Cn({viewBox:r},t,{key:"label-implicit"})):null:null},x2=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!t||!t.children&&n&&!t.label)return null;var i=t.children,a=Iv(t),o=Ge(i,Te).map(function(c,s){return B.cloneElement(c,{viewBox:r||a,key:"label-".concat(s)})});if(!n)return o;var u=b2(t.label,r||a);return[u].concat(n2(o))};Te.parseViewBox=Iv;Te.renderCallByParent=x2;function w2(e){var t=e==null?0:e.length;return t?e[t-1]:void 0}var O2=w2;const S2=ue(O2);function In(e){"@babel/helpers - typeof";return In=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},In(e)}var A2=["valueAccessor"],_2=["data","dataKey","clockWise","id","textBreakAll"];function P2(e){return j2(e)||E2(e)||T2(e)||$2()}function $2(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function T2(e,t){if(e){if(typeof e=="string")return Tu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Tu(e,t)}}function E2(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function j2(e){if(Array.isArray(e))return Tu(e)}function Tu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Ji(){return Ji=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ji.apply(this,arguments)}function Hf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Gf(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Hf(Object(r),!0).forEach(function(n){M2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Hf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function M2(e,t,r){return t=C2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function C2(e){var t=I2(e,"string");return In(t)=="symbol"?t:t+""}function I2(e,t){if(In(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(In(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Kf(e,t){if(e==null)return{};var r=D2(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function D2(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var k2=function(t){return Array.isArray(t.value)?S2(t.value):t.value};function Et(e){var t=e.valueAccessor,r=t===void 0?k2:t,n=Kf(e,A2),i=n.data,a=n.dataKey,o=n.clockWise,u=n.id,c=n.textBreakAll,s=Kf(n,_2);return!i||!i.length?null:_.createElement(de,{className:"recharts-label-list"},i.map(function(f,l){var p=Z(a)?r(f,l):We(f&&f.payload,a),h=Z(u)?{}:{id:"".concat(u,"-").concat(l)};return _.createElement(Te,Ji({},J(f,!0),s,h,{parentViewBox:f.parentViewBox,value:p,textBreakAll:c,viewBox:Te.parseViewBox(Z(o)?f:Gf(Gf({},f),{},{clockWise:o})),key:"label-".concat(l),index:l}))}))}Et.displayName="LabelList";function N2(e,t){return e?e===!0?_.createElement(Et,{key:"labelList-implicit",data:t}):_.isValidElement(e)||Y(e)?_.createElement(Et,{key:"labelList-implicit",data:t,content:e}):Br(e)?_.createElement(Et,Ji({data:t},e,{key:"labelList-implicit"})):null:null}function L2(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!e||!e.children&&r&&!e.label)return null;var n=e.children,i=Ge(n,Et).map(function(o,u){return B.cloneElement(o,{data:t,key:"labelList-".concat(u)})});if(!r)return i;var a=N2(e.label,t);return[a].concat(P2(i))}Et.renderCallByParent=L2;function Dn(e){"@babel/helpers - typeof";return Dn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dn(e)}function Eu(){return Eu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Eu.apply(this,arguments)}function Xf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Vf(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Xf(Object(r),!0).forEach(function(n){B2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Xf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function B2(e,t,r){return t=R2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function R2(e){var t=F2(e,"string");return Dn(t)=="symbol"?t:t+""}function F2(e,t){if(Dn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Dn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var W2=function(t,r){var n=rt(r-t),i=Math.min(Math.abs(r-t),359.999);return n*i},vi=function(t){var r=t.cx,n=t.cy,i=t.radius,a=t.angle,o=t.sign,u=t.isExternal,c=t.cornerRadius,s=t.cornerIsExternal,f=c*(u?1:-1)+i,l=Math.asin(c/f)/Zi,p=s?a:a+o*l,h=Pe(r,n,f,p),d=Pe(r,n,i,p),v=s?a-o*l:a,y=Pe(r,n,f*Math.cos(l*Zi),v);return{center:h,circleTangency:d,lineTangency:y,theta:l}},Dv=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.startAngle,u=t.endAngle,c=W2(o,u),s=o+c,f=Pe(r,n,a,o),l=Pe(r,n,a,s),p="M ".concat(f.x,",").concat(f.y,`
    A `).concat(a,",").concat(a,`,0,
    `).concat(+(Math.abs(c)>180),",").concat(+(o>s),`,
    `).concat(l.x,",").concat(l.y,`
  `);if(i>0){var h=Pe(r,n,i,o),d=Pe(r,n,i,s);p+="L ".concat(d.x,",").concat(d.y,`
            A `).concat(i,",").concat(i,`,0,
            `).concat(+(Math.abs(c)>180),",").concat(+(o<=s),`,
            `).concat(h.x,",").concat(h.y," Z")}else p+="L ".concat(r,",").concat(n," Z");return p},z2=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.cornerRadius,u=t.forceCornerRadius,c=t.cornerIsExternal,s=t.startAngle,f=t.endAngle,l=rt(f-s),p=vi({cx:r,cy:n,radius:a,angle:s,sign:l,cornerRadius:o,cornerIsExternal:c}),h=p.circleTangency,d=p.lineTangency,v=p.theta,y=vi({cx:r,cy:n,radius:a,angle:f,sign:-l,cornerRadius:o,cornerIsExternal:c}),b=y.circleTangency,w=y.lineTangency,x=y.theta,S=c?Math.abs(s-f):Math.abs(s-f)-v-x;if(S<0)return u?"M ".concat(d.x,",").concat(d.y,`
        a`).concat(o,",").concat(o,",0,0,1,").concat(o*2,`,0
        a`).concat(o,",").concat(o,",0,0,1,").concat(-o*2,`,0
      `):Dv({cx:r,cy:n,innerRadius:i,outerRadius:a,startAngle:s,endAngle:f});var m="M ".concat(d.x,",").concat(d.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(h.x,",").concat(h.y,`
    A`).concat(a,",").concat(a,",0,").concat(+(S>180),",").concat(+(l<0),",").concat(b.x,",").concat(b.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(w.x,",").concat(w.y,`
  `);if(i>0){var g=vi({cx:r,cy:n,radius:i,angle:s,sign:l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),O=g.circleTangency,A=g.lineTangency,P=g.theta,j=vi({cx:r,cy:n,radius:i,angle:f,sign:-l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),$=j.circleTangency,T=j.lineTangency,I=j.theta,C=c?Math.abs(s-f):Math.abs(s-f)-P-I;if(C<0&&o===0)return"".concat(m,"L").concat(r,",").concat(n,"Z");m+="L".concat(T.x,",").concat(T.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat($.x,",").concat($.y,`
      A`).concat(i,",").concat(i,",0,").concat(+(C>180),",").concat(+(l>0),",").concat(O.x,",").concat(O.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(A.x,",").concat(A.y,"Z")}else m+="L".concat(r,",").concat(n,"Z");return m},U2={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},kv=function(t){var r=Vf(Vf({},U2),t),n=r.cx,i=r.cy,a=r.innerRadius,o=r.outerRadius,u=r.cornerRadius,c=r.forceCornerRadius,s=r.cornerIsExternal,f=r.startAngle,l=r.endAngle,p=r.className;if(o<a||f===l)return null;var h=ee("recharts-sector",p),d=o-a,v=Vt(u,d,0,!0),y;return v>0&&Math.abs(f-l)<360?y=z2({cx:n,cy:i,innerRadius:a,outerRadius:o,cornerRadius:Math.min(v,d/2),forceCornerRadius:c,cornerIsExternal:s,startAngle:f,endAngle:l}):y=Dv({cx:n,cy:i,innerRadius:a,outerRadius:o,startAngle:f,endAngle:l}),_.createElement("path",Eu({},J(r,!0),{className:h,d:y,role:"img"}))};function kn(e){"@babel/helpers - typeof";return kn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kn(e)}function ju(){return ju=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ju.apply(this,arguments)}function Yf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Zf(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Yf(Object(r),!0).forEach(function(n){q2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Yf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function q2(e,t,r){return t=H2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function H2(e){var t=G2(e,"string");return kn(t)=="symbol"?t:t+""}function G2(e,t){if(kn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(kn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Jf={curveBasisClosed:qx,curveBasisOpen:Hx,curveBasis:Ux,curveBumpX:Ex,curveBumpY:jx,curveLinearClosed:Gx,curveLinear:Na,curveMonotoneX:Kx,curveMonotoneY:Xx,curveNatural:Vx,curveStep:Yx,curveStepAfter:Jx,curveStepBefore:Zx},yi=function(t){return t.x===+t.x&&t.y===+t.y},nn=function(t){return t.x},an=function(t){return t.y},K2=function(t,r){if(Y(t))return t;var n="curve".concat(Da(t));return(n==="curveMonotone"||n==="curveBump")&&r?Jf["".concat(n).concat(r==="vertical"?"Y":"X")]:Jf[n]||Na},X2=function(t){var r=t.type,n=r===void 0?"linear":r,i=t.points,a=i===void 0?[]:i,o=t.baseLine,u=t.layout,c=t.connectNulls,s=c===void 0?!1:c,f=K2(n,u),l=s?a.filter(function(v){return yi(v)}):a,p;if(Array.isArray(o)){var h=s?o.filter(function(v){return yi(v)}):o,d=l.map(function(v,y){return Zf(Zf({},v),{},{base:h[y]})});return u==="vertical"?p=oi().y(an).x1(nn).x0(function(v){return v.base.x}):p=oi().x(nn).y1(an).y0(function(v){return v.base.y}),p.defined(yi).curve(f),p(d)}return u==="vertical"&&R(o)?p=oi().y(an).x1(nn).x0(o):R(o)?p=oi().x(nn).y1(an).y0(o):p=kh().x(nn).y(an),p.defined(yi).curve(f),p(l)},Mu=function(t){var r=t.className,n=t.points,i=t.path,a=t.pathRef;if((!n||!n.length)&&!i)return null;var o=n&&n.length?X2(t):i;return _.createElement("path",ju({},J(t,!1),wi(t),{className:ee("recharts-curve",r),d:o,ref:a}))},Nv={exports:{}},V2="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",Y2=V2,Z2=Y2;function Lv(){}function Bv(){}Bv.resetWarningCache=Lv;var J2=function(){function e(n,i,a,o,u,c){if(c!==Z2){var s=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}e.isRequired=e;function t(){return e}var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:Bv,resetWarningCache:Lv};return r.PropTypes=r,r};Nv.exports=J2();var Q2=Nv.exports;const re=ue(Q2);var eD=Object.getOwnPropertyNames,tD=Object.getOwnPropertySymbols,rD=Object.prototype.hasOwnProperty;function Qf(e,t){return function(n,i,a){return e(n,i,a)&&t(n,i,a)}}function mi(e){return function(r,n,i){if(!r||!n||typeof r!="object"||typeof n!="object")return e(r,n,i);var a=i.cache,o=a.get(r),u=a.get(n);if(o&&u)return o===n&&u===r;a.set(r,n),a.set(n,r);var c=e(r,n,i);return a.delete(r),a.delete(n),c}}function ep(e){return eD(e).concat(tD(e))}var nD=Object.hasOwn||function(e,t){return rD.call(e,t)};function rr(e,t){return e===t||!e&&!t&&e!==e&&t!==t}var iD="__v",aD="__o",oD="_owner",tp=Object.getOwnPropertyDescriptor,rp=Object.keys;function uD(e,t,r){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(!r.equals(e[n],t[n],n,n,e,t,r))return!1;return!0}function cD(e,t){return rr(e.getTime(),t.getTime())}function sD(e,t){return e.name===t.name&&e.message===t.message&&e.cause===t.cause&&e.stack===t.stack}function lD(e,t){return e===t}function np(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.entries(),o,u,c=0;(o=a.next())&&!o.done;){for(var s=t.entries(),f=!1,l=0;(u=s.next())&&!u.done;){if(i[l]){l++;continue}var p=o.value,h=u.value;if(r.equals(p[0],h[0],c,l,e,t,r)&&r.equals(p[1],h[1],p[0],h[0],e,t,r)){f=i[l]=!0;break}l++}if(!f)return!1;c++}return!0}var fD=rr;function pD(e,t,r){var n=rp(e),i=n.length;if(rp(t).length!==i)return!1;for(;i-- >0;)if(!Rv(e,t,r,n[i]))return!1;return!0}function on(e,t,r){var n=ep(e),i=n.length;if(ep(t).length!==i)return!1;for(var a,o,u;i-- >0;)if(a=n[i],!Rv(e,t,r,a)||(o=tp(e,a),u=tp(t,a),(o||u)&&(!o||!u||o.configurable!==u.configurable||o.enumerable!==u.enumerable||o.writable!==u.writable)))return!1;return!0}function hD(e,t){return rr(e.valueOf(),t.valueOf())}function dD(e,t){return e.source===t.source&&e.flags===t.flags}function ip(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.values(),o,u;(o=a.next())&&!o.done;){for(var c=t.values(),s=!1,f=0;(u=c.next())&&!u.done;){if(!i[f]&&r.equals(o.value,u.value,o.value,u.value,e,t,r)){s=i[f]=!0;break}f++}if(!s)return!1}return!0}function vD(e,t){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(e[r]!==t[r])return!1;return!0}function yD(e,t){return e.hostname===t.hostname&&e.pathname===t.pathname&&e.protocol===t.protocol&&e.port===t.port&&e.hash===t.hash&&e.username===t.username&&e.password===t.password}function Rv(e,t,r,n){return(n===oD||n===aD||n===iD)&&(e.$$typeof||t.$$typeof)?!0:nD(t,n)&&r.equals(e[n],t[n],n,n,e,t,r)}var mD="[object Arguments]",gD="[object Boolean]",bD="[object Date]",xD="[object Error]",wD="[object Map]",OD="[object Number]",SD="[object Object]",AD="[object RegExp]",_D="[object Set]",PD="[object String]",$D="[object URL]",TD=Array.isArray,ap=typeof ArrayBuffer=="function"&&ArrayBuffer.isView?ArrayBuffer.isView:null,op=Object.assign,ED=Object.prototype.toString.call.bind(Object.prototype.toString);function jD(e){var t=e.areArraysEqual,r=e.areDatesEqual,n=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,o=e.areNumbersEqual,u=e.areObjectsEqual,c=e.arePrimitiveWrappersEqual,s=e.areRegExpsEqual,f=e.areSetsEqual,l=e.areTypedArraysEqual,p=e.areUrlsEqual;return function(d,v,y){if(d===v)return!0;if(d==null||v==null)return!1;var b=typeof d;if(b!==typeof v)return!1;if(b!=="object")return b==="number"?o(d,v,y):b==="function"?i(d,v,y):!1;var w=d.constructor;if(w!==v.constructor)return!1;if(w===Object)return u(d,v,y);if(TD(d))return t(d,v,y);if(ap!=null&&ap(d))return l(d,v,y);if(w===Date)return r(d,v,y);if(w===RegExp)return s(d,v,y);if(w===Map)return a(d,v,y);if(w===Set)return f(d,v,y);var x=ED(d);return x===bD?r(d,v,y):x===AD?s(d,v,y):x===wD?a(d,v,y):x===_D?f(d,v,y):x===SD?typeof d.then!="function"&&typeof v.then!="function"&&u(d,v,y):x===$D?p(d,v,y):x===xD?n(d,v,y):x===mD?u(d,v,y):x===gD||x===OD||x===PD?c(d,v,y):!1}}function MD(e){var t=e.circular,r=e.createCustomConfig,n=e.strict,i={areArraysEqual:n?on:uD,areDatesEqual:cD,areErrorsEqual:sD,areFunctionsEqual:lD,areMapsEqual:n?Qf(np,on):np,areNumbersEqual:fD,areObjectsEqual:n?on:pD,arePrimitiveWrappersEqual:hD,areRegExpsEqual:dD,areSetsEqual:n?Qf(ip,on):ip,areTypedArraysEqual:n?on:vD,areUrlsEqual:yD};if(r&&(i=op({},i,r(i))),t){var a=mi(i.areArraysEqual),o=mi(i.areMapsEqual),u=mi(i.areObjectsEqual),c=mi(i.areSetsEqual);i=op({},i,{areArraysEqual:a,areMapsEqual:o,areObjectsEqual:u,areSetsEqual:c})}return i}function CD(e){return function(t,r,n,i,a,o,u){return e(t,r,u)}}function ID(e){var t=e.circular,r=e.comparator,n=e.createState,i=e.equals,a=e.strict;if(n)return function(c,s){var f=n(),l=f.cache,p=l===void 0?t?new WeakMap:void 0:l,h=f.meta;return r(c,s,{cache:p,equals:i,meta:h,strict:a})};if(t)return function(c,s){return r(c,s,{cache:new WeakMap,equals:i,meta:void 0,strict:a})};var o={cache:void 0,equals:i,meta:void 0,strict:a};return function(c,s){return r(c,s,o)}}var DD=Dt();Dt({strict:!0});Dt({circular:!0});Dt({circular:!0,strict:!0});Dt({createInternalComparator:function(){return rr}});Dt({strict:!0,createInternalComparator:function(){return rr}});Dt({circular:!0,createInternalComparator:function(){return rr}});Dt({circular:!0,createInternalComparator:function(){return rr},strict:!0});function Dt(e){e===void 0&&(e={});var t=e.circular,r=t===void 0?!1:t,n=e.createInternalComparator,i=e.createState,a=e.strict,o=a===void 0?!1:a,u=MD(e),c=jD(u),s=n?n(c):CD(c);return ID({circular:r,comparator:c,createState:i,equals:s,strict:o})}function kD(e){typeof requestAnimationFrame<"u"&&requestAnimationFrame(e)}function up(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=-1,n=function i(a){r<0&&(r=a),a-r>t?(e(a),r=-1):kD(i)};requestAnimationFrame(n)}function Cu(e){"@babel/helpers - typeof";return Cu=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Cu(e)}function ND(e){return FD(e)||RD(e)||BD(e)||LD()}function LD(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function BD(e,t){if(e){if(typeof e=="string")return cp(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return cp(e,t)}}function cp(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function RD(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function FD(e){if(Array.isArray(e))return e}function WD(){var e={},t=function(){return null},r=!1,n=function i(a){if(!r){if(Array.isArray(a)){if(!a.length)return;var o=a,u=ND(o),c=u[0],s=u.slice(1);if(typeof c=="number"){up(i.bind(null,s),c);return}i(c),up(i.bind(null,s));return}Cu(a)==="object"&&(e=a,t(e)),typeof a=="function"&&a()}};return{stop:function(){r=!0},start:function(a){r=!1,n(a)},subscribe:function(a){return t=a,function(){t=function(){return null}}}}}function Nn(e){"@babel/helpers - typeof";return Nn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nn(e)}function sp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function lp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?sp(Object(r),!0).forEach(function(n){Fv(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Fv(e,t,r){return t=zD(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zD(e){var t=UD(e,"string");return Nn(t)==="symbol"?t:String(t)}function UD(e,t){if(Nn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Nn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var qD=function(t,r){return[Object.keys(t),Object.keys(r)].reduce(function(n,i){return n.filter(function(a){return i.includes(a)})})},HD=function(t){return t},GD=function(t){return t.replace(/([A-Z])/g,function(r){return"-".concat(r.toLowerCase())})},pn=function(t,r){return Object.keys(r).reduce(function(n,i){return lp(lp({},n),{},Fv({},i,t(i,r[i])))},{})},fp=function(t,r,n){return t.map(function(i){return"".concat(GD(i)," ").concat(r,"ms ").concat(n)}).join(",")};function KD(e,t){return YD(e)||VD(e,t)||Wv(e,t)||XD()}function XD(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function VD(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function YD(e){if(Array.isArray(e))return e}function ZD(e){return ek(e)||QD(e)||Wv(e)||JD()}function JD(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Wv(e,t){if(e){if(typeof e=="string")return Iu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Iu(e,t)}}function QD(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function ek(e){if(Array.isArray(e))return Iu(e)}function Iu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Qi=1e-4,zv=function(t,r){return[0,3*t,3*r-6*t,3*t-3*r+1]},Uv=function(t,r){return t.map(function(n,i){return n*Math.pow(r,i)}).reduce(function(n,i){return n+i})},pp=function(t,r){return function(n){var i=zv(t,r);return Uv(i,n)}},tk=function(t,r){return function(n){var i=zv(t,r),a=[].concat(ZD(i.map(function(o,u){return o*u}).slice(1)),[0]);return Uv(a,n)}},hp=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0],a=r[1],o=r[2],u=r[3];if(r.length===1)switch(r[0]){case"linear":i=0,a=0,o=1,u=1;break;case"ease":i=.25,a=.1,o=.25,u=1;break;case"ease-in":i=.42,a=0,o=1,u=1;break;case"ease-out":i=.42,a=0,o=.58,u=1;break;case"ease-in-out":i=0,a=0,o=.58,u=1;break;default:{var c=r[0].split("(");if(c[0]==="cubic-bezier"&&c[1].split(")")[0].split(",").length===4){var s=c[1].split(")")[0].split(",").map(function(y){return parseFloat(y)}),f=KD(s,4);i=f[0],a=f[1],o=f[2],u=f[3]}}}var l=pp(i,o),p=pp(a,u),h=tk(i,o),d=function(b){return b>1?1:b<0?0:b},v=function(b){for(var w=b>1?1:b,x=w,S=0;S<8;++S){var m=l(x)-w,g=h(x);if(Math.abs(m-w)<Qi||g<Qi)return p(x);x=d(x-m/g)}return p(x)};return v.isStepper=!1,v},rk=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=t.stiff,n=r===void 0?100:r,i=t.damping,a=i===void 0?8:i,o=t.dt,u=o===void 0?17:o,c=function(f,l,p){var h=-(f-l)*n,d=p*a,v=p+(h-d)*u/1e3,y=p*u/1e3+f;return Math.abs(y-l)<Qi&&Math.abs(v)<Qi?[l,0]:[y,v]};return c.isStepper=!0,c.dt=u,c},nk=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0];if(typeof i=="string")switch(i){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return hp(i);case"spring":return rk();default:if(i.split("(")[0]==="cubic-bezier")return hp(i)}return typeof i=="function"?i:null};function Ln(e){"@babel/helpers - typeof";return Ln=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ln(e)}function dp(e){return ok(e)||ak(e)||qv(e)||ik()}function ik(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ak(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function ok(e){if(Array.isArray(e))return ku(e)}function vp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function _e(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?vp(Object(r),!0).forEach(function(n){Du(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Du(e,t,r){return t=uk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function uk(e){var t=ck(e,"string");return Ln(t)==="symbol"?t:String(t)}function ck(e,t){if(Ln(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ln(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function sk(e,t){return pk(e)||fk(e,t)||qv(e,t)||lk()}function lk(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function qv(e,t){if(e){if(typeof e=="string")return ku(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ku(e,t)}}function ku(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function fk(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function pk(e){if(Array.isArray(e))return e}var ea=function(t,r,n){return t+(r-t)*n},Nu=function(t){var r=t.from,n=t.to;return r!==n},hk=function e(t,r,n){var i=pn(function(a,o){if(Nu(o)){var u=t(o.from,o.to,o.velocity),c=sk(u,2),s=c[0],f=c[1];return _e(_e({},o),{},{from:s,velocity:f})}return o},r);return n<1?pn(function(a,o){return Nu(o)?_e(_e({},o),{},{velocity:ea(o.velocity,i[a].velocity,n),from:ea(o.from,i[a].from,n)}):o},r):e(t,i,n-1)};const dk=function(e,t,r,n,i){var a=qD(e,t),o=a.reduce(function(y,b){return _e(_e({},y),{},Du({},b,[e[b],t[b]]))},{}),u=a.reduce(function(y,b){return _e(_e({},y),{},Du({},b,{from:e[b],velocity:0,to:t[b]}))},{}),c=-1,s,f,l=function(){return null},p=function(){return pn(function(b,w){return w.from},u)},h=function(){return!Object.values(u).filter(Nu).length},d=function(b){s||(s=b);var w=b-s,x=w/r.dt;u=hk(r,u,x),i(_e(_e(_e({},e),t),p())),s=b,h()||(c=requestAnimationFrame(l))},v=function(b){f||(f=b);var w=(b-f)/n,x=pn(function(m,g){return ea.apply(void 0,dp(g).concat([r(w)]))},o);if(i(_e(_e(_e({},e),t),x)),w<1)c=requestAnimationFrame(l);else{var S=pn(function(m,g){return ea.apply(void 0,dp(g).concat([r(1)]))},o);i(_e(_e(_e({},e),t),S))}};return l=r.isStepper?d:v,function(){return requestAnimationFrame(l),function(){cancelAnimationFrame(c)}}};function Ar(e){"@babel/helpers - typeof";return Ar=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ar(e)}var vk=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function yk(e,t){if(e==null)return{};var r=mk(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function mk(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function Co(e){return wk(e)||xk(e)||bk(e)||gk()}function gk(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function bk(e,t){if(e){if(typeof e=="string")return Lu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Lu(e,t)}}function xk(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function wk(e){if(Array.isArray(e))return Lu(e)}function Lu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function yp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ze(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?yp(Object(r),!0).forEach(function(n){cn(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function cn(e,t,r){return t=Hv(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ok(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Sk(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Hv(n.key),n)}}function Ak(e,t,r){return t&&Sk(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Hv(e){var t=_k(e,"string");return Ar(t)==="symbol"?t:String(t)}function _k(e,t){if(Ar(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ar(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Pk(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Bu(e,t)}function Bu(e,t){return Bu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Bu(e,t)}function $k(e){var t=Tk();return function(){var n=ta(e),i;if(t){var a=ta(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return Ru(this,i)}}function Ru(e,t){if(t&&(Ar(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Fu(e)}function Fu(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Tk(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function ta(e){return ta=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ta(e)}var xt=function(e){Pk(r,e);var t=$k(r);function r(n,i){var a;Ok(this,r),a=t.call(this,n,i);var o=a.props,u=o.isActive,c=o.attributeName,s=o.from,f=o.to,l=o.steps,p=o.children,h=o.duration;if(a.handleStyleChange=a.handleStyleChange.bind(Fu(a)),a.changeStyle=a.changeStyle.bind(Fu(a)),!u||h<=0)return a.state={style:{}},typeof p=="function"&&(a.state={style:f}),Ru(a);if(l&&l.length)a.state={style:l[0].style};else if(s){if(typeof p=="function")return a.state={style:s},Ru(a);a.state={style:c?cn({},c,s):s}}else a.state={style:{}};return a}return Ak(r,[{key:"componentDidMount",value:function(){var i=this.props,a=i.isActive,o=i.canBegin;this.mounted=!0,!(!a||!o)&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(i){var a=this.props,o=a.isActive,u=a.canBegin,c=a.attributeName,s=a.shouldReAnimate,f=a.to,l=a.from,p=this.state.style;if(u){if(!o){var h={style:c?cn({},c,f):f};this.state&&p&&(c&&p[c]!==f||!c&&p!==f)&&this.setState(h);return}if(!(DD(i.to,f)&&i.canBegin&&i.isActive)){var d=!i.canBegin||!i.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var v=d||s?l:i.to;if(this.state&&p){var y={style:c?cn({},c,v):v};(c&&p[c]!==v||!c&&p!==v)&&this.setState(y)}this.runAnimation(Ze(Ze({},this.props),{},{from:v,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var i=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),i&&i()}},{key:"handleStyleChange",value:function(i){this.changeStyle(i)}},{key:"changeStyle",value:function(i){this.mounted&&this.setState({style:i})}},{key:"runJSAnimation",value:function(i){var a=this,o=i.from,u=i.to,c=i.duration,s=i.easing,f=i.begin,l=i.onAnimationEnd,p=i.onAnimationStart,h=dk(o,u,nk(s),c,this.changeStyle),d=function(){a.stopJSAnimation=h()};this.manager.start([p,f,d,c,l])}},{key:"runStepAnimation",value:function(i){var a=this,o=i.steps,u=i.begin,c=i.onAnimationStart,s=o[0],f=s.style,l=s.duration,p=l===void 0?0:l,h=function(v,y,b){if(b===0)return v;var w=y.duration,x=y.easing,S=x===void 0?"ease":x,m=y.style,g=y.properties,O=y.onAnimationEnd,A=b>0?o[b-1]:y,P=g||Object.keys(m);if(typeof S=="function"||S==="spring")return[].concat(Co(v),[a.runJSAnimation.bind(a,{from:A.style,to:m,duration:w,easing:S}),w]);var j=fp(P,w,S),$=Ze(Ze(Ze({},A.style),m),{},{transition:j});return[].concat(Co(v),[$,w,O]).filter(HD)};return this.manager.start([c].concat(Co(o.reduce(h,[f,Math.max(p,u)])),[i.onAnimationEnd]))}},{key:"runAnimation",value:function(i){this.manager||(this.manager=WD());var a=i.begin,o=i.duration,u=i.attributeName,c=i.to,s=i.easing,f=i.onAnimationStart,l=i.onAnimationEnd,p=i.steps,h=i.children,d=this.manager;if(this.unSubscribe=d.subscribe(this.handleStyleChange),typeof s=="function"||typeof h=="function"||s==="spring"){this.runJSAnimation(i);return}if(p.length>1){this.runStepAnimation(i);return}var v=u?cn({},u,c):c,y=fp(Object.keys(v),o,s);d.start([f,a,Ze(Ze({},v),{},{transition:y}),o,l])}},{key:"render",value:function(){var i=this.props,a=i.children;i.begin;var o=i.duration;i.attributeName,i.easing;var u=i.isActive;i.steps,i.from,i.to,i.canBegin,i.onAnimationEnd,i.shouldReAnimate,i.onAnimationReStart;var c=yk(i,vk),s=B.Children.count(a),f=this.state.style;if(typeof a=="function")return a(f);if(!u||s===0||o<=0)return a;var l=function(h){var d=h.props,v=d.style,y=v===void 0?{}:v,b=d.className,w=B.cloneElement(h,Ze(Ze({},c),{},{style:Ze(Ze({},y),f),className:b}));return w};return s===1?l(B.Children.only(a)):_.createElement("div",null,B.Children.map(a,function(p){return l(p)}))}}]),r}(B.PureComponent);xt.displayName="Animate";xt.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}};xt.propTypes={from:re.oneOfType([re.object,re.string]),to:re.oneOfType([re.object,re.string]),attributeName:re.string,duration:re.number,begin:re.number,easing:re.oneOfType([re.string,re.func]),steps:re.arrayOf(re.shape({duration:re.number.isRequired,style:re.object.isRequired,easing:re.oneOfType([re.oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),re.func]),properties:re.arrayOf("string"),onAnimationEnd:re.func})),children:re.oneOfType([re.node,re.func]),isActive:re.bool,canBegin:re.bool,onAnimationEnd:re.func,shouldReAnimate:re.bool,onAnimationStart:re.func,onAnimationReStart:re.func};function Bn(e){"@babel/helpers - typeof";return Bn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Bn(e)}function ra(){return ra=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ra.apply(this,arguments)}function Ek(e,t){return Ik(e)||Ck(e,t)||Mk(e,t)||jk()}function jk(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Mk(e,t){if(e){if(typeof e=="string")return mp(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return mp(e,t)}}function mp(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Ck(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function Ik(e){if(Array.isArray(e))return e}function gp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function bp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?gp(Object(r),!0).forEach(function(n){Dk(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Dk(e,t,r){return t=kk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function kk(e){var t=Nk(e,"string");return Bn(t)=="symbol"?t:t+""}function Nk(e,t){if(Bn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Bn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var xp=function(t,r,n,i,a){var o=Math.min(Math.abs(n)/2,Math.abs(i)/2),u=i>=0?1:-1,c=n>=0?1:-1,s=i>=0&&n>=0||i<0&&n<0?1:0,f;if(o>0&&a instanceof Array){for(var l=[0,0,0,0],p=0,h=4;p<h;p++)l[p]=a[p]>o?o:a[p];f="M".concat(t,",").concat(r+u*l[0]),l[0]>0&&(f+="A ".concat(l[0],",").concat(l[0],",0,0,").concat(s,",").concat(t+c*l[0],",").concat(r)),f+="L ".concat(t+n-c*l[1],",").concat(r),l[1]>0&&(f+="A ".concat(l[1],",").concat(l[1],",0,0,").concat(s,`,
        `).concat(t+n,",").concat(r+u*l[1])),f+="L ".concat(t+n,",").concat(r+i-u*l[2]),l[2]>0&&(f+="A ".concat(l[2],",").concat(l[2],",0,0,").concat(s,`,
        `).concat(t+n-c*l[2],",").concat(r+i)),f+="L ".concat(t+c*l[3],",").concat(r+i),l[3]>0&&(f+="A ".concat(l[3],",").concat(l[3],",0,0,").concat(s,`,
        `).concat(t,",").concat(r+i-u*l[3])),f+="Z"}else if(o>0&&a===+a&&a>0){var d=Math.min(o,a);f="M ".concat(t,",").concat(r+u*d,`
            A `).concat(d,",").concat(d,",0,0,").concat(s,",").concat(t+c*d,",").concat(r,`
            L `).concat(t+n-c*d,",").concat(r,`
            A `).concat(d,",").concat(d,",0,0,").concat(s,",").concat(t+n,",").concat(r+u*d,`
            L `).concat(t+n,",").concat(r+i-u*d,`
            A `).concat(d,",").concat(d,",0,0,").concat(s,",").concat(t+n-c*d,",").concat(r+i,`
            L `).concat(t+c*d,",").concat(r+i,`
            A `).concat(d,",").concat(d,",0,0,").concat(s,",").concat(t,",").concat(r+i-u*d," Z")}else f="M ".concat(t,",").concat(r," h ").concat(n," v ").concat(i," h ").concat(-n," Z");return f},Lk=function(t,r){if(!t||!r)return!1;var n=t.x,i=t.y,a=r.x,o=r.y,u=r.width,c=r.height;if(Math.abs(u)>0&&Math.abs(c)>0){var s=Math.min(a,a+u),f=Math.max(a,a+u),l=Math.min(o,o+c),p=Math.max(o,o+c);return n>=s&&n<=f&&i>=l&&i<=p}return!1},Bk={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},os=function(t){var r=bp(bp({},Bk),t),n=B.useRef(),i=B.useState(-1),a=Ek(i,2),o=a[0],u=a[1];B.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var S=n.current.getTotalLength();S&&u(S)}catch{}},[]);var c=r.x,s=r.y,f=r.width,l=r.height,p=r.radius,h=r.className,d=r.animationEasing,v=r.animationDuration,y=r.animationBegin,b=r.isAnimationActive,w=r.isUpdateAnimationActive;if(c!==+c||s!==+s||f!==+f||l!==+l||f===0||l===0)return null;var x=ee("recharts-rectangle",h);return w?_.createElement(xt,{canBegin:o>0,from:{width:f,height:l,x:c,y:s},to:{width:f,height:l,x:c,y:s},duration:v,animationEasing:d,isActive:w},function(S){var m=S.width,g=S.height,O=S.x,A=S.y;return _.createElement(xt,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:v,isActive:b,easing:d},_.createElement("path",ra({},J(r,!0),{className:x,d:xp(O,A,m,g,p),ref:n})))}):_.createElement("path",ra({},J(r,!0),{className:x,d:xp(c,s,f,l,p)}))};function Wu(){return Wu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Wu.apply(this,arguments)}var us=function(t){var r=t.cx,n=t.cy,i=t.r,a=t.className,o=ee("recharts-dot",a);return r===+r&&n===+n&&i===+i?_.createElement("circle",Wu({},J(t,!1),wi(t),{className:o,cx:r,cy:n,r:i})):null};function Rn(e){"@babel/helpers - typeof";return Rn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rn(e)}var Rk=["x","y","top","left","width","height","className"];function zu(){return zu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},zu.apply(this,arguments)}function wp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Fk(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?wp(Object(r),!0).forEach(function(n){Wk(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):wp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Wk(e,t,r){return t=zk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zk(e){var t=Uk(e,"string");return Rn(t)=="symbol"?t:t+""}function Uk(e,t){if(Rn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Rn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function qk(e,t){if(e==null)return{};var r=Hk(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Hk(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var Gk=function(t,r,n,i,a,o){return"M".concat(t,",").concat(a,"v").concat(i,"M").concat(o,",").concat(r,"h").concat(n)},Kk=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.top,u=o===void 0?0:o,c=t.left,s=c===void 0?0:c,f=t.width,l=f===void 0?0:f,p=t.height,h=p===void 0?0:p,d=t.className,v=qk(t,Rk),y=Fk({x:n,y:a,top:u,left:s,width:l,height:h},v);return!R(n)||!R(a)||!R(l)||!R(h)||!R(u)||!R(s)?null:_.createElement("path",zu({},J(y,!0),{className:ee("recharts-cross",d),d:Gk(n,a,l,h,u,s)}))},Xk=ad,Vk=Xk(Object.getPrototypeOf,Object),Yk=Vk,Zk=wt,Jk=Yk,Qk=Ot,eN="[object Object]",tN=Function.prototype,rN=Object.prototype,Gv=tN.toString,nN=rN.hasOwnProperty,iN=Gv.call(Object);function aN(e){if(!Qk(e)||Zk(e)!=eN)return!1;var t=Jk(e);if(t===null)return!0;var r=nN.call(t,"constructor")&&t.constructor;return typeof r=="function"&&r instanceof r&&Gv.call(r)==iN}var oN=aN;const uN=ue(oN);var cN=wt,sN=Ot,lN="[object Boolean]";function fN(e){return e===!0||e===!1||sN(e)&&cN(e)==lN}var pN=fN;const hN=ue(pN);function Fn(e){"@babel/helpers - typeof";return Fn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fn(e)}function na(){return na=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},na.apply(this,arguments)}function dN(e,t){return gN(e)||mN(e,t)||yN(e,t)||vN()}function vN(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function yN(e,t){if(e){if(typeof e=="string")return Op(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Op(e,t)}}function Op(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function mN(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function gN(e){if(Array.isArray(e))return e}function Sp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ap(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Sp(Object(r),!0).forEach(function(n){bN(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Sp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function bN(e,t,r){return t=xN(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function xN(e){var t=wN(e,"string");return Fn(t)=="symbol"?t:t+""}function wN(e,t){if(Fn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Fn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var _p=function(t,r,n,i,a){var o=n-i,u;return u="M ".concat(t,",").concat(r),u+="L ".concat(t+n,",").concat(r),u+="L ".concat(t+n-o/2,",").concat(r+a),u+="L ".concat(t+n-o/2-i,",").concat(r+a),u+="L ".concat(t,",").concat(r," Z"),u},ON={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},SN=function(t){var r=Ap(Ap({},ON),t),n=B.useRef(),i=B.useState(-1),a=dN(i,2),o=a[0],u=a[1];B.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var x=n.current.getTotalLength();x&&u(x)}catch{}},[]);var c=r.x,s=r.y,f=r.upperWidth,l=r.lowerWidth,p=r.height,h=r.className,d=r.animationEasing,v=r.animationDuration,y=r.animationBegin,b=r.isUpdateAnimationActive;if(c!==+c||s!==+s||f!==+f||l!==+l||p!==+p||f===0&&l===0||p===0)return null;var w=ee("recharts-trapezoid",h);return b?_.createElement(xt,{canBegin:o>0,from:{upperWidth:0,lowerWidth:0,height:p,x:c,y:s},to:{upperWidth:f,lowerWidth:l,height:p,x:c,y:s},duration:v,animationEasing:d,isActive:b},function(x){var S=x.upperWidth,m=x.lowerWidth,g=x.height,O=x.x,A=x.y;return _.createElement(xt,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:v,easing:d},_.createElement("path",na({},J(r,!0),{className:w,d:_p(O,A,S,m,g),ref:n})))}):_.createElement("g",null,_.createElement("path",na({},J(r,!0),{className:w,d:_p(c,s,f,l,p)})))},AN=["option","shapeType","propTransformer","activeClassName","isActive"];function Wn(e){"@babel/helpers - typeof";return Wn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Wn(e)}function _N(e,t){if(e==null)return{};var r=PN(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function PN(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Pp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ia(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Pp(Object(r),!0).forEach(function(n){$N(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Pp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function $N(e,t,r){return t=TN(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function TN(e){var t=EN(e,"string");return Wn(t)=="symbol"?t:t+""}function EN(e,t){if(Wn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Wn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function jN(e,t){return ia(ia({},t),e)}function MN(e,t){return e==="symbols"}function $p(e){var t=e.shapeType,r=e.elementProps;switch(t){case"rectangle":return _.createElement(os,r);case"trapezoid":return _.createElement(SN,r);case"sector":return _.createElement(kv,r);case"symbols":if(MN(t))return _.createElement(_c,r);break;default:return null}}function CN(e){return B.isValidElement(e)?e.props:e}function IN(e){var t=e.option,r=e.shapeType,n=e.propTransformer,i=n===void 0?jN:n,a=e.activeClassName,o=a===void 0?"recharts-active-shape":a,u=e.isActive,c=_N(e,AN),s;if(B.isValidElement(t))s=B.cloneElement(t,ia(ia({},c),CN(t)));else if(Y(t))s=t(c);else if(uN(t)&&!hN(t)){var f=i(t,c);s=_.createElement($p,{shapeType:r,elementProps:f})}else{var l=c;s=_.createElement($p,{shapeType:r,elementProps:l})}return u?_.createElement(de,{className:o},s):s}function Ja(e,t){return t!=null&&"trapezoids"in e.props}function Qa(e,t){return t!=null&&"sectors"in e.props}function zn(e,t){return t!=null&&"points"in e.props}function DN(e,t){var r,n,i=e.x===(t==null||(r=t.labelViewBox)===null||r===void 0?void 0:r.x)||e.x===t.x,a=e.y===(t==null||(n=t.labelViewBox)===null||n===void 0?void 0:n.y)||e.y===t.y;return i&&a}function kN(e,t){var r=e.endAngle===t.endAngle,n=e.startAngle===t.startAngle;return r&&n}function NN(e,t){var r=e.x===t.x,n=e.y===t.y,i=e.z===t.z;return r&&n&&i}function LN(e,t){var r;return Ja(e,t)?r=DN:Qa(e,t)?r=kN:zn(e,t)&&(r=NN),r}function BN(e,t){var r;return Ja(e,t)?r="trapezoids":Qa(e,t)?r="sectors":zn(e,t)&&(r="points"),r}function RN(e,t){if(Ja(e,t)){var r;return(r=t.tooltipPayload)===null||r===void 0||(r=r[0])===null||r===void 0||(r=r.payload)===null||r===void 0?void 0:r.payload}if(Qa(e,t)){var n;return(n=t.tooltipPayload)===null||n===void 0||(n=n[0])===null||n===void 0||(n=n.payload)===null||n===void 0?void 0:n.payload}return zn(e,t)?t.payload:{}}function FN(e){var t=e.activeTooltipItem,r=e.graphicalItem,n=e.itemData,i=BN(r,t),a=RN(r,t),o=n.filter(function(c,s){var f=Va(a,c),l=r.props[i].filter(function(d){var v=LN(r,t);return v(d,t)}),p=r.props[i].indexOf(l[l.length-1]),h=s===p;return f&&h}),u=n.indexOf(o[o.length-1]);return u}var WN=Math.ceil,zN=Math.max;function UN(e,t,r,n){for(var i=-1,a=zN(WN((t-e)/(r||1)),0),o=Array(a);a--;)o[n?a:++i]=e,e+=r;return o}var qN=UN,HN=Ad,GN=1/0,KN=17976931348623157e292;function XN(e){if(!e)return e===0?e:0;if(e=HN(e),e===GN||e===-1/0){var t=e<0?-1:1;return t*KN}return e===e?e:0}var Kv=XN,VN=qN,YN=Fa,Io=Kv;function ZN(e){return function(t,r,n){return n&&typeof n!="number"&&YN(t,r,n)&&(r=n=void 0),t=Io(t),r===void 0?(r=t,t=0):r=Io(r),n=n===void 0?t<r?1:-1:Io(n),VN(t,r,n,e)}}var JN=ZN,QN=JN,eL=QN(),tL=eL;const aa=ue(tL);function Un(e){"@babel/helpers - typeof";return Un=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Un(e)}function Tp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ep(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Tp(Object(r),!0).forEach(function(n){Xv(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Tp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Xv(e,t,r){return t=rL(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function rL(e){var t=nL(e,"string");return Un(t)=="symbol"?t:t+""}function nL(e,t){if(Un(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Un(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var iL=["Webkit","Moz","O","ms"],aL=function(t,r){var n=t.replace(/(\w)/,function(a){return a.toUpperCase()}),i=iL.reduce(function(a,o){return Ep(Ep({},a),{},Xv({},o+n,r))},{});return i[t]=r,i};function _r(e){"@babel/helpers - typeof";return _r=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_r(e)}function oa(){return oa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},oa.apply(this,arguments)}function jp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Do(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?jp(Object(r),!0).forEach(function(n){Be(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):jp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function oL(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Mp(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Yv(n.key),n)}}function uL(e,t,r){return t&&Mp(e.prototype,t),r&&Mp(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function cL(e,t,r){return t=ua(t),sL(e,Vv()?Reflect.construct(t,r||[],ua(e).constructor):t.apply(e,r))}function sL(e,t){if(t&&(_r(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return lL(e)}function lL(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Vv(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Vv=function(){return!!e})()}function ua(e){return ua=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ua(e)}function fL(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Uu(e,t)}function Uu(e,t){return Uu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Uu(e,t)}function Be(e,t,r){return t=Yv(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Yv(e){var t=pL(e,"string");return _r(t)=="symbol"?t:t+""}function pL(e,t){if(_r(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(_r(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var hL=function(t){var r=t.data,n=t.startIndex,i=t.endIndex,a=t.x,o=t.width,u=t.travellerWidth;if(!r||!r.length)return{};var c=r.length,s=ln().domain(aa(0,c)).range([a,a+o-u]),f=s.domain().map(function(l){return s(l)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:s(n),endX:s(i),scale:s,scaleValues:f}},Cp=function(t){return t.changedTouches&&!!t.changedTouches.length},Pr=function(e){function t(r){var n;return oL(this,t),n=cL(this,t,[r]),Be(n,"handleDrag",function(i){n.leaveTimer&&(clearTimeout(n.leaveTimer),n.leaveTimer=null),n.state.isTravellerMoving?n.handleTravellerMove(i):n.state.isSlideMoving&&n.handleSlideDrag(i)}),Be(n,"handleTouchMove",function(i){i.changedTouches!=null&&i.changedTouches.length>0&&n.handleDrag(i.changedTouches[0])}),Be(n,"handleDragEnd",function(){n.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var i=n.props,a=i.endIndex,o=i.onDragEnd,u=i.startIndex;o==null||o({endIndex:a,startIndex:u})}),n.detachDragEndListener()}),Be(n,"handleLeaveWrapper",function(){(n.state.isTravellerMoving||n.state.isSlideMoving)&&(n.leaveTimer=window.setTimeout(n.handleDragEnd,n.props.leaveTimeOut))}),Be(n,"handleEnterSlideOrTraveller",function(){n.setState({isTextActive:!0})}),Be(n,"handleLeaveSlideOrTraveller",function(){n.setState({isTextActive:!1})}),Be(n,"handleSlideDragStart",function(i){var a=Cp(i)?i.changedTouches[0]:i;n.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:a.pageX}),n.attachDragEndListener()}),n.travellerDragStartHandlers={startX:n.handleTravellerDragStart.bind(n,"startX"),endX:n.handleTravellerDragStart.bind(n,"endX")},n.state={},n}return fL(t,e),uL(t,[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(n){var i=n.startX,a=n.endX,o=this.state.scaleValues,u=this.props,c=u.gap,s=u.data,f=s.length-1,l=Math.min(i,a),p=Math.max(i,a),h=t.getIndexInRange(o,l),d=t.getIndexInRange(o,p);return{startIndex:h-h%c,endIndex:d===f?f:d-d%c}}},{key:"getTextOfTick",value:function(n){var i=this.props,a=i.data,o=i.tickFormatter,u=i.dataKey,c=We(a[n],u,n);return Y(o)?o(c,n):c}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(n){var i=this.state,a=i.slideMoveStartX,o=i.startX,u=i.endX,c=this.props,s=c.x,f=c.width,l=c.travellerWidth,p=c.startIndex,h=c.endIndex,d=c.onChange,v=n.pageX-a;v>0?v=Math.min(v,s+f-l-u,s+f-l-o):v<0&&(v=Math.max(v,s-o,s-u));var y=this.getIndex({startX:o+v,endX:u+v});(y.startIndex!==p||y.endIndex!==h)&&d&&d(y),this.setState({startX:o+v,endX:u+v,slideMoveStartX:n.pageX})}},{key:"handleTravellerDragStart",value:function(n,i){var a=Cp(i)?i.changedTouches[0]:i;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:n,brushMoveStartX:a.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(n){var i=this.state,a=i.brushMoveStartX,o=i.movingTravellerId,u=i.endX,c=i.startX,s=this.state[o],f=this.props,l=f.x,p=f.width,h=f.travellerWidth,d=f.onChange,v=f.gap,y=f.data,b={startX:this.state.startX,endX:this.state.endX},w=n.pageX-a;w>0?w=Math.min(w,l+p-h-s):w<0&&(w=Math.max(w,l-s)),b[o]=s+w;var x=this.getIndex(b),S=x.startIndex,m=x.endIndex,g=function(){var A=y.length-1;return o==="startX"&&(u>c?S%v===0:m%v===0)||u<c&&m===A||o==="endX"&&(u>c?m%v===0:S%v===0)||u>c&&m===A};this.setState(Be(Be({},o,s+w),"brushMoveStartX",n.pageX),function(){d&&g()&&d(x)})}},{key:"handleTravellerMoveKeyboard",value:function(n,i){var a=this,o=this.state,u=o.scaleValues,c=o.startX,s=o.endX,f=this.state[i],l=u.indexOf(f);if(l!==-1){var p=l+n;if(!(p===-1||p>=u.length)){var h=u[p];i==="startX"&&h>=s||i==="endX"&&h<=c||this.setState(Be({},i,h),function(){a.props.onChange(a.getIndex({startX:a.state.startX,endX:a.state.endX}))})}}}},{key:"renderBackground",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.fill,s=n.stroke;return _.createElement("rect",{stroke:s,fill:c,x:i,y:a,width:o,height:u})}},{key:"renderPanorama",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.data,s=n.children,f=n.padding,l=B.Children.only(s);return l?_.cloneElement(l,{x:i,y:a,width:o,height:u,margin:f,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(n,i){var a,o,u=this,c=this.props,s=c.y,f=c.travellerWidth,l=c.height,p=c.traveller,h=c.ariaLabel,d=c.data,v=c.startIndex,y=c.endIndex,b=Math.max(n,this.props.x),w=Do(Do({},J(this.props,!1)),{},{x:b,y:s,width:f,height:l}),x=h||"Min value: ".concat((a=d[v])===null||a===void 0?void 0:a.name,", Max value: ").concat((o=d[y])===null||o===void 0?void 0:o.name);return _.createElement(de,{tabIndex:0,role:"slider","aria-label":x,"aria-valuenow":n,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[i],onTouchStart:this.travellerDragStartHandlers[i],onKeyDown:function(m){["ArrowLeft","ArrowRight"].includes(m.key)&&(m.preventDefault(),m.stopPropagation(),u.handleTravellerMoveKeyboard(m.key==="ArrowRight"?1:-1,i))},onFocus:function(){u.setState({isTravellerFocused:!0})},onBlur:function(){u.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},t.renderTraveller(p,w))}},{key:"renderSlide",value:function(n,i){var a=this.props,o=a.y,u=a.height,c=a.stroke,s=a.travellerWidth,f=Math.min(n,i)+s,l=Math.max(Math.abs(i-n)-s,0);return _.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:c,fillOpacity:.2,x:f,y:o,width:l,height:u})}},{key:"renderText",value:function(){var n=this.props,i=n.startIndex,a=n.endIndex,o=n.y,u=n.height,c=n.travellerWidth,s=n.stroke,f=this.state,l=f.startX,p=f.endX,h=5,d={pointerEvents:"none",fill:s};return _.createElement(de,{className:"recharts-brush-texts"},_.createElement(Ni,oa({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,p)-h,y:o+u/2},d),this.getTextOfTick(i)),_.createElement(Ni,oa({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,p)+c+h,y:o+u/2},d),this.getTextOfTick(a)))}},{key:"render",value:function(){var n=this.props,i=n.data,a=n.className,o=n.children,u=n.x,c=n.y,s=n.width,f=n.height,l=n.alwaysShowText,p=this.state,h=p.startX,d=p.endX,v=p.isTextActive,y=p.isSlideMoving,b=p.isTravellerMoving,w=p.isTravellerFocused;if(!i||!i.length||!R(u)||!R(c)||!R(s)||!R(f)||s<=0||f<=0)return null;var x=ee("recharts-brush",a),S=_.Children.count(o)===1,m=aL("userSelect","none");return _.createElement(de,{className:x,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:m},this.renderBackground(),S&&this.renderPanorama(),this.renderSlide(h,d),this.renderTravellerLayer(h,"startX"),this.renderTravellerLayer(d,"endX"),(v||y||b||w||l)&&this.renderText())}}],[{key:"renderDefaultTraveller",value:function(n){var i=n.x,a=n.y,o=n.width,u=n.height,c=n.stroke,s=Math.floor(a+u/2)-1;return _.createElement(_.Fragment,null,_.createElement("rect",{x:i,y:a,width:o,height:u,fill:c,stroke:"none"}),_.createElement("line",{x1:i+1,y1:s,x2:i+o-1,y2:s,fill:"none",stroke:"#fff"}),_.createElement("line",{x1:i+1,y1:s+2,x2:i+o-1,y2:s+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(n,i){var a;return _.isValidElement(n)?a=_.cloneElement(n,i):Y(n)?a=n(i):a=t.renderDefaultTraveller(i),a}},{key:"getDerivedStateFromProps",value:function(n,i){var a=n.data,o=n.width,u=n.x,c=n.travellerWidth,s=n.updateId,f=n.startIndex,l=n.endIndex;if(a!==i.prevData||s!==i.prevUpdateId)return Do({prevData:a,prevTravellerWidth:c,prevUpdateId:s,prevX:u,prevWidth:o},a&&a.length?hL({data:a,width:o,x:u,travellerWidth:c,startIndex:f,endIndex:l}):{scale:null,scaleValues:null});if(i.scale&&(o!==i.prevWidth||u!==i.prevX||c!==i.prevTravellerWidth)){i.scale.range([u,u+o-c]);var p=i.scale.domain().map(function(h){return i.scale(h)});return{prevData:a,prevTravellerWidth:c,prevUpdateId:s,prevX:u,prevWidth:o,startX:i.scale(n.startIndex),endX:i.scale(n.endIndex),scaleValues:p}}return null}},{key:"getIndexInRange",value:function(n,i){for(var a=n.length,o=0,u=a-1;u-o>1;){var c=Math.floor((o+u)/2);n[c]>i?u=c:o=c}return i>=n[u]?u:o}}])}(B.PureComponent);Be(Pr,"displayName","Brush");Be(Pr,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var dL=Cc;function vL(e,t){var r;return dL(e,function(n,i,a){return r=t(n,i,a),!r}),!!r}var yL=vL,mL=Zh,gL=Mt,bL=yL,xL=ke,wL=Fa;function OL(e,t,r){var n=xL(e)?mL:bL;return r&&wL(e,t,r)&&(t=void 0),n(e,gL(t))}var SL=OL;const AL=ue(SL);var ot=function(t,r){var n=t.alwaysShow,i=t.ifOverflow;return n&&(i="extendDomain"),i===r},Ip=bd;function _L(e,t,r){t=="__proto__"&&Ip?Ip(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}var PL=_L,$L=PL,TL=md,EL=Mt;function jL(e,t){var r={};return t=EL(t),TL(e,function(n,i,a){$L(r,i,t(n,i,a))}),r}var ML=jL;const CL=ue(ML);function IL(e,t){for(var r=-1,n=e==null?0:e.length;++r<n;)if(!t(e[r],r,e))return!1;return!0}var DL=IL,kL=Cc;function NL(e,t){var r=!0;return kL(e,function(n,i,a){return r=!!t(n,i,a),r}),r}var LL=NL,BL=DL,RL=LL,FL=Mt,WL=ke,zL=Fa;function UL(e,t,r){var n=WL(e)?BL:RL;return r&&zL(e,t,r)&&(t=void 0),n(e,FL(t))}var qL=UL;const Zv=ue(qL);var HL=["x","y"];function qn(e){"@babel/helpers - typeof";return qn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},qn(e)}function qu(){return qu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},qu.apply(this,arguments)}function Dp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function un(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Dp(Object(r),!0).forEach(function(n){GL(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Dp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function GL(e,t,r){return t=KL(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function KL(e){var t=XL(e,"string");return qn(t)=="symbol"?t:t+""}function XL(e,t){if(qn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(qn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function VL(e,t){if(e==null)return{};var r=YL(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function YL(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function ZL(e,t){var r=e.x,n=e.y,i=VL(e,HL),a="".concat(r),o=parseInt(a,10),u="".concat(n),c=parseInt(u,10),s="".concat(t.height||i.height),f=parseInt(s,10),l="".concat(t.width||i.width),p=parseInt(l,10);return un(un(un(un(un({},t),i),o?{x:o}:{}),c?{y:c}:{}),{},{height:f,width:p,name:t.name,radius:t.radius})}function kp(e){return _.createElement(IN,qu({shapeType:"rectangle",propTransformer:ZL,activeClassName:"recharts-active-bar"},e))}var JL=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return function(n,i){if(typeof t=="number")return t;var a=typeof n=="number";return a?t(n,i):(a||Zt(),r)}},QL=["value","background"],Jv;function $r(e){"@babel/helpers - typeof";return $r=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},$r(e)}function eB(e,t){if(e==null)return{};var r=tB(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function tB(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function ca(){return ca=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ca.apply(this,arguments)}function Np(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ye(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Np(Object(r),!0).forEach(function(n){$t(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Np(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function rB(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Lp(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ey(n.key),n)}}function nB(e,t,r){return t&&Lp(e.prototype,t),r&&Lp(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function iB(e,t,r){return t=sa(t),aB(e,Qv()?Reflect.construct(t,r||[],sa(e).constructor):t.apply(e,r))}function aB(e,t){if(t&&($r(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return oB(e)}function oB(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Qv(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Qv=function(){return!!e})()}function sa(e){return sa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},sa(e)}function uB(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Hu(e,t)}function Hu(e,t){return Hu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Hu(e,t)}function $t(e,t,r){return t=ey(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ey(e){var t=cB(e,"string");return $r(t)=="symbol"?t:t+""}function cB(e,t){if($r(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if($r(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Vr=function(e){function t(){var r;rB(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=iB(this,t,[].concat(i)),$t(r,"state",{isAnimationFinished:!1}),$t(r,"id",Zn("recharts-bar-")),$t(r,"handleAnimationEnd",function(){var o=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),o&&o()}),$t(r,"handleAnimationStart",function(){var o=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),o&&o()}),r}return uB(t,e),nB(t,[{key:"renderRectanglesStatically",value:function(n){var i=this,a=this.props,o=a.shape,u=a.dataKey,c=a.activeIndex,s=a.activeBar,f=J(this.props,!1);return n&&n.map(function(l,p){var h=p===c,d=h?s:o,v=ye(ye(ye({},f),l),{},{isActive:h,option:d,index:p,dataKey:u,onAnimationStart:i.handleAnimationStart,onAnimationEnd:i.handleAnimationEnd});return _.createElement(de,ca({className:"recharts-bar-rectangle"},Oi(i.props,l,p),{key:"rectangle-".concat(l==null?void 0:l.x,"-").concat(l==null?void 0:l.y,"-").concat(l==null?void 0:l.value,"-").concat(p)}),_.createElement(kp,v))})}},{key:"renderRectanglesWithAnimation",value:function(){var n=this,i=this.props,a=i.data,o=i.layout,u=i.isAnimationActive,c=i.animationBegin,s=i.animationDuration,f=i.animationEasing,l=i.animationId,p=this.state.prevData;return _.createElement(xt,{begin:c,duration:s,isActive:u,easing:f,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(h){var d=h.t,v=a.map(function(y,b){var w=p&&p[b];if(w){var x=et(w.x,y.x),S=et(w.y,y.y),m=et(w.width,y.width),g=et(w.height,y.height);return ye(ye({},y),{},{x:x(d),y:S(d),width:m(d),height:g(d)})}if(o==="horizontal"){var O=et(0,y.height),A=O(d);return ye(ye({},y),{},{y:y.y+y.height-A,height:A})}var P=et(0,y.width),j=P(d);return ye(ye({},y),{},{width:j})});return _.createElement(de,null,n.renderRectanglesStatically(v))})}},{key:"renderRectangles",value:function(){var n=this.props,i=n.data,a=n.isAnimationActive,o=this.state.prevData;return a&&i&&i.length&&(!o||!Va(o,i))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(i)}},{key:"renderBackground",value:function(){var n=this,i=this.props,a=i.data,o=i.dataKey,u=i.activeIndex,c=J(this.props.background,!1);return a.map(function(s,f){s.value;var l=s.background,p=eB(s,QL);if(!l)return null;var h=ye(ye(ye(ye(ye({},p),{},{fill:"#eee"},l),c),Oi(n.props,s,f)),{},{onAnimationStart:n.handleAnimationStart,onAnimationEnd:n.handleAnimationEnd,dataKey:o,index:f,className:"recharts-bar-background-rectangle"});return _.createElement(kp,ca({key:"background-bar-".concat(f),option:n.props.background,isActive:f===u},h))})}},{key:"renderErrorBar",value:function(n,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.data,u=a.xAxis,c=a.yAxis,s=a.layout,f=a.children,l=Ge(f,ni);if(!l)return null;var p=s==="vertical"?o[0].height/2:o[0].width/2,h=function(y,b){var w=Array.isArray(y.value)?y.value[1]:y.value;return{x:y.x,y:y.y,value:w,errorVal:We(y,b)}},d={clipPath:n?"url(#clipPath-".concat(i,")"):null};return _.createElement(de,d,l.map(function(v){return _.cloneElement(v,{key:"error-bar-".concat(i,"-").concat(v.props.dataKey),data:o,xAxis:u,yAxis:c,layout:s,offset:p,dataPointFormatter:h})}))}},{key:"render",value:function(){var n=this.props,i=n.hide,a=n.data,o=n.className,u=n.xAxis,c=n.yAxis,s=n.left,f=n.top,l=n.width,p=n.height,h=n.isAnimationActive,d=n.background,v=n.id;if(i||!a||!a.length)return null;var y=this.state.isAnimationFinished,b=ee("recharts-bar",o),w=u&&u.allowDataOverflow,x=c&&c.allowDataOverflow,S=w||x,m=Z(v)?this.id:v;return _.createElement(de,{className:b},w||x?_.createElement("defs",null,_.createElement("clipPath",{id:"clipPath-".concat(m)},_.createElement("rect",{x:w?s:s-l/2,y:x?f:f-p/2,width:w?l:l*2,height:x?p:p*2}))):null,_.createElement(de,{className:"recharts-bar-rectangles",clipPath:S?"url(#clipPath-".concat(m,")"):null},d?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(S,m),(!h||y)&&Et.renderCallByParent(this.props,a))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curData:n.data,prevData:i.curData}:n.data!==i.curData?{curData:n.data}:null}}])}(B.PureComponent);Jv=Vr;$t(Vr,"displayName","Bar");$t(Vr,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!Hr.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"});$t(Vr,"getComposedData",function(e){var t=e.props,r=e.item,n=e.barPosition,i=e.bandSize,a=e.xAxis,o=e.yAxis,u=e.xAxisTicks,c=e.yAxisTicks,s=e.stackedData,f=e.dataStartIndex,l=e.displayedData,p=e.offset,h=LI(n,r);if(!h)return null;var d=t.layout,v=r.type.defaultProps,y=v!==void 0?ye(ye({},v),r.props):r.props,b=y.dataKey,w=y.children,x=y.minPointSize,S=d==="horizontal"?o:a,m=s?S.scale.domain():null,g=HI({numericAxis:S}),O=Ge(w,Pd),A=l.map(function(P,j){var $,T,I,C,M,D;s?$=BI(s[f+j],m):($=We(P,b),Array.isArray($)||($=[g,$]));var k=JL(x,Jv.defaultProps.minPointSize)($[1],j);if(d==="horizontal"){var L,F=[o.scale($[0]),o.scale($[1])],q=F[0],G=F[1];T=Lf({axis:a,ticks:u,bandSize:i,offset:h.offset,entry:P,index:j}),I=(L=G??q)!==null&&L!==void 0?L:void 0,C=h.size;var z=q-G;if(M=Number.isNaN(z)?0:z,D={x:T,y:o.y,width:C,height:o.height},Math.abs(k)>0&&Math.abs(M)<Math.abs(k)){var K=rt(M||k)*(Math.abs(k)-Math.abs(M));I-=K,M+=K}}else{var ce=[a.scale($[0]),a.scale($[1])],ve=ce[0],Ne=ce[1];if(T=ve,I=Lf({axis:o,ticks:c,bandSize:i,offset:h.offset,entry:P,index:j}),C=Ne-ve,M=h.size,D={x:a.x,y:I,width:a.width,height:M},Math.abs(k)>0&&Math.abs(C)<Math.abs(k)){var kt=rt(C||k)*(Math.abs(k)-Math.abs(C));C+=kt}}return ye(ye(ye({},P),{},{x:T,y:I,width:C,height:M,value:s?$:$[1],payload:P,background:D},O&&O[j]&&O[j].props),{},{tooltipPayload:[Cv(r,P)],tooltipPosition:{x:T+C/2,y:I+M/2}})});return ye({data:A,layout:d},p)});function Hn(e){"@babel/helpers - typeof";return Hn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Hn(e)}function sB(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Bp(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ty(n.key),n)}}function lB(e,t,r){return t&&Bp(e.prototype,t),r&&Bp(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Rp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Je(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Rp(Object(r),!0).forEach(function(n){eo(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Rp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function eo(e,t,r){return t=ty(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ty(e){var t=fB(e,"string");return Hn(t)=="symbol"?t:t+""}function fB(e,t){if(Hn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Hn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var ry=function(t,r,n,i,a){var o=t.width,u=t.height,c=t.layout,s=t.children,f=Object.keys(r),l={left:n.left,leftMirror:n.left,right:o-n.right,rightMirror:o-n.right,top:n.top,topMirror:n.top,bottom:u-n.bottom,bottomMirror:u-n.bottom},p=!!Re(s,Vr);return f.reduce(function(h,d){var v=r[d],y=v.orientation,b=v.domain,w=v.padding,x=w===void 0?{}:w,S=v.mirror,m=v.reversed,g="".concat(y).concat(S?"Mirror":""),O,A,P,j,$;if(v.type==="number"&&(v.padding==="gap"||v.padding==="no-gap")){var T=b[1]-b[0],I=1/0,C=v.categoricalDomain.sort(m0);if(C.forEach(function(ce,ve){ve>0&&(I=Math.min((ce||0)-(C[ve-1]||0),I))}),Number.isFinite(I)){var M=I/T,D=v.layout==="vertical"?n.height:n.width;if(v.padding==="gap"&&(O=M*D/2),v.padding==="no-gap"){var k=Vt(t.barCategoryGap,M*D),L=M*D/2;O=L-k-(L-k)/D*k}}}i==="xAxis"?A=[n.left+(x.left||0)+(O||0),n.left+n.width-(x.right||0)-(O||0)]:i==="yAxis"?A=c==="horizontal"?[n.top+n.height-(x.bottom||0),n.top+(x.top||0)]:[n.top+(x.top||0)+(O||0),n.top+n.height-(x.bottom||0)-(O||0)]:A=v.range,m&&(A=[A[1],A[0]]);var F=kI(v,a,p),q=F.scale,G=F.realScaleType;q.domain(b).range(A),NI(q);var z=qI(q,Je(Je({},v),{},{realScaleType:G}));i==="xAxis"?($=y==="top"&&!S||y==="bottom"&&S,P=n.left,j=l[g]-$*v.height):i==="yAxis"&&($=y==="left"&&!S||y==="right"&&S,P=l[g]-$*v.width,j=n.top);var K=Je(Je(Je({},v),z),{},{realScaleType:G,x:P,y:j,scale:q,width:i==="xAxis"?n.width:v.width,height:i==="yAxis"?n.height:v.height});return K.bandSize=Yi(K,z),!v.hide&&i==="xAxis"?l[g]+=($?-1:1)*K.height:v.hide||(l[g]+=($?-1:1)*K.width),Je(Je({},h),{},eo({},d,K))},{})},ny=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return{x:Math.min(n,a),y:Math.min(i,o),width:Math.abs(a-n),height:Math.abs(o-i)}},pB=function(t){var r=t.x1,n=t.y1,i=t.x2,a=t.y2;return ny({x:r,y:n},{x:i,y:a})},iy=function(){function e(t){sB(this,e),this.scale=t}return lB(e,[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(r){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=n.bandAware,a=n.position;if(r!==void 0){if(a)switch(a){case"start":return this.scale(r);case"middle":{var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+o}case"end":{var u=this.bandwidth?this.bandwidth():0;return this.scale(r)+u}default:return this.scale(r)}if(i){var c=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+c}return this.scale(r)}}},{key:"isInRange",value:function(r){var n=this.range(),i=n[0],a=n[n.length-1];return i<=a?r>=i&&r<=a:r>=a&&r<=i}}],[{key:"create",value:function(r){return new e(r)}}])}();eo(iy,"EPS",1e-4);var cs=function(t){var r=Object.keys(t).reduce(function(n,i){return Je(Je({},n),{},eo({},i,iy.create(t[i])))},{});return Je(Je({},r),{},{apply:function(i){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=a.bandAware,u=a.position;return CL(i,function(c,s){return r[s].apply(c,{bandAware:o,position:u})})},isInRange:function(i){return Zv(i,function(a,o){return r[o].isInRange(a)})}})};function hB(e){return(e%180+180)%180}var dB=function(t){var r=t.width,n=t.height,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=hB(i),o=a*Math.PI/180,u=Math.atan(n/r),c=o>u&&o<Math.PI-u?n/Math.sin(o):r/Math.cos(o);return Math.abs(c)},vB=Mt,yB=Jn,mB=Ba;function gB(e){return function(t,r,n){var i=Object(t);if(!yB(t)){var a=vB(r);t=mB(t),r=function(u){return a(i[u],u,i)}}var o=e(t,r,n);return o>-1?i[a?t[o]:o]:void 0}}var bB=gB,xB=Kv;function wB(e){var t=xB(e),r=t%1;return t===t?r?t-r:t:0}var OB=wB,SB=fd,AB=Mt,_B=OB,PB=Math.max;function $B(e,t,r){var n=e==null?0:e.length;if(!n)return-1;var i=r==null?0:_B(r);return i<0&&(i=PB(n+i,0)),SB(e,AB(t),i)}var TB=$B,EB=bB,jB=TB,MB=EB(jB),CB=MB;const IB=ue(CB);var DB=wb(function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}},function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")}),ss=B.createContext(void 0),ls=B.createContext(void 0),ay=B.createContext(void 0),oy=B.createContext({}),uy=B.createContext(void 0),cy=B.createContext(0),sy=B.createContext(0),Fp=function(t){var r=t.state,n=r.xAxisMap,i=r.yAxisMap,a=r.offset,o=t.clipPathId,u=t.children,c=t.width,s=t.height,f=DB(a);return _.createElement(ss.Provider,{value:n},_.createElement(ls.Provider,{value:i},_.createElement(oy.Provider,{value:a},_.createElement(ay.Provider,{value:f},_.createElement(uy.Provider,{value:o},_.createElement(cy.Provider,{value:s},_.createElement(sy.Provider,{value:c},u)))))))},kB=function(){return B.useContext(uy)},ly=function(t){var r=B.useContext(ss);r==null&&Zt();var n=r[t];return n==null&&Zt(),n},NB=function(){var t=B.useContext(ss);return Pt(t)},LB=function(){var t=B.useContext(ls),r=IB(t,function(n){return Zv(n.domain,Number.isFinite)});return r||Pt(t)},fy=function(t){var r=B.useContext(ls);r==null&&Zt();var n=r[t];return n==null&&Zt(),n},BB=function(){var t=B.useContext(ay);return t},RB=function(){return B.useContext(oy)},fs=function(){return B.useContext(sy)},ps=function(){return B.useContext(cy)};function Tr(e){"@babel/helpers - typeof";return Tr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Tr(e)}function FB(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function WB(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,hy(n.key),n)}}function zB(e,t,r){return t&&WB(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function UB(e,t,r){return t=la(t),qB(e,py()?Reflect.construct(t,r||[],la(e).constructor):t.apply(e,r))}function qB(e,t){if(t&&(Tr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return HB(e)}function HB(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function py(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(py=function(){return!!e})()}function la(e){return la=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},la(e)}function GB(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Gu(e,t)}function Gu(e,t){return Gu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Gu(e,t)}function Wp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function zp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Wp(Object(r),!0).forEach(function(n){hs(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Wp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function hs(e,t,r){return t=hy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hy(e){var t=KB(e,"string");return Tr(t)=="symbol"?t:t+""}function KB(e,t){if(Tr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Tr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function XB(e,t){return JB(e)||ZB(e,t)||YB(e,t)||VB()}function VB(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function YB(e,t){if(e){if(typeof e=="string")return Up(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Up(e,t)}}function Up(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ZB(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function JB(e){if(Array.isArray(e))return e}function Ku(){return Ku=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ku.apply(this,arguments)}var QB=function(t,r){var n;return _.isValidElement(t)?n=_.cloneElement(t,r):Y(t)?n=t(r):n=_.createElement("line",Ku({},r,{className:"recharts-reference-line-line"})),n},eR=function(t,r,n,i,a,o,u,c,s){var f=a.x,l=a.y,p=a.width,h=a.height;if(n){var d=s.y,v=t.y.apply(d,{position:o});if(ot(s,"discard")&&!t.y.isInRange(v))return null;var y=[{x:f+p,y:v},{x:f,y:v}];return c==="left"?y.reverse():y}if(r){var b=s.x,w=t.x.apply(b,{position:o});if(ot(s,"discard")&&!t.x.isInRange(w))return null;var x=[{x:w,y:l+h},{x:w,y:l}];return u==="top"?x.reverse():x}if(i){var S=s.segment,m=S.map(function(g){return t.apply(g,{position:o})});return ot(s,"discard")&&AL(m,function(g){return!t.isInRange(g)})?null:m}return null};function tR(e){var t=e.x,r=e.y,n=e.segment,i=e.xAxisId,a=e.yAxisId,o=e.shape,u=e.className,c=e.alwaysShow,s=kB(),f=ly(i),l=fy(a),p=BB();if(!s||!p)return null;vt(c===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var h=cs({x:f.scale,y:l.scale}),d=we(t),v=we(r),y=n&&n.length===2,b=eR(h,d,v,y,p,e.position,f.orientation,l.orientation,e);if(!b)return null;var w=XB(b,2),x=w[0],S=x.x,m=x.y,g=w[1],O=g.x,A=g.y,P=ot(e,"hidden")?"url(#".concat(s,")"):void 0,j=zp(zp({clipPath:P},J(e,!0)),{},{x1:S,y1:m,x2:O,y2:A});return _.createElement(de,{className:ee("recharts-reference-line",u)},QB(o,j),Te.renderCallByParent(e,pB({x1:S,y1:m,x2:O,y2:A})))}var ds=function(e){function t(){return FB(this,t),UB(this,t,arguments)}return GB(t,e),zB(t,[{key:"render",value:function(){return _.createElement(tR,this.props)}}])}(_.Component);hs(ds,"displayName","ReferenceLine");hs(ds,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function Xu(){return Xu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Xu.apply(this,arguments)}function Er(e){"@babel/helpers - typeof";return Er=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Er(e)}function qp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Hp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?qp(Object(r),!0).forEach(function(n){to(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):qp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function rR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function nR(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,vy(n.key),n)}}function iR(e,t,r){return t&&nR(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function aR(e,t,r){return t=fa(t),oR(e,dy()?Reflect.construct(t,r||[],fa(e).constructor):t.apply(e,r))}function oR(e,t){if(t&&(Er(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return uR(e)}function uR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function dy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(dy=function(){return!!e})()}function fa(e){return fa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},fa(e)}function cR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Vu(e,t)}function Vu(e,t){return Vu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Vu(e,t)}function to(e,t,r){return t=vy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function vy(e){var t=sR(e,"string");return Er(t)=="symbol"?t:t+""}function sR(e,t){if(Er(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Er(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var lR=function(t){var r=t.x,n=t.y,i=t.xAxis,a=t.yAxis,o=cs({x:i.scale,y:a.scale}),u=o.apply({x:r,y:n},{bandAware:!0});return ot(t,"discard")&&!o.isInRange(u)?null:u},ro=function(e){function t(){return rR(this,t),aR(this,t,arguments)}return cR(t,e),iR(t,[{key:"render",value:function(){var n=this.props,i=n.x,a=n.y,o=n.r,u=n.alwaysShow,c=n.clipPathId,s=we(i),f=we(a);if(vt(u===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!s||!f)return null;var l=lR(this.props);if(!l)return null;var p=l.x,h=l.y,d=this.props,v=d.shape,y=d.className,b=ot(this.props,"hidden")?"url(#".concat(c,")"):void 0,w=Hp(Hp({clipPath:b},J(this.props,!0)),{},{cx:p,cy:h});return _.createElement(de,{className:ee("recharts-reference-dot",y)},t.renderDot(v,w),Te.renderCallByParent(this.props,{x:p-o,y:h-o,width:2*o,height:2*o}))}}])}(_.Component);to(ro,"displayName","ReferenceDot");to(ro,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1});to(ro,"renderDot",function(e,t){var r;return _.isValidElement(e)?r=_.cloneElement(e,t):Y(e)?r=e(t):r=_.createElement(us,Xu({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"})),r});function Yu(){return Yu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Yu.apply(this,arguments)}function jr(e){"@babel/helpers - typeof";return jr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},jr(e)}function Gp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Kp(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Gp(Object(r),!0).forEach(function(n){no(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Gp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function fR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function pR(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,my(n.key),n)}}function hR(e,t,r){return t&&pR(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function dR(e,t,r){return t=pa(t),vR(e,yy()?Reflect.construct(t,r||[],pa(e).constructor):t.apply(e,r))}function vR(e,t){if(t&&(jr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return yR(e)}function yR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function yy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(yy=function(){return!!e})()}function pa(e){return pa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},pa(e)}function mR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Zu(e,t)}function Zu(e,t){return Zu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Zu(e,t)}function no(e,t,r){return t=my(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function my(e){var t=gR(e,"string");return jr(t)=="symbol"?t:t+""}function gR(e,t){if(jr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(jr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var bR=function(t,r,n,i,a){var o=a.x1,u=a.x2,c=a.y1,s=a.y2,f=a.xAxis,l=a.yAxis;if(!f||!l)return null;var p=cs({x:f.scale,y:l.scale}),h={x:t?p.x.apply(o,{position:"start"}):p.x.rangeMin,y:n?p.y.apply(c,{position:"start"}):p.y.rangeMin},d={x:r?p.x.apply(u,{position:"end"}):p.x.rangeMax,y:i?p.y.apply(s,{position:"end"}):p.y.rangeMax};return ot(a,"discard")&&(!p.isInRange(h)||!p.isInRange(d))?null:ny(h,d)},io=function(e){function t(){return fR(this,t),dR(this,t,arguments)}return mR(t,e),hR(t,[{key:"render",value:function(){var n=this.props,i=n.x1,a=n.x2,o=n.y1,u=n.y2,c=n.className,s=n.alwaysShow,f=n.clipPathId;vt(s===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var l=we(i),p=we(a),h=we(o),d=we(u),v=this.props.shape;if(!l&&!p&&!h&&!d&&!v)return null;var y=bR(l,p,h,d,this.props);if(!y&&!v)return null;var b=ot(this.props,"hidden")?"url(#".concat(f,")"):void 0;return _.createElement(de,{className:ee("recharts-reference-area",c)},t.renderRect(v,Kp(Kp({clipPath:b},J(this.props,!0)),y)),Te.renderCallByParent(this.props,y))}}])}(_.Component);no(io,"displayName","ReferenceArea");no(io,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1});no(io,"renderRect",function(e,t){var r;return _.isValidElement(e)?r=_.cloneElement(e,t):Y(e)?r=e(t):r=_.createElement(os,Yu({},t,{className:"recharts-reference-area-rect"})),r});function gy(e,t,r){if(t<1)return[];if(t===1&&r===void 0)return e;for(var n=[],i=0;i<e.length;i+=t)n.push(e[i]);return n}function xR(e,t,r){var n={width:e.width+t.width,height:e.height+t.height};return dB(n,r)}function wR(e,t,r){var n=r==="width",i=e.x,a=e.y,o=e.width,u=e.height;return t===1?{start:n?i:a,end:n?i+o:a+u}:{start:n?i+o:a+u,end:n?i:a}}function ha(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function OR(e,t){return gy(e,t+1)}function SR(e,t,r,n,i){for(var a=(n||[]).slice(),o=t.start,u=t.end,c=0,s=1,f=o,l=function(){var d=n==null?void 0:n[c];if(d===void 0)return{v:gy(n,s)};var v=c,y,b=function(){return y===void 0&&(y=r(d,v)),y},w=d.coordinate,x=c===0||ha(e,w,b,f,u);x||(c=0,f=o,s+=1),x&&(f=w+e*(b()/2+i),c+=s)},p;s<=a.length;)if(p=l(),p)return p.v;return[]}function Gn(e){"@babel/helpers - typeof";return Gn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Gn(e)}function Xp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function $e(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Xp(Object(r),!0).forEach(function(n){AR(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Xp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function AR(e,t,r){return t=_R(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _R(e){var t=PR(e,"string");return Gn(t)=="symbol"?t:t+""}function PR(e,t){if(Gn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Gn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function $R(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,u=t.start,c=t.end,s=function(p){var h=a[p],d,v=function(){return d===void 0&&(d=r(h,p)),d};if(p===o-1){var y=e*(h.coordinate+e*v()/2-c);a[p]=h=$e($e({},h),{},{tickCoord:y>0?h.coordinate-y*e:h.coordinate})}else a[p]=h=$e($e({},h),{},{tickCoord:h.coordinate});var b=ha(e,h.tickCoord,v,u,c);b&&(c=h.tickCoord-e*(v()/2+i),a[p]=$e($e({},h),{},{isShow:!0}))},f=o-1;f>=0;f--)s(f);return a}function TR(e,t,r,n,i,a){var o=(n||[]).slice(),u=o.length,c=t.start,s=t.end;if(a){var f=n[u-1],l=r(f,u-1),p=e*(f.coordinate+e*l/2-s);o[u-1]=f=$e($e({},f),{},{tickCoord:p>0?f.coordinate-p*e:f.coordinate});var h=ha(e,f.tickCoord,function(){return l},c,s);h&&(s=f.tickCoord-e*(l/2+i),o[u-1]=$e($e({},f),{},{isShow:!0}))}for(var d=a?u-1:u,v=function(w){var x=o[w],S,m=function(){return S===void 0&&(S=r(x,w)),S};if(w===0){var g=e*(x.coordinate-e*m()/2-c);o[w]=x=$e($e({},x),{},{tickCoord:g<0?x.coordinate-g*e:x.coordinate})}else o[w]=x=$e($e({},x),{},{tickCoord:x.coordinate});var O=ha(e,x.tickCoord,m,c,s);O&&(c=x.tickCoord+e*(m()/2+i),o[w]=$e($e({},x),{},{isShow:!0}))},y=0;y<d;y++)v(y);return o}function vs(e,t,r){var n=e.tick,i=e.ticks,a=e.viewBox,o=e.minTickGap,u=e.orientation,c=e.interval,s=e.tickFormatter,f=e.unit,l=e.angle;if(!i||!i.length||!n)return[];if(R(c)||Hr.isSsr)return OR(i,typeof c=="number"&&R(c)?c:0);var p=[],h=u==="top"||u==="bottom"?"width":"height",d=f&&h==="width"?sn(f,{fontSize:t,letterSpacing:r}):{width:0,height:0},v=function(x,S){var m=Y(s)?s(x.value,S):x.value;return h==="width"?xR(sn(m,{fontSize:t,letterSpacing:r}),d,l):sn(m,{fontSize:t,letterSpacing:r})[h]},y=i.length>=2?rt(i[1].coordinate-i[0].coordinate):1,b=wR(a,y,h);return c==="equidistantPreserveStart"?SR(y,b,v,i,o):(c==="preserveStart"||c==="preserveStartEnd"?p=TR(y,b,v,i,o,c==="preserveStartEnd"):p=$R(y,b,v,i,o),p.filter(function(w){return w.isShow}))}var ER=["viewBox"],jR=["viewBox"],MR=["ticks"];function Mr(e){"@babel/helpers - typeof";return Mr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mr(e)}function sr(){return sr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},sr.apply(this,arguments)}function Vp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function je(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Vp(Object(r),!0).forEach(function(n){ys(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Vp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ko(e,t){if(e==null)return{};var r=CR(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function CR(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function IR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Yp(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,xy(n.key),n)}}function DR(e,t,r){return t&&Yp(e.prototype,t),r&&Yp(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function kR(e,t,r){return t=da(t),NR(e,by()?Reflect.construct(t,r||[],da(e).constructor):t.apply(e,r))}function NR(e,t){if(t&&(Mr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return LR(e)}function LR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function by(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(by=function(){return!!e})()}function da(e){return da=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},da(e)}function BR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ju(e,t)}function Ju(e,t){return Ju=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ju(e,t)}function ys(e,t,r){return t=xy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function xy(e){var t=RR(e,"string");return Mr(t)=="symbol"?t:t+""}function RR(e,t){if(Mr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Mr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Yr=function(e){function t(r){var n;return IR(this,t),n=kR(this,t,[r]),n.state={fontSize:"",letterSpacing:""},n}return BR(t,e),DR(t,[{key:"shouldComponentUpdate",value:function(n,i){var a=n.viewBox,o=ko(n,ER),u=this.props,c=u.viewBox,s=ko(u,jR);return!fr(a,c)||!fr(o,s)||!fr(i,this.state)}},{key:"componentDidMount",value:function(){var n=this.layerReference;if(n){var i=n.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];i&&this.setState({fontSize:window.getComputedStyle(i).fontSize,letterSpacing:window.getComputedStyle(i).letterSpacing})}}},{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.x,o=i.y,u=i.width,c=i.height,s=i.orientation,f=i.tickSize,l=i.mirror,p=i.tickMargin,h,d,v,y,b,w,x=l?-1:1,S=n.tickSize||f,m=R(n.tickCoord)?n.tickCoord:n.coordinate;switch(s){case"top":h=d=n.coordinate,y=o+ +!l*c,v=y-x*S,w=v-x*p,b=m;break;case"left":v=y=n.coordinate,d=a+ +!l*u,h=d-x*S,b=h-x*p,w=m;break;case"right":v=y=n.coordinate,d=a+ +l*u,h=d+x*S,b=h+x*p,w=m;break;default:h=d=n.coordinate,y=o+ +l*c,v=y+x*S,w=v+x*p,b=m;break}return{line:{x1:h,y1:v,x2:d,y2:y},tick:{x:b,y:w}}}},{key:"getTickTextAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o;switch(i){case"left":o=a?"start":"end";break;case"right":o=a?"end":"start";break;default:o="middle";break}return o}},{key:"getTickVerticalAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o="end";switch(i){case"left":case"right":o="middle";break;case"top":o=a?"start":"end";break;default:o=a?"end":"start";break}return o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.orientation,s=n.mirror,f=n.axisLine,l=je(je(je({},J(this.props,!1)),J(f,!1)),{},{fill:"none"});if(c==="top"||c==="bottom"){var p=+(c==="top"&&!s||c==="bottom"&&s);l=je(je({},l),{},{x1:i,y1:a+p*u,x2:i+o,y2:a+p*u})}else{var h=+(c==="left"&&!s||c==="right"&&s);l=je(je({},l),{},{x1:i+h*o,y1:a,x2:i+h*o,y2:a+u})}return _.createElement("line",sr({},l,{className:ee("recharts-cartesian-axis-line",He(f,"className"))}))}},{key:"renderTicks",value:function(n,i,a){var o=this,u=this.props,c=u.tickLine,s=u.stroke,f=u.tick,l=u.tickFormatter,p=u.unit,h=vs(je(je({},this.props),{},{ticks:n}),i,a),d=this.getTickTextAnchor(),v=this.getTickVerticalAnchor(),y=J(this.props,!1),b=J(f,!1),w=je(je({},y),{},{fill:"none"},J(c,!1)),x=h.map(function(S,m){var g=o.getTickLineCoord(S),O=g.line,A=g.tick,P=je(je(je(je({textAnchor:d,verticalAnchor:v},y),{},{stroke:"none",fill:s},b),A),{},{index:m,payload:S,visibleTicksCount:h.length,tickFormatter:l});return _.createElement(de,sr({className:"recharts-cartesian-axis-tick",key:"tick-".concat(S.value,"-").concat(S.coordinate,"-").concat(S.tickCoord)},Oi(o.props,S,m)),c&&_.createElement("line",sr({},w,O,{className:ee("recharts-cartesian-axis-tick-line",He(c,"className"))})),f&&t.renderTickItem(f,P,"".concat(Y(l)?l(S.value,m):S.value).concat(p||"")))});return _.createElement("g",{className:"recharts-cartesian-axis-ticks"},x)}},{key:"render",value:function(){var n=this,i=this.props,a=i.axisLine,o=i.width,u=i.height,c=i.ticksGenerator,s=i.className,f=i.hide;if(f)return null;var l=this.props,p=l.ticks,h=ko(l,MR),d=p;return Y(c)&&(d=p&&p.length>0?c(this.props):c(h)),o<=0||u<=0||!d||!d.length?null:_.createElement(de,{className:ee("recharts-cartesian-axis",s),ref:function(y){n.layerReference=y}},a&&this.renderAxisLine(),this.renderTicks(d,this.state.fontSize,this.state.letterSpacing),Te.renderCallByParent(this.props))}}],[{key:"renderTickItem",value:function(n,i,a){var o;return _.isValidElement(n)?o=_.cloneElement(n,i):Y(n)?o=n(i):o=_.createElement(Ni,sr({},i,{className:"recharts-cartesian-axis-tick-value"}),a),o}}])}(B.Component);ys(Yr,"displayName","CartesianAxis");ys(Yr,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var FR=["x1","y1","x2","y2","key"],WR=["offset"];function Jt(e){"@babel/helpers - typeof";return Jt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Jt(e)}function Zp(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ee(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Zp(Object(r),!0).forEach(function(n){zR(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Zp(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function zR(e,t,r){return t=UR(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function UR(e){var t=qR(e,"string");return Jt(t)=="symbol"?t:t+""}function qR(e,t){if(Jt(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Jt(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Ht(){return Ht=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ht.apply(this,arguments)}function Jp(e,t){if(e==null)return{};var r=HR(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function HR(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var GR=function(t){var r=t.fill;if(!r||r==="none")return null;var n=t.fillOpacity,i=t.x,a=t.y,o=t.width,u=t.height,c=t.ry;return _.createElement("rect",{x:i,y:a,ry:c,width:o,height:u,stroke:"none",fill:r,fillOpacity:n,className:"recharts-cartesian-grid-bg"})};function wy(e,t){var r;if(_.isValidElement(e))r=_.cloneElement(e,t);else if(Y(e))r=e(t);else{var n=t.x1,i=t.y1,a=t.x2,o=t.y2,u=t.key,c=Jp(t,FR),s=J(c,!1);s.offset;var f=Jp(s,WR);r=_.createElement("line",Ht({},f,{x1:n,y1:i,x2:a,y2:o,fill:"none",key:u}))}return r}function KR(e){var t=e.x,r=e.width,n=e.horizontal,i=n===void 0?!0:n,a=e.horizontalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,c){var s=Ee(Ee({},e),{},{x1:t,y1:u,x2:t+r,y2:u,key:"line-".concat(c),index:c});return wy(i,s)});return _.createElement("g",{className:"recharts-cartesian-grid-horizontal"},o)}function XR(e){var t=e.y,r=e.height,n=e.vertical,i=n===void 0?!0:n,a=e.verticalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,c){var s=Ee(Ee({},e),{},{x1:u,y1:t,x2:u,y2:t+r,key:"line-".concat(c),index:c});return wy(i,s)});return _.createElement("g",{className:"recharts-cartesian-grid-vertical"},o)}function VR(e){var t=e.horizontalFill,r=e.fillOpacity,n=e.x,i=e.y,a=e.width,o=e.height,u=e.horizontalPoints,c=e.horizontal,s=c===void 0?!0:c;if(!s||!t||!t.length)return null;var f=u.map(function(p){return Math.round(p+i-i)}).sort(function(p,h){return p-h});i!==f[0]&&f.unshift(0);var l=f.map(function(p,h){var d=!f[h+1],v=d?i+o-p:f[h+1]-p;if(v<=0)return null;var y=h%t.length;return _.createElement("rect",{key:"react-".concat(h),y:p,x:n,height:v,width:a,stroke:"none",fill:t[y],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return _.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},l)}function YR(e){var t=e.vertical,r=t===void 0?!0:t,n=e.verticalFill,i=e.fillOpacity,a=e.x,o=e.y,u=e.width,c=e.height,s=e.verticalPoints;if(!r||!n||!n.length)return null;var f=s.map(function(p){return Math.round(p+a-a)}).sort(function(p,h){return p-h});a!==f[0]&&f.unshift(0);var l=f.map(function(p,h){var d=!f[h+1],v=d?a+u-p:f[h+1]-p;if(v<=0)return null;var y=h%n.length;return _.createElement("rect",{key:"react-".concat(h),x:p,y:o,width:v,height:c,stroke:"none",fill:n[y],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return _.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},l)}var ZR=function(t,r){var n=t.xAxis,i=t.width,a=t.height,o=t.offset;return jv(vs(Ee(Ee(Ee({},Yr.defaultProps),n),{},{ticks:ht(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.left,o.left+o.width,r)},JR=function(t,r){var n=t.yAxis,i=t.width,a=t.height,o=t.offset;return jv(vs(Ee(Ee(Ee({},Yr.defaultProps),n),{},{ticks:ht(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.top,o.top+o.height,r)},or={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function QR(e){var t,r,n,i,a,o,u=fs(),c=ps(),s=RB(),f=Ee(Ee({},e),{},{stroke:(t=e.stroke)!==null&&t!==void 0?t:or.stroke,fill:(r=e.fill)!==null&&r!==void 0?r:or.fill,horizontal:(n=e.horizontal)!==null&&n!==void 0?n:or.horizontal,horizontalFill:(i=e.horizontalFill)!==null&&i!==void 0?i:or.horizontalFill,vertical:(a=e.vertical)!==null&&a!==void 0?a:or.vertical,verticalFill:(o=e.verticalFill)!==null&&o!==void 0?o:or.verticalFill,x:R(e.x)?e.x:s.left,y:R(e.y)?e.y:s.top,width:R(e.width)?e.width:s.width,height:R(e.height)?e.height:s.height}),l=f.x,p=f.y,h=f.width,d=f.height,v=f.syncWithTicks,y=f.horizontalValues,b=f.verticalValues,w=NB(),x=LB();if(!R(h)||h<=0||!R(d)||d<=0||!R(l)||l!==+l||!R(p)||p!==+p)return null;var S=f.verticalCoordinatesGenerator||ZR,m=f.horizontalCoordinatesGenerator||JR,g=f.horizontalPoints,O=f.verticalPoints;if((!g||!g.length)&&Y(m)){var A=y&&y.length,P=m({yAxis:x?Ee(Ee({},x),{},{ticks:A?y:x.ticks}):void 0,width:u,height:c,offset:s},A?!0:v);vt(Array.isArray(P),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(Jt(P),"]")),Array.isArray(P)&&(g=P)}if((!O||!O.length)&&Y(S)){var j=b&&b.length,$=S({xAxis:w?Ee(Ee({},w),{},{ticks:j?b:w.ticks}):void 0,width:u,height:c,offset:s},j?!0:v);vt(Array.isArray($),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(Jt($),"]")),Array.isArray($)&&(O=$)}return _.createElement("g",{className:"recharts-cartesian-grid"},_.createElement(GR,{fill:f.fill,fillOpacity:f.fillOpacity,x:f.x,y:f.y,width:f.width,height:f.height,ry:f.ry}),_.createElement(KR,Ht({},f,{offset:s,horizontalPoints:g,xAxis:w,yAxis:x})),_.createElement(XR,Ht({},f,{offset:s,verticalPoints:O,xAxis:w,yAxis:x})),_.createElement(VR,Ht({},f,{horizontalPoints:g})),_.createElement(YR,Ht({},f,{verticalPoints:O})))}QR.displayName="CartesianGrid";var eF=["type","layout","connectNulls","ref"],tF=["key"];function Cr(e){"@babel/helpers - typeof";return Cr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Cr(e)}function Qp(e,t){if(e==null)return{};var r=rF(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function rF(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function hn(){return hn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},hn.apply(this,arguments)}function eh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Le(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?eh(Object(r),!0).forEach(function(n){Qe(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ur(e){return oF(e)||aF(e)||iF(e)||nF()}function nF(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function iF(e,t){if(e){if(typeof e=="string")return Qu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Qu(e,t)}}function aF(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function oF(e){if(Array.isArray(e))return Qu(e)}function Qu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function uF(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function th(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Sy(n.key),n)}}function cF(e,t,r){return t&&th(e.prototype,t),r&&th(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function sF(e,t,r){return t=va(t),lF(e,Oy()?Reflect.construct(t,r||[],va(e).constructor):t.apply(e,r))}function lF(e,t){if(t&&(Cr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return fF(e)}function fF(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Oy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Oy=function(){return!!e})()}function va(e){return va=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},va(e)}function pF(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ec(e,t)}function ec(e,t){return ec=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},ec(e,t)}function Qe(e,t,r){return t=Sy(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Sy(e){var t=hF(e,"string");return Cr(t)=="symbol"?t:t+""}function hF(e,t){if(Cr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Cr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var ao=function(e){function t(){var r;uF(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=sF(this,t,[].concat(i)),Qe(r,"state",{isAnimationFinished:!0,totalLength:0}),Qe(r,"generateSimpleStrokeDasharray",function(o,u){return"".concat(u,"px ").concat(o-u,"px")}),Qe(r,"getStrokeDasharray",function(o,u,c){var s=c.reduce(function(b,w){return b+w});if(!s)return r.generateSimpleStrokeDasharray(u,o);for(var f=Math.floor(o/s),l=o%s,p=u-o,h=[],d=0,v=0;d<c.length;v+=c[d],++d)if(v+c[d]>l){h=[].concat(ur(c.slice(0,d)),[l-v]);break}var y=h.length%2===0?[0,p]:[p];return[].concat(ur(t.repeat(c,f)),ur(h),y).map(function(b){return"".concat(b,"px")}).join(", ")}),Qe(r,"id",Zn("recharts-line-")),Qe(r,"pathRef",function(o){r.mainCurve=o}),Qe(r,"handleAnimationEnd",function(){r.setState({isAnimationFinished:!0}),r.props.onAnimationEnd&&r.props.onAnimationEnd()}),Qe(r,"handleAnimationStart",function(){r.setState({isAnimationFinished:!1}),r.props.onAnimationStart&&r.props.onAnimationStart()}),r}return pF(t,e),cF(t,[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var n=this.getTotalLength();this.setState({totalLength:n})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var n=this.getTotalLength();n!==this.state.totalLength&&this.setState({totalLength:n})}}},{key:"getTotalLength",value:function(){var n=this.mainCurve;try{return n&&n.getTotalLength&&n.getTotalLength()||0}catch{return 0}}},{key:"renderErrorBar",value:function(n,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.points,u=a.xAxis,c=a.yAxis,s=a.layout,f=a.children,l=Ge(f,ni);if(!l)return null;var p=function(v,y){return{x:v.x,y:v.y,value:v.value,errorVal:We(v.payload,y)}},h={clipPath:n?"url(#clipPath-".concat(i,")"):null};return _.createElement(de,h,l.map(function(d){return _.cloneElement(d,{key:"bar-".concat(d.props.dataKey),data:o,xAxis:u,yAxis:c,layout:s,dataPointFormatter:p})}))}},{key:"renderDots",value:function(n,i,a){var o=this.props.isAnimationActive;if(o&&!this.state.isAnimationFinished)return null;var u=this.props,c=u.dot,s=u.points,f=u.dataKey,l=J(this.props,!1),p=J(c,!0),h=s.map(function(v,y){var b=Le(Le(Le({key:"dot-".concat(y),r:3},l),p),{},{index:y,cx:v.x,cy:v.y,value:v.value,dataKey:f,payload:v.payload,points:s});return t.renderDotItem(c,b)}),d={clipPath:n?"url(#clipPath-".concat(i?"":"dots-").concat(a,")"):null};return _.createElement(de,hn({className:"recharts-line-dots",key:"dots"},d),h)}},{key:"renderCurveStatically",value:function(n,i,a,o){var u=this.props,c=u.type,s=u.layout,f=u.connectNulls;u.ref;var l=Qp(u,eF),p=Le(Le(Le({},J(l,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:i?"url(#clipPath-".concat(a,")"):null,points:n},o),{},{type:c,layout:s,connectNulls:f});return _.createElement(Mu,hn({},p,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(n,i){var a=this,o=this.props,u=o.points,c=o.strokeDasharray,s=o.isAnimationActive,f=o.animationBegin,l=o.animationDuration,p=o.animationEasing,h=o.animationId,d=o.animateNewValues,v=o.width,y=o.height,b=this.state,w=b.prevPoints,x=b.totalLength;return _.createElement(xt,{begin:f,duration:l,isActive:s,easing:p,from:{t:0},to:{t:1},key:"line-".concat(h),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(S){var m=S.t;if(w){var g=w.length/u.length,O=u.map(function(T,I){var C=Math.floor(I*g);if(w[C]){var M=w[C],D=et(M.x,T.x),k=et(M.y,T.y);return Le(Le({},T),{},{x:D(m),y:k(m)})}if(d){var L=et(v*2,T.x),F=et(y/2,T.y);return Le(Le({},T),{},{x:L(m),y:F(m)})}return Le(Le({},T),{},{x:T.x,y:T.y})});return a.renderCurveStatically(O,n,i)}var A=et(0,x),P=A(m),j;if(c){var $="".concat(c).split(/[,\s]+/gim).map(function(T){return parseFloat(T)});j=a.getStrokeDasharray(P,x,$)}else j=a.generateSimpleStrokeDasharray(x,P);return a.renderCurveStatically(u,n,i,{strokeDasharray:j})})}},{key:"renderCurve",value:function(n,i){var a=this.props,o=a.points,u=a.isAnimationActive,c=this.state,s=c.prevPoints,f=c.totalLength;return u&&o&&o.length&&(!s&&f>0||!Va(s,o))?this.renderCurveWithAnimation(n,i):this.renderCurveStatically(o,n,i)}},{key:"render",value:function(){var n,i=this.props,a=i.hide,o=i.dot,u=i.points,c=i.className,s=i.xAxis,f=i.yAxis,l=i.top,p=i.left,h=i.width,d=i.height,v=i.isAnimationActive,y=i.id;if(a||!u||!u.length)return null;var b=this.state.isAnimationFinished,w=u.length===1,x=ee("recharts-line",c),S=s&&s.allowDataOverflow,m=f&&f.allowDataOverflow,g=S||m,O=Z(y)?this.id:y,A=(n=J(o,!1))!==null&&n!==void 0?n:{r:3,strokeWidth:2},P=A.r,j=P===void 0?3:P,$=A.strokeWidth,T=$===void 0?2:$,I=P0(o)?o:{},C=I.clipDot,M=C===void 0?!0:C,D=j*2+T;return _.createElement(de,{className:x},S||m?_.createElement("defs",null,_.createElement("clipPath",{id:"clipPath-".concat(O)},_.createElement("rect",{x:S?p:p-h/2,y:m?l:l-d/2,width:S?h:h*2,height:m?d:d*2})),!M&&_.createElement("clipPath",{id:"clipPath-dots-".concat(O)},_.createElement("rect",{x:p-D/2,y:l-D/2,width:h+D,height:d+D}))):null,!w&&this.renderCurve(g,O),this.renderErrorBar(g,O),(w||o)&&this.renderDots(g,M,O),(!v||b)&&Et.renderCallByParent(this.props,u))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curPoints:n.points,prevPoints:i.curPoints}:n.points!==i.curPoints?{curPoints:n.points}:null}},{key:"repeat",value:function(n,i){for(var a=n.length%2!==0?[].concat(ur(n),[0]):n,o=[],u=0;u<i;++u)o=[].concat(ur(o),ur(a));return o}},{key:"renderDotItem",value:function(n,i){var a;if(_.isValidElement(n))a=_.cloneElement(n,i);else if(Y(n))a=n(i);else{var o=i.key,u=Qp(i,tF),c=ee("recharts-line-dot",typeof n!="boolean"?n.className:"");a=_.createElement(us,hn({key:o},u,{className:c}))}return a}}])}(B.PureComponent);Qe(ao,"displayName","Line");Qe(ao,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!Hr.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1});Qe(ao,"getComposedData",function(e){var t=e.props,r=e.xAxis,n=e.yAxis,i=e.xAxisTicks,a=e.yAxisTicks,o=e.dataKey,u=e.bandSize,c=e.displayedData,s=e.offset,f=t.layout,l=c.map(function(p,h){var d=We(p,o);return f==="horizontal"?{x:Nf({axis:r,ticks:i,bandSize:u,entry:p,index:h}),y:Z(d)?null:n.scale(d),value:d,payload:p}:{x:Z(d)?null:r.scale(d),y:Nf({axis:n,ticks:a,bandSize:u,entry:p,index:h}),value:d,payload:p}});return Le({points:l,layout:f},s)});function Ir(e){"@babel/helpers - typeof";return Ir=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ir(e)}function dF(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function vF(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Py(n.key),n)}}function yF(e,t,r){return t&&vF(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function mF(e,t,r){return t=ya(t),gF(e,Ay()?Reflect.construct(t,r||[],ya(e).constructor):t.apply(e,r))}function gF(e,t){if(t&&(Ir(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return bF(e)}function bF(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ay(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Ay=function(){return!!e})()}function ya(e){return ya=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ya(e)}function xF(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&tc(e,t)}function tc(e,t){return tc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},tc(e,t)}function _y(e,t,r){return t=Py(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Py(e){var t=wF(e,"string");return Ir(t)=="symbol"?t:t+""}function wF(e,t){if(Ir(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ir(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function rc(){return rc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},rc.apply(this,arguments)}function OF(e){var t=e.xAxisId,r=fs(),n=ps(),i=ly(t);return i==null?null:_.createElement(Yr,rc({},i,{className:ee("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(o){return ht(o,!0)}}))}var oo=function(e){function t(){return dF(this,t),mF(this,t,arguments)}return xF(t,e),yF(t,[{key:"render",value:function(){return _.createElement(OF,this.props)}}])}(_.Component);_y(oo,"displayName","XAxis");_y(oo,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function Dr(e){"@babel/helpers - typeof";return Dr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dr(e)}function SF(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function AF(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Ey(n.key),n)}}function _F(e,t,r){return t&&AF(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function PF(e,t,r){return t=ma(t),$F(e,$y()?Reflect.construct(t,r||[],ma(e).constructor):t.apply(e,r))}function $F(e,t){if(t&&(Dr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return TF(e)}function TF(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function $y(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return($y=function(){return!!e})()}function ma(e){return ma=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ma(e)}function EF(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&nc(e,t)}function nc(e,t){return nc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},nc(e,t)}function Ty(e,t,r){return t=Ey(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ey(e){var t=jF(e,"string");return Dr(t)=="symbol"?t:t+""}function jF(e,t){if(Dr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Dr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function ic(){return ic=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ic.apply(this,arguments)}var MF=function(t){var r=t.yAxisId,n=fs(),i=ps(),a=fy(r);return a==null?null:_.createElement(Yr,ic({},a,{className:ee("recharts-".concat(a.axisType," ").concat(a.axisType),a.className),viewBox:{x:0,y:0,width:n,height:i},ticksGenerator:function(u){return ht(u,!0)}}))},uo=function(e){function t(){return SF(this,t),PF(this,t,arguments)}return EF(t,e),_F(t,[{key:"render",value:function(){return _.createElement(MF,this.props)}}])}(_.Component);Ty(uo,"displayName","YAxis");Ty(uo,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});function rh(e){return kF(e)||DF(e)||IF(e)||CF()}function CF(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function IF(e,t){if(e){if(typeof e=="string")return ac(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ac(e,t)}}function DF(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function kF(e){if(Array.isArray(e))return ac(e)}function ac(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var oc=function(t,r,n,i,a){var o=Ge(t,ds),u=Ge(t,ro),c=[].concat(rh(o),rh(u)),s=Ge(t,io),f="".concat(i,"Id"),l=i[0],p=r;if(c.length&&(p=c.reduce(function(v,y){if(y.props[f]===n&&ot(y.props,"extendDomain")&&R(y.props[l])){var b=y.props[l];return[Math.min(v[0],b),Math.max(v[1],b)]}return v},p)),s.length){var h="".concat(l,"1"),d="".concat(l,"2");p=s.reduce(function(v,y){if(y.props[f]===n&&ot(y.props,"extendDomain")&&R(y.props[h])&&R(y.props[d])){var b=y.props[h],w=y.props[d];return[Math.min(v[0],b,w),Math.max(v[1],b,w)]}return v},p)}return a&&a.length&&(p=a.reduce(function(v,y){return R(y)?[Math.min(v[0],y),Math.max(v[1],y)]:v},p)),p},jy={exports:{}};(function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1));function i(c,s,f){this.fn=c,this.context=s,this.once=f||!1}function a(c,s,f,l,p){if(typeof f!="function")throw new TypeError("The listener must be a function");var h=new i(f,l||c,p),d=r?r+s:s;return c._events[d]?c._events[d].fn?c._events[d]=[c._events[d],h]:c._events[d].push(h):(c._events[d]=h,c._eventsCount++),c}function o(c,s){--c._eventsCount===0?c._events=new n:delete c._events[s]}function u(){this._events=new n,this._eventsCount=0}u.prototype.eventNames=function(){var s=[],f,l;if(this._eventsCount===0)return s;for(l in f=this._events)t.call(f,l)&&s.push(r?l.slice(1):l);return Object.getOwnPropertySymbols?s.concat(Object.getOwnPropertySymbols(f)):s},u.prototype.listeners=function(s){var f=r?r+s:s,l=this._events[f];if(!l)return[];if(l.fn)return[l.fn];for(var p=0,h=l.length,d=new Array(h);p<h;p++)d[p]=l[p].fn;return d},u.prototype.listenerCount=function(s){var f=r?r+s:s,l=this._events[f];return l?l.fn?1:l.length:0},u.prototype.emit=function(s,f,l,p,h,d){var v=r?r+s:s;if(!this._events[v])return!1;var y=this._events[v],b=arguments.length,w,x;if(y.fn){switch(y.once&&this.removeListener(s,y.fn,void 0,!0),b){case 1:return y.fn.call(y.context),!0;case 2:return y.fn.call(y.context,f),!0;case 3:return y.fn.call(y.context,f,l),!0;case 4:return y.fn.call(y.context,f,l,p),!0;case 5:return y.fn.call(y.context,f,l,p,h),!0;case 6:return y.fn.call(y.context,f,l,p,h,d),!0}for(x=1,w=new Array(b-1);x<b;x++)w[x-1]=arguments[x];y.fn.apply(y.context,w)}else{var S=y.length,m;for(x=0;x<S;x++)switch(y[x].once&&this.removeListener(s,y[x].fn,void 0,!0),b){case 1:y[x].fn.call(y[x].context);break;case 2:y[x].fn.call(y[x].context,f);break;case 3:y[x].fn.call(y[x].context,f,l);break;case 4:y[x].fn.call(y[x].context,f,l,p);break;default:if(!w)for(m=1,w=new Array(b-1);m<b;m++)w[m-1]=arguments[m];y[x].fn.apply(y[x].context,w)}}return!0},u.prototype.on=function(s,f,l){return a(this,s,f,l,!1)},u.prototype.once=function(s,f,l){return a(this,s,f,l,!0)},u.prototype.removeListener=function(s,f,l,p){var h=r?r+s:s;if(!this._events[h])return this;if(!f)return o(this,h),this;var d=this._events[h];if(d.fn)d.fn===f&&(!p||d.once)&&(!l||d.context===l)&&o(this,h);else{for(var v=0,y=[],b=d.length;v<b;v++)(d[v].fn!==f||p&&!d[v].once||l&&d[v].context!==l)&&y.push(d[v]);y.length?this._events[h]=y.length===1?y[0]:y:o(this,h)}return this},u.prototype.removeAllListeners=function(s){var f;return s?(f=r?r+s:s,this._events[f]&&o(this,f)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,e.exports=u})(jy);var NF=jy.exports;const LF=ue(NF);var No=new LF,Lo="recharts.syncMouseEvents";function Kn(e){"@babel/helpers - typeof";return Kn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Kn(e)}function BF(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function RF(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,My(n.key),n)}}function FF(e,t,r){return t&&RF(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Bo(e,t,r){return t=My(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function My(e){var t=WF(e,"string");return Kn(t)=="symbol"?t:t+""}function WF(e,t){if(Kn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Kn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var zF=function(){function e(){BF(this,e),Bo(this,"activeIndex",0),Bo(this,"coordinateList",[]),Bo(this,"layout","horizontal")}return FF(e,[{key:"setDetails",value:function(r){var n,i=r.coordinateList,a=i===void 0?null:i,o=r.container,u=o===void 0?null:o,c=r.layout,s=c===void 0?null:c,f=r.offset,l=f===void 0?null:f,p=r.mouseHandlerCallback,h=p===void 0?null:p;this.coordinateList=(n=a??this.coordinateList)!==null&&n!==void 0?n:[],this.container=u??this.container,this.layout=s??this.layout,this.offset=l??this.offset,this.mouseHandlerCallback=h??this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(r){if(this.coordinateList.length!==0)switch(r.key){case"ArrowRight":{if(this.layout!=="horizontal")return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break}case"ArrowLeft":{if(this.layout!=="horizontal")return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse();break}}}},{key:"setIndex",value:function(r){this.activeIndex=r}},{key:"spoofMouse",value:function(){var r,n;if(this.layout==="horizontal"&&this.coordinateList.length!==0){var i=this.container.getBoundingClientRect(),a=i.x,o=i.y,u=i.height,c=this.coordinateList[this.activeIndex].coordinate,s=((r=window)===null||r===void 0?void 0:r.scrollX)||0,f=((n=window)===null||n===void 0?void 0:n.scrollY)||0,l=a+c+s,p=o+this.offset.top+u/2+f;this.mouseHandlerCallback({pageX:l,pageY:p})}}}])}();function UF(e,t,r){if(r==="number"&&t===!0&&Array.isArray(e)){var n=e==null?void 0:e[0],i=e==null?void 0:e[1];if(n&&i&&R(n)&&R(i))return!0}return!1}function qF(e,t,r,n){var i=n/2;return{stroke:"none",fill:"#ccc",x:e==="horizontal"?t.x-i:r.left+.5,y:e==="horizontal"?r.top+.5:t.y-i,width:e==="horizontal"?n:r.width-1,height:e==="horizontal"?r.height-1:n}}function Cy(e){var t=e.cx,r=e.cy,n=e.radius,i=e.startAngle,a=e.endAngle,o=Pe(t,r,n,i),u=Pe(t,r,n,a);return{points:[o,u],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function HF(e,t,r){var n,i,a,o;if(e==="horizontal")n=t.x,a=n,i=r.top,o=r.top+r.height;else if(e==="vertical")i=t.y,o=i,n=r.left,a=r.left+r.width;else if(t.cx!=null&&t.cy!=null)if(e==="centric"){var u=t.cx,c=t.cy,s=t.innerRadius,f=t.outerRadius,l=t.angle,p=Pe(u,c,s,l),h=Pe(u,c,f,l);n=p.x,i=p.y,a=h.x,o=h.y}else return Cy(t);return[{x:n,y:i},{x:a,y:o}]}function Xn(e){"@babel/helpers - typeof";return Xn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Xn(e)}function nh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function gi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?nh(Object(r),!0).forEach(function(n){GF(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function GF(e,t,r){return t=KF(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function KF(e){var t=XF(e,"string");return Xn(t)=="symbol"?t:t+""}function XF(e,t){if(Xn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Xn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function VF(e){var t,r,n=e.element,i=e.tooltipEventType,a=e.isActive,o=e.activeCoordinate,u=e.activePayload,c=e.offset,s=e.activeTooltipIndex,f=e.tooltipAxisBandSize,l=e.layout,p=e.chartName,h=(t=n.props.cursor)!==null&&t!==void 0?t:(r=n.type.defaultProps)===null||r===void 0?void 0:r.cursor;if(!n||!h||!a||!o||p!=="ScatterChart"&&i!=="axis")return null;var d,v=Mu;if(p==="ScatterChart")d=o,v=Kk;else if(p==="BarChart")d=qF(l,o,c,f),v=os;else if(l==="radial"){var y=Cy(o),b=y.cx,w=y.cy,x=y.radius,S=y.startAngle,m=y.endAngle;d={cx:b,cy:w,startAngle:S,endAngle:m,innerRadius:x,outerRadius:x},v=kv}else d={points:HF(l,o,c)},v=Mu;var g=gi(gi(gi(gi({stroke:"#ccc",pointerEvents:"none"},c),d),J(h,!1)),{},{payload:u,payloadIndex:s,className:ee("recharts-tooltip-cursor",h.className)});return B.isValidElement(h)?B.cloneElement(h,g):B.createElement(v,g)}var YF=["item"],ZF=["children","className","width","height","style","compact","title","desc"];function kr(e){"@babel/helpers - typeof";return kr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kr(e)}function lr(){return lr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},lr.apply(this,arguments)}function ih(e,t){return e3(e)||QF(e,t)||Dy(e,t)||JF()}function JF(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function QF(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function e3(e){if(Array.isArray(e))return e}function ah(e,t){if(e==null)return{};var r=t3(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function t3(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function r3(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n3(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ky(n.key),n)}}function i3(e,t,r){return t&&n3(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function a3(e,t,r){return t=ga(t),o3(e,Iy()?Reflect.construct(t,r||[],ga(e).constructor):t.apply(e,r))}function o3(e,t){if(t&&(kr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return u3(e)}function u3(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Iy(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Iy=function(){return!!e})()}function ga(e){return ga=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ga(e)}function c3(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&uc(e,t)}function uc(e,t){return uc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},uc(e,t)}function Nr(e){return f3(e)||l3(e)||Dy(e)||s3()}function s3(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Dy(e,t){if(e){if(typeof e=="string")return cc(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return cc(e,t)}}function l3(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function f3(e){if(Array.isArray(e))return cc(e)}function cc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function oh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function E(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?oh(Object(r),!0).forEach(function(n){H(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):oh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function H(e,t,r){return t=ky(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ky(e){var t=p3(e,"string");return kr(t)=="symbol"?t:t+""}function p3(e,t){if(kr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(kr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var h3={xAxis:["bottom","top"],yAxis:["left","right"]},d3={width:"100%",height:"100%"},Ny={x:0,y:0};function bi(e){return e}var v3=function(t,r){return r==="horizontal"?t.x:r==="vertical"?t.y:r==="centric"?t.angle:t.radius},y3=function(t,r,n,i){var a=r.find(function(f){return f&&f.index===n});if(a){if(t==="horizontal")return{x:a.coordinate,y:i.y};if(t==="vertical")return{x:i.x,y:a.coordinate};if(t==="centric"){var o=a.coordinate,u=i.radius;return E(E(E({},i),Pe(i.cx,i.cy,u,o)),{},{angle:o,radius:u})}var c=a.coordinate,s=i.angle;return E(E(E({},i),Pe(i.cx,i.cy,c,s)),{},{angle:s,radius:c})}return Ny},co=function(t,r){var n=r.graphicalItems,i=r.dataStartIndex,a=r.dataEndIndex,o=(n??[]).reduce(function(u,c){var s=c.props.data;return s&&s.length?[].concat(Nr(u),Nr(s)):u},[]);return o.length>0?o:t&&t.length&&R(i)&&R(a)?t.slice(i,a+1):[]};function Ly(e){return e==="number"?[0,"auto"]:void 0}var sc=function(t,r,n,i){var a=t.graphicalItems,o=t.tooltipAxis,u=co(r,t);return n<0||!a||!a.length||n>=u.length?null:a.reduce(function(c,s){var f,l=(f=s.props.data)!==null&&f!==void 0?f:r;l&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=n&&(l=l.slice(t.dataStartIndex,t.dataEndIndex+1));var p;if(o.dataKey&&!o.allowDuplicatedCategory){var h=l===void 0?u:l;p=xi(h,o.dataKey,i)}else p=l&&l[n]||u[n];return p?[].concat(Nr(c),[Cv(s,p)]):c},[])},uh=function(t,r,n,i){var a=i||{x:t.chartX,y:t.chartY},o=v3(a,n),u=t.orderedTooltipTicks,c=t.tooltipAxis,s=t.tooltipTicks,f=EI(o,u,s,c);if(f>=0&&s){var l=s[f]&&s[f].value,p=sc(t,r,f,l),h=y3(n,u,f,a);return{activeTooltipIndex:f,activeLabel:l,activePayload:p,activeCoordinate:h}}return null},m3=function(t,r){var n=r.axes,i=r.graphicalItems,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.layout,l=t.children,p=t.stackOffset,h=Ev(f,a);return n.reduce(function(d,v){var y,b=v.type.defaultProps!==void 0?E(E({},v.type.defaultProps),v.props):v.props,w=b.type,x=b.dataKey,S=b.allowDataOverflow,m=b.allowDuplicatedCategory,g=b.scale,O=b.ticks,A=b.includeHidden,P=b[o];if(d[P])return d;var j=co(t.data,{graphicalItems:i.filter(function(z){var K,ce=o in z.props?z.props[o]:(K=z.type.defaultProps)===null||K===void 0?void 0:K[o];return ce===P}),dataStartIndex:c,dataEndIndex:s}),$=j.length,T,I,C;UF(b.domain,S,w)&&(T=Pu(b.domain,null,S),h&&(w==="number"||g!=="auto")&&(C=fn(j,x,"category")));var M=Ly(w);if(!T||T.length===0){var D,k=(D=b.domain)!==null&&D!==void 0?D:M;if(x){if(T=fn(j,x,w),w==="category"&&h){var L=y0(T);m&&L?(I=T,T=aa(0,$)):m||(T=Ff(k,T,v).reduce(function(z,K){return z.indexOf(K)>=0?z:[].concat(Nr(z),[K])},[]))}else if(w==="category")m?T=T.filter(function(z){return z!==""&&!Z(z)}):T=Ff(k,T,v).reduce(function(z,K){return z.indexOf(K)>=0||K===""||Z(K)?z:[].concat(Nr(z),[K])},[]);else if(w==="number"){var F=DI(j,i.filter(function(z){var K,ce,ve=o in z.props?z.props[o]:(K=z.type.defaultProps)===null||K===void 0?void 0:K[o],Ne="hide"in z.props?z.props.hide:(ce=z.type.defaultProps)===null||ce===void 0?void 0:ce.hide;return ve===P&&(A||!Ne)}),x,a,f);F&&(T=F)}h&&(w==="number"||g!=="auto")&&(C=fn(j,x,"category"))}else h?T=aa(0,$):u&&u[P]&&u[P].hasStack&&w==="number"?T=p==="expand"?[0,1]:Mv(u[P].stackGroups,c,s):T=Tv(j,i.filter(function(z){var K=o in z.props?z.props[o]:z.type.defaultProps[o],ce="hide"in z.props?z.props.hide:z.type.defaultProps.hide;return K===P&&(A||!ce)}),w,f,!0);if(w==="number")T=oc(l,T,P,a,O),k&&(T=Pu(k,T,S));else if(w==="category"&&k){var q=k,G=T.every(function(z){return q.indexOf(z)>=0});G&&(T=q)}}return E(E({},d),{},H({},P,E(E({},b),{},{axisType:a,domain:T,categoricalDomain:C,duplicateDomain:I,originalDomain:(y=b.domain)!==null&&y!==void 0?y:M,isCategorical:h,layout:f})))},{})},g3=function(t,r){var n=r.graphicalItems,i=r.Axis,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.layout,l=t.children,p=co(t.data,{graphicalItems:n,dataStartIndex:c,dataEndIndex:s}),h=p.length,d=Ev(f,a),v=-1;return n.reduce(function(y,b){var w=b.type.defaultProps!==void 0?E(E({},b.type.defaultProps),b.props):b.props,x=w[o],S=Ly("number");if(!y[x]){v++;var m;return d?m=aa(0,h):u&&u[x]&&u[x].hasStack?(m=Mv(u[x].stackGroups,c,s),m=oc(l,m,x,a)):(m=Pu(S,Tv(p,n.filter(function(g){var O,A,P=o in g.props?g.props[o]:(O=g.type.defaultProps)===null||O===void 0?void 0:O[o],j="hide"in g.props?g.props.hide:(A=g.type.defaultProps)===null||A===void 0?void 0:A.hide;return P===x&&!j}),"number",f),i.defaultProps.allowDataOverflow),m=oc(l,m,x,a)),E(E({},y),{},H({},x,E(E({axisType:a},i.defaultProps),{},{hide:!0,orientation:He(h3,"".concat(a,".").concat(v%2),null),domain:m,originalDomain:S,isCategorical:d,layout:f})))}return y},{})},b3=function(t,r){var n=r.axisType,i=n===void 0?"xAxis":n,a=r.AxisComp,o=r.graphicalItems,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.children,l="".concat(i,"Id"),p=Ge(f,a),h={};return p&&p.length?h=m3(t,{axes:p,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:u,dataStartIndex:c,dataEndIndex:s}):o&&o.length&&(h=g3(t,{Axis:a,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:u,dataStartIndex:c,dataEndIndex:s})),h},x3=function(t){var r=Pt(t),n=ht(r,!1,!0);return{tooltipTicks:n,orderedTooltipTicks:Ic(n,function(i){return i.coordinate}),tooltipAxis:r,tooltipAxisBandSize:Yi(r,n)}},ch=function(t){var r=t.children,n=t.defaultShowTooltip,i=Re(r,Pr),a=0,o=0;return t.data&&t.data.length!==0&&(o=t.data.length-1),i&&i.props&&(i.props.startIndex>=0&&(a=i.props.startIndex),i.props.endIndex>=0&&(o=i.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:a,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!n}},w3=function(t){return!t||!t.length?!1:t.some(function(r){var n=dt(r&&r.type);return n&&n.indexOf("Bar")>=0})},sh=function(t){return t==="horizontal"?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:t==="vertical"?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:t==="centric"?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},O3=function(t,r){var n=t.props,i=t.graphicalItems,a=t.xAxisMap,o=a===void 0?{}:a,u=t.yAxisMap,c=u===void 0?{}:u,s=n.width,f=n.height,l=n.children,p=n.margin||{},h=Re(l,Pr),d=Re(l,pr),v=Object.keys(c).reduce(function(m,g){var O=c[g],A=O.orientation;return!O.mirror&&!O.hide?E(E({},m),{},H({},A,m[A]+O.width)):m},{left:p.left||0,right:p.right||0}),y=Object.keys(o).reduce(function(m,g){var O=o[g],A=O.orientation;return!O.mirror&&!O.hide?E(E({},m),{},H({},A,He(m,"".concat(A))+O.height)):m},{top:p.top||0,bottom:p.bottom||0}),b=E(E({},y),v),w=b.bottom;h&&(b.bottom+=h.props.height||Pr.defaultProps.height),d&&r&&(b=CI(b,i,n,r));var x=s-b.left-b.right,S=f-b.top-b.bottom;return E(E({brushBottom:w},b),{},{width:Math.max(x,0),height:Math.max(S,0)})},S3=function(t,r){if(r==="xAxis")return t[r].width;if(r==="yAxis")return t[r].height},By=function(t){var r=t.chartName,n=t.GraphicalChild,i=t.defaultTooltipEventType,a=i===void 0?"axis":i,o=t.validateTooltipEventTypes,u=o===void 0?["axis"]:o,c=t.axisComponents,s=t.legendContent,f=t.formatAxisMap,l=t.defaultProps,p=function(b,w){var x=w.graphicalItems,S=w.stackGroups,m=w.offset,g=w.updateId,O=w.dataStartIndex,A=w.dataEndIndex,P=b.barSize,j=b.layout,$=b.barGap,T=b.barCategoryGap,I=b.maxBarSize,C=sh(j),M=C.numericAxisName,D=C.cateAxisName,k=w3(x),L=[];return x.forEach(function(F,q){var G=co(b.data,{graphicalItems:[F],dataStartIndex:O,dataEndIndex:A}),z=F.type.defaultProps!==void 0?E(E({},F.type.defaultProps),F.props):F.props,K=z.dataKey,ce=z.maxBarSize,ve=z["".concat(M,"Id")],Ne=z["".concat(D,"Id")],kt={},Ce=c.reduce(function(Nt,Lt){var so=w["".concat(Lt.axisType,"Map")],ms=z["".concat(Lt.axisType,"Id")];so&&so[ms]||Lt.axisType==="zAxis"||Zt();var gs=so[ms];return E(E({},Nt),{},H(H({},Lt.axisType,gs),"".concat(Lt.axisType,"Ticks"),ht(gs)))},kt),W=Ce[D],X=Ce["".concat(D,"Ticks")],V=S&&S[ve]&&S[ve].hasStack&&GI(F,S[ve].stackGroups),N=dt(F.type).indexOf("Bar")>=0,pe=Yi(W,X),Q=[],ge=k&&jI({barSize:P,stackGroups:S,totalSize:S3(Ce,D)});if(N){var be,Ie,At=Z(ce)?I:ce,nr=(be=(Ie=Yi(W,X,!0))!==null&&Ie!==void 0?Ie:At)!==null&&be!==void 0?be:0;Q=MI({barGap:$,barCategoryGap:T,bandSize:nr!==pe?nr:pe,sizeList:ge[Ne],maxBarSize:At}),nr!==pe&&(Q=Q.map(function(Nt){return E(E({},Nt),{},{position:E(E({},Nt.position),{},{offset:Nt.position.offset-nr/2})})}))}var ii=F&&F.type&&F.type.getComposedData;ii&&L.push({props:E(E({},ii(E(E({},Ce),{},{displayedData:G,props:b,dataKey:K,item:F,bandSize:pe,barPosition:Q,offset:m,stackedData:V,layout:j,dataStartIndex:O,dataEndIndex:A}))),{},H(H(H({key:F.key||"item-".concat(q)},M,Ce[M]),D,Ce[D]),"animationId",g)),childIndex:E0(F,b.children),item:F})}),L},h=function(b,w){var x=b.props,S=b.dataStartIndex,m=b.dataEndIndex,g=b.updateId;if(!Cs({props:x}))return null;var O=x.children,A=x.layout,P=x.stackOffset,j=x.data,$=x.reverseStackOrder,T=sh(A),I=T.numericAxisName,C=T.cateAxisName,M=Ge(O,n),D=UI(j,M,"".concat(I,"Id"),"".concat(C,"Id"),P,$),k=c.reduce(function(z,K){var ce="".concat(K.axisType,"Map");return E(E({},z),{},H({},ce,b3(x,E(E({},K),{},{graphicalItems:M,stackGroups:K.axisType===I&&D,dataStartIndex:S,dataEndIndex:m}))))},{}),L=O3(E(E({},k),{},{props:x,graphicalItems:M}),w==null?void 0:w.legendBBox);Object.keys(k).forEach(function(z){k[z]=f(x,k[z],L,z.replace("Map",""),r)});var F=k["".concat(C,"Map")],q=x3(F),G=p(x,E(E({},k),{},{dataStartIndex:S,dataEndIndex:m,updateId:g,graphicalItems:M,stackGroups:D,offset:L}));return E(E({formattedGraphicalItems:G,graphicalItems:M,offset:L,stackGroups:D},q),k)},d=function(y){function b(w){var x,S,m;return r3(this,b),m=a3(this,b,[w]),H(m,"eventEmitterSymbol",Symbol("rechartsEventEmitter")),H(m,"accessibilityManager",new zF),H(m,"handleLegendBBoxUpdate",function(g){if(g){var O=m.state,A=O.dataStartIndex,P=O.dataEndIndex,j=O.updateId;m.setState(E({legendBBox:g},h({props:m.props,dataStartIndex:A,dataEndIndex:P,updateId:j},E(E({},m.state),{},{legendBBox:g}))))}}),H(m,"handleReceiveSyncEvent",function(g,O,A){if(m.props.syncId===g){if(A===m.eventEmitterSymbol&&typeof m.props.syncMethod!="function")return;m.applySyncEvent(O)}}),H(m,"handleBrushChange",function(g){var O=g.startIndex,A=g.endIndex;if(O!==m.state.dataStartIndex||A!==m.state.dataEndIndex){var P=m.state.updateId;m.setState(function(){return E({dataStartIndex:O,dataEndIndex:A},h({props:m.props,dataStartIndex:O,dataEndIndex:A,updateId:P},m.state))}),m.triggerSyncEvent({dataStartIndex:O,dataEndIndex:A})}}),H(m,"handleMouseEnter",function(g){var O=m.getMouseInfo(g);if(O){var A=E(E({},O),{},{isTooltipActive:!0});m.setState(A),m.triggerSyncEvent(A);var P=m.props.onMouseEnter;Y(P)&&P(A,g)}}),H(m,"triggeredAfterMouseMove",function(g){var O=m.getMouseInfo(g),A=O?E(E({},O),{},{isTooltipActive:!0}):{isTooltipActive:!1};m.setState(A),m.triggerSyncEvent(A);var P=m.props.onMouseMove;Y(P)&&P(A,g)}),H(m,"handleItemMouseEnter",function(g){m.setState(function(){return{isTooltipActive:!0,activeItem:g,activePayload:g.tooltipPayload,activeCoordinate:g.tooltipPosition||{x:g.cx,y:g.cy}}})}),H(m,"handleItemMouseLeave",function(){m.setState(function(){return{isTooltipActive:!1}})}),H(m,"handleMouseMove",function(g){g.persist(),m.throttleTriggeredAfterMouseMove(g)}),H(m,"handleMouseLeave",function(g){m.throttleTriggeredAfterMouseMove.cancel();var O={isTooltipActive:!1};m.setState(O),m.triggerSyncEvent(O);var A=m.props.onMouseLeave;Y(A)&&A(O,g)}),H(m,"handleOuterEvent",function(g){var O=T0(g),A=He(m.props,"".concat(O));if(O&&Y(A)){var P,j;/.*touch.*/i.test(O)?j=m.getMouseInfo(g.changedTouches[0]):j=m.getMouseInfo(g),A((P=j)!==null&&P!==void 0?P:{},g)}}),H(m,"handleClick",function(g){var O=m.getMouseInfo(g);if(O){var A=E(E({},O),{},{isTooltipActive:!0});m.setState(A),m.triggerSyncEvent(A);var P=m.props.onClick;Y(P)&&P(A,g)}}),H(m,"handleMouseDown",function(g){var O=m.props.onMouseDown;if(Y(O)){var A=m.getMouseInfo(g);O(A,g)}}),H(m,"handleMouseUp",function(g){var O=m.props.onMouseUp;if(Y(O)){var A=m.getMouseInfo(g);O(A,g)}}),H(m,"handleTouchMove",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&m.throttleTriggeredAfterMouseMove(g.changedTouches[0])}),H(m,"handleTouchStart",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&m.handleMouseDown(g.changedTouches[0])}),H(m,"handleTouchEnd",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&m.handleMouseUp(g.changedTouches[0])}),H(m,"handleDoubleClick",function(g){var O=m.props.onDoubleClick;if(Y(O)){var A=m.getMouseInfo(g);O(A,g)}}),H(m,"handleContextMenu",function(g){var O=m.props.onContextMenu;if(Y(O)){var A=m.getMouseInfo(g);O(A,g)}}),H(m,"triggerSyncEvent",function(g){m.props.syncId!==void 0&&No.emit(Lo,m.props.syncId,g,m.eventEmitterSymbol)}),H(m,"applySyncEvent",function(g){var O=m.props,A=O.layout,P=O.syncMethod,j=m.state.updateId,$=g.dataStartIndex,T=g.dataEndIndex;if(g.dataStartIndex!==void 0||g.dataEndIndex!==void 0)m.setState(E({dataStartIndex:$,dataEndIndex:T},h({props:m.props,dataStartIndex:$,dataEndIndex:T,updateId:j},m.state)));else if(g.activeTooltipIndex!==void 0){var I=g.chartX,C=g.chartY,M=g.activeTooltipIndex,D=m.state,k=D.offset,L=D.tooltipTicks;if(!k)return;if(typeof P=="function")M=P(L,g);else if(P==="value"){M=-1;for(var F=0;F<L.length;F++)if(L[F].value===g.activeLabel){M=F;break}}var q=E(E({},k),{},{x:k.left,y:k.top}),G=Math.min(I,q.x+q.width),z=Math.min(C,q.y+q.height),K=L[M]&&L[M].value,ce=sc(m.state,m.props.data,M),ve=L[M]?{x:A==="horizontal"?L[M].coordinate:G,y:A==="horizontal"?z:L[M].coordinate}:Ny;m.setState(E(E({},g),{},{activeLabel:K,activeCoordinate:ve,activePayload:ce,activeTooltipIndex:M}))}else m.setState(g)}),H(m,"renderCursor",function(g){var O,A=m.state,P=A.isTooltipActive,j=A.activeCoordinate,$=A.activePayload,T=A.offset,I=A.activeTooltipIndex,C=A.tooltipAxisBandSize,M=m.getTooltipEventType(),D=(O=g.props.active)!==null&&O!==void 0?O:P,k=m.props.layout,L=g.key||"_recharts-cursor";return _.createElement(VF,{key:L,activeCoordinate:j,activePayload:$,activeTooltipIndex:I,chartName:r,element:g,isActive:D,layout:k,offset:T,tooltipAxisBandSize:C,tooltipEventType:M})}),H(m,"renderPolarAxis",function(g,O,A){var P=He(g,"type.axisType"),j=He(m.state,"".concat(P,"Map")),$=g.type.defaultProps,T=$!==void 0?E(E({},$),g.props):g.props,I=j&&j[T["".concat(P,"Id")]];return B.cloneElement(g,E(E({},I),{},{className:ee(P,I.className),key:g.key||"".concat(O,"-").concat(A),ticks:ht(I,!0)}))}),H(m,"renderPolarGrid",function(g){var O=g.props,A=O.radialLines,P=O.polarAngles,j=O.polarRadius,$=m.state,T=$.radiusAxisMap,I=$.angleAxisMap,C=Pt(T),M=Pt(I),D=M.cx,k=M.cy,L=M.innerRadius,F=M.outerRadius;return B.cloneElement(g,{polarAngles:Array.isArray(P)?P:ht(M,!0).map(function(q){return q.coordinate}),polarRadius:Array.isArray(j)?j:ht(C,!0).map(function(q){return q.coordinate}),cx:D,cy:k,innerRadius:L,outerRadius:F,key:g.key||"polar-grid",radialLines:A})}),H(m,"renderLegend",function(){var g=m.state.formattedGraphicalItems,O=m.props,A=O.children,P=O.width,j=O.height,$=m.props.margin||{},T=P-($.left||0)-($.right||0),I=Pv({children:A,formattedGraphicalItems:g,legendWidth:T,legendContent:s});if(!I)return null;var C=I.item,M=ah(I,YF);return B.cloneElement(C,E(E({},M),{},{chartWidth:P,chartHeight:j,margin:$,onBBoxUpdate:m.handleLegendBBoxUpdate}))}),H(m,"renderTooltip",function(){var g,O=m.props,A=O.children,P=O.accessibilityLayer,j=Re(A,lt);if(!j)return null;var $=m.state,T=$.isTooltipActive,I=$.activeCoordinate,C=$.activePayload,M=$.activeLabel,D=$.offset,k=(g=j.props.active)!==null&&g!==void 0?g:T;return B.cloneElement(j,{viewBox:E(E({},D),{},{x:D.left,y:D.top}),active:k,label:M,payload:k?C:[],coordinate:I,accessibilityLayer:P})}),H(m,"renderBrush",function(g){var O=m.props,A=O.margin,P=O.data,j=m.state,$=j.offset,T=j.dataStartIndex,I=j.dataEndIndex,C=j.updateId;return B.cloneElement(g,{key:g.key||"_recharts-brush",onChange:di(m.handleBrushChange,g.props.onChange),data:P,x:R(g.props.x)?g.props.x:$.left,y:R(g.props.y)?g.props.y:$.top+$.height+$.brushBottom-(A.bottom||0),width:R(g.props.width)?g.props.width:$.width,startIndex:T,endIndex:I,updateId:"brush-".concat(C)})}),H(m,"renderReferenceElement",function(g,O,A){if(!g)return null;var P=m,j=P.clipPathId,$=m.state,T=$.xAxisMap,I=$.yAxisMap,C=$.offset,M=g.type.defaultProps||{},D=g.props,k=D.xAxisId,L=k===void 0?M.xAxisId:k,F=D.yAxisId,q=F===void 0?M.yAxisId:F;return B.cloneElement(g,{key:g.key||"".concat(O,"-").concat(A),xAxis:T[L],yAxis:I[q],viewBox:{x:C.left,y:C.top,width:C.width,height:C.height},clipPathId:j})}),H(m,"renderActivePoints",function(g){var O=g.item,A=g.activePoint,P=g.basePoint,j=g.childIndex,$=g.isRange,T=[],I=O.props.key,C=O.item.type.defaultProps!==void 0?E(E({},O.item.type.defaultProps),O.item.props):O.item.props,M=C.activeDot,D=C.dataKey,k=E(E({index:j,dataKey:D,cx:A.x,cy:A.y,r:4,fill:as(O.item),strokeWidth:2,stroke:"#fff",payload:A.payload,value:A.value},J(M,!1)),wi(M));return T.push(b.renderActiveDot(M,k,"".concat(I,"-activePoint-").concat(j))),P?T.push(b.renderActiveDot(M,E(E({},k),{},{cx:P.x,cy:P.y}),"".concat(I,"-basePoint-").concat(j))):$&&T.push(null),T}),H(m,"renderGraphicChild",function(g,O,A){var P=m.filterFormatItem(g,O,A);if(!P)return null;var j=m.getTooltipEventType(),$=m.state,T=$.isTooltipActive,I=$.tooltipAxis,C=$.activeTooltipIndex,M=$.activeLabel,D=m.props.children,k=Re(D,lt),L=P.props,F=L.points,q=L.isRange,G=L.baseLine,z=P.item.type.defaultProps!==void 0?E(E({},P.item.type.defaultProps),P.item.props):P.item.props,K=z.activeDot,ce=z.hide,ve=z.activeBar,Ne=z.activeShape,kt=!!(!ce&&T&&k&&(K||ve||Ne)),Ce={};j!=="axis"&&k&&k.props.trigger==="click"?Ce={onClick:di(m.handleItemMouseEnter,g.props.onClick)}:j!=="axis"&&(Ce={onMouseLeave:di(m.handleItemMouseLeave,g.props.onMouseLeave),onMouseEnter:di(m.handleItemMouseEnter,g.props.onMouseEnter)});var W=B.cloneElement(g,E(E({},P.props),Ce));function X(Lt){return typeof I.dataKey=="function"?I.dataKey(Lt.payload):null}if(kt)if(C>=0){var V,N;if(I.dataKey&&!I.allowDuplicatedCategory){var pe=typeof I.dataKey=="function"?X:"payload.".concat(I.dataKey.toString());V=xi(F,pe,M),N=q&&G&&xi(G,pe,M)}else V=F==null?void 0:F[C],N=q&&G&&G[C];if(Ne||ve){var Q=g.props.activeIndex!==void 0?g.props.activeIndex:C;return[B.cloneElement(g,E(E(E({},P.props),Ce),{},{activeIndex:Q})),null,null]}if(!Z(V))return[W].concat(Nr(m.renderActivePoints({item:P,activePoint:V,basePoint:N,childIndex:C,isRange:q})))}else{var ge,be=(ge=m.getItemByXY(m.state.activeCoordinate))!==null&&ge!==void 0?ge:{graphicalItem:W},Ie=be.graphicalItem,At=Ie.item,nr=At===void 0?g:At,ii=Ie.childIndex,Nt=E(E(E({},P.props),Ce),{},{activeIndex:ii});return[B.cloneElement(nr,Nt),null,null]}return q?[W,null,null]:[W,null]}),H(m,"renderCustomized",function(g,O,A){return B.cloneElement(g,E(E({key:"recharts-customized-".concat(A)},m.props),m.state))}),H(m,"renderMap",{CartesianGrid:{handler:bi,once:!0},ReferenceArea:{handler:m.renderReferenceElement},ReferenceLine:{handler:bi},ReferenceDot:{handler:m.renderReferenceElement},XAxis:{handler:bi},YAxis:{handler:bi},Brush:{handler:m.renderBrush,once:!0},Bar:{handler:m.renderGraphicChild},Line:{handler:m.renderGraphicChild},Area:{handler:m.renderGraphicChild},Radar:{handler:m.renderGraphicChild},RadialBar:{handler:m.renderGraphicChild},Scatter:{handler:m.renderGraphicChild},Pie:{handler:m.renderGraphicChild},Funnel:{handler:m.renderGraphicChild},Tooltip:{handler:m.renderCursor,once:!0},PolarGrid:{handler:m.renderPolarGrid,once:!0},PolarAngleAxis:{handler:m.renderPolarAxis},PolarRadiusAxis:{handler:m.renderPolarAxis},Customized:{handler:m.renderCustomized}}),m.clipPathId="".concat((x=w.id)!==null&&x!==void 0?x:Zn("recharts"),"-clip"),m.throttleTriggeredAfterMouseMove=_d(m.triggeredAfterMouseMove,(S=w.throttleDelay)!==null&&S!==void 0?S:1e3/60),m.state={},m}return c3(b,y),i3(b,[{key:"componentDidMount",value:function(){var x,S;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:(x=this.props.margin.left)!==null&&x!==void 0?x:0,top:(S=this.props.margin.top)!==null&&S!==void 0?S:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var x=this.props,S=x.children,m=x.data,g=x.height,O=x.layout,A=Re(S,lt);if(A){var P=A.props.defaultIndex;if(!(typeof P!="number"||P<0||P>this.state.tooltipTicks.length-1)){var j=this.state.tooltipTicks[P]&&this.state.tooltipTicks[P].value,$=sc(this.state,m,P,j),T=this.state.tooltipTicks[P].coordinate,I=(this.state.offset.top+g)/2,C=O==="horizontal",M=C?{x:T,y:I}:{y:T,x:I},D=this.state.formattedGraphicalItems.find(function(L){var F=L.item;return F.type.name==="Scatter"});D&&(M=E(E({},M),D.props.points[P].tooltipPosition),$=D.props.points[P].tooltipPayload);var k={activeTooltipIndex:P,isTooltipActive:!0,activeLabel:j,activePayload:$,activeCoordinate:M};this.setState(k),this.renderCursor(A),this.accessibilityManager.setIndex(P)}}}},{key:"getSnapshotBeforeUpdate",value:function(x,S){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==S.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==x.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==x.margin){var m,g;this.accessibilityManager.setDetails({offset:{left:(m=this.props.margin.left)!==null&&m!==void 0?m:0,top:(g=this.props.margin.top)!==null&&g!==void 0?g:0}})}return null}},{key:"componentDidUpdate",value:function(x){Wo([Re(x.children,lt)],[Re(this.props.children,lt)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var x=Re(this.props.children,lt);if(x&&typeof x.props.shared=="boolean"){var S=x.props.shared?"axis":"item";return u.indexOf(S)>=0?S:a}return a}},{key:"getMouseInfo",value:function(x){if(!this.container)return null;var S=this.container,m=S.getBoundingClientRect(),g=tE(m),O={chartX:Math.round(x.pageX-g.left),chartY:Math.round(x.pageY-g.top)},A=m.width/S.offsetWidth||1,P=this.inRange(O.chartX,O.chartY,A);if(!P)return null;var j=this.state,$=j.xAxisMap,T=j.yAxisMap,I=this.getTooltipEventType(),C=uh(this.state,this.props.data,this.props.layout,P);if(I!=="axis"&&$&&T){var M=Pt($).scale,D=Pt(T).scale,k=M&&M.invert?M.invert(O.chartX):null,L=D&&D.invert?D.invert(O.chartY):null;return E(E({},O),{},{xValue:k,yValue:L},C)}return C?E(E({},O),C):null}},{key:"inRange",value:function(x,S){var m=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,g=this.props.layout,O=x/m,A=S/m;if(g==="horizontal"||g==="vertical"){var P=this.state.offset,j=O>=P.left&&O<=P.left+P.width&&A>=P.top&&A<=P.top+P.height;return j?{x:O,y:A}:null}var $=this.state,T=$.angleAxisMap,I=$.radiusAxisMap;if(T&&I){var C=Pt(T);return Uf({x:O,y:A},C)}return null}},{key:"parseEventsOfWrapper",value:function(){var x=this.props.children,S=this.getTooltipEventType(),m=Re(x,lt),g={};m&&S==="axis"&&(m.props.trigger==="click"?g={onClick:this.handleClick}:g={onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu});var O=wi(this.props,this.handleOuterEvent);return E(E({},O),g)}},{key:"addListener",value:function(){No.on(Lo,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){No.removeListener(Lo,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(x,S,m){for(var g=this.state.formattedGraphicalItems,O=0,A=g.length;O<A;O++){var P=g[O];if(P.item===x||P.props.key===x.key||S===dt(P.item.type)&&m===P.childIndex)return P}return null}},{key:"renderClipPath",value:function(){var x=this.clipPathId,S=this.state.offset,m=S.left,g=S.top,O=S.height,A=S.width;return _.createElement("defs",null,_.createElement("clipPath",{id:x},_.createElement("rect",{x:m,y:g,height:O,width:A})))}},{key:"getXScales",value:function(){var x=this.state.xAxisMap;return x?Object.entries(x).reduce(function(S,m){var g=ih(m,2),O=g[0],A=g[1];return E(E({},S),{},H({},O,A.scale))},{}):null}},{key:"getYScales",value:function(){var x=this.state.yAxisMap;return x?Object.entries(x).reduce(function(S,m){var g=ih(m,2),O=g[0],A=g[1];return E(E({},S),{},H({},O,A.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(x){var S;return(S=this.state.xAxisMap)===null||S===void 0||(S=S[x])===null||S===void 0?void 0:S.scale}},{key:"getYScaleByAxisId",value:function(x){var S;return(S=this.state.yAxisMap)===null||S===void 0||(S=S[x])===null||S===void 0?void 0:S.scale}},{key:"getItemByXY",value:function(x){var S=this.state,m=S.formattedGraphicalItems,g=S.activeItem;if(m&&m.length)for(var O=0,A=m.length;O<A;O++){var P=m[O],j=P.props,$=P.item,T=$.type.defaultProps!==void 0?E(E({},$.type.defaultProps),$.props):$.props,I=dt($.type);if(I==="Bar"){var C=(j.data||[]).find(function(L){return Lk(x,L)});if(C)return{graphicalItem:P,payload:C}}else if(I==="RadialBar"){var M=(j.data||[]).find(function(L){return Uf(x,L)});if(M)return{graphicalItem:P,payload:M}}else if(Ja(P,g)||Qa(P,g)||zn(P,g)){var D=FN({graphicalItem:P,activeTooltipItem:g,itemData:T.data}),k=T.activeIndex===void 0?D:T.activeIndex;return{graphicalItem:E(E({},P),{},{childIndex:k}),payload:zn(P,g)?T.data[D]:P.props.data[D]}}}return null}},{key:"render",value:function(){var x=this;if(!Cs(this))return null;var S=this.props,m=S.children,g=S.className,O=S.width,A=S.height,P=S.style,j=S.compact,$=S.title,T=S.desc,I=ah(S,ZF),C=J(I,!1);if(j)return _.createElement(Fp,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},_.createElement(Uo,lr({},C,{width:O,height:A,title:$,desc:T}),this.renderClipPath(),Ds(m,this.renderMap)));if(this.props.accessibilityLayer){var M,D;C.tabIndex=(M=this.props.tabIndex)!==null&&M!==void 0?M:0,C.role=(D=this.props.role)!==null&&D!==void 0?D:"application",C.onKeyDown=function(L){x.accessibilityManager.keyboardEvent(L)},C.onFocus=function(){x.accessibilityManager.focus()}}var k=this.parseEventsOfWrapper();return _.createElement(Fp,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},_.createElement("div",lr({className:ee("recharts-wrapper",g),style:E({position:"relative",cursor:"default",width:O,height:A},P)},k,{ref:function(F){x.container=F}}),_.createElement(Uo,lr({},C,{width:O,height:A,title:$,desc:T,style:d3}),this.renderClipPath(),Ds(m,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}])}(B.Component);H(d,"displayName",r),H(d,"defaultProps",E({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},l)),H(d,"getDerivedStateFromProps",function(y,b){var w=y.dataKey,x=y.data,S=y.children,m=y.width,g=y.height,O=y.layout,A=y.stackOffset,P=y.margin,j=b.dataStartIndex,$=b.dataEndIndex;if(b.updateId===void 0){var T=ch(y);return E(E(E({},T),{},{updateId:0},h(E(E({props:y},T),{},{updateId:0}),b)),{},{prevDataKey:w,prevData:x,prevWidth:m,prevHeight:g,prevLayout:O,prevStackOffset:A,prevMargin:P,prevChildren:S})}if(w!==b.prevDataKey||x!==b.prevData||m!==b.prevWidth||g!==b.prevHeight||O!==b.prevLayout||A!==b.prevStackOffset||!fr(P,b.prevMargin)){var I=ch(y),C={chartX:b.chartX,chartY:b.chartY,isTooltipActive:b.isTooltipActive},M=E(E({},uh(b,x,O)),{},{updateId:b.updateId+1}),D=E(E(E({},I),C),M);return E(E(E({},D),h(E({props:y},D),b)),{},{prevDataKey:w,prevData:x,prevWidth:m,prevHeight:g,prevLayout:O,prevStackOffset:A,prevMargin:P,prevChildren:S})}if(!Wo(S,b.prevChildren)){var k,L,F,q,G=Re(S,Pr),z=G&&(k=(L=G.props)===null||L===void 0?void 0:L.startIndex)!==null&&k!==void 0?k:j,K=G&&(F=(q=G.props)===null||q===void 0?void 0:q.endIndex)!==null&&F!==void 0?F:$,ce=z!==j||K!==$,ve=!Z(x),Ne=ve&&!ce?b.updateId:b.updateId+1;return E(E({updateId:Ne},h(E(E({props:y},b),{},{updateId:Ne,dataStartIndex:z,dataEndIndex:K}),b)),{},{prevChildren:S,dataStartIndex:z,dataEndIndex:K})}return null}),H(d,"renderActiveDot",function(y,b,w){var x;return B.isValidElement(y)?x=B.cloneElement(y,b):Y(y)?x=y(b):x=_.createElement(us,b),_.createElement(de,{className:"recharts-active-dot",key:w},x)});var v=B.forwardRef(function(b,w){return _.createElement(d,lr({},b,{ref:w}))});return v.displayName=d.displayName,v},P3=By({chartName:"LineChart",GraphicalChild:ao,axisComponents:[{axisType:"xAxis",AxisComp:oo},{axisType:"yAxis",AxisComp:uo}],formatAxisMap:ry}),$3=By({chartName:"BarChart",GraphicalChild:Vr,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:oo},{axisType:"yAxis",AxisComp:uo}],formatAxisMap:ry});export{$3 as B,QR as C,P3 as L,_3 as R,lt as T,oo as X,uo as Y,Vr as a,ao as b};
