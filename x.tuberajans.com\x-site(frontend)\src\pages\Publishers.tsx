import React, { useState, useEffect, Component, ErrorInfo } from 'react';
import {
  Users,
  Plus,
  Edit,
  Trash,
  Search,
  RefreshCw,
  X,
  FileSpreadsheet,
  Upload,
  Mail,
  MessageSquare
} from 'lucide-react';
import { PageTitle, RefreshButton } from '../components/ThemeStyles';
import {
  Popover,
  Button,
  Form,
  Input,
  DatePicker,
  message,
  Spin,
  Alert,
  Modal,
  Table,
  Tooltip,
  Space,
  Select,
  Divider,
  InputNumber,
  Popconfirm,
  Card,
  Descriptions,
  Upload as AntUpload,
  Dropdown,
  Menu,
  Avatar,
  Progress
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import moment from 'moment';
import 'moment/locale/tr';
import * as XLSX from 'xlsx';
import toast from 'react-hot-toast';
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UsergroupAddOutlined,
  DownloadOutlined,
  ReloadOutlined,
  UploadOutlined,
  WhatsAppOutlined,
  MailOutlined,
  EllipsisOutlined,
  CloudDownloadOutlined,
  UserOutlined
} from '@ant-design/icons';
import { useApi } from '../hooks/useApi';
import { API_CONFIG } from '../config';
import { Publisher as ApiPublisher, createPublisher, updatePublisher, deletePublisher } from '../lib/api';
import { useTheme } from '../contexts/ThemeContext';
import { PUBLISHERS } from '../lib/mockData';
import { Button as AntButton } from 'antd';
import { SECURITY_CONFIG } from '../config';
import SingleEmailModal from '../components/SingleEmailModal';

// Geliştirme ortamında mı yoksa üretim ortamında mı olduğumuzu belirten değişken
const IS_DEVELOPMENT = process.env.NODE_ENV === 'development';

// Bileşen içinde kullanılacak Publisher tipi (ApiPublisher'ı genişletebilir veya uyarlayabiliriz)
interface Publisher extends ApiPublisher {
  // Gerekirse UI'a özel ek alanlar burada tanımlanabilir
  // Örneğin: status?: 'active' | 'inactive' | 'pending';
  profile_image?: string;
}

interface PublishersProps {
  hideHeader?: boolean;
  isMobileView?: boolean;
  onDataChange?: (count: number) => void;
  darkTheme?: boolean;
}

// Progress bar için global state
const getInitialProgress = () => {
  const saved = localStorage.getItem('publisherProfileImageProgress');
  if (saved) {
    try {
      const parsed = JSON.parse(saved);
      // JSON yapısının beklenen gibi olup olmadığını da kontrol edebilirsiniz
      if (
        parsed &&
        typeof parsed.total === 'number' &&
        typeof parsed.current === 'number' &&
        typeof parsed.success === 'number' &&
        typeof parsed.fail === 'number' &&
        typeof parsed.running === 'boolean'
      ) {
        return parsed;
      }
    } catch (e) {
      console.error(
        'Error parsing publisherProfileImageProgress from localStorage (getInitialProgress):',
        e
      );
      localStorage.removeItem('publisherProfileImageProgress'); // Bozuk veriyi temizle
    }
  }
  return { total: 0, current: 0, success: 0, fail: 0, running: false };
};

// Hata sınırı bileşeni
class ErrorBoundary extends Component<
  { children: React.ReactNode; fallback?: React.ReactNode },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: { children: React.ReactNode; fallback?: React.ReactNode }) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log the error to an error reporting service
    console.error("Publishers component error:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      // Özel hata UI göster
      return this.props.fallback || (
        <div className="p-8 text-center">
          <h2 className="text-xl font-bold text-red-600 mb-4">Sayfa yüklenirken beklenmeyen bir hata oluştu</h2>
          <p className="mb-4">Teknik detay: {this.state.error?.message}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Sayfayı Yenile
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

export default function Publishers({ hideHeader = false, isMobileView = false, onDataChange, darkTheme }: PublishersProps) {
  // Her zaman useApi hook'unu çağır
  const { data: apiPublishers = [], loading: apiLoading, error: apiError, refetch } = 
    useApi<Publisher[]>(`${API_CONFIG.X_SITE_BASE_URL}${API_CONFIG.ENDPOINTS.PUBLISHERS}`);

  const [useBackupData, setUseBackupData] = useState<boolean>(IS_DEVELOPMENT);
  const [mockData, setMockData] = useState<Publisher[]>(PUBLISHERS);
  const [loading, setLoading] = useState<boolean>(!IS_DEVELOPMENT && apiLoading);

  // publishers verisinin dizi olduğundan emin olalım
  const publishersList: Publisher[] = Array.isArray(apiPublishers) ? apiPublishers : [];
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddEditModal, setShowAddEditModal] = useState(false);
  const [editingPublisher, setEditingPublisher] = useState<Publisher | null>(null);
  const [form] = Form.useForm();
  const { darkMode } = useTheme();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [emailModalVisible, setEmailModalVisible] = useState(false);
  const [emailForm] = Form.useForm();
  const [whatsappModalVisible, setWhatsappModalVisible] = useState(false);
  const [whatsappForm] = Form.useForm();
  const [templates, setTemplates] = useState<{name:string;subject:string;content:string}[]>([]);
  const [selectedTemplateName, setSelectedTemplateName] = useState<string | undefined>(undefined);
  const [newTemplateModalVisible, setNewTemplateModalVisible] = useState(false);
  const [templateForm] = Form.useForm();
  const [whatsappTemplates, setWhatsappTemplates] = useState<{name:string;content:string}[]>([]);
  const [selectedWhatsappTemplateName, setSelectedWhatsappTemplateName] = useState<string | undefined>(undefined);
  const [newWhatsappTemplateModalVisible, setNewWhatsappTemplateModalVisible] = useState(false);
  const [editWhatsappTemplateModalVisible, setEditWhatsappTemplateModalVisible] = useState(false);
  const [currentTemplateForEdit, setCurrentTemplateForEdit] = useState<{name:string;content:string} | null>(null);
  const [whatsappTemplateForm] = Form.useForm();
  const [minFollowersFilter, setMinFollowersFilter] = useState<number | null>(null);
  const [maxFollowersFilter, setMaxFollowersFilter] = useState<number | null>(null);
  const [fileList, setFileList] = useState<any[]>([]);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewContent, setPreviewContent] = useState<string>('');
  const [previewSubject, setPreviewSubject] = useState<string>('');
  const [emailSending, setEmailSending] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [deleteTargetId, setDeleteTargetId] = useState<number | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [editingCell, setEditingCell] = useState<{ key: number; dataIndex: string } | null>(null);
  const [editingValue, setEditingValue] = useState<string | number | undefined>(undefined);
  const [tableData, setTableData] = useState<Publisher[]>([]);
  const [progress, setProgress] = useState(getInitialProgress());

  // API hatası olduğunda veya geliştirme ortamındaysak mock verileri kullan
  useEffect(() => {
    if (apiError && !IS_DEVELOPMENT) {
      console.warn('API hatası nedeniyle mock veri kullanılıyor:', apiError);
      setUseBackupData(true);
    }
    setLoading(apiLoading && !IS_DEVELOPMENT);
  }, [apiError, apiLoading]);

  // API'den veri ilk kez geldiğinde local state'e ata
  useEffect(() => {
    try {
      if (!IS_DEVELOPMENT && Array.isArray(publishersList) && publishersList.length > 0) {
        // API'den geçerli veriler geldi, bunları render-safe bir hale getiriyoruz
        const safeData = publishersList.map(publisher => ({
          ...publisher,
          // Burada null değerleri boş string ile değiştiriyoruz render sorunlarını önlemek için
          isim_soyisim: publisher.isim_soyisim || '',
          username: publisher.username || '',
          mail: publisher.mail || '',
          telefon: publisher.telefon || '',
          sehir: publisher.sehir || '',
          meslek: publisher.meslek || '',
          // ID değerini kontrol et ve string ise numbera çevir - null yerine undefined kullan
          id: publisher.id !== undefined ?
               (typeof publisher.id === 'string' ? parseInt(publisher.id, 10) : publisher.id)
               : undefined
        }));
        setTableData(safeData);
        console.log('API verileri tableData state\'ine atandı:', safeData.length);
      }
    } catch (error) {
      console.error('API verilerini işlerken hata oluştu:', error);
      // Hata durumunda en azından boş bir array koy ki render sırasında patlama olmasın
      setTableData([]);
    }
  }, [publishersList, IS_DEVELOPMENT]);

  // API'den veri geldiyse kullan, yoksa veya geliştirme ortamındaysak mock veri kullan
  // Yeni effectivePublisherList tanımlaması - veri olup olmadığını kontrol ederek
  const effectivePublisherList = React.useMemo(() => {
    try {
      if (useBackupData) {
        return Array.isArray(mockData) ? mockData : [];
      } else {
        // tableData'yı kullan - bu, publishersList'ten güvenli bir şekilde işlenmiş veri
        return Array.isArray(tableData) ? tableData : [];
      }
    } catch (error) {
      console.error('effectivePublisherList hesaplanırken hata:', error);
      return []; // Her durumda dizi dön
    }
  }, [useBackupData, mockData, tableData]);

  // Oluşturma, güncelleme ve silme işlemleri sonrası veriyi tekrar yükle
  const refresh = () => {
    if (!IS_DEVELOPMENT) {
      refetch();
    } else {
      // Geliştirme ortamında mock verileri yeniden yükleyelim
      setMockData([...PUBLISHERS]);
      message.success('Veriler yenilendi');
    }
  };

  // Load email templates from localStorage
  useEffect(() => {
    try {
      const saved = localStorage.getItem('publisherEmailTemplates');
      if (saved) {
        setTemplates(JSON.parse(saved));
      } else {
        // Default templates
        const defaultTemplates = [
          {
            name: 'Yayıncı Bilgilendirme',
            subject: 'Önemli Duyuru',
            content: 'Merhaba {username}, yeni kampanyamız hakkında bilgilendirmek istiyoruz. Detaylar için lütfen bizimle iletişime geçin.'
          },
          {
            name: 'Etkinlik Daveti',
            subject: 'Yeni Etkinlik Daveti',
            content: 'Merhaba {username}, yaklaşan etkinliğimize sizi davet etmek istiyoruz. Katılım durumunuzu en kısa sürede bildirmenizi rica ederiz.'
          }
        ];
        setTemplates(defaultTemplates);
        localStorage.setItem('publisherEmailTemplates', JSON.stringify(defaultTemplates));
      }
    } catch (e) {
      console.error('Error loading or parsing email templates from localStorage:', e);
      // İsteğe bağlı: localStorage.removeItem('publisherEmailTemplates'); // Bozuk veriyi temizle
    }
  }, []);

  // Load whatsapp templates from localStorage
  useEffect(() => {
    try {
      const saved = localStorage.getItem('publisherWhatsappTemplates');
      if (saved) {
        setWhatsappTemplates(JSON.parse(saved));
      } else {
        // Default templates
        const defaultTemplates = [
          {
            name: 'Yayın Hatırlatması',
            content: 'Merhaba {username}, bugün planlanan yayınınız hakkında hatırlatma yapmak istedik. İyi yayınlar dileriz.'
          },
          {
            name: 'Performans Bilgisi',
            content: 'Merhaba {username}, geçen haftaki performansınız hakkında bilgi vermek istiyoruz. Detayları görmek ister misiniz?'
          }
        ];
        setWhatsappTemplates(defaultTemplates);
        localStorage.setItem('publisherWhatsappTemplates', JSON.stringify(defaultTemplates));
      }
    } catch (error) {
      console.error('Şablonlar yüklenirken hata:', error);
    }
  }, []);

  // Filtrelenmiş yayıncılar - güvenli listedeki publishersList kullanılır
  const filteredPublishers = React.useMemo(() => {
    try {
      const sourceData = IS_DEVELOPMENT ? mockData : tableData;
      if (!Array.isArray(sourceData)) {
        console.warn('filteredPublishers için kaynak veri dizi değil:', sourceData);
        return [];
      }

      if (!searchTerm.trim()) {
        return sourceData;
      }

      const searchTermLower = searchTerm.toLowerCase();
      return sourceData.filter(publisher => {
        // Null kontrolü yap
        if (!publisher) return false;

        return (
          (publisher.isim_soyisim || '').toLowerCase().includes(searchTermLower) ||
          (publisher.mail || '').toLowerCase().includes(searchTermLower) ||
          (publisher.username || '').toLowerCase().includes(searchTermLower) ||
          (publisher.sehir || '').toLowerCase().includes(searchTermLower) ||
          (publisher.meslek || '').toLowerCase().includes(searchTermLower)
        );
      });
    } catch (error) {
      console.error('Filtreleme işlemi sırasında hata:', error);
      return [];
    }
  }, [IS_DEVELOPMENT, mockData, tableData, searchTerm]);

  // Yayıncı düzenleme işlevi
  const handleEdit = (publisher: Publisher) => {
    setEditingPublisher(publisher);
    form.setFieldsValue({
      ...publisher,
      dogum_tarihi: publisher.dogum_tarihi ? moment(publisher.dogum_tarihi, 'YYYY-MM-DD') : null
    });
    setShowAddEditModal(true);
  };

  // Yayıncı silme işlevi
  const handleDeleteConfirm = async (id?: number) => {
    if (!id) {
      toast.error('Geçersiz yayıncı ID\'si.');
      return;
    }
    setLoading(true);
    try {
      let newList;
      if (!IS_DEVELOPMENT) {
        // Token'ı SECURITY_CONFIG.TOKEN_KEY ile localStorage'dan al
        let token = localStorage.getItem(SECURITY_CONFIG.TOKEN_KEY) || '';

        // Token yoksa kullanıcı bilgisinden almayı dene
        if (!token) {
          try {
            const storedUser = localStorage.getItem(SECURITY_CONFIG.USER_KEY);
            if (storedUser) {
              const userData = JSON.parse(storedUser);
              if (userData && userData.token) {
                token = userData.token;
              }
            }
          } catch (e) {
            console.error('Token alma hatası:', e);
          }
        }

        // Hala token yoksa, oturum hatası göster
        if (!token) {
          toast.error('Oturum bilgileriniz bulunamadı. Lütfen tekrar giriş yapın.');
          setTimeout(() => {
            window.location.href = '/login';
          }, 2000);
          return;
        }

        const response = await fetch(`${API_CONFIG.X_SITE_BASE_URL}${API_CONFIG.ENDPOINTS.PUBLISHERS}?id=${id}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ id })
        });

        if (!response.ok) {
          const responseText = await response.text();

          // 401 hatası durumunda oturumun sonlandığını belirt
          if (response.status === 401) {
            toast.error('Oturumunuz sonlandı. Lütfen tekrar giriş yapın.');
            setTimeout(() => {
              window.location.href = '/login';
            }, 2000);
            return;
          }

          throw new Error(`Sunucu hatası: ${response.status} ${responseText}`);
        }

        const data = await response.json();
        if (!data.success) {
          throw new Error(data.error || 'Silme başarısız');
        }
        newList = tableData.filter(item => item.id !== id);
        setTableData(newList);
        const totalAfterDelete = newList.length;
        const lastPage = Math.ceil(totalAfterDelete / pageSize) || 1;
        if ((currentPage - 1) * pageSize >= totalAfterDelete && currentPage > 1) {
          setCurrentPage(currentPage - 1);
        }
        if (onDataChange) onDataChange(newList.length);
      } else {
        newList = mockData.filter(item => item.id !== id);
        setMockData(newList);
        const totalAfterDelete = newList.length;
        const lastPage = Math.ceil(totalAfterDelete / pageSize) || 1;
        if ((currentPage - 1) * pageSize >= totalAfterDelete && currentPage > 1) {
          setCurrentPage(currentPage - 1);
        }
        if (onDataChange) onDataChange(newList.length);
      }
      toast.success('Yayıncı başarıyla silindi');
    } catch (err: any) {
      toast.error('Yayıncı silinirken bir hata oluştu: ' + (err?.message || ''));
      console.error('Yayıncı silme hatası:', err);
    }
    setLoading(false);
  };

  // Ekleme/Düzenleme Formu Kaydetme
  const handleFormSubmit = async (values: any) => {
    try {
      setLoading(true);

      // Form değerlerini hazırla
      const formData = { ...values };

      // Backend'in zorunlu tuttuğu tüm alanlar için varsayılan değerler atayalım
      formData.isim_soyisim = formData.isim_soyisim || "Belirtilmedi";
      formData.username = formData.username || "user_" + Date.now();
      formData.telefon = formData.telefon || "0000000000";
      formData.mail = formData.mail || "<EMAIL>";
      formData.dogum_tarihi = formData.dogum_tarihi ? moment(formData.dogum_tarihi).format('YYYY-MM-DD') : moment().format('YYYY-MM-DD');
      formData.sehir = formData.sehir || "Belirtilmedi";
      formData.meslek = formData.meslek || "Belirtilmedi";

      console.log('Gönderilecek veri:', formData);

      if (IS_DEVELOPMENT) {
        if (editingPublisher?.id) {
          setMockData(prev =>
            prev.map(item =>
              item.id === editingPublisher.id ? { ...formData, id: editingPublisher.id } : item
            )
          );
        } else {
          const newId = Math.max(...mockData.map(p => p.id || 0)) + 1;
          setMockData(prev => [...prev, { ...formData, id: newId }]);
        }
        toast.success(editingPublisher?.id ? 'Yayıncı başarıyla güncellendi' : 'Yayıncı başarıyla eklendi');
      } else {
        // Token'ı SECURITY_CONFIG.TOKEN_KEY ile localStorage'dan al
        let token = localStorage.getItem(SECURITY_CONFIG.TOKEN_KEY) || '';

        // Token yoksa kullanıcı bilgisinden almayı dene
        if (!token) {
          try {
            const storedUser = localStorage.getItem(SECURITY_CONFIG.USER_KEY);
            if (storedUser) {
              const userData = JSON.parse(storedUser);
              if (userData && userData.token) {
                token = userData.token;
              }
            }
          } catch (e) {
            console.error('Token alma hatası:', e);
          }
        }

        // Hala token yoksa, oturum hatası göster
        if (!token) {
          toast.error('Oturum bilgileriniz bulunamadı. Lütfen tekrar giriş yapın.');
          setTimeout(() => {
            window.location.href = '/login';
          }, 2000);
          return;
        }

        try {
          const apiUrl = `${API_CONFIG.X_SITE_BASE_URL}${API_CONFIG.ENDPOINTS.PUBLISHERS}`;
          console.log('API URL:', apiUrl);

          if (editingPublisher?.id) {
            // PUT isteği için token ekle
            const response = await fetch(apiUrl, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
              },
              body: JSON.stringify({ ...formData, id: editingPublisher.id })
            });

            const responseText = await response.text();
            console.log('API PUT yanıtı:', responseText);

            if (!response.ok) {
              // 401 hatası durumunda oturumun sonlandığını belirt
              if (response.status === 401) {
                toast.error('Oturumunuz sonlandı. Lütfen tekrar giriş yapın.');
                setTimeout(() => {
                  window.location.href = '/login';
                }, 2000);
                return;
              }

              throw new Error(`Sunucu hatası: ${response.status} - ${responseText}`);
            }

            try {
              const data = JSON.parse(responseText);
              if (!data.success) {
                throw new Error(data.error || 'Güncelleme başarısız');
              }
            } catch (jsonError) {
              console.error('JSON parse hatası:', jsonError);
              // Response içeriği JSON değilse ama başarılı ise devam et
              if (response.ok) {
                console.log('Yanıt JSON değil ama başarılı');
              } else {
                throw new Error('Sunucu yanıtı geçersiz format içeriyor');
              }
            }

            toast.success('Yayıncı başarıyla güncellendi');
          } else {
            // POST isteği için token ekle
            console.log('POST isteği yapılıyor...');

            // ID sağlamak için mevcut verilerdeki en büyük ID'yi bulup bir artırıyoruz
            let nextId = 1;
            if (Array.isArray(effectivePublisherList) && effectivePublisherList.length > 0) {
              const ids = effectivePublisherList
                .map(p => p.id)
                .filter(id => id !== undefined && id !== null)
                .map(id => typeof id === 'string' ? parseInt(id, 10) : id);

              if (ids.length > 0) {
                nextId = Math.max(...ids) + 1;
              }
            }

            // ID'yi formData'ya ekliyoruz - kritik
            formData.id = nextId;
            console.log('Yeni oluşturulan ID:', nextId);

            const response = await fetch(apiUrl, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
              },
              body: JSON.stringify(formData)
            });

            const responseText = await response.text();
            console.log('API POST yanıtı:', responseText);

            if (!response.ok) {
              // 401 hatası durumunda oturumun sonlandığını belirt
              if (response.status === 401) {
                toast.error('Oturumunuz sonlandı. Lütfen tekrar giriş yapın.');
                setTimeout(() => {
                  window.location.href = '/login';
                }, 2000);
                return;
              }

              throw new Error(`Sunucu hatası: ${response.status} - ${responseText}`);
            }

            try {
              const data = JSON.parse(responseText);
              if (!data.success) {
                throw new Error(data.error || 'Ekleme başarısız');
              }
            } catch (jsonError) {
              console.error('JSON parse hatası:', jsonError);
              // Response içeriği JSON değilse ama başarılı ise devam et
              if (response.ok) {
                console.log('Yanıt JSON değil ama başarılı');
              } else {
                throw new Error('Sunucu yanıtı geçersiz format içeriyor');
              }
            }

            toast.success('Yayıncı başarıyla eklendi');
          }

          await refresh();
          if (onDataChange) onDataChange(effectivePublisherList.length + (editingPublisher?.id ? 0 : 1));
        } catch (apiError: any) {
          console.error('API hatası:', apiError);
          toast.error(`İşlem sırasında hata oluştu: ${apiError.message}`);
          setLoading(false);
          return;
        }
      }

      setShowAddEditModal(false);
      setEditingPublisher(null);
      form.resetFields();
    } catch (err: any) {
      toast.error('İşlem sırasında bir hata oluştu: ' + (err?.message || ''));
      console.error('Yayıncı kaydetme/güncelleme hatası:', err);
    }
    setLoading(false);
  };

  // Excel olarak dışa aktarma
  const exportToExcel = () => {
    const dataToExport = effectivePublisherList
        .filter(p => p.id !== undefined)
        .map(({ id, ...rest }) => rest);
    if(dataToExport.length === 0) {
        toast('Dışa aktarılacak veri bulunamadı.', { icon: '⚠️' });
        return;
    }
    const ws = XLSX.utils.json_to_sheet(dataToExport);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Yayıncılar");
    XLSX.writeFile(wb, "yayincilar.xlsx");
  };

  // Excel'den içe aktarma
  const importFromExcel = (e: React.ChangeEvent<HTMLInputElement>) => {
    toast('Excel\'den içe aktarma özelliği henüz tamamlanmadı.', { icon: 'ℹ️' });
    e.target.value = ''
  };

  // Yeni yayıncı ekleme modalını açar
  const handleAddNew = () => {
    setEditingPublisher(null);
    form.resetFields();
    form.setFieldsValue({ dogum_tarihi: null });
    setShowAddEditModal(true);
  };

  // Add new email template
  const addTemplate = (vals: any) => {
    const newTemp = { name: vals.name, subject: vals.subject, content: vals.content };
    const updated = [...templates, newTemp];
    setTemplates(updated);
    localStorage.setItem('publisherEmailTemplates', JSON.stringify(updated));
    setNewTemplateModalVisible(false);
    templateForm.resetFields();
  };

  // Add new WhatsApp template
  const addWhatsappTemplate = (vals: any) => {
    const newTemp = { name: vals.name, content: vals.content };
    const updated = [...whatsappTemplates, newTemp];
    setWhatsappTemplates(updated);
    localStorage.setItem('publisherWhatsappTemplates', JSON.stringify(updated));
    setNewWhatsappTemplateModalVisible(false);
    message.success('Şablon eklendi');
  };

  // Update WhatsApp template
  const updateWhatsappTemplate = (vals: any) => {
    if (!currentTemplateForEdit) return;

    const updated = whatsappTemplates.map(t =>
      t.name === currentTemplateForEdit.name ? { name: vals.name, content: vals.content } : t
    );

    setWhatsappTemplates(updated);
    localStorage.setItem('publisherWhatsappTemplates', JSON.stringify(updated));
    setEditWhatsappTemplateModalVisible(false);
    setCurrentTemplateForEdit(null);
    whatsappTemplateForm.resetFields();
    message.success('Şablon güncellendi');
  };

  // Delete WhatsApp template
  const deleteWhatsappTemplate = (templateName: string) => {
    const updated = whatsappTemplates.filter(t => t.name !== templateName);
    setWhatsappTemplates(updated);
    localStorage.setItem('publisherWhatsappTemplates', JSON.stringify(updated));

    if (selectedWhatsappTemplateName === templateName) {
      setSelectedWhatsappTemplateName(undefined);
    }

    message.success('Şablon silindi');
  };

  // Edit WhatsApp template
  const editWhatsappTemplate = (template: {name: string, content: string}) => {
    setCurrentTemplateForEdit(template);
    whatsappTemplateForm.setFieldsValue(template);
    setEditWhatsappTemplateModalVisible(true);
  };

  // WhatsApp mesaj gönderme fonksiyonu
  const handleWhatsAppMessage = (record: Publisher) => {
    if (!record.telefon) {
      message.error('Bu yayıncının telefon numarası bulunamadı');
      return;
    }

    try {
      // Telefon numarasını düzenleme (başındaki 0'ı kaldırma ve ülke kodunu ekleme)
      let phone = record.telefon.toString().replace(/\s+/g, '');
      if (phone.startsWith('0')) {
        phone = phone.substring(1);
      }
      if (!phone.startsWith('+')) {
        phone = '+90' + phone; // Türkiye için ülke kodu
      }

      // WhatsApp mesaj metni
      const messageText = `Merhaba ${record.isim_soyisim || record.username || ''},`;

      // WhatsApp link oluşturma
      const whatsappUrl = `https://wa.me/${phone}?text=${encodeURIComponent(messageText)}`;

      // Yeni sekmede WhatsApp linkini açma
      window.open(whatsappUrl, '_blank');

      message.success('WhatsApp mesajı açılıyor...');
    } catch (error) {
      console.error('WhatsApp mesajı oluşturma hatası:', error);
      message.error('WhatsApp mesajı oluşturulurken bir hata oluştu');
    }
  };

  // Tekil e-posta modal state'leri
  const [singleEmailModalVisible, setSingleEmailModalVisible] = useState(false);
  const [selectedPublisherForEmail, setSelectedPublisherForEmail] = useState<Publisher | null>(null);

  // E-posta gönderme fonksiyonu
  const handleEmailMessage = (record: Publisher) => {
    if (!record.mail) {
      message.error('Bu yayıncının e-posta adresi bulunamadı');
      return;
    }

    // Seçili yayıncıyı set et ve modal'ı aç
    setSelectedPublisherForEmail(record);
    setSingleEmailModalVisible(true);
  };

  // Toplu e-posta gönderme
  const sendBulkEmail = async () => {
    try {
      const values = await emailForm.validateFields();
      setEmailSending(true);

      // Eğer hiç yayıncı seçilmemişse, mesaj göster ama işlemi durdurmadan devam et
      let recipients: Publisher[] = [];

      if (!Array.isArray(selectedRowKeys) || selectedRowKeys.length === 0) {
        message.info('Hiçbir yayıncı seçmediniz. Tablo üzerinde seçim yapmak istediğiniz yayıncıları işaretleyebilirsiniz.');

        // filteredPublishers'ın dizi olduğundan emin ol
        if (!Array.isArray(filteredPublishers)) {
          throw new Error('Filtrelenmiş yayıncı listesi alınamadı');
        }

        recipients = filteredPublishers.filter(p => p && p.mail);
      } else {
        // effectivePublisherList'in dizi olduğundan emin ol
        if (!Array.isArray(effectivePublisherList)) {
          throw new Error('Etkin yayıncı listesi alınamadı');
        }

        recipients = effectivePublisherList.filter(p =>
          p && selectedRowKeys.includes(p.id as React.Key) && p.mail
        );
      }

      if (recipients.length === 0) {
        message.error('Geçerli e-posta adresi olan yayıncı bulunamadı');
        setEmailSending(false);
        return;
      }

      // Mail adresleri güvenli bir şekilde çıkar
      const emails = recipients
        .map(p => p.mail)
        .filter(mail => mail && typeof mail === 'string' && mail.includes('@'));

      if (emails.length === 0) {
        message.error('Geçerli e-posta adresi olan yayıncı bulunamadı');
        setEmailSending(false);
        return;
      }

      if (IS_DEVELOPMENT) {
        // Geliştirme ortamında e-posta göndermek yerine simüle et
        message.success('Geliştirme ortamında e-posta gönderimi simüle edildi');
        console.log('Gönderilecek e-postalar:', emails);
        console.log('Konu:', values.subject);
        console.log('İçerik:', values.content);
      } else {
        // Dosya eklerini hazırla
        const attachments = await Promise.all(
          fileList.map(async (file) => {
            return {
              filename: file.name,
              content: file.originFileObj,
              contentType: file.type
            };
          })
        );

        message.loading({ content: 'Mailler gönderiliyor...', key: 'bulk-email' });
        await sendEmailToPublishers(emails, values.subject, values.content, values.fromEmail);
        message.success({ content: 'Mailler gönderildi', key: 'bulk-email' });
      }

      // Form temizle ve modalı kapat
      setEmailModalVisible(false);
      emailForm.resetFields();
      setSelectedRowKeys([]);
      setFileList([]);
    } catch (err) {
      console.error('Toplu e-posta gönderme hatası:', err);
      message.error({ content: 'Mail gönderilemedi: ' + (err instanceof Error ? err.message : 'Bilinmeyen hata'), key: 'bulk-email' });
    } finally {
      setEmailSending(false);
    }
  };

  // Show email preview
  const showEmailPreview = async () => {
    try {
      const values = await emailForm.validateFields();
      setPreviewSubject(values.subject);
      setPreviewContent(values.content);
      setPreviewVisible(true);
    } catch (err) {
      message.error('Lütfen tüm alanları doldurun');
    }
  };

  // Yayıncılara toplu email gönderme
  const sendEmailToPublishers = async (emails: string[], subject: string, content: string, fromEmail?: string) => {
    if (!emails || emails.length === 0) return;

    try {
      // SendGrid API'sini kullanarak e-posta gönder
      const response = await fetch(`${API_CONFIG.BASE_URL}/send-email.php`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem(SECURITY_CONFIG.TOKEN_KEY)}`
        },
        body: JSON.stringify({
          emails: emails,
          subject: subject,
          content: content,
          fromEmail: fromEmail
        })
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'E-posta gönderimi başarısız');
      }

      return result;
    } catch (error) {
      console.error('SendGrid e-posta gönderme hatası:', error);
      throw error;
    }
  };

  // Toplu WhatsApp mesaj gönderme
  const sendBulkWhatsApp = async () => {
    try {
      const values = await whatsappForm.validateFields();

      // Eğer hiç yayıncı seçilmemişse, mesaj göster ama işlemi durdurmadan devam et
      let recipients: Publisher[] = [];

      if (!Array.isArray(selectedRowKeys) || selectedRowKeys.length === 0) {
        message.info('Hiçbir yayıncı seçmediniz. Tablo üzerinde seçim yapmak istediğiniz yayıncıları işaretleyebilirsiniz.');

        // filteredPublishers'ın dizi olduğundan emin ol
        if (!Array.isArray(filteredPublishers)) {
          throw new Error('Filtrelenmiş yayıncı listesi alınamadı');
        }

        recipients = filteredPublishers.filter(p => p && p.telefon);
      } else {
        // effectivePublisherList'in dizi olduğundan emin ol
        if (!Array.isArray(effectivePublisherList)) {
          throw new Error('Etkin yayıncı listesi alınamadı');
        }

        recipients = effectivePublisherList.filter(p =>
          p && selectedRowKeys.includes(p.id as React.Key) && p.telefon
        );
      }

      if (recipients.length === 0) {
        message.error('Geçerli telefon numarası olan yayıncı bulunamadı');
        return;
      }

      // Her yayıncı için WhatsApp linkini aç
      let successCount = 0;
      let errorCount = 0;

      message.loading({ content: 'WhatsApp mesajları hazırlanıyor...', key: 'whatsapp-sending' });

      for (let index = 0; index < recipients.length; index++) {
        const recipient = recipients[index];
        try {
          if (!recipient.telefon) {
            console.warn(`Telefon numarası yok: ${recipient.username || recipient.isim_soyisim || 'Bilinmeyen kullanıcı'}`);
            errorCount++;
            continue;
          }

          let phone = recipient.telefon.toString().replace(/\s+/g, '');
          if (phone.startsWith('0')) {
            phone = phone.substring(1);
          }
          if (!phone.startsWith('+')) {
            phone = '+90' + phone;
          }

          const messageText = values.content.replace('{username}', recipient.isim_soyisim || recipient.username || '');
          const whatsappUrl = `https://wa.me/${phone}?text=${encodeURIComponent(messageText)}`;

          // Eş zamanlı açmayı önlemek için gecikme ekle
          await new Promise(resolve => setTimeout(resolve, 300));

          window.open(whatsappUrl, '_blank');
          successCount++;
        } catch (err) {
          console.error(`WhatsApp mesajı hazırlanırken hata: ${recipient.username || recipient.isim_soyisim || 'Bilinmeyen kullanıcı'}`, err);
          errorCount++;
        }
      }

      message.success({
        content: `WhatsApp mesajları açılıyor... (Başarılı: ${successCount}, Hatalı: ${errorCount})`,
        key: 'whatsapp-sending'
      });

      setWhatsappModalVisible(false);
      whatsappForm.resetFields();

    } catch (err) {
      console.error('Toplu WhatsApp gönderme hatası:', err);
      message.error({
        content: 'WhatsApp mesajları gönderilemedi: ' + (err instanceof Error ? err.message : 'Bilinmeyen hata'),
        key: 'whatsapp-sending'
      });
    }
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: (keys: React.Key[]) => setSelectedRowKeys(keys),
  };

  // Inline hücre güncelleme fonksiyonu
  const handleCellSave = async (record: Publisher, dataIndex: string, value: any) => {
    const prevValue = (record as any)[dataIndex];
    let sendValue = value;
    if (dataIndex === 'dogum_tarihi' && value) {
      // Tarih inputunu her zaman YYYY-MM-DD formatında gönder
      sendValue = moment(value).format('YYYY-MM-DD');
    }
    if (prevValue === sendValue) {
      setEditingCell(null);
      return;
    }
    setLoading(true);
    try {
      const updated = { ...record, [dataIndex]: sendValue };
      let apiResult = { success: true };

      // Development mod kontrolü
      if (!IS_DEVELOPMENT) {
        // Token'ı SECURITY_CONFIG.TOKEN_KEY ile localStorage'dan al
        let token = localStorage.getItem(SECURITY_CONFIG.TOKEN_KEY) || '';

        // Token yoksa kullanıcı bilgisinden almayı dene
        if (!token) {
          try {
            const storedUser = localStorage.getItem(SECURITY_CONFIG.USER_KEY);
            if (storedUser) {
              const userData = JSON.parse(storedUser);
              if (userData && userData.token) {
                token = userData.token;
              }
            }
          } catch (e) {
            console.error('Token alma hatası:', e);
          }
        }

        // Hala token yoksa, oturum hatası göster
        if (!token) {
          toast.error('Oturum bilgileriniz bulunamadı. Lütfen tekrar giriş yapın.');
          setTimeout(() => {
            window.location.href = '/login';
          }, 2000);
          return;
        }

        // PUT isteği için token ekle
        const response = await fetch(`${API_CONFIG.X_SITE_BASE_URL}${API_CONFIG.ENDPOINTS.PUBLISHERS}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ ...updated, id: record.id })
        });

        if (!response.ok) {
          const responseText = await response.text();

          // 401 hatası durumunda oturumun sonlandığını belirt
          if (response.status === 401) {
            toast.error('Oturumunuz sonlandı. Lütfen tekrar giriş yapın.');
            setTimeout(() => {
              window.location.href = '/login';
            }, 2000);
            return;
          }

          throw new Error(`Sunucu hatası: ${response.status} ${responseText}`);
        }

        try {
          apiResult = await response.json();
        } catch (e) {
          throw new Error('Sunucu yanıtı geçersiz format içeriyor');
        }

        if (!apiResult.success) throw new Error('Güncelleme başarısız');
        // Local state'te ilgili satırı güncelle
        setTableData(prev => prev.map(item => item.id === record.id ? updated : item));
      } else {
        setMockData(prev => prev.map(item => item.id === record.id ? updated : item));
      }
      toast.success('Güncellendi');
      setEditingCell(null);
      if (onDataChange) onDataChange((IS_DEVELOPMENT ? mockData : tableData).length);
    } catch (err: any) {
      toast.error('Güncelleme hatası: ' + (err?.message || ''));
      console.error('Güncelleme hatası detayı:', err);
      // Hata olursa eski değeri geri yükle
      if (!IS_DEVELOPMENT) {
        setTableData(prev => prev.map(item => item.id === record.id ? { ...item, [dataIndex]: prevValue } : item));
      } else {
        setMockData(prev => prev.map(item => item.id === record.id ? { ...item, [dataIndex]: prevValue } : item));
      }
      setEditingCell(null);
    }
    setLoading(false);
  };

  // Inline edit için hücre render fonksiyonu
  const renderEditableCell = (dataIndex: string) => (text: any, record: Publisher) => {
    // record.id null veya undefined olabilir, güvenli bir şekilde kontrol ediyoruz
    const isEditing = editingCell && editingCell.key === record.id && editingCell.dataIndex === dataIndex;
    if (isEditing) {
      let inputType = 'text';
      if (dataIndex === 'dogum_tarihi') inputType = 'date';
      return (
        <input
          type={inputType}
          autoFocus
          value={editingValue !== undefined ? editingValue : (dataIndex === 'dogum_tarihi' && text ? moment(text).format('YYYY-MM-DD') : text || '')}
          onChange={e => setEditingValue(inputType === 'date' ? e.target.value : e.target.value)}
          onBlur={e => handleCellSave(record, dataIndex, inputType === 'date' ? e.target.value : editingValue)}
          onKeyDown={e => {
            if (e.key === 'Enter') {
              handleCellSave(record, dataIndex, inputType === 'date' ? (e.target as HTMLInputElement).value : editingValue);
            } else if (e.key === 'Escape') {
              setEditingCell(null);
            }
          }}
          style={{ width: '100%' }}
        />
      );
    }
    return (
      <div
        style={{ minHeight: 24, cursor: 'pointer' }}
        onClick={() => {
          // record.id null veya undefined ise, düzenleme yapmıyoruz
          if (record.id !== undefined && record.id !== null) {
            setEditingCell({ key: record.id, dataIndex });
            setEditingValue(dataIndex === 'dogum_tarihi' && text ? moment(text).format('YYYY-MM-DD') : text);
          } else {
            console.warn("Düzenleme yapmak için ID'si olmayan kayıt:", record);
            message.warning("Bu kayıt düzenlenemez, kayıt ID'si eksik.");
          }
        }}
      >
        {dataIndex === 'dogum_tarihi' && text ? moment(text, 'YYYY-MM-DD').format('DD.MM.YYYY') : text || '-'}
      </div>
    );
  };

  // Mobil görünüm için tablo sütunlarını düzenleyelim
  const getTableColumns = () => {
    const BASE_URL = "https://x.tuberajans.com";
    const baseColumns: ColumnsType<Publisher> = [
      {
        title: ' ',
        dataIndex: 'profile_image',
        key: 'profile_image',
        width: 48,
        render: (_: any, record: Publisher) => {
          // Profil resmi yolu: API'den geliyorsa kullan, yoksa username ile oluştur
          // username kontrol edelim, boşsa veya null ise default avatar gösterelim
          if (!record.username) {
            return (
              <Avatar
                icon={<UserOutlined />}
                size={36}
                style={{ backgroundColor: '#f0f0f0', marginRight: 8 }}
                alt="Kullanıcı"
              />
            );
          }

          const imageUrl = record.profile_image
            ? `${BASE_URL}${record.profile_image}`
            : `${BASE_URL}/uploads/profile_images/${record.username}.jpg`;
          return (
            <Avatar
              src={imageUrl}
              icon={<UserOutlined />}
              size={36}
              style={{ backgroundColor: '#f0f0f0', marginRight: 8 }}
              alt={record.isim_soyisim || record.username || 'Kullanıcı'}
              onError={() => {
                // Resim yüklenemezse sadece icon göster
                console.log(`Profil resmi yüklenemedi: ${record.username}`);
                return false;
              }}
            />
          );
        },
        fixed: isMobileView ? undefined : 'left',
      },
      {
        title: 'İSİM SOYİSİM',
        dataIndex: 'isim_soyisim',
        key: 'isim_soyisim',
        sorter: (a: Publisher, b: Publisher) => {
          // null/undefined kontrolü yapıp güvenli bir şekilde karşılaştırıyoruz
          const nameA = a.isim_soyisim || '';
          const nameB = b.isim_soyisim || '';
          return nameA.localeCompare(nameB);
        },
        width: isMobileView ? 120 : 180,
        ellipsis: isMobileView,
        render: renderEditableCell('isim_soyisim'),
      },
      {
        title: 'KULLANICI ADI',
        dataIndex: 'username',
        key: 'username',
        sorter: (a: Publisher, b: Publisher) => {
          // null/undefined kontrolü yapıp güvenli bir şekilde karşılaştırıyoruz
          const usernameA = a.username || '';
          const usernameB = b.username || '';
          return usernameA.localeCompare(usernameB);
        },
        width: isMobileView ? 100 : 150,
        ellipsis: isMobileView,
        render: renderEditableCell('username'),
      },
      {
        title: 'TELEFON',
        dataIndex: 'telefon',
        key: 'telefon',
        width: isMobileView ? 120 : 150,
        ellipsis: isMobileView,
        render: renderEditableCell('telefon'),
      },
      {
        title: 'E-POSTA',
        dataIndex: 'mail',
        key: 'mail',
        width: isMobileView ? 150 : 200,
        ellipsis: isMobileView,
        render: renderEditableCell('mail'),
      },
      {
        title: 'DOĞUM TARİHİ',
        dataIndex: 'dogum_tarihi',
        key: 'dogum_tarihi',
        sorter: (a: Publisher, b: Publisher) => {
          // Geçerli tarih mi kontrolü yapıyoruz
          try {
            const birthDateA = a.dogum_tarihi ? new Date(a.dogum_tarihi) : new Date(0);
            const birthDateB = b.dogum_tarihi ? new Date(b.dogum_tarihi) : new Date(0);

            const timeA = isNaN(birthDateA.getTime()) ? 0 : birthDateA.getTime();
            const timeB = isNaN(birthDateB.getTime()) ? 0 : birthDateB.getTime();

            return timeA - timeB;
          } catch (error) {
            console.error("Tarih sıralaması hatası:", error);
            return 0;
          }
        },
        width: isMobileView ? 120 : 150,
        ellipsis: isMobileView,
        render: renderEditableCell('dogum_tarihi'),
      },
      {
        title: 'ŞEHİR',
        dataIndex: 'sehir',
        key: 'sehir',
        width: isMobileView ? 100 : 120,
        ellipsis: isMobileView,
        render: renderEditableCell('sehir'),
      },
      {
        title: 'MESLEK',
        dataIndex: 'meslek',
        key: 'meslek',
        width: isMobileView ? 100 : 120,
        ellipsis: isMobileView,
        render: renderEditableCell('meslek'),
      },
      {
        title: 'İŞLEMLER',
        key: 'actions',
        width: isMobileView ? 100 : 150,
        fixed: isMobileView ? 'right' : undefined,
        render: (_: any, record: Publisher) => (
          <div className={isMobileView ? "action-column-mobile" : "action-column"}>
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => {
                if (selectedRowKeys.length > 0) {
                  // Eğer seçili satırlar varsa, toplu silme işlemi yapılacak
                  setDeleteModalOpen(true);
                } else {
                  // Seçili satır yoksa, tek kayıt silme işlemi yapılacak
                  setDeleteTargetId(record.id ?? null);
                  setDeleteModalOpen(true);
                }
              }}
              size={isMobileView ? "small" : "middle"}
            />
            <Button
              type="text"
              icon={<WhatsAppOutlined />}
              style={{ color: '#25D366' }}
              onClick={() => handleWhatsAppMessage(record)}
              size={isMobileView ? "small" : "middle"}
            />
            <Button
              type="text"
              icon={<MailOutlined />}
              style={{ color: '#1890ff' }}
              onClick={() => handleEmailMessage(record)}
              size={isMobileView ? "small" : "middle"}
            />
          </div>
        ),
      },
    ];

    // Mobil görünümde bazı sütunları kaldır
    if (isMobileView) {
      return baseColumns.filter(col =>
        col.key !== 'isim_soyisim' &&
        col.key !== 'dogum_tarihi' &&
        col.key !== 'sehir' &&
        col.key !== 'meslek'
      );
    }

    return baseColumns;
  };

  // API'den veri geldiyse veya mock veri kullanıldığında onDataChange'i çağır
  useEffect(() => {
    const currentData = useBackupData ? mockData : publishersList;
    if (onDataChange && Array.isArray(currentData)) {
      onDataChange(currentData.length);
    }
  }, [useBackupData, mockData, publishersList, onDataChange]);

  // Veritabanındaki profil resmi yollarını güncelleme fonksiyonu
  const handleUpdateProfilePaths = async () => {
    const token = localStorage.getItem(SECURITY_CONFIG.TOKEN_KEY);
    if (!token) {
      message.error('Oturum bilgileriniz bulunamadı. Lütfen tekrar giriş yapın.');
      setTimeout(() => { window.location.href = '/login'; }, 1500);
      return;
    }

    message.loading({ content: 'Profil resmi yolları güncelleniyor...', key: 'update-paths' });

    try {
      const response = await fetch(`${API_CONFIG.X_SITE_BASE_URL}/update_profile_image_paths.php`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        console.error(`API error: ${response.status} ${response.statusText}`);
        const errorText = await response.text();
        console.error(`Error details: ${errorText}`);
        message.error({ content: 'Profil yolları güncellenemedi.', key: 'update-paths' });
        return;
      }

      const data = await response.json();
      if (data.success) {
        message.success({
          content: `Profil yolları başarıyla güncellendi! Publisher: ${data.publisher_updated}, Influencer: ${data.influencer_updated}`,
          key: 'update-paths'
        });
        // Tabloyu yenile
        if (typeof refetch === 'function') refetch();
      } else {
        message.error({ content: data.error || 'Profil yolları güncellenemedi.', key: 'update-paths' });
      }
    } catch (error) {
      console.error('Network error:', error);
      message.error({ content: 'Profil yolları güncellenirken hata oluştu.', key: 'update-paths' });
    }
  };

  // Tek kullanıcı için profil resmi çekme fonksiyonu
  const handleFetchSingleProfileImage = async (username: string) => {
    if (!username) {
      message.error('Kullanıcı adı bulunamadı.');
      return;
    }

    const token = localStorage.getItem(SECURITY_CONFIG.TOKEN_KEY);
    if (!token) {
      message.error('Oturum bilgileriniz bulunamadı. Lütfen tekrar giriş yapın.');
      setTimeout(() => { window.location.href = '/login'; }, 1500);
      return;
    }

    message.loading({ content: `${username} için profil resmi çekiliyor...`, key: 'single-profile-fetch' });

    try {
      const response = await fetch(`${API_CONFIG.X_SITE_BASE_URL}/fetch_profile_images.php`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ usernames: [username], type: 'publisher' })
      });

      if (!response.ok) {
        console.error(`API error for ${username}: ${response.status} ${response.statusText}`);
        const errorText = await response.text();
        console.error(`Error details for ${username}: ${errorText}`);
        message.error({ content: `${username} için profil resmi çekilemedi.`, key: 'single-profile-fetch' });
        return;
      }

      const data = await response.json();
      if (data.success && data.results && data.results[0] && data.results[0].success) {
        message.success({ content: `${username} için profil resmi başarıyla çekildi!`, key: 'single-profile-fetch' });
        // Tabloyu yenile
        if (typeof refetch === 'function') refetch();
      } else {
        console.warn(`Profile image fetch failed for ${username}:`, data.results ? data.results[0] : 'No results or unsuccessful');
        message.error({ content: `${username} için profil resmi çekilemedi.`, key: 'single-profile-fetch' });
      }
    } catch (error) {
      console.error(`Network error or other issue for ${username}:`, error);
      message.error({ content: `${username} için profil resmi çekilirken hata oluştu.`, key: 'single-profile-fetch' });
    }
  };

  // Profil Resimlerini Çek fonksiyonu (progress bar ile)
  const handleFetchProfileImages = async () => {
    const usernames = tableData.map(p => p.username).filter(Boolean);
    if (usernames.length === 0) {
      message.info('Profil resmi çekilecek yayıncı bulunamadı.');
      setProgress({ total: 0, current: 0, success: 0, fail: 0, running: false });
      localStorage.removeItem('publisherProfileImageProgress');
      return;
    }

    setProgress({ total: usernames.length, current: 0, success: 0, fail: 0, running: true });
    localStorage.setItem('publisherProfileImageProgress', JSON.stringify({ total: usernames.length, current: 0, success: 0, fail: 0, running: true }));

    const token = localStorage.getItem(SECURITY_CONFIG.TOKEN_KEY);
    if (!token) {
      message.error('Oturum bilgileriniz bulunamadı. Lütfen tekrar giriş yapın.');
      setProgress({ total: 0, current: 0, success: 0, fail: 0, running: false });
      localStorage.removeItem('publisherProfileImageProgress');
      setTimeout(() => { window.location.href = '/login'; }, 1500);
      return;
    }

    let successCount = 0;
    let failCount = 0;

    for (let i = 0; i < usernames.length; i++) {
      const currentUsername = usernames[i];
      try {
        const response = await fetch(`${API_CONFIG.X_SITE_BASE_URL}/fetch_profile_images.php`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ usernames: [currentUsername], type: 'publisher' })
        });

        if (!response.ok) {
          console.error(`API error for ${currentUsername}: ${response.status} ${response.statusText}`);
          try {
            const errorText = await response.text();
            console.error(`Error details for ${currentUsername}: ${errorText}`);
          } catch (textError) {
            console.error(`Could not get error text for ${currentUsername}:`, textError);
          }
          failCount++;
        } else {
          try {
            const data = await response.json();
            if (data.success && data.results && data.results[0] && data.results[0].success) {
              successCount++;
            } else {
              failCount++;
              console.warn(`Profile image fetch failed for ${currentUsername}:`, data.results ? data.results[0] : 'No results or unsuccessful');
            }
          } catch (jsonError) {
            console.error(`JSON parse error for ${currentUsername}:`, jsonError);
            // JSON parse hatası durumunda ham yanıtı loglamaya çalış
            try {
                const responseText = await response.text(); // Define responseText here
                console.error(`Non-JSON response for ${currentUsername}:`, responseText);
            } catch (textParseError) {
                console.error(`Could not get text from non-JSON response for ${currentUsername}:`, textParseError);
            }
            failCount++;
          }
        }
      } catch (networkError) {
        console.error(`Network error or other issue for ${currentUsername}:`, networkError);
        failCount++;
      }
      setProgress({ total: usernames.length, current: i + 1, success: successCount, fail: failCount, running: true });
      localStorage.setItem('publisherProfileImageProgress', JSON.stringify({ total: usernames.length, current: i + 1, success: successCount, fail: failCount, running: true }));
    }

    setProgress({ total: usernames.length, current: usernames.length, success: successCount, fail: failCount, running: false });
    localStorage.removeItem('publisherProfileImageProgress');
    message.success(`Profil resimleri çekildi! Başarılı: ${successCount}, Başarısız: ${failCount}`);
    if (typeof refetch === 'function') refetch();
  };

  // Sayfa yenilendiğinde progress bar devam etsin
  useEffect(() => {
    const saved = localStorage.getItem('publisherProfileImageProgress');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        if (
          parsed &&
          typeof parsed.total === 'number' &&
          typeof parsed.current === 'number' &&
          typeof parsed.success === 'number' &&
          typeof parsed.fail === 'number' &&
          typeof parsed.running === 'boolean'
        ) {
          if (parsed.running) { // Sadece hala çalışıyorsa devam et
            setProgress(parsed);
          } else {
            localStorage.removeItem('publisherProfileImageProgress'); // İşlem bitmişse temizle
          }
        } else {
           // Beklenmedik yapı, temizle
           localStorage.removeItem('publisherProfileImageProgress');
        }
      } catch (e) {
        // Error parsing publisherProfileImageProgress from localStorage - temizle
        localStorage.removeItem('publisherProfileImageProgress'); // Bozuk veriyi temizle
      }
    }
  }, []);

  // Üretim ortamında API hatası varsa ve mock veri kullanılmıyorsa hata mesajı göster
  if (apiError && !useBackupData && !IS_DEVELOPMENT) {
    return (
      <ErrorBoundary>
        <div className="m-6">
          <Alert
            message="Veri Yükleme Hatası"
            description={
              <div>
                <p>API sunucusu yanıt vermiyor veya bir sorun oluştu: {apiError}</p>
                <Button
                  type="primary"
                  onClick={() => setUseBackupData(true)}
                  className="mt-4"
                >
                  Demo Verileri İle Devam Et
                </Button>
              </div>
            }
            type="error"
            showIcon
          />
        </div>
      </ErrorBoundary>
    );
  }

  // Yükleme durumu
  if (loading) {
    return (
      <ErrorBoundary>
        <Spin tip="Yükleniyor..." size="large" className="flex justify-center items-center h-full p-6" />
      </ErrorBoundary>
    );
  }

  // Toplu silme işlemi için fonksiyon
  const handleBulkDelete = async () => {
    if (selectedRowKeys.length === 0) {
      toast.error('Silinecek yayıncı seçilmedi.');
      return;
    }

    setLoading(true);
    try {
      let successCount = 0;
      let errorCount = 0;

      if (!IS_DEVELOPMENT) {
        // Token'ı SECURITY_CONFIG.TOKEN_KEY ile localStorage'dan al
        let token = localStorage.getItem(SECURITY_CONFIG.TOKEN_KEY) || '';

        // Token yoksa kullanıcı bilgisinden almayı dene
        if (!token) {
          try {
            const storedUser = localStorage.getItem(SECURITY_CONFIG.USER_KEY);
            if (storedUser) {
              const userData = JSON.parse(storedUser);
              if (userData && userData.token) {
                token = userData.token;
              }
            }
          } catch (e) {
            console.error('Token alma hatası:', e);
          }
        }

        // Hala token yoksa, oturum hatası göster
        if (!token) {
          toast.error('Oturum bilgileriniz bulunamadı. Lütfen tekrar giriş yapın.');
          setTimeout(() => {
            window.location.href = '/login';
          }, 2000);
          return;
        }

        // Her bir seçili ID için silme işlemi yap
        for (const key of selectedRowKeys) {
          const id = typeof key === 'string' ? parseInt(key, 10) : key;

          try {
            const response = await fetch(`${API_CONFIG.X_SITE_BASE_URL}${API_CONFIG.ENDPOINTS.PUBLISHERS}?id=${id}`, {
              method: 'DELETE',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
              },
              body: JSON.stringify({ id })
            });

            if (!response.ok) {
              const responseText = await response.text();

              // 401 hatası durumunda oturumun sonlandığını belirt
              if (response.status === 401) {
                toast.error('Oturumunuz sonlandı. Lütfen tekrar giriş yapın.');
                setTimeout(() => {
                  window.location.href = '/login';
                }, 2000);
                return;
              }

              throw new Error(`Sunucu hatası: ${response.status} ${responseText}`);
            }

            const data = await response.json();
            if (!data.success) {
              throw new Error(data.error || 'Silme başarısız');
            }

            successCount++;
          } catch (error) {
            console.error(`ID ${id} silinirken hata:`, error);
            errorCount++;
          }
        }

        // Başarılı silme işlemleri olduysa tablodan da kaldır
        if (successCount > 0) {
          const newList = tableData.filter(item => !selectedRowKeys.includes(item.id as React.Key));
          setTableData(newList);
          const totalAfterDelete = newList.length;
          const lastPage = Math.ceil(totalAfterDelete / pageSize) || 1;
          if (currentPage > lastPage) {
            setCurrentPage(Math.max(1, lastPage));
          }
          if (onDataChange) onDataChange(newList.length);
        }
      } else {
        // Geliştirme ortamında mock veriyi güncelle
        const newList = mockData.filter(item => !selectedRowKeys.includes(item.id as React.Key));
        setMockData(newList);
        successCount = selectedRowKeys.length;

        const totalAfterDelete = newList.length;
        const lastPage = Math.ceil(totalAfterDelete / pageSize) || 1;
        if (currentPage > lastPage) {
          setCurrentPage(Math.max(1, lastPage));
        }
        if (onDataChange) onDataChange(newList.length);
      }

      // Sonucu bildir
      if (errorCount === 0) {
        toast.success(`${successCount} yayıncı başarıyla silindi`);
      } else {
        toast.success(`${successCount} yayıncı silindi, ${errorCount} yayıncı silinemedi`);
      }

      // Seçimleri temizle
      setSelectedRowKeys([]);

    } catch (err: any) {
      toast.error('Yayıncılar silinirken bir hata oluştu: ' + (err?.message || ''));
      console.error('Toplu yayıncı silme hatası:', err);
    }
    setLoading(false);
  };

  return (
    <ErrorBoundary>
      <div className={hideHeader ? "bg-transparent p-0" : "m-6"}>
        {/* Başlık bölümü (hideHeader true ise gizle) */}
        {!hideHeader && (
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-semibold text-violet-600">Yayıncılar</h1>
            <span className="bg-violet-100 text-violet-800 text-sm font-medium px-2.5 py-1 rounded">
              Toplam: {(publishersList?.length || mockData?.length || 0)}
            </span>
          </div>
        )}

        <div className={`bg-white dark:bg-gray-800 ${hideHeader ? 'rounded-none shadow-none border-0' : 'rounded-xl shadow-lg p-6 border border-gray-100 dark:border-gray-700'} ${darkTheme ? 'dark-theme' : ''}`}>
          {/* Progress Bar */}
          {progress.running && (
            <div style={{ marginBottom: 16 }}>
              <Progress percent={Math.round((progress.current / progress.total) * 100)} status="active" />
              <div style={{ marginTop: 8, fontSize: 14 }}>
                {`Çekilen: ${progress.current} / ${progress.total} | Başarılı: ${progress.success} | Hatalı: ${progress.fail}`}
              </div>
            </div>
          )}
          {/* Arama ve Filtreler */}
          <div className="flex flex-wrap gap-4 mb-6 px-6 pt-6 pb-2">
            <div className="relative" style={{ width: 384 }}>
              <SearchOutlined className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Yayıncı ara (İsim, Mail, Kullanıcı Adı...)"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm('')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <X className="w-4 h-4" />
                </button>
              )}
            </div>

            {/* Tüm Butonlar */}
            <div className="flex flex-wrap gap-2 items-center">
              <input
                id="excel-import-input"
                type="file"
                accept=".xlsx, .xls"
                onChange={importFromExcel}
                className="hidden"
              />
              <Button
                className="flex items-center"
                onClick={() => setEmailModalVisible(true)}
              >
                <Mail className="h-4 w-4 mr-2" />
                Toplu Mail Gönder
              </Button>
              <Button
                className="flex items-center"
                onClick={() => setWhatsappModalVisible(true)}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  stroke="none"
                  className="h-4 w-4 mr-2"
                >
                  <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"/>
                </svg>
                Toplu WhatsApp
              </Button>
              <Dropdown
                overlay={
                  <Menu>
                    <Menu.Item key="fetchProfileImages" icon={<CloudDownloadOutlined />} onClick={handleFetchProfileImages}>
                      Profil Resimlerini Çek
                    </Menu.Item>
                    <Menu.Item key="updateProfilePaths" icon={<ReloadOutlined />} onClick={handleUpdateProfilePaths}>
                      Profil Yollarını Güncelle
                    </Menu.Item>
                    <Menu.Item key="1" icon={<PlusOutlined />} onClick={handleAddNew}>
                      Yeni Yayıncı
                    </Menu.Item>
                    <Menu.Item key="2" icon={<DownloadOutlined />} onClick={exportToExcel}>
                      Excel'e Aktar
                    </Menu.Item>
                    <Menu.Item key="3" icon={<UploadOutlined />} onClick={() => document.getElementById('excel-import-input')?.click()}>
                      Excel'den İçe Aktar
                    </Menu.Item>
                  </Menu>
                }
                trigger={['click']}
              >
                <Button icon={<EllipsisOutlined />} type="text" style={{ border: 'none', background: 'transparent', boxShadow: 'none' }} />
              </Dropdown>
            </div>
          </div>

          {/* Seçili satırlar varsa göster */}
          {selectedRowKeys.length > 0 && (
            <div style={{ marginBottom: 16, padding: '0 24px' }}>
              <Alert
                message={
                  <span>
                    <strong>{selectedRowKeys.length}</strong> yayıncı seçildi.{' '}
                    <Button
                      type="link"
                      size="small"
                      onClick={() => setSelectedRowKeys([])}
                      style={{ padding: '0 4px' }}
                    >
                      Seçimi Temizle
                    </Button>
                  </span>
                }
                type="info"
                showIcon
              />
            </div>
          )}

          {/* Yayıncılar Tablosu */}
          <div className="w-full px-0">
            <Table
              rowSelection={rowSelection}
              columns={getTableColumns()}
              dataSource={filteredPublishers}
              loading={loading}
              rowKey={(record) => {
                // Güvenli rowKey implementasyonu
                if (!record) return Math.random().toString();

                if (record.id !== undefined && record.id !== null) {
                  return record.id.toString();
                }

                if (record.username) {
                  return `username-${record.username}`;
                }

                return `temp-${Math.random()}`;
              }}
              pagination={{
                current: currentPage,
                pageSize: pageSize,
                onChange: (page: number, pageSize: number) => {
                  setCurrentPage(page);
                  setPageSize(pageSize);
                },
                position: ['bottomCenter'],
                showSizeChanger: true,
                pageSizeOptions: [10, 20, 50, 100]
              }}
              locale={{
                emptyText: effectivePublisherList.length > 0
                  ? 'Arama kriterlerine uygun yayıncı bulunamadı.'
                  : 'Henüz yayıncı eklenmemiş.'
              }}
              className="publishers-table w-full"
              scroll={{ x: 'max-content' }}
              style={{ width: '100%' }}
            />
          </div>
        </div>

        {/* Ekleme/Düzenleme Modalı */}
        <Modal
          title={editingPublisher ? "Yayıncı Düzenle" : "Yeni Yayıncı Ekle"}
          visible={showAddEditModal}
          onCancel={() => { setShowAddEditModal(false); setEditingPublisher(null); form.resetFields(); }}
          footer={null}
          destroyOnClose
          width={600}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleFormSubmit}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4">
              <Form.Item name="isim_soyisim" label="İsim Soyisim">
                <Input placeholder="Örn: Ahmet Yılmaz" />
              </Form.Item>
              <Form.Item name="username" label="Kullanıcı Adı" rules={[{ required: true, message: 'Lütfen kullanıcı adı girin' }]}>
                <Input placeholder="Örn: ahmetyilmaz" />
              </Form.Item>
              <Form.Item name="telefon" label="Telefon">
                <Input placeholder="Örn: 5551234567" />
              </Form.Item>
              <Form.Item name="mail" label="E-posta" rules={[{ required: false, type: 'email', message: 'Lütfen geçerli bir e-posta girin' }]}>
                <Input placeholder="Örn: <EMAIL>" />
              </Form.Item>
              <Form.Item name="dogum_tarihi" label="Doğum Tarihi">
                <DatePicker format="DD.MM.YYYY" style={{ width: '100%' }} placeholder="Tarih Seçin" />
              </Form.Item>
              <Form.Item name="sehir" label="Şehir">
                <Input placeholder="Örn: İstanbul" />
              </Form.Item>
              <Form.Item name="meslek" label="Meslek">
                <Input placeholder="Örn: Yayıncı" />
              </Form.Item>
            </div>
            <Form.Item className="mt-4">
              <Button type="primary" htmlType="submit" loading={loading} block size="large"
                className="bg-indigo-600 hover:bg-indigo-700 border-0 text-white">
                {editingPublisher ? 'Değişiklikleri Kaydet' : 'Yeni Yayıncı Oluştur'}
              </Button>
            </Form.Item>
          </Form>
        </Modal>

        {/* Toplu E-posta Gönderme Modal */}
        <Modal
          title="Toplu E-posta Gönder"
          open={emailModalVisible}
          onCancel={() => setEmailModalVisible(false)}
          footer={[
            <Button
              key="back"
              onClick={() => setEmailModalVisible(false)}
              style={{ borderColor: darkMode ? '#6b7280' : '#d1d5db', color: darkMode ? '#e5e7eb' : '#374151' }}
            >
              İptal
            </Button>,
            <Button
              key="preview"
              onClick={showEmailPreview}
              style={{ borderColor: '#1890ff', color: '#1890ff' }}
            >
              Önizle
            </Button>,
            <Button
              key="submit"
              type="primary"
              onClick={sendBulkEmail}
              loading={emailSending}
              style={{ backgroundColor: '#1890ff', borderColor: '#1890ff', color: 'white' }}
            >
              Gönder
            </Button>
          ]}
          width={700}
          centered
          bodyStyle={{
            paddingRight: 16
          }}
          style={{ top: 20 }}
        >
          <Form form={emailForm} layout="vertical">
            <Form.Item label="Şablon">
              <Select
                placeholder="Şablon seçin (opsiyonel)"
                value={selectedTemplateName}
                onChange={(value) => {
                  setSelectedTemplateName(value);
                  const selectedTemplate = templates.find(t => t.name === value);
                  if (selectedTemplate) {
                    emailForm.setFieldsValue({
                      subject: selectedTemplate.subject,
                      content: selectedTemplate.content
                    });
                  }
                }}
                allowClear
                style={{width: '100%'}}
                dropdownRender={menu => (
                  <>
                    {menu}
                    <Divider style={{ margin: '8px 0' }} />
                    <div style={{ display: 'flex', flexWrap: 'nowrap', padding: '8px' }}>
                      <Button
                        type="link"
                        onClick={() => setNewTemplateModalVisible(true)}
                        icon={<Plus size={14} />}
                      >
                        Yeni Şablon Ekle
                      </Button>
                    </div>
                  </>
                )}
              >
                {templates.map(t => (
                  <Select.Option key={t.name} value={t.name}>{t.name}</Select.Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item
              name="fromEmail"
              label="Gönderen E-posta"
              rules={[{ required: true, message: 'Lütfen gönderen e-posta seçin' }]}
              initialValue="<EMAIL>"
            >
              <Select placeholder="Gönderen e-posta seçin">
                <Select.Option value="<EMAIL>">İşbirliği Ekibi (<EMAIL>)</Select.Option>
                <Select.Option value="<EMAIL>">Rapor Ekibi (<EMAIL>)</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item
              name="subject"
              label="Konu"
              rules={[{ required: true, message: 'Lütfen bir konu girin!' }]}
            >
              <Input placeholder="E-posta Konusu" />
            </Form.Item>
            <Form.Item
              name="content"
              label="İçerik"
              rules={[{ required: true, message: 'Lütfen bir mesaj girin!' }]}
            >
              <Input.TextArea
                placeholder="E-posta İçeriği - {username} ifadesi yayıncının adıyla değiştirilecektir."
                rows={6}
              />
            </Form.Item>
            <Form.Item label="Dosya Ekle">
              <AntUpload
                listType="picture"
                fileList={fileList}
                onChange={({ fileList }) => setFileList(fileList)}
                beforeUpload={() => false} // Otomatik yüklemeyi önle
                multiple
              >
                <Button icon={<UploadOutlined />}>Dosya Seç</Button>
              </AntUpload>
              <div className="text-xs text-gray-500 mt-1">
                Desteklenen dosya türleri: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG, GIF (maks. 10MB)
              </div>
            </Form.Item>
          </Form>
        </Modal>

        {/* E-posta Önizleme Modalı */}
        <Modal
          title={`E-posta Önizleme: ${previewSubject}`}
          open={previewVisible}
          onCancel={() => setPreviewVisible(false)}
          footer={[
            <Button
              key="back"
              onClick={() => setPreviewVisible(false)}
              style={{ borderColor: darkMode ? '#6b7280' : '#d1d5db', color: darkMode ? '#e5e7eb' : '#374151' }}
            >
              Kapat
            </Button>
          ]}
          width={700}
        >
          <div className="email-preview-container">
            <div style={{
              border: '1px solid #e6e6e6',
              borderRadius: '4px',
              padding: '20px',
              backgroundColor: '#fff'
            }}>
              <div style={{ marginBottom: '15px', borderBottom: '1px solid #f0f0f0', paddingBottom: '10px' }}>
                <div><strong>Konu:</strong> {previewSubject}</div>
                <div><strong>Ekler:</strong> {fileList.length > 0 ? fileList.map(f => f.name).join(', ') : 'Yok'}</div>
              </div>
              <div
                dangerouslySetInnerHTML={{ __html: previewContent }}
                style={{ padding: '10px', minHeight: '300px' }}
              />
            </div>
          </div>
        </Modal>

        {/* Toplu WhatsApp Gönderme Modal */}
        <Modal
          title="Toplu WhatsApp Mesajı Gönder"
          open={whatsappModalVisible}
          onCancel={() => setWhatsappModalVisible(false)}
          footer={[
            <Button
              key="back"
              onClick={() => setWhatsappModalVisible(false)}
              style={{ borderColor: darkMode ? '#6b7280' : '#d1d5db', color: darkMode ? '#e5e7eb' : '#374151' }}
            >
              İptal
            </Button>,
            <Button
              key="submit"
              type="primary"
              onClick={sendBulkWhatsApp}
              style={{ backgroundColor: '#25D366', borderColor: '#25D366', color: 'white' }}
            >
              Gönder
            </Button>
          ]}
          width={600}
          centered
          bodyStyle={{
            paddingRight: 16
          }}
          style={{ top: 20 }}
        >
          <Form form={whatsappForm} layout="vertical">
            <Form.Item label="Şablon">
              <Select
                placeholder="Şablon seçin (opsiyonel)"
                value={selectedWhatsappTemplateName}
                onChange={(value) => {
                  setSelectedWhatsappTemplateName(value);
                  const selectedTemplate = whatsappTemplates.find(t => t.name === value);
                  if (selectedTemplate) {
                    whatsappForm.setFieldsValue({
                      content: selectedTemplate.content
                    });
                  }
                }}
                allowClear
                style={{width: '100%'}}
                dropdownRender={menu => (
                  <>
                    {menu}
                    <Divider style={{ margin: '8px 0' }} />
                    <div style={{ display: 'flex', justifyContent: 'space-between', padding: '8px' }}>
                      <Button
                        type="link"
                        onClick={() => setNewWhatsappTemplateModalVisible(true)}
                        icon={<PlusOutlined />}
                      >
                        Yeni Şablon Ekle
                      </Button>
                      {selectedWhatsappTemplateName && (
                        <Space>
                          <Button
                            type="link"
                            onClick={() => {
                              const template = whatsappTemplates.find(t => t.name === selectedWhatsappTemplateName);
                              if (template) editWhatsappTemplate(template);
                            }}
                            icon={<EditOutlined />}
                          >
                            Düzenle
                          </Button>
                          <Popconfirm
                            title="Bu şablonu silmek istediğinize emin misiniz?"
                            onConfirm={() => deleteWhatsappTemplate(selectedWhatsappTemplateName)}
                            okText="Evet"
                            cancelText="Hayır"
                          >
                            <Button
                              type="link"
                              danger
                              icon={<DeleteOutlined />}
                            >
                              Sil
                            </Button>
                          </Popconfirm>
                        </Space>
                      )}
                    </div>
                  </>
                )}
              >
                {whatsappTemplates.map(t => (
                  <Select.Option key={t.name} value={t.name}>{t.name}</Select.Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item
              name="content"
              label="Mesaj"
              rules={[{ required: true, message: 'Lütfen bir mesaj girin!' }]}
            >
              <Input.TextArea
                placeholder="WhatsApp Mesajı - {username} ifadesi yayıncının adıyla değiştirilecektir."
                rows={6}
              />
            </Form.Item>
          </Form>
        </Modal>

        {/* Yeni E-posta Şablonu Oluşturma Modal */}
        <Modal
          title="Yeni E-posta Şablonu"
          open={newTemplateModalVisible}
          onCancel={() => setNewTemplateModalVisible(false)}
          footer={[
            <Button key="back" onClick={() => setNewTemplateModalVisible(false)}>
              İptal
            </Button>,
            <Button key="submit" type="primary" onClick={() => templateForm.submit()} style={{ backgroundColor: '#1890ff', borderColor: '#1890ff' }}>
              Kaydet
            </Button>,
          ]}
          width={600}
          centered
          bodyStyle={{
            paddingRight: 16
          }}
          style={{ top: 20 }}
        >
          <Form form={templateForm} layout="vertical" onFinish={addTemplate}>
            <Form.Item
              name="name"
              label="Şablon Adı"
              rules={[{ required: true, message: 'Lütfen şablon adı girin!' }]}
            >
              <Input placeholder="Örn: Marka İşbirliği Teklifi" />
            </Form.Item>
            <Form.Item
              name="subject"
              label="E-posta Konusu"
              rules={[{ required: true, message: 'Lütfen e-posta konusu girin!' }]}
            >
              <Input placeholder="Örn: Yeni İşbirliği Teklifi" />
            </Form.Item>
            <Form.Item
              name="content"
              label="E-posta İçeriği"
              rules={[{ required: true, message: 'Lütfen e-posta içeriği girin!' }]}
            >
              <Input.TextArea
                placeholder="E-posta içeriği... {username} ifadesi yayıncının adıyla değiştirilecektir."
                rows={6}
              />
            </Form.Item>
          </Form>
        </Modal>

        {/* Yeni WhatsApp Şablonu Oluşturma Modal */}
        <Modal
          title="Yeni WhatsApp Şablonu"
          open={newWhatsappTemplateModalVisible}
          onCancel={() => setNewWhatsappTemplateModalVisible(false)}
          footer={[
            <Button key="back" onClick={() => setNewWhatsappTemplateModalVisible(false)}>
              İptal
            </Button>,
            <Button key="submit" type="primary" onClick={() => whatsappTemplateForm.submit()}>
              Kaydet
            </Button>,
          ]}
          width={600}
          centered
          bodyStyle={{
            paddingRight: 16
          }}
          style={{ top: 20 }}
        >
          <Form form={whatsappTemplateForm} layout="vertical" onFinish={addWhatsappTemplate}>
            <Form.Item
              name="name"
              label="Şablon Adı"
              rules={[{ required: true, message: 'Lütfen şablon adı girin!' }]}
            >
              <Input placeholder="Örn: Etkinlik Daveti" />
            </Form.Item>
            <Form.Item
              name="content"
              label="Mesaj İçeriği"
              rules={[{ required: true, message: 'Lütfen mesaj içeriği girin!' }]}
            >
              <Input.TextArea
                placeholder="Mesaj içeriği... {username} ifadesi yayıncının adıyla değiştirilecektir."
                rows={6}
              />
            </Form.Item>
          </Form>
        </Modal>

        {/* WhatsApp Şablonu Düzenleme Modal */}
        <Modal
          title="WhatsApp Şablonunu Düzenle"
          open={editWhatsappTemplateModalVisible}
          onCancel={() => {
            setEditWhatsappTemplateModalVisible(false);
            setCurrentTemplateForEdit(null);
          }}
          footer={[
            <Button key="back" onClick={() => {
              setEditWhatsappTemplateModalVisible(false);
              setCurrentTemplateForEdit(null);
            }}>
              İptal
            </Button>,
            <Button key="submit" type="primary" onClick={() => whatsappTemplateForm.submit()}>
              Güncelle
            </Button>,
          ]}
          width={600}
          centered
          bodyStyle={{
            paddingRight: 16
          }}
          style={{ top: 20 }}
        >
          <Form form={whatsappTemplateForm} layout="vertical" onFinish={updateWhatsappTemplate}>
            <Form.Item
              name="name"
              label="Şablon Adı"
              rules={[{ required: true, message: 'Lütfen şablon adı girin!' }]}
            >
              <Input placeholder="Örn: Etkinlik Daveti" />
            </Form.Item>
            <Form.Item
              name="content"
              label="Mesaj İçeriği"
              rules={[{ required: true, message: 'Lütfen mesaj içeriği girin!' }]}
            >
              <Input.TextArea
                placeholder="Mesaj içeriği... {username} ifadesi yayıncının adıyla değiştirilecektir."
                rows={6}
              />
            </Form.Item>
          </Form>
        </Modal>

        {/* Silme Onayı Modalı */}
        <Modal
          open={deleteModalOpen}
          onCancel={() => setDeleteModalOpen(false)}
          onOk={() => {
            if (selectedRowKeys.length > 0) {
              handleBulkDelete();
            } else if (deleteTargetId) {
              handleDeleteConfirm(deleteTargetId);
            }
            setDeleteModalOpen(false);
          }}
          okText="Evet, Sil"
          cancelText="Vazgeç"
          title={selectedRowKeys.length > 0 ? "Yayıncıları Sil" : "Yayıncıyı Sil"}
          centered
          bodyStyle={{ padding: 24 }}
          style={{ top: 120 }}
        >
          {selectedRowKeys.length > 0 ? (
            <p>{selectedRowKeys.length} yayıncıyı silmek istediğinize emin misiniz?</p>
          ) : (
            <p>Bu yayıncıyı silmek istediğinize emin misiniz?</p>
          )}
        </Modal>

        {/* Tekil E-posta Modal */}
        {selectedPublisherForEmail && (
          <SingleEmailModal
            isOpen={singleEmailModalVisible}
            onClose={() => {
              setSingleEmailModalVisible(false);
              setSelectedPublisherForEmail(null);
            }}
            recipientEmail={selectedPublisherForEmail.mail || ''}
            recipientName={selectedPublisherForEmail.isim_soyisim || selectedPublisherForEmail.username || 'Yayıncı'}
            defaultSubject="Tuber Ajans - İletişim"
          />
        )}
      </div>
    </ErrorBoundary>
  );
}