import sys
import logging
import time
import traceback
import threading
import subprocess
from database_manager import DatabaseManager
from scraper_thread import ScraperThread
from status_checker import <PERSON><PERSON>heckerThread
from message_sender_thread import MessageSenderThread
from system_monitor import SystemMonitor

# Basit loglama - DUPLIKASYON ÖNLEME
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler("app.log")
    ]
)

logger = logging.getLogger(__name__)

class MainApp:
    """Basitleştirilmiş MainApp - SIRALI ÇALIŞMA"""

    def __init__(self):
        self.db_manager = DatabaseManager()
        self.cycle_running = False

        # Thread'ler - sadece bir tane aktif olacak
        self.current_thread = None
        self.current_phase = None  # 'scraper', 'status_checker', 'message_sender'

        # Ayarlar
        self.duration = 1  # Varsayılan döngü süresi (dakika)
        self.headless = False
        self.message_mode = 'both'

        # STANDART Chrome yolları - TÜM THREAD'LERDE AYNI
        self.chrome_binary_path = r"C:\Users\<USER>\Downloads\chrome-win64 (2)\chrome-win64\chrome.exe"
        self.chrome_profile_path = r"C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data"
        self.chrome_profile_directory = "Profile 1"
        self.chromedriver_path = r"C:\Users\<USER>\Desktop\Yayıncı Avı Modüler Yeni\chromedriver.exe"

        # Sıralı çalışma için
        self.phase_lock = threading.Lock()
        self.monitor_thread = None
        self.monitor_running = False

        # System Monitor
        self.system_monitor = SystemMonitor(main_app=self)

    def set_duration(self, duration):
        self.duration = duration

    def set_headless(self, headless):
        self.headless = headless

    def set_message_mode(self, mode):
        self.message_mode = mode or 'both'

    def force_close_chrome(self):
        """Chrome işlemlerini zorla kapatır"""
        try:
            import subprocess

            logger.info("🔄 Chrome kapatılıyor...")

            # Tüm Chrome işlemlerini kapat
            subprocess.run(['taskkill', '/f', '/im', 'chrome.exe'],
                         capture_output=True, text=True)
            subprocess.run(['taskkill', '/f', '/im', 'chromedriver.exe'],
                         capture_output=True, text=True)

            time.sleep(3)  # Kapanma için bekle
            logger.info("✅ Chrome kapatıldı")

        except Exception as e:
            logger.warning(f"⚠️ Chrome kapatma hatası: {e}")

    def start_automation(self):
        """Otomasyon döngüsünü başlatır - BASITLEŞTIRILMIŞ"""
        try:
            if self.cycle_running:
                logger.info("⚠️ Otomasyon zaten çalışıyor")
                return False

            self.cycle_running = True
            logger.info("🚀 Otomasyon döngüsü başlatılıyor...")

            # Monitör thread'ini başlat
            self.start_monitor()

            # System Monitor'ı başlat
            self.system_monitor.start_monitoring()

            # İlk aşama: Scraper
            self.start_scraper()

            return True

        except Exception as e:
            logger.error(f"❌ Otomasyon başlatma hatası: {e}")
            logger.error("📋 Hata detayları:", exc_info=True)

            # Kaynakları temizle
            try:
                self.stop_automation()
            except Exception as cleanup_error:
                logger.error(f"❌ Temizleme hatası: {cleanup_error}")

            self.cycle_running = False
            return False

    def start_monitor(self):
        """Basitleştirilmiş monitör - SIRALI ÇALIŞMA + TIMEOUT KONTROLÜ"""
        def monitor_loop():
            self.monitor_running = True
            logger.info("🔍 Monitör başlatıldı")

            last_activity_time = time.time()
            from constants import THREAD_TIMEOUT
            timeout_seconds = THREAD_TIMEOUT  # constants.py'den al

            while self.monitor_running and self.cycle_running:
                try:
                    with self.phase_lock:
                        current_time = time.time()

                        # Mevcut thread'in durumunu kontrol et - SADECE PYTHON THREAD
                        if self.current_thread:
                            thread_alive = self.current_thread.is_alive()

                            # Thread'in finished flag'ini kontrol et (daha güvenilir)
                            thread_finished = False
                            if hasattr(self.current_thread, 'is_finished'):
                                thread_finished = self.current_thread.is_finished

                            # Her 30 saniyede bir durum raporu
                            if int(current_time) % 30 == 0:
                                logger.info(f"📊 Thread durumu: {self.current_phase} - Alive: {thread_alive}, Finished: {thread_finished}")

                        # Thread tamamlanma kontrolü - FINISHED FLAG ÖNCELIKLI
                        thread_completed = False
                        if self.current_thread:
                            # Önce finished flag'ini kontrol et
                            if hasattr(self.current_thread, 'is_finished') and self.current_thread.is_finished:
                                thread_completed = True
                                logger.info(f"🏁 {self.current_phase} tamamlandı (finished flag)")
                            # Sonra is_alive() kontrol et
                            elif not self.current_thread.is_alive():
                                thread_completed = True
                                logger.info(f"🏁 {self.current_phase} tamamlandı (thread dead)")

                        if thread_completed:
                            phase = self.current_phase

                            # Thread'i temizle - PHASE LOCK İLE
                            logger.info(f"🔧 Thread temizleniyor: {phase}")
                            self.current_thread = None
                            self.current_phase = None
                            logger.info("✅ Thread temizlendi")

                            # Aktivite zamanını güncelle
                            last_activity_time = current_time

                            # Aşamalar arası bekleme
                            time.sleep(2)

                            # Sonraki aşamaya geç
                            logger.info(f"🔄 {phase} sonrası işlem başlatılıyor...")
                            if phase == 'scraper':
                                self.handle_scraper_completed()
                            elif phase == 'status_checker':
                                self.handle_status_checker_completed()
                            elif phase == 'message_sender':
                                self.handle_message_sender_completed()

                        # TIMEOUT KONTROLÜ - Thread takılmış mı?
                        elif self.current_thread and self.current_thread.is_alive():
                            # Timeout kontrolü - activity time'ı sürekli güncelleme!
                            if current_time - last_activity_time > timeout_seconds:
                                logger.warning(f"⚠️ {self.current_phase} thread {timeout_seconds} saniyedir yanıt vermiyor, yeniden başlatılıyor...")

                                # Thread'i zorla durdur
                                try:
                                    if hasattr(self.current_thread, 'stop'):
                                        self.current_thread.stop()
                                    self.current_thread.terminate()
                                except:
                                    pass

                                # Chrome zaten thread'ler tarafından kapatılıyor

                                # Thread'i temizle
                                self.current_thread = None
                                self.current_phase = None

                                # Scraper'ı yeniden başlat
                                logger.info("🔄 Timeout nedeniyle scraper yeniden başlatılıyor")
                                time.sleep(5)
                                self.start_scraper()

                                # Aktivite zamanını sıfırla
                                last_activity_time = current_time

                except Exception as e:
                    logger.error(f"❌ Monitör hatası: {e}")
                    time.sleep(5)

                time.sleep(2)  # Kontrol aralığı

            logger.info("🔍 Monitör durduruldu")

        # Monitör thread'ini başlat
        self.monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self.monitor_thread.start()

    def is_system_healthy(self) -> bool:
        """
        Sistem sağlığını kontrol eder.

        Returns:
            bool: Sistem sağlıklı mı
        """
        try:
            # 1. Cycle running kontrolü
            if not self.cycle_running:
                logger.warning("⚠️ Health Check: cycle_running = False")
                return False

            # 2. Monitor running kontrolü
            if not self.monitor_running:
                logger.warning("⚠️ Health Check: monitor_running = False")
                return False

            # 3. Database bağlantı kontrolü
            try:
                if not self.db_manager.check_connection():
                    logger.warning("⚠️ Health Check: Database bağlantısı yok")
                    return False
            except Exception as db_error:
                logger.warning(f"⚠️ Health Check: Database hatası: {db_error}")
                return False

            # 4. Thread deadlock kontrolü
            if self.current_thread:
                # Thread 10 dakikadan fazla çalışıyorsa sorun var
                if hasattr(self, 'thread_start_time'):
                    if time.time() - self.thread_start_time > 600:  # 10 dakika
                        logger.warning("⚠️ Health Check: Thread 10 dakikadan fazla çalışıyor")
                        return False

            # 5. Memory kullanım kontrolü (opsiyonel)
            try:
                import psutil
                memory_percent = psutil.virtual_memory().percent
                if memory_percent > 90:  # %90'dan fazla memory kullanımı
                    logger.warning(f"⚠️ Health Check: Yüksek memory kullanımı: %{memory_percent}")
                    return False
            except ImportError:
                pass  # psutil yoksa atla

            return True

        except Exception as e:
            logger.error(f"❌ Health Check hatası: {e}")
            return False

    def stop_automation(self):
        """Otomasyon döngüsünü durdurur"""
        if self.cycle_running:
            logger.info("⏹️ Otomasyon durduruluyor...")

            self.cycle_running = False
            self.monitor_running = False

            # System Monitor'ı durdur
            self.system_monitor.stop_monitoring()

            # Aktif thread'i durdur
            if self.current_thread and hasattr(self.current_thread, 'stop'):
                self.current_thread.stop()

            logger.info("✅ Otomasyon durduruldu")
        else:
            logger.info("ℹ️ Otomasyon zaten durdurulmuş")

    def handle_scraper_completed(self):
        """Scraper tamamlandığında çağrılır"""
        if not self.cycle_running:
            return

        logger.info("🔄 Scraper callback çağrıldı")

        # Thread'i temizle - CALLBACK'LERDE LOCK KULLANMA!
        logger.info("🔧 Thread temizleniyor (callback)")
        self.current_thread = None
        self.current_phase = None
        logger.info("✅ Scraper thread temizlendi (callback)")

        try:
            # Veritabanından "Bekleniyor" kullanıcıları al - DEBUG İLE
            users = self.db_manager.execute_query(f"""
                SELECT username, status FROM live_data
                WHERE status='Bekleniyor'
                AND timestamp >= DATE_SUB(NOW(), INTERVAL {self.duration} MINUTE)
            """)

            # DEBUG: Tüm son kullanıcıları da kontrol et
            all_recent_users = self.db_manager.execute_query(f"""
                SELECT username, status, timestamp FROM live_data
                WHERE timestamp >= DATE_SUB(NOW(), INTERVAL {self.duration} MINUTE)
                ORDER BY timestamp DESC
            """)

            logger.info(f"🔍 Son {self.duration} dakikada toplam {len(all_recent_users)} kullanıcı:")
            for user in all_recent_users:
                logger.info(f"  - {user['username']}: {user['status']} ({user['timestamp']})")

            if users:
                usernames = [user['username'] for user in users]
                logger.info(f"2️⃣ Status Checker başlatılıyor ({len(usernames)} kullanıcı)")
                self.start_status_checker(usernames)
            else:
                logger.info("🔄 'Bekleniyor' statüsünde kullanıcı bulunamadı, scraper tekrar başlatılıyor")
                time.sleep(2)  # Kısa bekleme
                self.start_scraper()

        except Exception as e:
            logger.error(f"❌ Scraper tamamlama hatası: {e}")
            time.sleep(2)  # Kısa bekleme
            self.start_scraper()

    def handle_status_checker_completed(self):
        """Status checker tamamlandığında çağrılır"""
        if not self.cycle_running:
            return

        logger.info("🔄 Status Checker callback çağrıldı")

        # Thread'i temizle - CALLBACK'LERDE LOCK KULLANMA!
        logger.info("🔧 Thread temizleniyor (callback)")
        self.current_thread = None
        self.current_phase = None
        logger.info("✅ Status Checker thread temizlendi (callback)")

        try:
            # Uygun kullanıcıları al - SÜTUN ADI DÜZELTİLDİ
            users = self.db_manager.execute_query(f"""
                SELECT id, username, status FROM live_data
                WHERE (status='Uygun' OR status='Uygun Elite')
                AND message_log IS NULL
                AND timestamp >= DATE_SUB(NOW(), INTERVAL {self.duration} MINUTE)
            """)

            if users:
                logger.info(f"3️⃣ Message Sender başlatılıyor ({len(users)} kullanıcı)")
                self.start_message_sender(users)
            else:
                logger.info("🔄 Uygun kullanıcı yok, scraper tekrar başlatılıyor")
                time.sleep(2)  # Kısa bekleme
                self.start_scraper()

        except Exception as e:
            logger.error(f"❌ Status checker tamamlama hatası: {e}")
            time.sleep(2)  # Kısa bekleme
            self.start_scraper()

    def handle_message_sender_completed(self):
        """Message sender tamamlandığında çağrılır"""
        if not self.cycle_running:
            return

        logger.info("🔄 Message Sender callback çağrıldı")

        # Thread'i temizle - CALLBACK'LERDE LOCK KULLANMA!
        logger.info("🔧 Thread temizleniyor (callback)")
        self.current_thread = None
        self.current_phase = None
        logger.info("✅ Message Sender thread temizlendi (callback)")

        # Scraper'ı direkt başlat - lock yok!
        logger.info("🔄 Döngü tamamlandı, scraper tekrar başlatılıyor")
        time.sleep(2)
        self.start_scraper()

    def start_scraper(self):
        """Scraper thread'ini başlatır"""
        if not self.cycle_running:
            logger.warning("⚠️ Döngü durduruldu, scraper başlatılmıyor")
            return

        logger.info("🔧 start_scraper çağrıldı")

        logger.info("🔧 Phase lock alınmaya çalışılıyor...")

        with self.phase_lock:
            logger.info("🔧 Phase lock alındı")

            if self.current_thread is not None:
                logger.warning(f"⚠️ Başka thread aktif: {self.current_thread}, scraper bekliyor")
                logger.warning(f"⚠️ Thread alive: {self.current_thread.is_alive()}")
                logger.warning(f"⚠️ Current phase: {self.current_phase}")

                # Thread ölmüşse temizle
                if not self.current_thread.is_alive():
                    logger.info("🧹 Ölü thread temizleniyor...")
                    self.current_thread = None
                    self.current_phase = None
                    logger.info("✅ Ölü thread temizlendi")
                else:
                    logger.warning("⚠️ Thread hala yaşıyor, scraper başlatılmıyor")
                    return

            logger.info("🔧 Current thread None, scraper başlatılıyor")

            try:
                logger.info(f"1️⃣ Scraper başlatılıyor (süre: {self.duration} dk)")

                self.current_thread = ScraperThread(
                    db_manager=self.db_manager,
                    duration=self.duration,
                    chrome_binary_path=self.chrome_binary_path,
                    chrome_profile_path=self.chrome_profile_path,
                    chrome_profile_directory=self.chrome_profile_directory,
                    headless=self.headless,
                    callback=self.handle_scraper_completed
                )

                self.current_phase = 'scraper'
                self.thread_start_time = time.time()  # Thread başlangıç zamanını kaydet
                self.current_thread.start()
                logger.info("✅ Scraper thread başlatıldı")

            except Exception as e:
                logger.error(f"❌ Scraper başlatma hatası: {e}")
                self.current_thread = None
                self.current_phase = None

    def start_status_checker(self, usernames):
        """Status checker thread'ini başlatır - NORMAL PYTHON THREAD"""
        logger.info("🔧 start_status_checker çağrıldı")

        if not self.cycle_running:
            logger.warning("⚠️ Döngü durduruldu, status checker başlatılmıyor")
            return

        logger.info("🔧 Phase lock alınıyor...")
        with self.phase_lock:
            logger.info("🔧 Phase lock alındı")

            if self.current_thread is not None:
                logger.warning(f"⚠️ Başka thread aktif: {self.current_thread}, status checker bekliyor")
                logger.warning(f"⚠️ Thread alive: {self.current_thread.is_alive()}")
                logger.warning(f"⚠️ Current phase: {self.current_phase}")
                return

            logger.info("🔧 Current thread None, devam ediliyor")

            try:
                logger.info(f"2️⃣ Status Checker başlatılıyor ({len(usernames)} kullanıcı)")
                logger.info(f"📋 Kullanıcılar: {usernames}")

                # Kullanıcı listesini uygun formata çevir
                publishers = [{"username": username} for username in usernames]
                logger.info(f"📋 Publishers formatı: {publishers}")

                logger.info("🔧 StatusCheckerThread oluşturuluyor...")

                # Status Checker'ı direkt thread olarak başlat
                try:
                    self.current_thread = StatusCheckerThread(
                        db_manager=self.db_manager,
                        publishers=publishers,
                        parent=None,
                        callback=self.handle_status_checker_completed
                    )
                    logger.info("✅ StatusCheckerThread oluşturuldu")
                    logger.info(f"🔧 Thread tipi: {type(self.current_thread)}")
                    logger.info(f"🔧 Thread daemon: {self.current_thread.daemon}")

                except Exception as create_error:
                    logger.error(f"❌ StatusCheckerThread oluşturma hatası: {create_error}")
                    import traceback
                    logger.error(f"❌ Hata detayı: {traceback.format_exc()}")
                    raise create_error

                self.current_phase = 'status_checker'
                logger.info("🚀 Status Checker thread başlatılıyor...")

                try:
                    self.current_thread.start()
                    logger.info("✅ Status Checker thread.start() çağrıldı")

                except Exception as start_error:
                    logger.error(f"❌ Status Checker thread.start() hatası: {start_error}")
                    import traceback
                    logger.error(f"❌ Hata detayı: {traceback.format_exc()}")
                    raise start_error

                # Thread başlatıldı - callback sistemi devreye girecek
                logger.info("✅ Status Checker thread başlatıldı, callback bekliyor")

            except Exception as e:
                logger.error(f"❌ Status checker başlatma hatası: {e}")
                import traceback
                logger.error(f"❌ Hata detayı: {traceback.format_exc()}")
                self.current_thread = None
                self.current_phase = None

                # Hata durumunda scraper'a geri dön
                logger.info("🔄 Hata nedeniyle scraper'a geri dönülüyor")
                time.sleep(2)  # Kısa bekleme
                self.start_scraper()

    def start_message_sender(self, users):
        """Message sender thread'ini başlatır"""
        if not self.cycle_running:
            return

        # Mesaj gönderme modunu kontrol et
        if self.message_mode == 'none':
            logger.info("ℹ️ Mesaj gönderme kapalı, scraper'a geçiliyor")
            # Thread'i temizle - NESTED LOCK ÖNLEME
            self.current_thread = None
            self.current_phase = None
            logger.info("✅ Thread temizlendi, scraper başlatılıyor")

            # Kısa bekleme sonrası scraper'ı başlat
            time.sleep(1)
            self.start_scraper()
            return

        with self.phase_lock:
            if self.current_thread is not None:
                logger.warning("⚠️ Başka thread aktif, message sender bekliyor")
                return

            try:
                logger.info(f"3️⃣ Message Sender başlatılıyor ({len(users)} kullanıcı)")

                self.current_thread = MessageSenderThread(
                    db_manager=self.db_manager,
                    users=users,
                    parent=None,
                    callback=self.handle_message_sender_completed
                )

                self.current_phase = 'message_sender'
                self.current_thread.start()

            except Exception as e:
                logger.error(f"❌ Message sender başlatma hatası: {e}")
                self.current_thread = None
                self.current_phase = None

# Gereksiz sinyal işleyicileri kaldırıldı - monitör sistemi kullanılıyor

if __name__ == "__main__":
    print("🚀 TikTok Otomasyon Sistemi - Test Modu")
    print(f"📋 Argümanlar: {sys.argv}")

    try:
        if len(sys.argv) > 1:
            cmd = sys.argv[1]
            duration = 1  # Varsayılan 1 dakika
            headless = False

            if cmd == "start":
                if len(sys.argv) > 2:
                    try:
                        duration = int(sys.argv[2])
                    except ValueError:
                        pass
                if len(sys.argv) > 3 and sys.argv[3].lower() == "headless":
                    headless = True

                print(f"⏱️ Süre: {duration} dakika, Headless: {headless}")

                main_app = MainApp()
                main_app.set_duration(duration)
                main_app.set_headless(headless)

                # SONSUZ DÖNGÜ AUTO-RECOVERY SİSTEMİ
                print("🔄 Sonsuz döngü auto-recovery sistemi başlatılıyor...")

                restart_count = 0
                max_restart_attempts = 10  # Maksimum yeniden başlatma sayısı
                restart_delay = 30  # Yeniden başlatma arası bekleme (saniye)

                while restart_count < max_restart_attempts:
                    try:
                        print(f"🚀 Otomasyon başlatılıyor... (Deneme: {restart_count + 1})")

                        success = main_app.start_automation()

                        if success:
                            print("✅ Otomasyon başlatıldı")
                            restart_count = 0  # Başarılı başlatma, sayacı sıfırla

                            # Ana döngü - sistem çalışırken bekle
                            try:
                                while main_app.cycle_running:
                                    time.sleep(1)

                                    # Health check - sistem sağlığını kontrol et
                                    if not main_app.is_system_healthy():
                                        print("⚠️ Sistem sağlığı bozuldu, yeniden başlatılıyor...")
                                        main_app.stop_automation()
                                        break

                            except KeyboardInterrupt:
                                print("⏹️ Kullanıcı tarafından durduruldu")
                                main_app.stop_automation()
                                return  # Kullanıcı durdurdu, çık

                        else:
                            print(f"❌ Otomasyon başlatılamadı (Deneme: {restart_count + 1})")
                            restart_count += 1

                        # Sistem durdu, yeniden başlatmayı dene
                        if restart_count < max_restart_attempts:
                            print(f"🔄 {restart_delay} saniye sonra yeniden başlatılacak...")
                            time.sleep(restart_delay)

                            # Yeni MainApp instance oluştur (memory leak önlemi)
                            main_app = MainApp()
                            main_app.set_duration(duration)
                            main_app.set_headless(headless)

                    except Exception as e:
                        print(f"❌ Kritik hata: {e}")
                        restart_count += 1
                        if restart_count < max_restart_attempts:
                            print(f"🔄 {restart_delay} saniye sonra yeniden başlatılacak...")
                            time.sleep(restart_delay)

                print(f"❌ Maksimum yeniden başlatma sayısına ulaşıldı ({max_restart_attempts})")
                print("🛑 Sistem tamamen durduruldu")

            elif cmd == "stop":
                print("⏹️ Durdurma komutu (test modunda çalışmaz)")
            else:
                print("📖 Kullanım: python main.py [start <süre(dk)> [headless]|stop]")
        else:
            print("📖 Kullanım: python main.py [start <süre(dk)> [headless]|stop]")

    except Exception as e:
        logger.error(f"❌ Ana program hatası: {e}")
        print(f"❌ HATA: {e}")
        print("📋 Detaylar için app.log dosyasını kontrol edin")