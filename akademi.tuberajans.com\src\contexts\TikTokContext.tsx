import React, { createContext, useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '../hooks/useAuth';
import axios from 'axios';

export interface Video {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  url: string;
  views: string;
  likes: string;
  comments: string;
  shares: string;
  saves: string;
  published_date: string;
}

export interface TikTokUser {
  username: string;
  tiktok_username?: string;
  display_name?: string;
  avatar_url?: string;
  followers_count?: number;
  following_count?: number;
  likes_count?: number;
  video_count?: number;
  is_verified?: boolean;
  bio?: string;
  tiktok_bio?: string;
  is_agency_publisher?: boolean;
  videos?: Video[];
  linked_at?: string;
}

interface TikTokContextType {
  tiktokUser: TikTokUser | null;
  loading: boolean;
  error: string | null;
  linkTikTokAccount: (tiktokData: Record<string, unknown>) => Promise<boolean>;
  unlinkTikTokAccount: () => Promise<boolean>;
  refreshTikTokData: () => Promise<void>;
  clearError: () => void;
}

export const TikTokContext = createContext<TikTokContextType | undefined>(undefined);

// Hook moved to separate file: src/hooks/useTikTok.ts

export const TikTokProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [tiktokUser, setTiktokUser] = useState<TikTokUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Development mode kontrolü
  const isDevMode = import.meta.env.VITE_DISABLE_AUTH === 'true' || import.meta.env.DEV;

  // Authentication durumunu kontrol et - Hook'u her zaman çağır
  const authContext = useAuth();

  const isAuthenticated = isDevMode
    ? !!localStorage.getItem('token')
    : (authContext?.isAuthenticated || !!localStorage.getItem('token'));

  // TikTok durumunu API'den fresh data alarak başlat
  const initializeTikTokUser = useCallback(async () => {
    console.log('initializeTikTokUser çağrıldı, isAuthenticated:', isAuthenticated);

    if (!isAuthenticated) {
      console.log('Kullanıcı giriş yapmamış, TikTok temizleniyor');
      clearTikTokUser();
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      console.log('TikTok API çağrısı yapılıyor...');

      // API'den güncel TikTok kullanıcı bilgilerini al
      const response = await axios.get('/backend/api/tiktok-api.php?action=get_user', {
        withCredentials: true
      });

      console.log('TikTok API yanıtı:', response.status, response.data);

      if (response.data.status === 'success' && response.data.data) {
        const userData = response.data.data;
        console.log('API\'den kullanıcı bilgileri alındı:', userData);

        // TikTok bağlantısı kontrolü - veritabanından gelen bilgiler
        if (userData.tiktok_linked === 1 || userData.tiktok_open_id) {
          const tikTokData = {
            username: userData.username,
            tiktok_username: userData.tiktok_username,
            display_name: userData.tiktok_display_name || userData.display_name,
            avatar_url: userData.avatar_url || userData.tiktok_avatar_url,
            linked_at: userData.tiktok_linked_at,
            followers_count: parseInt(userData.followers_count) || 0,
            following_count: parseInt(userData.following_count) || 0,
            likes_count: parseInt(userData.likes_count) || 0,
            video_count: parseInt(userData.video_count) || 0,
            is_verified: userData.is_verified === 1 || userData.is_verified === true,
            bio: userData.tiktok_bio || userData.bio || '',
            tiktok_bio: userData.tiktok_bio || userData.bio || ''
          };

          setTiktokUser(tikTokData);
          localStorage.setItem('tiktokUser', JSON.stringify(tikTokData));
          console.log('TikTok kullanıcısı API\'den başarıyla yüklendi:', tikTokData);
        } else {
          console.log('TikTok hesabı bağlı değil - tiktok_linked:', userData.tiktok_linked);
          clearTikTokUser();
        }
      } else {
        // API'den TikTok bilgisi alınamadı, localStorage'dan fallback kullan
        const storedUser = localStorage.getItem('user');
        if (storedUser) {
          try {
            const user = JSON.parse(storedUser);
            // TikTok bağlantısı kontrolü - daha esnek kontrol
            console.log('localStorage user TikTok kontrolü:', {
              tiktok_open_id: user.tiktok_open_id,
              tiktok_linked: user.tiktok_linked,
              tiktok_username: user.tiktok_username
            });

            if (user.tiktok_open_id || user.tiktok_linked || user.tiktok_username) {
              const tikTokData = {
                username: user.username,
                tiktok_username: user.tiktok_username,
                display_name: user.tiktok_display_name,
                avatar_url: user.tiktok_avatar_url,
                linked_at: user.tiktok_linked_at,
                followers_count: parseInt(user.followers_count) || 0,
                following_count: parseInt(user.following_count) || 0,
                likes_count: parseInt(user.likes_count) || 0,
                video_count: parseInt(user.video_count) || 0,
                is_verified: user.is_verified === 1,
                bio: user.bio,
                tiktok_bio: user.tiktok_bio
              };

              setTiktokUser(tikTokData);
              localStorage.setItem('tiktokUser', JSON.stringify(tikTokData));
            } else {
              clearTikTokUser();
            }
          } catch (parseError) {
            console.error('Error parsing stored user data:', parseError);
            clearTikTokUser();
          }
        } else {
          clearTikTokUser();
        }
      }
    } catch (error) {
      console.error('TikTok user initialization error:', error);
      // Hata durumunda localStorage'dan fallback kullan
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        try {
          const user = JSON.parse(storedUser);
          if (user.tiktok_open_id) {
            const tikTokData = {
              username: user.username,
              tiktok_username: user.tiktok_username,
              display_name: user.tiktok_display_name,
              avatar_url: user.tiktok_avatar_url,
              linked_at: user.tiktok_linked_at,
              followers_count: parseInt(user.followers_count) || 0,
              following_count: parseInt(user.following_count) || 0,
              likes_count: parseInt(user.likes_count) || 0,
              video_count: parseInt(user.video_count) || 0,
              is_verified: user.is_verified === 1,
              bio: user.bio,
              tiktok_bio: user.tiktok_bio
            };

            setTiktokUser(tikTokData);
            localStorage.setItem('tiktokUser', JSON.stringify(tikTokData));
          } else {
            clearTikTokUser();
          }
        } catch (parseError) {
          console.error('Error parsing stored user data in fallback:', parseError);
          clearTikTokUser();
        }
      } else {
        clearTikTokUser();
      }
      setError('TikTok bilgileri yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated]);

  // TikTok OAuth popup mesajlarını ve kullanıcı güncellemelerini dinle
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === 'tiktok_oauth_result') {
        console.log('TikTok OAuth sonucu alındı:', event.data);

        if (event.data.status === 'success') {
          // Başarılı bağlantı sonrası TikTok verilerini yenile
          setTimeout(() => {
            initializeTikTokUser();
          }, 1000);
        } else {
          setError(event.data.message || 'TikTok bağlantısı başarısız');
        }
      }
    };

    const handleUserLoggedIn = () => {
      console.log('Kullanıcı giriş eventi alındı, TikTok durumu kontrol ediliyor...');
      // Kullanıcı bilgileri güncellendiğinde TikTok durumunu kontrol et
      setTimeout(() => {
        initializeTikTokUser();
      }, 500);
    };

    window.addEventListener('message', handleMessage);
    window.addEventListener('userLoggedIn', handleUserLoggedIn);

    return () => {
      window.removeEventListener('message', handleMessage);
      window.removeEventListener('userLoggedIn', handleUserLoggedIn);
    };
  }, [initializeTikTokUser]);

  // Component mount olduğunda ve auth durumu değiştiğinde TikTok durumunu kontrol et
  useEffect(() => {
    console.log('TikTok useEffect tetiklendi, isAuthenticated:', isAuthenticated, 'user:', authContext?.user?.id);
    if (isAuthenticated && authContext?.user?.id) {
      initializeTikTokUser();
    }
  }, [isAuthenticated, authContext?.user?.id, initializeTikTokUser]);

  // URL parametrelerini kontrol et (TikTok bağlantı başarılı olduğunda)
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('tiktok_link') === 'success') {
      // URL'den parametreyi temizle
      window.history.replaceState({}, document.title, window.location.pathname);
      // TikTok kullanıcı bilgilerini yeniden çek
      setTimeout(() => {
        initializeTikTokUser();
      }, 1000); // 1 saniye bekle
    }
  }, [initializeTikTokUser]);

  // Login sonrası TikTok verilerini refresh et
  useEffect(() => {
    const handleUserLogin = () => {
      setTimeout(() => {
        initializeTikTokUser();
      }, 500);
    };

    const handleTikTokLinked = () => {
      console.log('TikTok linked event received, refreshing data...');
      setTimeout(() => {
        initializeTikTokUser();
      }, 500);
    };

    window.addEventListener('userLoggedIn', handleUserLogin);
    window.addEventListener('tiktokLinked', handleTikTokLinked);
    return () => {
      window.removeEventListener('userLoggedIn', handleUserLogin);
      window.removeEventListener('tiktokLinked', handleTikTokLinked);
    };
  }, [initializeTikTokUser]);

  const clearTikTokUser = () => {
    setTiktokUser(null);
    localStorage.removeItem('tiktokUser');
  };

  // TikTok hesabını bağla
  const linkTikTokAccount = useCallback(async (tiktokData: Record<string, unknown>): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.post('/backend/api/tiktok-api.php?action=link_account', tiktokData, {
        withCredentials: true
      });

      if (response.data && response.data.status === 'success') {
        // User bilgilerini güncelle
        const updatedUser = {
          ...JSON.parse(localStorage.getItem('user') || '{}'),
          tiktok_username: tiktokData.username,
          tiktok_display_name: tiktokData.display_name,
          tiktok_avatar_url: tiktokData.avatar_url,
          tiktok_bio: tiktokData.bio_description,
          followers_count: tiktokData.followers_count,
          following_count: tiktokData.following_count,
          likes_count: tiktokData.likes_count,
          video_count: tiktokData.video_count,
          is_verified: tiktokData.is_verified,
          tiktok_open_id: response.data.tiktok_open_id,
          tiktok_linked_at: new Date().toISOString()
        };
        
        localStorage.setItem('user', JSON.stringify(updatedUser));
        initializeTikTokUser(); // TikTok durumunu güncelle
        return true;
      } else {
        setError(response.data?.message || 'TikTok hesabı bağlanamadı');
        return false;
      }
    } catch (error) {
      console.error('TikTok hesabı bağlanırken hata:', error);
      setError('TikTok hesabı bağlanırken bir hata oluştu');
      return false;
    } finally {
      setLoading(false);
    }
  }, [initializeTikTokUser]);

  // TikTok hesap bağlantısını kaldır
  const unlinkTikTokAccount = useCallback(async (): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.post('/backend/api/tiktok-api.php?action=unlink', {}, {
        withCredentials: true
      });

      if (response.data && response.data.status === 'success') {
        // User bilgilerinden TikTok verilerini temizle
        const storedUser = JSON.parse(localStorage.getItem('user') || '{}');
        const updatedUser = {
          ...storedUser,
          tiktok_username: null,
          tiktok_display_name: null,
          tiktok_avatar_url: null,
          tiktok_bio: null,
          followers_count: null,
          following_count: null,
          likes_count: null,
          video_count: null,
          is_verified: null,
          tiktok_open_id: null,
          tiktok_linked_at: null
        };
        
        localStorage.setItem('user', JSON.stringify(updatedUser));
        clearTikTokUser();
        return true;
      } else {
        setError(response.data?.message || 'TikTok hesap bağlantısı kaldırılamadı');
        return false;
      }
    } catch (error) {
      console.error('TikTok hesap bağlantısı kaldırma hatası:', error);
      setError('TikTok hesap bağlantısı kaldırılırken hata oluştu');
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  // Error temizleme fonksiyonu
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // TikTok verilerini yenileme fonksiyonu
  const refreshTikTokData = useCallback(async () => {
    initializeTikTokUser();
  }, [initializeTikTokUser]);

  // Context value'yu memoize et
  const value = useMemo(() => ({
    tiktokUser,
    loading,
    error,
    linkTikTokAccount,
    unlinkTikTokAccount,
    refreshTikTokData,
    clearError
  }), [tiktokUser, loading, error, linkTikTokAccount, unlinkTikTokAccount, refreshTikTokData, clearError]);

  return (
    <TikTokContext.Provider value={value}>
      {children}
    </TikTokContext.Provider>
  );
};
