# TikTok Otomasyon Sistemi - Environment Variables
# Bu dosyayı .env olarak kopyalayın ve kendi değerlerinizi girin

# Chrome Ayarları
CHROME_PATH=C:\Program Files\Google\Chrome\Application\chrome.exe
CHROMEDRIVER_PATH=C:\chromedriver\chromedriver.exe
CHROME_BINARY_PATH=C:\Program Files\Google\Chrome\Application\chrome.exe
CHROME_PROFILE_BASE=C:\Users\<USER>\AppData\Local\Google\Chrome\User Data

# MySQL Veritabanı
MYSQL_HOST=localhost
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=your_database

# Sistem Ayarları
ENV=production
LOG_LEVEL=INFO

# TikTok Ayarları
TIKTOK_USERNAME=your_tiktok_username
TIKTOK_PASSWORD=your_tiktok_password

# Timeout Ayarları (saniye)
PAGE_LOAD_TIMEOUT=20
ELEMENT_WAIT_TIMEOUT=10
CLICK_TIMEOUT=5

# Thread Ayarları
THREAD_MONITOR_INTERVAL=5
THREAD_TIMEOUT=300

# Scraper Ayarları
SCRAPER_DURATION=1
SCRAPER_HEADLESS=false
SCRAPER_TIMEOUT=15

# Auto-Recovery Ayarları
MAX_RESTART_ATTEMPTS=10
RESTART_DELAY=30
HEALTH_CHECK_INTERVAL=30
MEMORY_THRESHOLD=85
CPU_THRESHOLD=90
