import React, { useState } from 'react';
import { Button, Input, Form, message, Card, Select } from 'antd';
import { sendSingleEmail } from '../services/email-service';

const { TextArea } = Input;
const { Option } = Select;

const EmailTest: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSend = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      await sendSingleEmail({
        email: values.email,
        subject: values.subject,
        content: values.content,
        fromEmail: values.fromEmail
      });

      message.success('E-posta başarıyla gönderildi!');
      form.resetFields();
    } catch (error) {
      console.error('E-posta gönderme hatası:', error);
      message.error('E-posta gönderilirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: 24 }}>
      <Card title="SendGrid E-posta Test" style={{ maxWidth: 600 }}>
        <Form form={form} layout="vertical">
          <Form.Item
            label="Gönderen E-posta"
            name="fromEmail"
            rules={[{ required: true, message: 'Lütfen gönderen e-posta seçin' }]}
            initialValue="<EMAIL>"
          >
            <Select placeholder="Gönderen e-posta seçin">
              <Option value="<EMAIL>">İşbirliği Ekibi (<EMAIL>)</Option>
              <Option value="<EMAIL>">Rapor Ekibi (<EMAIL>)</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="Alıcı E-posta"
            name="email"
            rules={[
              { required: true, message: 'E-posta adresi gerekli' },
              { type: 'email', message: 'Geçerli bir e-posta adresi girin' }
            ]}
          >
            <Input placeholder="<EMAIL>" />
          </Form.Item>

          <Form.Item
            label="Konu"
            name="subject"
            rules={[{ required: true, message: 'Konu gerekli' }]}
          >
            <Input placeholder="Test E-postası" />
          </Form.Item>

          <Form.Item
            label="İçerik"
            name="content"
            rules={[{ required: true, message: 'İçerik gerekli' }]}
          >
            <TextArea
              rows={6}
              placeholder="E-posta içeriğinizi buraya yazın..."
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              onClick={handleSend}
              loading={loading}
              style={{ width: '100%' }}
            >
              E-posta Gönder
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default EmailTest;
