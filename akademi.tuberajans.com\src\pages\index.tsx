import React, { useState, useContext, useEffect, useMemo, useCallback } from 'react';
import { useLocation, Link, Outlet } from 'react-router-dom';
import Sidebar from '../components/dashboard/Sidebar';
import Topbar from '../components/dashboard/Topbar';
import { FaUsers, FaHeart, FaClock, FaBullhorn, FaMapMarkerAlt, FaCalendarAlt, FaVideo } from 'react-icons/fa';
import { BsCalendar3 } from 'react-icons/bs';
import { getAnnouncementTypeStyles } from '../utils/styleHelpers';
import { AcademicCapIcon } from '@heroicons/react/24/outline';
import { SidebarContext } from '../contexts/SidebarContext';
import { useAuth } from '../hooks/useAuth';
import { useTikTok } from '../hooks/useTikTok';
import axios from 'axios';
import { SiTiktok } from "react-icons/si";

// API Base URL'i ayarla
const API_BASE_URL = import.meta.env.PROD 
  ? 'https://akademi.tuberajans.com/backend/api' 
  : '/backend/api';

// Alt sayfaları import et
// Artık burada importlara gerek yok, iç içe route'lar içinde kullanılıyor

interface DashboardProps {
  onLogout: () => void;
}

const Dashboard: React.FC<DashboardProps> = ({ onLogout }) => {
  // Development mode kontrolü
  const isDevMode = import.meta.env.VITE_DISABLE_AUTH === 'true' || import.meta.env.DEV;

  // AuthContext'i her zaman çağır (hook rules için gerekli)
  useAuth();

  const { isSidebarOpen, isMobile } = useContext(SidebarContext);
  const [courseCategories, setCourseCategories] = useState<string[]>(['Tüm Kategoriler']);
  const [activeCategory, setActiveCategory] = useState('Tüm Kategoriler');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Eğitim tipi tanımı
  interface Course {
    id: number;
    title: string;
    description: string;
    date: string;
    duration?: string;
    category?: string;
    image_url?: string;
    created_at?: string;
  }

  // Duyuru tipi tanımı
  interface Announcement {
    id: number;
    title: string;
    content: string;
    date?: string;
    dateFormatted?: string;
    description?: string;
    created_at: string;
    category?: string;
    image_url?: string;
    type: string;
  }

  // API'den çekilen veriler için state'ler
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [coursesByCategory, setCoursesByCategory] = useState<Record<string, Course[]>>({
    'Genel': [],
    'Başlangıç': [],
    'Orta Seviye': [],
    'İleri Seviye': [],
    'Monetizasyon': []
  });

  // Etkinlikler için state
  const [events, setEvents] = useState<Event[]>([]);

  // Modal state'leri
  const [selectedAnnouncement, setSelectedAnnouncement] = useState<Announcement | null>(null);
  const [isAnnouncementModalOpen, setIsAnnouncementModalOpen] = useState(false);

  // TikTok Context'ten verileri al
  const { tiktokUser, loading: tiktokLoading } = useTikTok();

  // Topbar refresh için trigger
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Yayın performans verileri için state
  const [broadcastStats, setBroadcastStats] = useState({
    currentMonth: {
      totalHours: 0,
      targetHours: 15, // Aylık minimum hedef saat
      completedDays: 0,
      targetDays: 7, // Aylık minimum hedef gün
    },
    lastUpdate: new Date().toISOString()
  });

  // Yayın performans verilerini yükle
  const fetchBroadcastStats = useCallback(async () => {
    try {
      // Development modunda mock veri kullan
      if (isDevMode) {
        const mockStats = {
          currentMonth: {
            totalHours: 12, // Bu aya kadar toplam saat
            targetHours: 15, // Aylık minimum hedef
            completedDays: 5, // Bu aya kadar yayın yapılan gün
            targetDays: 7, // Aylık minimum hedef gün
          },
          lastUpdate: new Date().toISOString()
        };
        setBroadcastStats(mockStats);
        return;
      }

      // Production modunda gerçek API çağrısı
      const response = await axios.get(`${API_BASE_URL}/broadcast_stats.php`);
      if (response.data?.success) {
        setBroadcastStats(response.data.data);
      }
    } catch (error) {
      console.error('Yayın istatistikleri alınamadı:', error);
      // Hata durumunda varsayılan değerleri kullan
    }
  }, [isDevMode]);

  // Sayfa location'ını al
  const pageLocation = useLocation();
  
  // Dashboard verilerini tek bir API çağrısıyla çek
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Mock veri kullanımı kaldırıldı - sadece API'den veri al

        // Production modunda gerçek API çağrısı
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 saniye timeout

        const response = await axios.get(`${API_BASE_URL}/dashboard_data.php`, {
          signal: controller.signal,
          timeout: 15000 // 15 saniye timeout - biraz daha uzun
        });

        clearTimeout(timeoutId);

        if (response.data && response.data.status === 'success') {
          // 1. Duyuruları formatla
          if (response.data.announcements) {
            const formattedAnnouncements = response.data.announcements.map((item: {
              id: number;
              title: string;
              content: string;
              created_at: string;
              category: string;
              type?: string;
            }) => ({
              id: item.id,
              title: item.title,
              content: item.content,
              description: item.content ? item.content.substring(0, 120) + '...' : '',
              dateFormatted: item.created_at ? new Date(item.created_at).toLocaleDateString('tr-TR', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              }) : '',
              created_at: item.created_at,
              category: item.category,
              type: item.type || item.category
            }));
            setAnnouncements(formattedAnnouncements.slice(0, 4)); // Sadece ilk 4 duyuruyu göster
          }

          // 2. Eğitimleri formatla
          if (response.data.courses) {
            const allCourses = response.data.courses;

            // Kategorileri dinamik olarak çıkar
            const categorySet = new Set<string>();
            allCourses.forEach((course: Course) => {
              const category = course.category || 'Genel';
              categorySet.add(category);
            });

            // Kategori listesini güncelle
            const uniqueCategories = ['Tüm Kategoriler', ...Array.from(categorySet)];
            setCourseCategories(uniqueCategories);

            // Kategorilere göre grupla
            const categorized: Record<string, Course[]> = {};
            Array.from(categorySet).forEach(cat => {
              categorized[cat] = [];
            });

            allCourses.forEach((course: Course) => {
              const category = course.category || 'Genel';
              if (categorized[category]) {
                categorized[category].push(course);
              } else {
                categorized['Genel'] = categorized['Genel'] || [];
                categorized['Genel'].push(course);
              }
            });
            setCoursesByCategory(categorized);
          }

          // 3. Etkinlikleri formatla
          if (response.data.events) {
            const processedEvents = response.data.events.map((event: {
              id: number;
              title: string;
              description: string;
              start_date: string;
              end_date: string;
              location: string;
              category: string;
              is_featured?: boolean | number;
              max_participants?: number;
              current_participants?: number;
              status?: string;
            }) => {
              const startDate = new Date(event.start_date);
              const endDate = new Date(event.end_date);

              // Durum bilgisine göre renk ve metin belirle
              let statusColor = 'bg-blue-100 text-blue-600';
              let statusText = 'Planlanıyor';

              if (event.status === 'active') {
                statusColor = 'bg-green-100 text-green-600';
                statusText = 'Onaylandı';
              } else if (event.status === 'pending') {
                statusColor = 'bg-orange-100 text-orange-600';
                statusText = 'Beklemede';
              }

              return {
                ...event,
                date: startDate,
                endDate: endDate,
                isFeatured: Boolean(event.is_featured),
                isPastEvent: startDate <= new Date(),
                statusColor,
                statusText
              };
            });

            // Sadece gelecek etkinlikleri filtrele ve tarihe göre sırala
            const now = new Date();
            const futureEvents = processedEvents
              .filter((event: Event) => event.date && event.date > now && event.status === 'active')
              .sort((a: Event, b: Event) => a.date!.getTime() - b.date!.getTime());

            setEvents(futureEvents.slice(0, 3)); // Sadece ilk 3 etkinliği göster
          }
        }
      } catch (err: unknown) {
        console.error('Dashboard verileri yüklenirken hata oluştu:', err);

        if (axios.isCancel(err)) {
          setError('Sunucu yanıt vermedi. Lütfen daha sonra tekrar deneyin.');
        } else if (err && typeof err === 'object' && 'code' in err && err.code === 'ECONNABORTED') {
          setError('Sunucu yanıt vermedi. Lütfen daha sonra tekrar deneyin.');
        } else {
          const errorMessage = err instanceof Error ? err.message : 'Bilinmeyen hata';
          setError(`Dashboard verileri yüklenirken bir hata oluştu: ${errorMessage}`);
        }

        // Hata durumunda boş veriler ata
        setAnnouncements([]);
        setCoursesByCategory({
          'Genel': [],
          'Başlangıç': [],
          'Orta Seviye': [],
          'İleri Seviye': [],
          'Monetizasyon': []
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [isDevMode]);

  // Yayın istatistiklerini sadece ana dashboard sayfasında çek
  useEffect(() => {
    if (pageLocation.pathname === '/dashboard' || pageLocation.pathname === '/dashboard/') {
      fetchBroadcastStats();
    }
  }, [pageLocation.pathname, fetchBroadcastStats]);

  // URL parametrelerini kontrol et (TikTok giriş başarılı olduğunda)
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('tiktok_login') === 'success') {
      // URL'den parametreyi temizle
      window.history.replaceState({}, document.title, window.location.pathname);
      // TikTok kullanıcı bilgilerini yeniden çek - session'ın oluşması için biraz daha bekle
      setTimeout(() => {
        setRefreshTrigger(prev => prev + 1);
      }, 2000); // 2 saniye bekle
    }
  }, []); // Dependency array'den refreshTikTokUser'ı kaldır

  const location = useLocation();

  // Etkinlik tipi tanımı
  interface Event {
    id: number;
    title: string;
    description?: string;
    start_date?: string;
    end_date?: string;
    location?: string;
    capacity?: number;
    registered?: number;
    thumbnail?: string;
    instructor?: string;
    category?: string;
    is_featured?: boolean | number; // API'den 0/1 olarak da gelebilir
    status?: string;
    date?: Date; // Frontend için eklenen tarih nesnesi
    endDate?: Date; // Frontend için eklenen bitiş tarihi nesnesi
    isFeatured?: boolean; // Frontend için eklenen özellik
    isPastEvent?: boolean; // Frontend için eklenen özellik
    statusColor?: string; // Frontend için eklenen özellik
    statusText?: string; // Frontend için eklenen özellik
  }

  // Render announcements content
  const renderAnnouncementsContent = () => {
    if (loading) {
      return (
        <div className="p-6 text-center">
          <p className="text-sm text-gray-500 dark:text-gray-400">Duyurular yükleniyor...</p>
        </div>
      );
    }

    if (error) {
      return (
        <div className="p-6 text-center">
          <p className="text-sm text-red-500">{error}</p>
        </div>
      );
    }

    if (announcements.length === 0) {
      return (
        <div className="p-6 text-center">
          <p className="text-sm text-gray-500 dark:text-gray-400">Henüz duyuru bulunmamaktadır.</p>
        </div>
      );
    }

    return announcements.map((announcement) => (
      <div
        key={announcement.id}
        className="p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors cursor-pointer"
        onClick={() => {
          setSelectedAnnouncement(announcement);
          setIsAnnouncementModalOpen(true);
        }}
      >
        <div className="flex items-stretch">
          <div className="min-w-[4px] bg-[#FF3E71] rounded-full mr-3"></div>
          <div className="flex-1 overflow-hidden">
            <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mb-1">
              <BsCalendar3 className="text-gray-500 dark:text-gray-400 mr-1 text-xs" />
              <span>{announcement.dateFormatted}</span>
              {['Genel', 'Etkinlik', 'Önemli', 'Marka İş Birliği'].includes(announcement.type || '') && (
                <span className={`ml-2 px-4 py-0.5 rounded-full text-xs font-medium ${getAnnouncementTypeStyles(announcement.type || '')}`}>
                  {announcement.type}
                </span>
              )}
            </div>
            <h3 className="text-sm font-medium mb-1 text-gray-800 dark:text-white truncate">{announcement.title}</h3>
            <p className="text-xs text-gray-500 dark:text-gray-400 line-clamp-2">{announcement.description}</p>
          </div>
        </div>
      </div>
    ));
  };

  const filteredCourses = useMemo(() => {
    const allCourses = activeCategory === 'Tüm Kategoriler'
      ? Object.values(coursesByCategory).flat()
      : coursesByCategory[activeCategory] || [];
    // Tarihe göre azalan sırala (en yeni başta)
    return allCourses.slice().sort((a, b) => new Date(b.created_at || b.date).getTime() - new Date(a.created_at || a.date).getTime());
  }, [activeCategory, coursesByCategory]);

  const handleTikTokLogin = () => {
    const clientKey = 'awfw8k9nim1e8dmu';
    const redirectUri = encodeURIComponent('https://akademi.tuberajans.com/backend/api/tiktok-callback.php');
    const state = 'dashboard_' + Math.random().toString(36).substring(2, 15);
    localStorage.setItem('tiktok_oauth_state', state);
    localStorage.setItem('tiktok_redirect_after', 'dashboard');
    const scope = 'user.info.basic,user.info.profile,user.info.stats,video.list';
    const url = `https://www.tiktok.com/v2/auth/authorize/?client_key=${clientKey}&response_type=code&scope=${scope}&redirect_uri=${redirectUri}&state=${state}`;
    window.location.href = url;
  };

  // TikTok callback sonrası state doğrulaması ve sayfa yenileme - Optimized
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const tiktokLogin = urlParams.get('tiktok_login');
    const tiktokState = urlParams.get('state');

    if (tiktokLogin === 'success') {
      // TikTok verilerini yenile
      setRefreshTrigger(prev => prev + 1);

      // URL'den parametreleri temizle
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    }

    if (tiktokState) {
      const storedState = localStorage.getItem('tiktok_oauth_state');
      if (storedState !== tiktokState) {
        console.error('TikTok OAuth state mismatch');
        // Use toast instead of alert for better UX
        // alert('Güvenlik uyarısı: TikTok giriş isteği ile dönen state uyuşmuyor! Lütfen tekrar deneyin.');
      } else {
        // State doğrulandı, localStorage'dan temizle
        localStorage.removeItem('tiktok_oauth_state');
      }
    }
  }, []); // Empty dependency array is correct here

  const renderContent = () => {
    const path = location.pathname;

    if (path !== '/dashboard') {
      return <div style={{
        maxWidth: isMobile ? '100%' : 'none',
        width: isMobile ? '100%' : 'auto',
        overflowX: 'hidden',
        position: 'relative',
        left: 0,
        paddingLeft: isMobile ? '0.25rem' : 0,
        paddingRight: isMobile ? '0.25rem' : 0,
        boxSizing: 'border-box'
      }}><Outlet /></div>;
    }

    // Ana dashboard içeriği (sadece /dashboard yolunda gösterilir)
    return (
      <div className="container" style={{
        maxWidth: isMobile ? '100%' : 'none',
        width: isMobile ? '100%' : 'auto',
        overflowX: 'hidden',
        position: 'relative',
        left: 0,
        marginLeft: 0,
        paddingTop: isMobile ? '0.5rem' : '0.75rem',
        paddingLeft: isMobile ? '0.25rem' : '0rem',
        paddingRight: isMobile ? '0.25rem' : '1rem',
        boxSizing: 'border-box'
      }}>
        <div className="flex flex-col md:flex-row gap-2 sm:gap-4">
          {/* Sol Sütun */}
          <div className="w-full md:w-3/4 space-y-4">
            {/* Duyurular */}
            <div className="bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden">
              <div className="flex items-center justify-between px-4 py-3 border-b border-gray-100 dark:border-gray-700">
                <div className="flex items-center">
                  <FaBullhorn className="text-gray-700 dark:text-gray-300 mr-2" />
                  <h2 className="text-base font-semibold text-gray-800 dark:text-white">Duyurular</h2>
                </div>
                <Link to="/dashboard/announcements" className="text-[#FF3E71] text-sm font-medium hover:underline">
                  Tümünü Gör
                </Link>
              </div>
              <div className="divide-y divide-gray-100 dark:divide-gray-700">
                {renderAnnouncementsContent()}
              </div>
            </div>

            {/* Eğitimler */}
            <div className="bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden">
              <div className="flex items-center justify-between px-4 py-3 border-b border-gray-100 dark:border-gray-700">
                <div className="flex items-center">
                  <AcademicCapIcon className="w-5 h-5 mr-2 text-gray-700 dark:text-gray-300" />
                  <h2 className="text-base font-semibold text-gray-800 dark:text-white">Eğitimler</h2>
                </div>
                <Link to="/dashboard/courses" className="text-[#FF3E71] text-sm font-medium hover:underline">
                  Tümü
                </Link>
              </div>
              <div className="px-4 py-3">
                <div className="overflow-x-auto mb-4 px-2 sm:px-0">
                  <div className="flex gap-2 sm:gap-3 pb-1" style={{ minWidth: 'max-content' }}>
                    {courseCategories.map((category) => (
                      <button
                        key={category}
                        onClick={() => setActiveCategory(category)}
                        className={`whitespace-nowrap px-4 sm:px-5 py-1.5 sm:py-2 text-xs sm:text-sm font-medium rounded-full transition-all duration-200 flex-shrink-0 ${
                          activeCategory === category
                            ? 'bg-[#FF3E71] text-white'
                            : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                        }`}
                      >
                        {category}
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              <div className="px-4 pb-4">
                {loading ? (
                  // Yükleniyor durumu
                  <div className="p-6 text-center">
                    <p className="text-sm text-gray-500 dark:text-gray-400">Eğitimler yükleniyor...</p>
                  </div>
                ) : error ? (
                  // Hata durumu
                  <div className="p-6 text-center">
                    <p className="text-sm text-red-500">{error}</p>
                  </div>
                ) : (
                  filteredCourses.length === 0 ? (
                    // Veri yok durumu
                    <div className="p-6 text-center">
                      <p className="text-sm text-gray-500 dark:text-gray-400">Bu kategoride henüz eğitim bulunmamaktadır.</p>
                    </div>
                  ) : (
                    // Eğitimleri listele - Mobile 2 columns
                    <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4">
                      {filteredCourses.map((course) => (
                        <Link
                          to={`/dashboard/courses/${course.id}`}
                          key={course.id}
                          className="relative group block"
                        >
                          <div className="rounded-lg overflow-hidden aspect-video relative">
                            {/* Modern gradient arka plan */}
                            <div className="absolute inset-0 bg-gradient-to-br from-[#FF3E71]/90 to-[#FF5F87]/90 brightness-90"></div>

                            {/* Dekoratif desen */}
                            <div className="absolute inset-0 opacity-10">
                              <div className="absolute inset-0" style={{
                                backgroundImage: 'radial-gradient(circle, rgba(255,255,255,0.8) 1px, transparent 1px)',
                                backgroundSize: '20px 20px'
                              }}></div>
                            </div>

                            {/* Parlama efekti */}
                            <div className="absolute -top-20 -right-20 w-40 h-40 rounded-full bg-white opacity-20 blur-2xl"></div>
                            <div className="absolute -bottom-10 -left-10 w-32 h-32 rounded-full bg-white opacity-10 blur-xl"></div>

                            {/* Başlık ortada */}
                            <div className="absolute inset-0 flex flex-col justify-center items-center p-3 sm:p-4 text-center">
                              <h2 className="text-sm sm:text-base lg:text-xl font-bold text-white drop-shadow-lg line-clamp-2 mb-1 sm:mb-2">
                                {course.title}
                              </h2>
                            </div>

                            {/* Kategori etiketi */}
                            <div className="absolute top-2 right-2 bg-black/40 backdrop-blur-md text-white text-[9px] xs:text-[10px] px-2.5 xs:px-3.5 py-0.5 xs:py-1 rounded-full border border-white/20 shadow-lg font-medium tracking-wide">
                              {course.category || activeCategory}
                            </div>
                          </div>
                          <div className="mt-2">
                            <h3 className="text-xs sm:text-sm font-medium text-gray-800 dark:text-white">{course.title}</h3>
                            <p className="text-[10px] xs:text-xs text-gray-500 dark:text-gray-400 mt-1 line-clamp-1">{course.description}</p>
                            <div className="flex items-center justify-between text-[9px] xs:text-[10px] sm:text-xs text-gray-500 dark:text-gray-400 mt-1">
                              <div className="flex items-center">
                                <BsCalendar3 className="mr-1" />
                                <span>{new Date(course.created_at || '').toLocaleDateString('tr-TR')}</span>
                              </div>
                            </div>
                          </div>
                        </Link>
                      ))}
                    </div>
                  )
                )}
              </div>
            </div>
          </div>

          {/* Sağ Sütun */}
          <div className="w-full md:w-1/4 space-y-4 flex flex-col" style={{
            paddingRight: isMobile ? '0' : '1rem'
          }}>
            {/* Planlı Etkinlikler */}
            <div className="bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden">
              <div className="flex items-center justify-between px-4 py-3 border-b border-gray-100 dark:border-gray-700">
                <div className="flex items-center">
                  <FaCalendarAlt className="text-gray-700 dark:text-gray-300 mr-2" />
                  <h2 className="text-base font-semibold text-gray-800 dark:text-white">Planlı Etkinlikler</h2>
                </div>
                <Link to="/dashboard/events" className="text-[#FF3E71] text-sm font-medium hover:underline">
                  Tümü
                </Link>
              </div>
              <div className="divide-y divide-gray-100 dark:divide-gray-700">
                {/* Etkinlikler yüklenirken */}
                {loading && (
                  <div className="p-6 text-center">
                    <p className="text-sm text-gray-500 dark:text-gray-400">Etkinlikler yükleniyor...</p>
                  </div>
                )}

                {/* Hata durumu */}
                {!loading && error && (
                  <div className="p-6 text-center">
                    <p className="text-sm text-red-500">{error}</p>
                  </div>
                )}

                {/* Etkinlikleri tarihe göre sırala (en yakın tarihli etkinlik en üstte) */}
                {!loading && !error && events.length > 0 && [...events]
                  .map((event, idx) => {
                    // Sadece ilk etkinlik (en yakın tarihli) pembemsi görünsün
                    const isFirstEvent = idx === 0;

                    return (
                      <div key={event.id} className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors ${
                        isFirstEvent ? 'bg-pink-50/30 dark:bg-pink-900/10' : ''
                      }`}>
                        <div className="flex items-start space-x-3">
                          {/* Tarih Badge */}
                          <div className={`flex-shrink-0 rounded-lg px-2 py-1.5 shadow-sm min-w-[60px] ${
                            isFirstEvent
                              ? 'bg-gradient-to-r from-[#FF3E71]/80 to-[#FF5F87]/80 text-white'
                              : 'bg-white/90 dark:bg-gray-800/90'
                          }`}>
                            <div className="flex flex-col items-center">
                              <div className={`text-lg font-bold leading-tight ${
                                isFirstEvent ? 'text-white' : 'text-[#FF3E71]'
                              }`}>
                                {event.date?.getDate()}
                              </div>
                              <div className={`text-xs font-medium leading-tight ${
                                isFirstEvent ? 'text-white' : 'text-gray-700 dark:text-gray-300'
                              }`}>
                                {event.date?.toLocaleDateString('tr-TR', { month: 'short' })}
                              </div>
                            </div>
                          </div>

                          {/* Etkinlik Bilgileri */}
                          <div className="flex-1 min-w-0">
                            <h4 className={`text-sm font-medium truncate ${
                              isFirstEvent ? 'text-gray-700 dark:text-gray-200' : 'text-gray-900 dark:text-white'
                            }`}>
                              {event.title}
                              {event.isFeatured && isFirstEvent && <span className="ml-1">✨</span>}
                            </h4>
                            <div className="flex items-center mt-1 text-xs text-gray-500 dark:text-gray-400 gap-4">
                              <span className="flex items-center">
                                <FaMapMarkerAlt className="mr-1 text-gray-500" />
                                {event.location}
                              </span>
                              <span className="flex items-center">
                                <FaClock className="mr-1 text-gray-500" />
                                {event.date?.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' })}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}

                {!loading && !error && events.length === 0 && (
                  <div className="p-6 text-center">
                    <p className="text-sm text-gray-500 dark:text-gray-400">Yaklaşan etkinlik bulunmamaktadır.</p>
                  </div>
                )}
              </div>
            </div>

            {/* Canlı Yayın Hedefi */}
            <div className="bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden">
              <div className="flex items-center justify-between px-4 py-3 border-b border-gray-100 dark:border-gray-700">
                <div className="flex items-center">
                  <svg className="w-5 h-5 mr-2 text-gray-700 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10a2 2 0 012 2v14a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"/>
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4"/>
                  </svg>
                  <h2 className="text-base font-semibold text-gray-800 dark:text-white">Canlı Yayın Hedefi</h2>
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {new Date().toLocaleDateString('tr-TR', { month: 'long', year: 'numeric' })}
                </div>
              </div>
              <div className="p-4 space-y-4">
                {/* Geçerli Canlı Yayın Günü (Üstte) */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Geçerli Canlı Yayın Günü</span>
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {broadcastStats.currentMonth.completedDays}/{broadcastStats.currentMonth.targetDays} gün
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] h-2 rounded-full transition-all duration-500"
                      style={{
                        width: `${Math.min((broadcastStats.currentMonth.completedDays / broadcastStats.currentMonth.targetDays) * 100, 100)}%`
                      }}
                    ></div>
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    %{Math.round((broadcastStats.currentMonth.completedDays / broadcastStats.currentMonth.targetDays) * 100)} tamamlandı
                  </div>
                </div>

                {/* Canlı Yayın Saati (Altta) */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Canlı Yayın Saati</span>
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {broadcastStats.currentMonth.totalHours}/{broadcastStats.currentMonth.targetHours} saat
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] h-2 rounded-full transition-all duration-500"
                      style={{ 
                        width: `${Math.min((broadcastStats.currentMonth.totalHours / broadcastStats.currentMonth.targetHours) * 100, 100)}%` 
                      }}
                    ></div>
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    %{Math.round((broadcastStats.currentMonth.totalHours / broadcastStats.currentMonth.targetHours) * 100)} tamamlandı
                  </div>
                </div>

                {/* Elmaslar */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <svg className="w-4 h-4 mr-2 text-cyan-500 dark:text-cyan-400" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M6 2L3 6V8C3 12 7 16 12 16S21 12 21 8V6L18 2H6Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" fill="none"/>
                        <path d="M6 8L12 12L18 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" fill="none"/>
                      </svg>
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Elmaslar</span>
                    </div>
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      0/50.000
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-gradient-to-r from-cyan-400 to-cyan-600 h-2 rounded-full transition-all duration-500"
                      style={{ 
                        width: `0%` 
                      }}
                    ></div>
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    %0 tamamlandı
                  </div>
                </div>
              </div>
            </div>

            {/* İstatistikler */}
            <div className="bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden">
              <div className="flex items-center justify-between px-4 py-3 border-b border-gray-100 dark:border-gray-700">
                <div className="flex items-center">
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-gray-700 dark:text-gray-300 mr-2">
                    <path d="M3 22H21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M6 18V11" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M10 18V7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M14 18V14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M18 18V4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <h2 className="text-base font-semibold text-gray-800 dark:text-white">İstatistikler</h2>
                </div>
                <Link 
                  to="/dashboard/profile" 
                  className="text-xs text-tuber-pink dark:text-pink-400 hover:text-tuber-purple dark:hover:text-purple-400 transition-colors duration-200 flex items-center"
                >
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  Profilim
                </Link>
              </div>
              <div className="p-4 relative">
                {/* TikTok ile Giriş Alanı */}
                <div className="flex flex-col items-center justify-center min-h-[200px]">
                  {tiktokLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 dark:border-white"></div>
                      <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">Yükleniyor...</span>
                    </div>
                  ) : !tiktokUser ? (
                    <div className="relative w-full">
                      {/* Bulanık Arka Plan Önizleme */}
                      <div className="absolute inset-0 pointer-events-none">
                        <div className="grid grid-cols-2 gap-2 w-full opacity-30 blur-sm">
                          <div className="bg-gradient-to-br from-pink-50 to-pink-100 dark:from-pink-900/20 dark:to-pink-800/20 rounded-lg p-2 flex items-center border border-pink-200 dark:border-pink-700">
                            <FaUsers className="text-pink-600 dark:text-pink-400 text-lg mr-2 flex-shrink-0" />
                            <div className="flex flex-col min-w-0">
                              <div className="font-bold text-sm text-gray-800 dark:text-white truncate">125.4K</div>
                              <div className="text-xs text-gray-600 dark:text-gray-300">Takipçi</div>
                            </div>
                          </div>
                          <div className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 rounded-lg p-2 flex items-center border border-red-200 dark:border-red-700">
                            <FaHeart className="text-red-600 dark:text-red-400 text-lg mr-2 flex-shrink-0" />
                            <div className="flex flex-col min-w-0">
                              <div className="font-bold text-sm text-gray-800 dark:text-white truncate">2.5M</div>
                              <div className="text-xs text-gray-600 dark:text-gray-300">Beğeni</div>
                            </div>
                          </div>
                          <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-2 flex items-center border border-blue-200 dark:border-blue-700">
                            <FaVideo className="text-blue-600 dark:text-blue-400 text-lg mr-2 flex-shrink-0" />
                            <div className="flex flex-col min-w-0">
                              <div className="font-bold text-sm text-gray-800 dark:text-white truncate">245</div>
                              <div className="text-xs text-gray-600 dark:text-gray-300">Video</div>
                            </div>
                          </div>
                          <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg p-2 flex items-center border border-purple-200 dark:border-purple-700">
                            <FaUsers className="text-purple-600 dark:text-purple-400 text-lg mr-2 flex-shrink-0" />
                            <div className="flex flex-col min-w-0">
                              <div className="font-bold text-sm text-gray-800 dark:text-white truncate">850</div>
                              <div className="text-xs text-gray-600 dark:text-gray-300">Takip</div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Ortalanmış Giriş Butonu */}
                      <div className="relative z-10 flex flex-col items-center justify-center text-center py-8">
                        <button
                          className="bg-gray-800 text-white dark:bg-white dark:text-gray-800 px-6 py-3 rounded-full font-semibold shadow-lg hover:bg-gray-900 dark:hover:bg-gray-200 transition-all duration-200 flex items-center mx-auto mb-3 hover:scale-105"
                          onClick={handleTikTokLogin}
                          disabled={tiktokLoading}
                        >
                          <SiTiktok className="w-5 h-5 mr-2" />
                          TikTok ile Giriş Yap
                        </button>
                        <p className="text-xs text-gray-500 dark:text-gray-400 max-w-xs leading-relaxed">
                          TikTok hesabınızla giriş yaparak istatistiklerinizi görüntüleyin
                        </p>
                      </div>
                    </div>
                  ) : (
                    <>
                      <div className="flex flex-col items-center mb-4 w-full">
                        <div className="relative">
                          <img
                            src={tiktokUser.avatar_url || 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face&auto=format&q=80'}
                            alt="Profil"
                            className="w-16 h-16 rounded-full mb-2 border-2 border-gray-200 shadow"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face&auto=format&q=80';
                            }}
                          />
                          {tiktokUser.is_verified && (
                            <div className="absolute bottom-1 right-0 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center border-2 border-white dark:border-gray-800 shadow-md">
                              <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            </div>
                          )}
                        </div>
                        <div className="font-bold text-lg text-center">{tiktokUser.display_name}</div>
                        {tiktokUser.username && (
                          <div className="text-xs text-gray-500 dark:text-gray-400 text-center mb-1">
                            @{tiktokUser.username}
                          </div>
                        )}
                        {/* TikTok Bio */}
                        {tiktokUser.tiktok_bio && (
                          <div className="text-xs text-gray-600 dark:text-gray-300 text-center mb-3 max-w-xs">
                            {tiktokUser.tiktok_bio}
                          </div>
                        )}
                      </div>

                      <div className="grid grid-cols-2 gap-2 w-full">
                        <div className="bg-gradient-to-br from-pink-50 to-pink-100 dark:from-pink-900/20 dark:to-pink-800/20 rounded-lg p-2 flex items-center border border-pink-200 dark:border-pink-700">
                          <FaUsers className="text-pink-600 dark:text-pink-400 text-lg mr-2 flex-shrink-0" />
                          <div className="flex flex-col min-w-0">
                            <div className="font-bold text-sm text-gray-800 dark:text-white truncate">
                              {tiktokUser.followers_count?.toLocaleString() ?? '0'}
                            </div>
                            <div className="text-xs text-gray-600 dark:text-gray-300">Takipçi</div>
                          </div>
                        </div>

                        <div className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 rounded-lg p-2 flex items-center border border-red-200 dark:border-red-700">
                          <FaHeart className="text-red-600 dark:text-red-400 text-lg mr-2 flex-shrink-0" />
                          <div className="flex flex-col min-w-0">
                            <div className="font-bold text-sm text-gray-800 dark:text-white truncate">
                              {tiktokUser.likes_count?.toLocaleString() ?? '0'}
                            </div>
                            <div className="text-xs text-gray-600 dark:text-gray-300">Beğeni</div>
                          </div>
                        </div>

                        <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-2 flex items-center border border-blue-200 dark:border-blue-700">
                          <FaVideo className="text-blue-600 dark:text-blue-400 text-lg mr-2 flex-shrink-0" />
                          <div className="flex flex-col min-w-0">
                            <div className="font-bold text-sm text-gray-800 dark:text-white truncate">
                              {tiktokUser.video_count?.toLocaleString() ?? '0'}
                            </div>
                            <div className="text-xs text-gray-600 dark:text-gray-300">Video</div>
                          </div>
                        </div>

                        <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg p-2 flex items-center border border-purple-200 dark:border-purple-700">
                          <FaUsers className="text-purple-600 dark:text-purple-400 text-lg mr-2 flex-shrink-0" />
                          <div className="flex flex-col min-w-0">
                            <div className="font-bold text-sm text-gray-800 dark:text-white truncate">
                              {tiktokUser.following_count?.toLocaleString() ?? '0'}
                            </div>
                            <div className="text-xs text-gray-600 dark:text-gray-300">Takip</div>
                          </div>
                        </div>
                      </div>


                    </>
                  )}
                </div>
              </div>
            </div>


          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-[#0d0c11] flex flex-col" style={{
      width: '100%',
      maxWidth: '100%',
      overflowX: 'hidden'
    }}>
      {/* Topbar */}
      <Topbar
        onLogout={onLogout}
        refreshTrigger={refreshTrigger}
      />

      <div className="flex flex-1 overflow-hidden" style={{
        width: '100%',
        maxWidth: '100%',
        overflowX: 'hidden'
      }}>
        {/* Sidebar */}
        <div className={`fixed top-16 left-0 h-[calc(100vh-4rem)] z-10 ${isMobile ? 'w-[280px]' : (isSidebarOpen ? 'w-[280px]' : 'w-[78px]')} transition-all duration-300 ease-in-out ${isMobile && !isSidebarOpen ? '-translate-x-full' : ''}`} style={{pointerEvents: 'auto'}}>
          <Sidebar
            onLogout={onLogout}
          />
        </div>

        {/* Ana içerik */}
        <div className={`flex-1 flex flex-col overflow-hidden transition-all duration-300 ease-in-out relative z-0 ${isSidebarOpen ? 'sidebar-open' : 'sidebar-closed'}`} style={{
          marginLeft: isMobile ? 0 : (isSidebarOpen ? '280px' : '78px'),
          width: isMobile ? '100%' : `calc(100vw - ${isSidebarOpen ? '280px' : '78px'})`,
          maxWidth: isMobile ? '100%' : `calc(100vw - ${isSidebarOpen ? '280px' : '78px'})`,
          overflowX: 'hidden',
          position: 'relative'
        }}>
          {/* İçerik alanı */}
          <main className="w-full flex-1 overflow-y-auto overflow-x-hidden" style={{
            width: '100%',
            maxWidth: '100%',
            padding: isMobile ? '0.25rem' : '0.25rem',
            boxSizing: 'border-box'
          }}>
            {/* Sayfa içeriğini render et */}
            {renderContent()}
          </main>
        </div>
      </div>

      {/* Duyuru Modal */}
      {isAnnouncementModalOpen && selectedAnnouncement && (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-md flex items-center justify-center z-50 p-4">
          <div className="relative bg-white dark:bg-[#16151c] rounded-xl overflow-hidden border border-gray-200 dark:border-gray-700 max-w-2xl w-full max-h-[80vh] overflow-y-auto shadow-2xl">
            {/* Modal Header */}
            <div className="sticky top-0 bg-white dark:bg-[#16151c] border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex items-center justify-between">
              <div className="flex items-center">
                <FaBullhorn className="text-[#FF3E71] mr-3 text-lg" />
                <h2 className="text-xl font-bold text-gray-800 dark:text-white">Duyuru Detayı</h2>
              </div>
              <button
                onClick={() => {
                  setIsAnnouncementModalOpen(false);
                  setSelectedAnnouncement(null);
                }}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Modal Content */}
            <div className="p-6">
              {/* Tarih ve Kategori */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                  <BsCalendar3 className="mr-2" />
                  <span>{selectedAnnouncement.dateFormatted}</span>
                  {['Genel', 'Etkinlik', 'Önemli', 'Marka İş Birliği'].includes(selectedAnnouncement?.type || '') && (
                    <span className={`ml-2 px-3 py-1 rounded-full text-xs font-medium whitespace-nowrap ${getAnnouncementTypeStyles(selectedAnnouncement.type || '')}`}>
                      {selectedAnnouncement.type}
                    </span>
                  )}
                </div>
              </div>

              {/* Başlık */}
              <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">
                {selectedAnnouncement.title}
              </h1>

              {/* İçerik */}
              <div className="prose prose-gray dark:prose-invert max-w-none">
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed whitespace-pre-wrap">
                  {selectedAnnouncement.content || selectedAnnouncement.description}
                </p>
              </div>

              {/* Resim varsa göster */}
              {selectedAnnouncement.image_url && (
                <div className="mt-6">
                  <img
                    src={selectedAnnouncement.image_url}
                    alt={selectedAnnouncement.title}
                    className="w-full rounded-lg shadow-md"
                    onError={(e) => {
                      (e.target as HTMLImageElement).style.display = 'none';
                    }}
                  />
                </div>
              )}
            </div>

            {/* Modal Footer */}
            <div className="sticky bottom-0 bg-gray-50 dark:bg-gray-800/50 px-6 py-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex justify-end">
                <button
                  onClick={() => {
                    setIsAnnouncementModalOpen(false);
                    setSelectedAnnouncement(null);
                  }}
                  className="px-4 py-2 bg-[#FF3E71] text-white rounded-lg hover:bg-[#FF3E71]/90 transition-colors font-medium"
                >
                  Kapat
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;