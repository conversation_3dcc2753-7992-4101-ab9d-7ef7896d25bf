import React, { useEffect, useState, useContext } from 'react';
import { useTikTok } from '../hooks/useTikTok';
import { SidebarContext } from '../contexts/SidebarContext';
import { motion } from 'framer-motion';
import { SiT<PERSON><PERSON> } from 'react-icons/si';
import { FaHeart, FaVideo, FaEye, FaShare, FaPlayCircle, FaUnlink, FaComment, FaExternalLinkAlt } from 'react-icons/fa';
import { MdVerified } from 'react-icons/md';
import { HiChartBar, HiTrendingUp } from 'react-icons/hi';
import { Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, BarChart } from 'recharts';
import { formatNumber, formatDuration, formatDate } from '../utils/formatters';

interface TikTokVideo {
  id: string;
  title: string;
  cover_image_url: string;
  share_url: string;
  video_description: string;
  duration: number;
  create_time: string;
  view_count: number;
  like_count: number;
  comment_count: number;
  share_count: number;
  engagement_rate: number;
  hashtags: string[];
}

interface Analytics {
  total_views: number;
  engagement_rate: number;
  average_views: number;
  average_likes: number;
  average_comments: number;
  average_shares: number;
  most_used_hashtags: { [key: string]: number };
  performance_data: Array<{
    date: string;
    title: string;
    views: number;
    likes: number;
    comments: number;
    shares: number;
    engagement_rate: number;
  }>;
}

const Profile: React.FC = () => {
  const { isMobile } = useContext(SidebarContext);
  const { tiktokUser, loading, unlinkTikTokAccount } = useTikTok();
  const [videos, setVideos] = useState<TikTokVideo[]>([]);
  const [analytics, setAnalytics] = useState<Analytics | null>(null);
  const [isDisconnecting, setIsDisconnecting] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  // TikTok durumunu kontrol et
  useEffect(() => {
    const initializeProfile = async () => {
      setIsInitialLoading(false);
    };

    initializeProfile();
  }, []); // Empty dependency array is correct here

  // URL parametrelerini kontrol et (TikTok bağlantı sonucu)
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const tiktokLink = urlParams.get('tiktok_link');
    const message = urlParams.get('message');

    if (tiktokLink === 'success') {
      console.log('TikTok bağlantısı başarılı, context yenileniyor...');
      // URL'yi temizle
      window.history.replaceState({}, document.title, window.location.pathname);
      // TikTok context'i yenile
      window.dispatchEvent(new CustomEvent('tiktokLinked'));
      // Kısa bir süre sonra sayfayı yenile
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } else if (tiktokLink === 'error' && message) {
      alert('TikTok bağlantı hatası: ' + decodeURIComponent(message));
      // URL'yi temizle
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, []);

  useEffect(() => {
    const fetchVideos = async () => {
      console.log('fetchVideos çağrıldı, tiktokUser:', tiktokUser);

      if (tiktokUser) {
        try {
          console.log('Video API çağrısı yapılıyor...');
          const response = await fetch('/backend/api/tiktok-videos.php', {
            credentials: 'include'
          });

          console.log('Video API yanıtı:', response.status);
          const data = await response.json();
          console.log('Video API verisi:', data);

          if (data.status === 'success') {
            setVideos(data.data.videos || []);
            setAnalytics(data.data.analytics || null);
            console.log('Videolar set edildi:', data.data.videos?.length || 0);
          } else {
            console.error('Videos API error:', data.message);
            setVideos([]);
            setAnalytics(null);
          }
        } catch (error) {
          console.error('Videos yüklenirken hata:', error);
          setVideos([]);
          setAnalytics(null);
        }
      } else {
        console.log('TikTok user yok, videolar temizleniyor');
        setVideos([]);
        setAnalytics(null);
      }
    };

    fetchVideos();
  }, [tiktokUser]);

  // TikTok hesap bağlantısını kes
  const handleDisconnectTikTok = async () => {
    setIsDisconnecting(true);
    try {
      const success = await unlinkTikTokAccount();
      if (success) {
        setVideos([]); // Videoları temizle
        setAnalytics(null); // Analytics'i temizle
        // TikTok hesap bağlantısı başarıyla kaldırıldı
      } else {
        console.error('TikTok bağlantısı kaldırılamadı');
      }
    } catch (error) {
      console.error('TikTok bağlantısı kaldırılırken hata:', error);
    } finally {
      setIsDisconnecting(false);
    }
  };

  // TikTok hesabını bağla
  const handleConnectTikTok = () => {
    const clientKey = 'awfw8k9nim1e8dmu';
    const redirectUri = encodeURIComponent('https://akademi.tuberajans.com/backend/api/tiktok-callback.php');
    const state = 'profile_' + Math.random().toString(36).substring(2, 15);
    const scope = 'user.info.basic,user.info.profile,user.info.stats,video.list';

    const url = `https://www.tiktok.com/v2/auth/authorize/` +
      `?client_key=${clientKey}` +
      `&scope=${scope}` +
      `&response_type=code` +
      `&redirect_uri=${redirectUri}` +
      `&state=${state}`;

    localStorage.setItem('tiktok_oauth_state', state);
    localStorage.setItem('tiktok_redirect_after', 'profile');

    // Aynı sekmede aç
    window.location.href = url;
  };

  // TikTok OAuth popup mesajlarını dinle
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      console.log('Received message:', event.data); // Debug için
      if (event.data.type === 'tiktok_oauth_result') {
        if (event.data.status === 'success') {
          // Başarılı bağlantı - TikTok context'i yenile
          console.log('TikTok bağlantısı başarılı, context yenileniyor...');
          // Önce TikTok context'i yenile
          window.dispatchEvent(new CustomEvent('tiktokLinked'));
          // Sonra sayfayı yenile
          setTimeout(() => {
            window.location.reload();
          }, 500);
        } else {
          // Hata durumu - kullanıcıya bildir
          alert('TikTok bağlantı hatası: ' + event.data.message);
        }
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  // Loading durumlarını birleştir
  const isLoading = loading || isInitialLoading;

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-tuber-pink mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Yükleniyor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container" style={{
      maxWidth: isMobile ? '100%' : 'none',
      width: isMobile ? '100%' : 'auto',
      overflowX: 'hidden',
      position: 'relative',
      left: 0,
      marginLeft: 0,
      paddingTop: isMobile ? '0.5rem' : '0.75rem',
      paddingLeft: isMobile ? '0.25rem' : '0rem',
      paddingRight: isMobile ? '0.25rem' : '1rem',
      boxSizing: 'border-box'
    }}>
      <div className="py-6">
        


        {tiktokUser ? (
          <motion.div 
            className="space-y-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.1 }}
          >
            
            {/* TikTok Profil Kartı */}
            <div className="bg-white dark:bg-[#16151c] rounded-xl shadow-lg border border-gray-100 dark:border-gray-800 overflow-hidden">
              {/* Profil Bilgileri */}
              <div className="p-4 lg:p-6">
                <div className="space-y-2">
                  {/* Üst kısım - Profil bilgileri */}
                  <div className="flex items-center space-x-4">
                    <div className="relative flex-shrink-0">
                      <img 
                        src={tiktokUser.avatar_url}
                        alt={tiktokUser.display_name}
                        className="w-20 h-20 lg:w-24 lg:h-24 rounded-full object-cover border-4 border-white dark:border-gray-700 shadow-lg"
                      />
                      {tiktokUser.is_verified && (
                        <div className="absolute -bottom-1 -right-1 w-7 h-7 bg-blue-500 rounded-full flex items-center justify-center border-2 border-white dark:border-gray-700">
                          <MdVerified className="w-4 h-4 text-white" />
                        </div>
                      )}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center mb-2">
                        <h2 className="text-xl lg:text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                          Tuber Ajans
                          {' '}
                          <button
                            onClick={handleDisconnectTikTok}
                            disabled={isDisconnecting}
                            className="ml-3 text-gray-400 hover:text-red-500 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed p-1 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20"
                            title={isDisconnecting ? 'Bağlantı kesiliyor...' : 'TikTok bağlantısını kes'}
                          >
                            <FaUnlink className="text-sm" />
                          </button>
                        </h2>
                      </div>
                      <p className="text-gray-600 dark:text-gray-400">@{tiktokUser.username}</p>
                      {/* TikTok Bio */}
                      {tiktokUser.tiktok_bio && (
                        <p className="text-gray-600 dark:text-gray-400 mt-2">
                          {tiktokUser.tiktok_bio}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Alt kısım - İstatistikler */}
                  <div className="ml-20 lg:ml-24">
                    <div className="flex justify-start space-x-4 md:space-x-6 lg:space-x-8">
                      <div className="text-center">
                        <div className="text-sm lg:text-2xl font-bold text-gray-900 dark:text-white">
                          {formatNumber(tiktokUser.followers_count)}
                        </div>
                        <div className="text-xs lg:text-sm text-gray-600 dark:text-gray-400">Takipçi</div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm lg:text-2xl font-bold text-gray-900 dark:text-white">
                          {formatNumber(tiktokUser.following_count)}
                        </div>
                        <div className="text-xs lg:text-sm text-gray-600 dark:text-gray-400">Takip</div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm lg:text-2xl font-bold text-gray-900 dark:text-white">
                          {formatNumber(tiktokUser.likes_count)}
                        </div>
                        <div className="text-xs lg:text-sm text-gray-600 dark:text-gray-400">Beğeni</div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm lg:text-2xl font-bold text-gray-900 dark:text-white">
                          {formatNumber(tiktokUser.video_count)}
                        </div>
                        <div className="text-xs lg:text-sm text-gray-600 dark:text-gray-400">Video</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Toplam Performans Metrikleri */}
            {analytics && (
              <div className="bg-white dark:bg-[#16151c] rounded-xl shadow-lg border border-gray-100 dark:border-gray-800">
                <div className="px-4 lg:px-6 py-4 border-b border-gray-100 dark:border-gray-700">
                  <div className="flex items-center">
                    <HiChartBar className="text-tuber-pink mr-2 text-xl" />
                    <h3 className="text-lg lg:text-xl font-semibold text-gray-900 dark:text-white">Toplam Performans Metrikleri</h3>
                  </div>
                </div>
                
                <div className="p-4 lg:p-6">
                  <div className="grid grid-cols-2 lg:grid-cols-4 xl:grid-cols-7 gap-2 lg:gap-4">
                    <div className="bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 rounded-xl p-4 border border-red-200 dark:border-red-800">
                      <div className="text-xs text-red-600 dark:text-red-400 mb-1">Toplam İzlenme</div>
                      <div className="text-xl font-bold text-red-700 dark:text-red-300">
                        {formatNumber(analytics.total_views)}
                      </div>
                    </div>
                    

                    <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-xl p-4 border border-blue-200 dark:border-blue-800">
                      <div className="text-xs text-blue-600 dark:text-blue-400 mb-1">Etkileşim Oranı</div>
                      <div className="text-xl font-bold text-blue-700 dark:text-blue-300">
                        {analytics.engagement_rate}%
                      </div>
                    </div>
                    
                    <div className="bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-xl p-4 border border-orange-200 dark:border-orange-800">
                      <div className="text-xs text-orange-600 dark:text-orange-400 mb-1">Ortalama İzlenme</div>
                      <div className="text-xl font-bold text-orange-700 dark:text-orange-300">
                        {formatNumber(analytics.average_views)}
                      </div>
                    </div>
                    
                    <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-xl p-4 border border-green-200 dark:border-green-800">
                      <div className="text-xs text-green-600 dark:text-green-400 mb-1">Ortalama Video Beğeni</div>
                      <div className="text-xl font-bold text-green-700 dark:text-green-300">
                        {formatNumber(analytics.average_likes)}
                      </div>
                    </div>
                    
                    <div className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-xl p-4 border border-purple-200 dark:border-purple-800">
                      <div className="text-xs text-purple-600 dark:text-purple-400 mb-1">Ortalama Video Yorum</div>
                      <div className="text-xl font-bold text-purple-700 dark:text-purple-300">
                        {analytics.average_comments}
                      </div>
                    </div>
                    
                    <div className="bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-xl p-4 border border-orange-200 dark:border-orange-800">
                      <div className="text-xs text-orange-600 dark:text-orange-400 mb-1">Ortalama Video Paylaşım</div>
                      <div className="text-xl font-bold text-orange-700 dark:text-orange-300">
                        {analytics.average_shares}
                      </div>
                    </div>
                    

                  </div>
                </div>
              </div>
            )}

            {/* En Çok Kullanılan Hashtag'ler */}
            {analytics && (Object.keys(analytics.most_used_hashtags || {}).length > 0 || true) && (
              <div className="bg-white dark:bg-[#16151c] rounded-xl shadow-lg border border-gray-100 dark:border-gray-800">
                <div className="px-4 lg:px-6 py-4 border-b border-gray-100 dark:border-gray-700">
                  <div className="flex items-center">
                    <span className="text-tuber-pink mr-2 text-xl">#</span>
                    <h3 className="text-lg lg:text-xl font-semibold text-gray-900 dark:text-white">En Çok Kullanılan Hashtag'ler</h3>
                  </div>
                </div>
                
                <div className="p-4 lg:p-6">
                  <div className="flex flex-wrap gap-3">
                    {analytics.most_used_hashtags && Object.keys(analytics.most_used_hashtags).length > 0 ? (
                      Object.entries(analytics.most_used_hashtags).map(([hashtag, count]) => (
                        <span
                          key={hashtag}
                          className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-tuber-pink/10 to-tuber-purple/10 text-tuber-pink border border-tuber-pink/20 hover:border-tuber-pink/40 hover:shadow-sm transition-all duration-200"
                        >
                          <span className="font-semibold">#</span>
                          <span className="ml-0.5">{hashtag}</span>
                          <span className="ml-2 flex items-center justify-center w-5 h-5 bg-tuber-pink/20 text-tuber-pink text-xs rounded-full font-semibold">
                            {count}
                          </span>
                        </span>
                      ))
                    ) : (
                      // Varsayılan hashtag'ler (veri yoksa)
                      [
                        { hashtag: 'cover', count: 2 },
                        { hashtag: 'kışarkıları', count: 1 },
                        { hashtag: 'animasyonmüzikleri', count: 1 },
                        { hashtag: 'athena', count: 1 },
                        { hashtag: 'yorulmakolmaz', count: 1 },
                        { hashtag: 'duman', count: 1 },
                        { hashtag: 'manga', count: 1 },
                        { hashtag: 'wecouldbethesame', count: 1 }
                      ].map(({ hashtag, count }) => (
                        <span
                          key={hashtag}
                          className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r from-tuber-pink/10 to-tuber-purple/10 text-tuber-pink border border-tuber-pink/20 hover:border-tuber-pink/40 hover:shadow-sm transition-all duration-200"
                        >
                          <span className="font-semibold">#</span>
                          <span className="ml-0.5">{hashtag}</span>
                          <span className="ml-2 flex items-center justify-center w-5 h-5 bg-tuber-pink/20 text-tuber-pink text-xs rounded-full font-semibold">
                            {count}
                          </span>
                        </span>
                      ))
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Video Detayları */}
            <div className="bg-white dark:bg-[#16151c] rounded-xl shadow-lg border border-gray-100 dark:border-gray-800">
              <div className="px-4 md:px-6 py-4 border-b border-gray-100 dark:border-gray-700">
                <div className="flex items-center">
                  <FaVideo className="text-tuber-pink mr-2 text-xl" />
                  <div>
                    <h3 className="text-lg lg:text-xl font-semibold text-gray-900 dark:text-white">Video Detayları ({videos?.length || 0} video)</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      Son video performansları
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="p-4 md:p-6">
                {videos && videos.length > 0 ? (
                  <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-8 gap-2 md:gap-3">
                    {videos.map((video, index) => (
                      <motion.div
                        key={video.id}
                        className="group cursor-pointer bg-gray-50 dark:bg-gray-800/50 rounded-lg overflow-hidden hover:shadow-lg hover:scale-105 transition-all duration-300 border border-gray-200 dark:border-gray-700"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                      >
                        <div className="relative aspect-[9/16] bg-gray-200 dark:bg-gray-700 overflow-hidden">
                          <img
                            src={video.cover_image_url || video.thumbnail || `https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=300&h=400&fit=crop&random=${video.video_id || index}`}
                            alt={video.title}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                            onError={(e) => {
                              // Thumbnail yüklenemezse Unsplash placeholder göster
                              const target = e.target as HTMLImageElement;
                              const fallbackUrl = `https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=300&h=400&fit=crop&random=${video.video_id || index}`;
                              if (target.src !== fallbackUrl) {
                                target.src = fallbackUrl;
                              } else {
                                // Unsplash da yüklenemezse SVG placeholder göster
                                target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik04MCA3MFYxMzBMMTMwIDEwMEw4MCA3MFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+';
                              }
                            }}
                          />
                          <div className="absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-colors duration-300"></div>
                          
                          {/* Play Button */}
                          <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <FaPlayCircle className="text-white text-2xl" />
                          </div>

                          {/* Duration ve Engagement Rate */}
                          <div className="absolute top-1 left-1 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded">
                            {formatDuration(video.duration)}
                          </div>
                          <div className="absolute top-1 right-1 bg-tuber-pink/90 text-white text-xs px-1.5 py-0.5 rounded">
                            %{video.engagement_rate || 0}
                          </div>
                        </div>
                        
                        <div className="p-2">
                          <div className="flex items-start justify-between mb-1 gap-1">
                            <div className="flex-1 min-w-0">
                              <h4 className="font-medium text-gray-900 dark:text-white text-xs line-clamp-1 mb-1 pr-1">
                                {video.title || 'Başlıksız Video'}
                              </h4>
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                {formatDate(video.create_time)}
                              </p>
                            </div>
                            <a
                              href={video.share_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-tuber-pink hover:text-tuber-purple transition-colors flex-shrink-0 p-1 hover:bg-tuber-pink/10 rounded min-w-[24px] min-h-[24px] flex items-center justify-center"
                              title="Videoyu TikTok'ta aç"
                            >
                              <FaExternalLinkAlt className="text-sm" />
                            </a>
                          </div>

                          {/* Video İstatistikleri - Daha Geniş */}
                          <div className="grid grid-cols-2 gap-x-2 gap-y-1 text-xs">
                            <div className="flex items-center">
                              <FaEye className="text-blue-500 text-xs" />
                              <span className="text-gray-600 dark:text-gray-400 text-xs ml-1">
                                {formatNumber(video.view_count || 0)}
                              </span>
                            </div>
                            <div className="flex items-center">
                              <FaHeart className="text-red-500 text-xs" />
                              <span className="text-gray-600 dark:text-gray-400 text-xs ml-1">
                                {formatNumber(video.like_count || 0)}
                              </span>
                            </div>
                            <div className="flex items-center">
                              <FaComment className="text-green-500 text-xs" />
                              <span className="text-gray-600 dark:text-gray-400 text-xs ml-1">
                                {formatNumber(video.comment_count || 0)}
                              </span>
                            </div>
                            <div className="flex items-center">
                              <FaShare className="text-purple-500 text-xs" />
                              <span className="text-gray-600 dark:text-gray-400 text-xs ml-1">
                                {formatNumber(video.share_count || 0)}
                              </span>
                            </div>
                          </div>

                          {/* Hashtag'ler */}
                          {video.hashtags && video.hashtags.length > 0 && (
                            <div className="mt-1.5 pt-1.5 border-t border-gray-200 dark:border-gray-700">
                              <div className="flex flex-wrap gap-0.5">
                                {video.hashtags.slice(0, 1).map((hashtag) => (
                                  <span
                                    key={hashtag}
                                    className="text-xs bg-tuber-pink/10 text-tuber-pink px-1 py-0.5 rounded"
                                  >
                                    #{hashtag}
                                  </span>
                                ))}
                                {video.hashtags.length > 1 && (
                                  <span className="text-xs text-gray-500 dark:text-gray-400 px-0.5">
                                    +{video.hashtags.length - 1}
                                  </span>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      </motion.div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <FaVideo className="mx-auto text-6xl text-gray-300 dark:text-gray-600 mb-4" />
                    <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Henüz video bulunamadı</h4>
                    <p className="text-gray-600 dark:text-gray-400">
                      TikTok hesabınızda video bulunmuyor veya videolarınız henüz yüklenmemiş.
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Video Performans Analizi */}
            {analytics?.performance_data && analytics.performance_data.length > 0 && (
              <div className="bg-white dark:bg-[#16151c] rounded-xl shadow-lg border border-gray-100 dark:border-gray-800">
                <div className="px-4 md:px-6 py-4 border-b border-gray-100 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <HiChartBar className="text-tuber-pink mr-2 text-xl" />
                      <h3 className="text-lg lg:text-xl font-semibold text-gray-900 dark:text-white">Video Performans Analizi</h3>
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      Son {analytics?.performance_data?.length || 0} video
                    </div>
                  </div>
                </div>
                
                <div className="p-4 md:p-6">
                  {/* Metrik Seçenekleri */}
                  <div className="grid grid-cols-2 md:flex md:flex-wrap gap-2 mb-4 md:mb-6">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-blue-500 rounded mr-2"></div>
                      <span className="text-xs md:text-sm text-gray-600 dark:text-gray-400">İzlenme</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-red-500 rounded mr-2"></div>
                      <span className="text-xs md:text-sm text-gray-600 dark:text-gray-400">Beğeni</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-green-500 rounded mr-2"></div>
                      <span className="text-xs md:text-sm text-gray-600 dark:text-gray-400">Yorum</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-purple-500 rounded mr-2"></div>
                      <span className="text-xs md:text-sm text-gray-600 dark:text-gray-400">Paylaşım</span>
                    </div>
                  </div>

                  <div className="h-72 md:h-96">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart 
                        data={analytics?.performance_data?.slice(-8).map(item => ({
                          ...item,
                          views_scaled: Math.log10(Math.max(item.views, 1)),
                          likes_scaled: Math.log10(Math.max(item.likes, 1)) * 2,
                          comments_scaled: Math.log10(Math.max(item.comments, 1)) * 3,
                          shares_scaled: Math.log10(Math.max(item.shares, 1)) * 4
                        }))} 
                        margin={{ top: 5, right: 5, left: 5, bottom: 60 }}
                        barCategoryGap="20%"
                      >
                        <CartesianGrid strokeDasharray="3 3" stroke="#e0e7ff" />
                        <XAxis 
                          dataKey="date" 
                          tick={{ fontSize: 9, fill: '#6b7280' }}
                          angle={-45}
                          textAnchor="end"
                          height={60}
                          interval={0}
                          tickFormatter={(value) => {
                            const date = new Date(value);
                            // Mobilde sadece gün/ay, desktop'ta tam tarih
                            if (window.innerWidth < 768) {
                              return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}`;
                            } else {
                              return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
                            }
                          }}
                        />
                        <YAxis 
                          tick={{ fontSize: 11, fill: '#6b7280' }}
                          tickFormatter={(value) => {
                            return `${value.toFixed(1)}`;
                          }}
                        />
                        <Tooltip 
                          contentStyle={{ 
                            backgroundColor: '#ffffff',
                            border: '1px solid #e5e7eb',
                            borderRadius: '12px',
                            fontSize: '13px',
                            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
                          }}
                          labelFormatter={(value) => {
                            const date = new Date(value);
                            return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
                          }}
                          formatter={(_value: number, name: string, props: { payload?: { date: string } }) => {
                            let originalValue = 0;
                            const originalData = analytics?.performance_data?.find(item => item.date === props.payload?.date);
                            if (originalData) {
                              if (name === 'views_scaled') originalValue = originalData.views;
                              else if (name === 'likes_scaled') originalValue = originalData.likes;
                              else if (name === 'comments_scaled') originalValue = originalData.comments;
                              else if (name === 'shares_scaled') originalValue = originalData.shares;
                            }
                            const formattedValue = formatNumber(originalValue);
                            const labels: { [key: string]: string } = {
                              'views_scaled': 'İzlenme',
                              'likes_scaled': 'Beğeni', 
                              'comments_scaled': 'Yorum',
                              'shares_scaled': 'Paylaşım'
                            };
                            return [formattedValue, labels[name] || name];
                          }}
                        />
                        <Bar dataKey="views_scaled" fill="#3b82f6" radius={[1, 1, 0, 0]} minPointSize={3} />
                        <Bar dataKey="likes_scaled" fill="#ef4444" radius={[1, 1, 0, 0]} minPointSize={3} />
                        <Bar dataKey="comments_scaled" fill="#10b981" radius={[1, 1, 0, 0]} minPointSize={3} />
                        <Bar dataKey="shares_scaled" fill="#8b5cf6" radius={[1, 1, 0, 0]} minPointSize={3} />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                  
                  <div className="mt-0 text-center">
                    <p className="text-xs md:text-sm text-gray-500 dark:text-gray-400">
                      Son 8 video performansı • Grafik üzerine gelerek detaylı bilgi alabilirsiniz
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Etkileşim Oranı Trendi */}
            {analytics?.performance_data && analytics.performance_data.length > 0 && (
              <div className="bg-white dark:bg-[#16151c] rounded-xl shadow-lg border border-gray-100 dark:border-gray-800">
                <div className="px-4 md:px-6 py-4 border-b border-gray-100 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <HiTrendingUp className="text-tuber-pink mr-2 text-xl" />
                      <h3 className="text-lg lg:text-xl font-semibold text-gray-900 dark:text-white">Etkileşim Oranı Trendi</h3>
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      Ortalama: {analytics?.engagement_rate}%
                    </div>
                  </div>
                </div>
                
                <div className="p-4 md:p-6">
                  <div className="h-56 md:h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart 
                        data={analytics?.performance_data?.slice(-10) || []}
                        margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                        <XAxis 
                          dataKey="date" 
                          tick={{ fontSize: 10 }}
                          tickFormatter={(value) => {
                            const date = new Date(value);
                            if (window.innerWidth < 768) {
                              return `${date.getDate()}/${(date.getMonth() + 1)}`;
                            } else {
                              return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}`;
                            }
                          }}
                        />
                        <YAxis 
                          tick={{ fontSize: 10 }}
                          domain={['dataMin - 0.1', 'dataMax + 0.1']}
                        />
                        <Tooltip 
                          contentStyle={{ 
                            backgroundColor: 'rgba(255, 255, 255, 0.95)',
                            border: '1px solid #e5e7eb',
                            borderRadius: '8px',
                            fontSize: '11px'
                          }}
                          formatter={(value: number) => [`${value}%`, 'Etkileşim Oranı']}
                          labelFormatter={(value) => {
                            const date = new Date(value);
                            return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;
                          }}
                        />
                        <Line 
                          type="monotone" 
                          dataKey="engagement_rate" 
                          stroke="#ec4899" 
                          strokeWidth={2}
                          dot={{ fill: '#ec4899', strokeWidth: 1, r: 3 }}
                          activeDot={{ r: 5 }}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </div>
            )}



          </motion.div>
        ) : (
          <div className="max-w-2xl mx-auto">
            <div className="bg-white dark:bg-[#16151c] rounded-xl shadow-lg p-6 text-center">
              <div className="mb-6">
                <SiTiktok className="w-16 h-16 mx-auto text-tuber-pink" />
              </div>
              <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
                TikTok Hesabını Bağla
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                TikTok hesabınızı bağlayarak video performansınızı takip edebilir ve detaylı analitiklerinizi görüntüleyebilirsiniz.
              </p>
              <button
                onClick={handleConnectTikTok}
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-full shadow-sm text-white bg-tuber-pink hover:bg-tuber-pink-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-tuber-pink transition-colors duration-200"
              >
                <SiTiktok className="w-5 h-5 mr-2" />
                TikTok ile Bağlan
              </button>
            </div>
          </div>
        )}

      </div>
    </div>
  );
};

export default Profile;
