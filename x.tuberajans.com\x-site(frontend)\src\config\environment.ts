/**
 * Environment Configuration
 * Centralized environment variable management with type safety
 */

// Environment detection
export const isDevelopment = import.meta.env.DEV;
export const isProduction = import.meta.env.PROD;
export const isTest = import.meta.env.MODE === 'test';

// Feature flags
export const useMockData = isDevelopment && import.meta.env.VITE_USE_MOCK === 'true';
export const enableDebugLogs = isDevelopment || import.meta.env.VITE_DEBUG === 'true';
export const enablePerformanceMonitoring = import.meta.env.VITE_PERFORMANCE_MONITORING === 'true';

// API Configuration
export const apiConfig = {
  baseUrl: import.meta.env.VITE_API_BASE_URL || (
    isProduction 
      ? 'https://x.tuberajans.com/backend/x-site'
      : 'http://localhost/backend/x-site'
  ),
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT || '10000'),
  retryAttempts: parseInt(import.meta.env.VITE_API_RETRY_ATTEMPTS || '3'),
} as const;

// Security Configuration
export const securityConfig = {
  tokenKey: 'x_tuber_token',
  userKey: 'x_tuber_user',
  themeKey: 'x_tuber_theme',
  sessionTimeout: parseInt(import.meta.env.VITE_SESSION_TIMEOUT || '3600000'), // 1 hour
  enableCSRF: isProduction,
} as const;

// Application Configuration
export const appConfig = {
  name: 'Tuber Ajans X Yönetim',
  version: import.meta.env.VITE_APP_VERSION || '1.0.0',
  buildDate: import.meta.env.VITE_BUILD_DATE || new Date().toISOString(),
  supportEmail: import.meta.env.VITE_SUPPORT_EMAIL || '<EMAIL>',
} as const;

// Performance Configuration
export const performanceConfig = {
  enableLazyLoading: true,
  enableCodeSplitting: true,
  chunkSizeWarningLimit: 1000, // KB
  enableBundleAnalysis: isDevelopment,
} as const;

// Logging Configuration
export const loggingConfig = {
  level: isDevelopment ? 'debug' : 'error',
  enableConsole: isDevelopment,
  enableRemote: isProduction,
  maxLogEntries: 100,
  remoteEndpoint: import.meta.env.VITE_LOGGING_ENDPOINT,
} as const;

// Validation helpers
export const validateEnvironment = (): string[] => {
  const errors: string[] = [];

  // Required environment variables for production
  if (isProduction) {
    const requiredVars = [
      'VITE_API_BASE_URL',
      'VITE_APP_VERSION',
    ];

    requiredVars.forEach(varName => {
      if (!import.meta.env[varName]) {
        errors.push(`Missing required environment variable: ${varName}`);
      }
    });
  }

  // Validate API timeout
  if (isNaN(apiConfig.timeout) || apiConfig.timeout <= 0) {
    errors.push('Invalid API timeout configuration');
  }

  // Validate session timeout
  if (isNaN(securityConfig.sessionTimeout) || securityConfig.sessionTimeout <= 0) {
    errors.push('Invalid session timeout configuration');
  }

  return errors;
};

// Initialize environment validation
if (isDevelopment) {
  const errors = validateEnvironment();
  if (errors.length > 0) {
    console.warn('Environment validation warnings:', errors);
  }
}

// Export all configurations
export default {
  isDevelopment,
  isProduction,
  isTest,
  useMockData,
  enableDebugLogs,
  enablePerformanceMonitoring,
  apiConfig,
  securityConfig,
  appConfig,
  performanceConfig,
  loggingConfig,
  validateEnvironment,
} as const;

// Type exports for better TypeScript support
export type ApiConfig = typeof apiConfig;
export type SecurityConfig = typeof securityConfig;
export type AppConfig = typeof appConfig;
export type PerformanceConfig = typeof performanceConfig;
export type LoggingConfig = typeof loggingConfig;
