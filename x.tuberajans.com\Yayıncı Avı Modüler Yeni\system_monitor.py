"""
Sistem Durumu Monitörü - Sistem sağlığını izler ve otomatik kurtarma yapar
"""

import time
import threading
import logging
import psutil
import subprocess
from typing import Optional, Dict, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class SystemMonitor:
    """
    Sistem durumunu izleyen ve otomatik kurtarma yapan sınıf.
    """
    
    def __init__(self, main_app=None):
        self.main_app = main_app
        self.monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.check_interval = 30  # 30 saniye aralıklarla kontrol
        
        # Sistem metrikleri
        self.last_health_check = time.time()
        self.consecutive_failures = 0
        self.max_consecutive_failures = 3
        
        # Performance metrikleri
        self.memory_threshold = 85  # %85 memory kullanımı
        self.cpu_threshold = 90     # %90 CPU kullanımı
        
        logger.info("🔍 System Monitor başlatıldı")
    
    def start_monitoring(self):
        """
        Sistem monitörünü başlatır.
        """
        if self.monitoring:
            logger.warning("⚠️ System Monitor zaten çalışıyor")
            return
            
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("✅ System Monitor başlatıldı")
    
    def stop_monitoring(self):
        """
        Sistem monitörünü durdurur.
        """
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("⏹️ System Monitor durduruldu")
    
    def _monitor_loop(self):
        """
        Ana monitör döngüsü.
        """
        while self.monitoring:
            try:
                # Sistem sağlığını kontrol et
                health_status = self._check_system_health()
                
                if health_status['healthy']:
                    self.consecutive_failures = 0
                    self.last_health_check = time.time()
                else:
                    self.consecutive_failures += 1
                    logger.warning(f"⚠️ Sistem sağlığı bozuk: {health_status['issues']}")
                    
                    # Otomatik kurtarma dene
                    if self.consecutive_failures >= self.max_consecutive_failures:
                        logger.error(f"❌ {self.consecutive_failures} ardışık hata, otomatik kurtarma başlatılıyor...")
                        self._attempt_recovery(health_status)
                
                # Performance metrikleri logla
                if time.time() - self.last_health_check > 300:  # 5 dakikada bir
                    self._log_performance_metrics()
                    self.last_health_check = time.time()
                
            except Exception as e:
                logger.error(f"❌ Monitor döngüsü hatası: {e}")
            
            time.sleep(self.check_interval)
    
    def _check_system_health(self) -> Dict[str, Any]:
        """
        Sistem sağlığını kontrol eder.
        
        Returns:
            Dict: Sağlık durumu bilgileri
        """
        issues = []
        healthy = True
        
        try:
            # 1. Main app durumu
            if self.main_app:
                if not self.main_app.cycle_running:
                    issues.append("Main cycle durdu")
                    healthy = False
                
                if not self.main_app.monitor_running:
                    issues.append("Main monitor durdu")
                    healthy = False
            
            # 2. Memory kullanımı
            memory = psutil.virtual_memory()
            if memory.percent > self.memory_threshold:
                issues.append(f"Yüksek memory kullanımı: %{memory.percent:.1f}")
                if memory.percent > 95:
                    healthy = False
            
            # 3. CPU kullanımı
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > self.cpu_threshold:
                issues.append(f"Yüksek CPU kullanımı: %{cpu_percent:.1f}")
                if cpu_percent > 98:
                    healthy = False
            
            # 4. Chrome işlem sayısı
            chrome_processes = self._count_chrome_processes()
            if chrome_processes > 10:  # 10'dan fazla Chrome işlemi
                issues.append(f"Çok fazla Chrome işlemi: {chrome_processes}")
                if chrome_processes > 20:
                    healthy = False
            
            # 5. Disk alanı
            disk = psutil.disk_usage('/')
            if disk.percent > 90:
                issues.append(f"Disk alanı az: %{disk.percent:.1f}")
                if disk.percent > 95:
                    healthy = False
            
        except Exception as e:
            issues.append(f"Health check hatası: {e}")
            healthy = False
        
        return {
            'healthy': healthy,
            'issues': issues,
            'timestamp': datetime.now(),
            'memory_percent': memory.percent if 'memory' in locals() else 0,
            'cpu_percent': cpu_percent if 'cpu_percent' in locals() else 0,
            'chrome_processes': chrome_processes if 'chrome_processes' in locals() else 0
        }
    
    def _count_chrome_processes(self) -> int:
        """
        Chrome işlem sayısını sayar.
        """
        try:
            chrome_count = 0
            for proc in psutil.process_iter(['name']):
                if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                    chrome_count += 1
            return chrome_count
        except Exception:
            return 0
    
    def _attempt_recovery(self, health_status: Dict[str, Any]):
        """
        Otomatik kurtarma işlemlerini yapar.
        
        Args:
            health_status: Sistem sağlık durumu
        """
        try:
            logger.info("🔧 Otomatik kurtarma başlatılıyor...")
            
            # 1. Chrome işlemlerini temizle
            if health_status.get('chrome_processes', 0) > 10:
                logger.info("🔄 Chrome işlemleri temizleniyor...")
                self._cleanup_chrome_processes()
            
            # 2. Memory temizleme
            if health_status.get('memory_percent', 0) > 90:
                logger.info("🔄 Memory temizleme yapılıyor...")
                self._cleanup_memory()
            
            # 3. Main app yeniden başlatma
            if self.main_app and not self.main_app.cycle_running:
                logger.info("🔄 Main app yeniden başlatılıyor...")
                self._restart_main_app()
            
            # Kurtarma sonrası bekleme
            time.sleep(10)
            self.consecutive_failures = 0
            
            logger.info("✅ Otomatik kurtarma tamamlandı")
            
        except Exception as e:
            logger.error(f"❌ Otomatik kurtarma hatası: {e}")
    
    def _cleanup_chrome_processes(self):
        """
        Chrome işlemlerini temizler.
        """
        try:
            subprocess.run(['taskkill', '/f', '/im', 'chrome.exe'], 
                         capture_output=True, text=True, timeout=10)
            subprocess.run(['taskkill', '/f', '/im', 'chromedriver.exe'], 
                         capture_output=True, text=True, timeout=10)
            time.sleep(3)
            logger.info("✅ Chrome işlemleri temizlendi")
        except Exception as e:
            logger.error(f"❌ Chrome temizleme hatası: {e}")
    
    def _cleanup_memory(self):
        """
        Memory temizleme yapar.
        """
        try:
            import gc
            gc.collect()
            logger.info("✅ Memory temizleme yapıldı")
        except Exception as e:
            logger.error(f"❌ Memory temizleme hatası: {e}")
    
    def _restart_main_app(self):
        """
        Main app'i yeniden başlatmaya çalışır.
        """
        try:
            if self.main_app:
                # Önce durdur
                self.main_app.stop_automation()
                time.sleep(5)
                
                # Sonra başlat
                self.main_app.start_automation()
                logger.info("✅ Main app yeniden başlatıldı")
        except Exception as e:
            logger.error(f"❌ Main app yeniden başlatma hatası: {e}")
    
    def _log_performance_metrics(self):
        """
        Performance metriklerini loglar.
        """
        try:
            memory = psutil.virtual_memory()
            cpu = psutil.cpu_percent()
            chrome_count = self._count_chrome_processes()
            
            logger.info(f"📊 Performance: Memory: %{memory.percent:.1f}, CPU: %{cpu:.1f}, Chrome: {chrome_count}")
            
        except Exception as e:
            logger.error(f"❌ Performance logging hatası: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """
        Monitor durumunu döndürür.
        """
        return {
            'monitoring': self.monitoring,
            'consecutive_failures': self.consecutive_failures,
            'last_health_check': self.last_health_check,
            'check_interval': self.check_interval
        }
