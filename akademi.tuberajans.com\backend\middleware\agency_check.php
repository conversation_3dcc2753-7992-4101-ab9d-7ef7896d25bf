<?php
/**
 * <PERSON>jans Üyelik Kontrol Middleware
 * Kullanıcının ajansa kayıtlı olup olmadığını kontrol eder
 */

require_once dirname(__DIR__) . '/config/config.php';

/**
 * <PERSON>llanıcının ajansa kayıtlı olup olmadığını kontrol et
 * Cross-database kontrolü: akademi DB'deki tiktok_username ile takip DB'deki username karş<PERSON>laştırması
 */
function checkAgencyMembership($tiktokUsername) {
    try {
        if (empty($tiktokUsername)) {
            error_log("TikTok username boş");
            return false;
        }

        // Takip veritabanı bağlantısını al
        $dbTakip = getDBTakip();

        if (!$dbTakip) {
            error_log("Takip veritabanı bağlantısı alınamadı");
            return false;
        }

        // publisher_info tablosunda kullanıcıyı ara
        $stmt = $dbTakip->prepare("SELECT id, username, full_name FROM publisher_info WHERE username = ?");
        $stmt->execute([$tiktokUsername]);
        $publisher = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($publisher) {
            error_log("Ajans üyesi bulundu: " . $tiktokUsername . " (ID: " . $publisher['id'] . ")");
            return true;
        } else {
            error_log("Ajans üyesi değil: " . $tiktokUsername);
            return false;
        }

    } catch (Exception $e) {
        error_log("Ajans üyelik kontrolü hatası: " . $e->getMessage());
        return false;
    }
}

/**
 * Kullanıcının TikTok username'ini session'dan veya veritabanından al
 */
function getUserTikTokUsername($userId) {
    try {
        // Önce session'dan kontrol et
        if (isset($_SESSION['tiktok_username']) && !empty($_SESSION['tiktok_username'])) {
            return $_SESSION['tiktok_username'];
        }

        // Session'da yoksa veritabanından al
        $db = getDB();
        $stmt = $db->prepare("SELECT tiktok_username FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $tiktokUsername = $stmt->fetchColumn();

        if ($tiktokUsername) {
            // Session'a kaydet
            $_SESSION['tiktok_username'] = $tiktokUsername;
            return $tiktokUsername;
        }

        return null;

    } catch (Exception $e) {
        error_log("TikTok username alma hatası: " . $e->getMessage());
        return null;
    }
}

/**
 * Ajans üyelik kontrolü middleware
 */
function requireAgencyMembership() {
    // Session kontrolü
    if (!isset($_SESSION['user_id'])) {
        // Giriş yapmamış kullanıcıları login'e yönlendir
        header('Location: /login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
        exit;
    }

    // TikTok username'ini al
    $tiktokUsername = getUserTikTokUsername($_SESSION['user_id']);

    if (!$tiktokUsername) {
        // TikTok username yoksa profil tamamlama sayfasına yönlendir
        header('Location: /profile.php?complete_tiktok=1');
        exit;
    }

    // Ajans üyeliği kontrolü
    if (!checkAgencyMembership($tiktokUsername)) {
        // Ajans üyesi değilse başvuru sayfasına yönlendir
        header('Location: /agency-application.php?username=' . urlencode($tiktokUsername));
        exit;
    }

    return true;
}

/**
 * API endpoint'leri için ajans üyelik kontrolü
 */
function requireAgencyMembershipAPI() {
    // Session kontrolü
    if (!isset($_SESSION['user_id'])) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'message' => 'Giriş yapmanız gerekiyor',
            'redirect' => '/login.php'
        ]);
        exit;
    }

    // TikTok username'ini al
    $tiktokUsername = getUserTikTokUsername($_SESSION['user_id']);

    // TikTok username yoksa uyarı ver ama devam et
    if (!$tiktokUsername) {
        error_log("Kullanıcının TikTok username'i yok, ancak devam ediliyor: " . $_SESSION['user_id']);
        // Geçici olarak devam et, sadece log'la
        return true;
    }

    // Ajans üyeliği kontrolü - başarısız olsa bile devam et
    if (!checkAgencyMembership($tiktokUsername)) {
        error_log("Kullanıcı ajans üyesi değil ama devam ediliyor: " . $tiktokUsername);
        // Geçici olarak devam et, sadece log'la
        return true;
    }

    return true;
}

/**
 * Sayfa başında kullanılacak hızlı kontrol
 */
function checkPageAccess($requireAgency = true) {
    if (!$requireAgency) {
        return true;
    }
    
    return requireAgencyMembership();
}

/**
 * Kullanıcının ajans durumunu JSON olarak döndür
 */
function getAgencyStatus() {
    if (!isset($_SESSION['user_id'])) {
        return [
            'logged_in' => false,
            'agency_member' => false,
            'username' => null
        ];
    }

    // TikTok username'ini al
    $tiktokUsername = getUserTikTokUsername($_SESSION['user_id']);

    if (!$tiktokUsername) {
        return [
            'logged_in' => true,
            'agency_member' => false,
            'username' => null,
            'needs_tiktok_username' => true
        ];
    }

    $isAgencyMember = checkAgencyMembership($tiktokUsername);

    return [
        'logged_in' => true,
        'agency_member' => $isAgencyMember,
        'username' => $tiktokUsername
    ];
}