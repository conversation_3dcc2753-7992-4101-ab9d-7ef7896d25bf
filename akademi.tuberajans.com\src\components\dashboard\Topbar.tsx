import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { useTikTok } from '../../hooks/useTikTok';
import { useSidebar } from '../../hooks/useSidebar';
import { motion, AnimatePresence } from 'framer-motion';
import axios from 'axios';
import {
  UserCircleIcon,
  ArrowRightStartOnRectangleIcon,
  SunIcon,
  MoonIcon,
  BellIcon,
  BellAlertIcon
} from '@heroicons/react/24/outline';
import { FaBars } from 'react-icons/fa';

interface Notification {
  id: number;
  title: string;
  message: string;
  time: string;
  read: boolean;
  type: string;
  source?: string;
  source_id?: string;
  link?: string;
}

export interface TopbarProps {
  onLogout?: () => void;
  refreshTrigger?: number;
}

interface UserInfo {
  id: number;
  name: string;
  username?: string;
  email: string;
  profile_image?: string;
  avatar_url?: string | null;
  is_verified?: boolean;
  role: string;
  tiktok_linked?: number;
  tiktok_username?: string | null;
  tiktok_display_name?: string | null;
  tiktok_avatar_url?: string | null;
}

const Topbar: React.FC<TopbarProps> = ({
  onLogout,
  refreshTrigger
}) => {
  // Development mode kontrolü
  const isDevMode = import.meta.env.VITE_DISABLE_AUTH === 'true' || import.meta.env.DEV;

  // Sidebar context hook
  const { toggleSidebar } = useSidebar();

  // AuthContext'i her zaman çağır (hooks kuralı)
  const authContext = useAuth();

  // Logout fonksiyonu - development mode'a göre belirle
  const logout = (() => {
    if (!isDevMode && authContext?.logout) {
      return authContext.logout;
    }
    // Fallback logout fonksiyonu
    return () => {
      localStorage.removeItem('user');
      localStorage.removeItem('token');
      return Promise.resolve();
    };
  })();

  const [profileMenuOpen, setProfileMenuOpen] = useState(false);
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const [darkMode, setDarkMode] = useState(localStorage.getItem('darkMode') === 'true');
  const profileMenuRef = useRef<HTMLDivElement>(null);
  const notificationsRef = useRef<HTMLDivElement>(null);

  // Profil menüsünü dışına tıklanınca kapat
  const handleClickOutside = useCallback((event: MouseEvent) => {
    if (profileMenuRef.current && !profileMenuRef.current.contains(event.target as Node)) {
      setProfileMenuOpen(false);
    }
    if (notificationsRef.current && !notificationsRef.current.contains(event.target as Node)) {
      setNotificationsOpen(false);
    }
  }, []);

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [handleClickOutside]);

  // Tema değişimi için useEffect
  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('darkMode', 'true');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('darkMode', 'false');
    }
  }, [darkMode]);

  // Tema değişim işlevi
  const toggleDarkMode = useCallback(() => {
    setDarkMode(!darkMode);
  }, [darkMode]);

  // Bildirimler için state
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const unreadCount = notifications.filter(n => !n.read).length;

  // Render user avatar helper
  const renderUserAvatar = () => {
    if (userLoading) {
      return <div className="w-7 h-7 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse"></div>;
    }

    const avatarUrl = tiktokUser?.avatar_url || userInfo?.avatar_url;
    if (avatarUrl) {
      return (
        <img
          src={avatarUrl}
          alt={tiktokUser?.display_name || userInfo?.name}
          className="w-7 h-7 rounded-full object-cover shadow-md transition-all duration-200 hover:shadow-lg border-2 border-gray-300 dark:border-gray-600"
          onError={(e) => {
            (e.target as HTMLImageElement).src = 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face&auto=format&q=80';
          }}
        />
      );
    }

    // Fallback avatar with initials
    const storedUser = localStorage.getItem('user');
    let currentUser = null;
    if (storedUser) {
      try {
        currentUser = JSON.parse(storedUser);
      } catch (e) {
        console.error('Stored user parse error:', e);
      }
    }

    const displayName = currentUser?.username || currentUser?.name || userInfo?.username || userInfo?.name || tiktokUser?.username || tiktokUser?.display_name || 'U';

    return (
      <div className="w-7 h-7 rounded-full bg-gradient-to-r from-tuber-pink to-tuber-purple flex items-center justify-center text-white font-medium shadow-md transition-all duration-200 hover:shadow-lg">
        {displayName.substring(0, 1).toUpperCase()}
      </div>
    );
  };

  // Kullanıcı bilgileri için state
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [userLoading, setUserLoading] = useState(false); // Başlangıçta false yap

  // TikTok Context'ten verileri al
  const { tiktokUser } = useTikTok();

  // Bildirimleri getir
  const fetchNotifications = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      // Kullanıcı ID'sini al
      const storedUser = localStorage.getItem('user');
      let userId = 1; // Fallback

      if (storedUser) {
        try {
          const user = JSON.parse(storedUser);
          userId = user.id || 1;
        } catch (e) {
          console.error('User parse error:', e);
        }
      }

      // Gerçek API çağrısı
      const response = await axios.get('/backend/api/notifications.php', {
        params: {
          user_id: userId,
          limit: 10
        }
      });

      if (response.data.status === 'success') {
        // API'den gelen verileri formatla
        const formattedNotifications = response.data.data.map((item: {
          id: number;
          type: string;
          content: string;
          created_at: string;
          is_read?: boolean | number;
          sender_username?: string;
          sender_tiktok_username?: string;
          sender_avatar?: string;
          related_id?: number;
        }) => {
          // Bildirim tipine göre başlık ve mesaj oluştur
          let title = 'Bildirim';
          let message = item.content;
          let link = '';
          let source = 'general';

          switch (item.type) {
            case 'new_course':
              title = 'Yeni Eğitim Eklendi';
              link = `/dashboard/courses/${item.related_id}`;
              source = 'new_course';
              break;
            case 'new_event':
              title = 'Yeni Etkinlik';
              link = `/dashboard/events/${item.related_id}`;
              source = 'new_event';
              break;
            case 'course_completed':
              title = 'Eğitim Tamamlandı';
              link = `/dashboard/courses/${item.related_id}`;
              source = 'course_completed';
              break;
            case 'like':
              title = 'Beğeni Aldınız';
              message = `${item.sender_username || 'Birisi'} gönderinizi beğendi`;
              link = '/dashboard/feed';
              source = 'like';
              break;
            case 'comment':
              title = 'Yeni Yorum';
              message = `${item.sender_username || 'Birisi'} gönderinize yorum yaptı`;
              link = '/dashboard/feed';
              source = 'comment';
              break;
            case 'follow':
              title = 'Yeni Takipçi';
              message = `${item.sender_username || 'Birisi'} sizi takip etmeye başladı`;
              link = '/dashboard/profile';
              source = 'follow';
              break;
            default:
              title = 'Genel Bildirim';
              source = 'general';
          }

          return {
            id: item.id,
            title,
            message,
            time: formatTimeAgo(new Date(item.created_at)),
            read: Boolean(item.is_read),
            type: item.type || 'info',
            source,
            source_id: item.related_id?.toString(),
            link
          };
        });

        setNotifications(formattedNotifications);
      } else {
        setError('Bildirimler alınamadı');
        console.error('Bildirimler alınamadı:', response.data.message);
      }
    } catch (err) {
      setError('Bildirimler yüklenirken bir hata oluştu');
      console.error('Bildirimler yüklenirken hata:', err);
    } finally {
      setLoading(false);
    }
  }, [isDevMode]); // useCallback dependency array

  // Kullanıcı bilgilerini getir - cache ile - Optimized
  const fetchUserInfo = useCallback(async () => {
    try {
      // Cache kontrolü - son 60 saniye içinde çağrı yapıldıysa skip et
      const lastFetchTime = localStorage.getItem('user_info_last_fetch');
      const now = Date.now();

      if (lastFetchTime && (now - parseInt(lastFetchTime)) < 60000) {
        return;
      }

      setUserLoading(true);
      // Development modunda mock veri kullan
      const isDevMode = import.meta.env.VITE_DISABLE_AUTH === 'true' || import.meta.env.DEV;

      if (isDevMode) {

        // localStorage'dan kullanıcı bilgilerini al
        const savedUser = localStorage.getItem('user');
        if (savedUser) {
          const parsedUser = JSON.parse(savedUser);
          const mockUserData = {
            id: parsedUser.id || 1,
            name: parsedUser.name || parsedUser.username || 'Ahmet Yılmaz',
            username: parsedUser.username || 'ahmet_yilmaz',
            email: parsedUser.email || '<EMAIL>',
            avatar_url: parsedUser.avatar_url || 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face&auto=format&q=80',
            is_verified: parsedUser.is_verified || true,
            role: parsedUser.role || 'admin'
          };
          setUserInfo(mockUserData);
        } else {
          // Fallback mock kullanıcı verisi
          const mockUserData = {
            id: 1,
            name: 'Ahmet Yılmaz',
            username: 'ahmet_yilmaz',
            email: '<EMAIL>',
            avatar_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face&auto=format&q=80',
            is_verified: true,
            role: 'admin'
          };
          setUserInfo(mockUserData);
        }

        setUserLoading(false);
        return;
      }

      // Production modunda gerçek API çağrısı
      const response = await axios.get('/backend/api/user_session.php');

      // Cache zamanını güncelle
      localStorage.setItem('user_info_last_fetch', now.toString());

      if (response.data.status === 'success') {
        setUserInfo(response.data.data);
      } else {
        console.warn('Kullanıcı bilgileri alınamadı:', response.data.message);
        // API başarısız olduğunda localStorage'dan veri kullan
        const savedUser = localStorage.getItem('user');
        if (savedUser) {
          const parsedUser = JSON.parse(savedUser);
          setUserInfo({
            id: parsedUser.id || 1,
            name: parsedUser.name || parsedUser.username || 'Kullanıcı',
            username: parsedUser.username || 'kullanici',
            email: parsedUser.email || '<EMAIL>',
            avatar_url: parsedUser.avatar_url || parsedUser.tiktok_avatar_url || '',
            is_verified: parsedUser.is_verified || false,
            role: parsedUser.role || 'user',
            tiktok_linked: parsedUser.tiktok_linked || 0,
            tiktok_username: parsedUser.tiktok_username || null,
            tiktok_display_name: parsedUser.tiktok_display_name || null,
            tiktok_avatar_url: parsedUser.tiktok_avatar_url || null
          });
        } else {
          setMockUserData();
        }
      }
    } catch (err: unknown) {
      console.error('Kullanıcı bilgileri yüklenirken hata:', err);

      // Hata türüne göre işlem yap
      if (axios.isAxiosError(err)) {
        if (err.code === 'ECONNABORTED' || err.message?.includes('timeout')) {
          console.error('Request timeout');
        } else if (err.response?.status === 404) {
          console.error('User not found');
        } else {
          console.error('API error:', err.response?.status);
        }
      } else {
        console.error('Unknown error:', err);
      }

      // Hata durumunda localStorage'dan veri kullan
      const savedUser = localStorage.getItem('user');
      if (savedUser) {
        const parsedUser = JSON.parse(savedUser);
        setUserInfo({
          id: parsedUser.id || 1,
          name: parsedUser.name || parsedUser.username || 'Kullanıcı',
          username: parsedUser.username || 'kullanici',
          email: parsedUser.email || '<EMAIL>',
          avatar_url: parsedUser.avatar_url || parsedUser.tiktok_avatar_url || '',
          is_verified: parsedUser.is_verified || false,
          role: parsedUser.role || 'user',
          tiktok_linked: parsedUser.tiktok_linked || 0,
          tiktok_username: parsedUser.tiktok_username || null,
          tiktok_display_name: parsedUser.tiktok_display_name || null,
          tiktok_avatar_url: parsedUser.tiktok_avatar_url || null
        });
      } else {
        setMockUserData();
      }
    } finally {
      setUserLoading(false);
    }
  }, []); // useCallback dependency array

  // Helper functions for notification styling
  const getNotificationTypeClass = (type: string) => {
    switch (type) {
      case 'info': return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300';
      case 'success': return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300';
      case 'warning': return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300';
      case 'error': return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300';
      default: return 'bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-300';
    }
  };

  const getNotificationTypeLabel = (notification: Notification) => {
    if (notification.source === 'new_event') return 'Etkinlik';
    if (notification.source === 'new_course') return 'Eğitim';
    if (notification.source === 'ticket_reply') return 'Destek';

    switch (notification.type) {
      case 'info': return 'Bilgi';
      case 'success': return 'Başarılı';
      case 'warning': return 'Uyarı';
      case 'error': return 'Hata';
      default: return 'Genel';
    }
  };

  // Notification content rendering helper
  const renderNotificationContent = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-tuber-pink"></div>
          <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">Yükleniyor...</span>
        </div>
      );
    }

    if (error) {
      return (
        <div className="p-4 text-center">
          <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
        </div>
      );
    }

    if (notifications.length === 0) {
      return (
        <div className="p-4 text-center">
          <p className="text-sm text-gray-600 dark:text-gray-400">Henüz bildirim yok</p>
        </div>
      );
    }

    return (
      <div className="max-h-80 overflow-y-auto">
        {notifications.map((notification) => (
          <button
            key={notification.id}
            className={`w-full text-left p-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border-none ${!notification.read ? 'bg-pink-50/50 dark:bg-pink-900/10' : ''}`}
            onClick={() => {
              markAsRead(notification.id);
              if (notification.link) {
                window.location.href = notification.link;
              }
            }}
            aria-label={`Bildirim: ${notification.title}`}
          >
            <div className="flex items-start">
              <div className={`mt-0.5 w-2 h-2 rounded-full flex-shrink-0 ${!notification.read ? 'bg-tuber-pink' : 'bg-gray-300 dark:bg-gray-600'}`}></div>
              <div className="ml-3 flex-1">
                <div className="text-sm font-medium text-gray-900 dark:text-white">{notification.title}</div>
                <p className="text-xs text-gray-600 dark:text-gray-300 mt-0.5">{notification.message}</p>
                <div className="flex items-center mt-1">
                  <span className="text-xs text-gray-500 dark:text-gray-400">{notification.time}</span>
                  {notification.type && (
                    <span className={`ml-2 text-xs px-1.5 py-0.5 rounded-full ${getNotificationTypeClass(notification.type)}`}>
                      {getNotificationTypeLabel(notification)}
                    </span>
                  )}
                </div>
              </div>
            </div>
          </button>
        ))}
      </div>
    );
  };

  // Bildirimleri ve kullanıcı bilgilerini API'den çek - sadece bir kez
  useEffect(() => {
    fetchNotifications();
    fetchUserInfo();
  }, [fetchNotifications, fetchUserInfo]); // Dependencies eklendi

  // TikTok giriş sonrası kullanıcı bilgilerini yenile - Optimized
  useEffect(() => {
    if (refreshTrigger && refreshTrigger > 0) { // Only trigger when refreshTrigger is actually set
      fetchUserInfo();
      // TikTok Context zaten kendi cache'ini yönetiyor, gereksiz çağrı yapma
    }
  }, [refreshTrigger, fetchUserInfo]); // Dependency array correct

  // Mock veri ayarlama fonksiyonu
  const setMockUserData = () => {
    // localStorage'dan kullanıcı bilgilerini almaya çalış
    const savedUser = localStorage.getItem('user');
    let mockUserData;
    
    if (savedUser) {
      const parsedUser = JSON.parse(savedUser);
      mockUserData = {
        id: parsedUser.id || 1,
        name: parsedUser.name || parsedUser.username || 'tuberajans',
        username: parsedUser.username || 'tuberajans',
        email: parsedUser.email || '<EMAIL>',
        avatar_url: parsedUser.avatar_url || '',
        is_verified: parsedUser.is_verified || false,
        role: parsedUser.role || 'user'
      };
    } else {
      mockUserData = {
        id: 1,
        name: 'tuberajans',
        username: 'tuberajans',
        email: '<EMAIL>',
        avatar_url: '',
        is_verified: false,
        role: 'user'
      };
    }
    
    setUserInfo(mockUserData);
    setUserLoading(false);
  };

  // Tek bir bildirimi okundu yap
  const markAsRead = async (id: number) => {
    try {
      // Kullanıcı ID'sini al
      const storedUser = localStorage.getItem('user');
      let userId = 1;

      if (storedUser) {
        try {
          const user = JSON.parse(storedUser);
          userId = user.id || 1;
        } catch (e) {
          console.error('User parse error:', e);
        }
      }

      const response = await axios.post('/backend/api/notifications.php', {
        action: 'mark_as_read',
        notification_id: id,
        user_id: userId
      });

      if (response.data.status === 'success') {
        setNotifications(notifications.map(n =>
          n.id === id ? { ...n, read: true } : n
        ));
      }
    } catch (err) {
      console.error('Bildirim okundu olarak işaretlenirken hata:', err);
    }
  };

  // Tüm bildirimleri okundu yap
  const markAllAsRead = async () => {
    try {
      // Kullanıcı ID'sini al
      const storedUser = localStorage.getItem('user');
      let userId = 1;

      if (storedUser) {
        try {
          const user = JSON.parse(storedUser);
          userId = user.id || 1;
        } catch (e) {
          console.error('User parse error:', e);
        }
      }

      const response = await axios.post('/backend/api/notifications.php', {
        action: 'mark_all_as_read',
        user_id: userId
      });

      if (response.data.status === 'success') {
        setNotifications(notifications.map(n => ({ ...n, read: true })));
      }
    } catch (err) {
      console.error('Tüm bildirimler okundu olarak işaretlenirken hata:', err);
    }
  };

  // Zaman formatı için yardımcı fonksiyon
  const formatTimeAgo = (date: Date): string => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSec = Math.floor(diffMs / 1000);
    const diffMin = Math.floor(diffSec / 60);
    const diffHour = Math.floor(diffMin / 60);
    const diffDay = Math.floor(diffHour / 24);

    if (diffSec < 60) {
      return `${diffSec} saniye önce`;
    } else if (diffMin < 60) {
      return `${diffMin} dakika önce`;
    } else if (diffHour < 24) {
      return `${diffHour} saat önce`;
    } else if (diffDay < 30) {
      return `${diffDay} gün önce`;
    } else {
      return date.toLocaleDateString('tr-TR');
    }
  };

  return (
    <header className="bg-black dark:bg-black border-b border-gray-800 h-16 flex items-center shadow-sm z-20 fixed top-0 left-0 right-0 transition-colors duration-200">
      <button
        onClick={toggleSidebar}
        className="p-2 mr-2 focus:outline-none rounded-lg transition-all text-gray-300 flex items-center justify-center"
        aria-label="Toggle sidebar"
        style={{ width: '40px', height: '40px', marginLeft: '12px' }}
      >
        <FaBars className="w-5 h-5" />
      </button>
      {/* Logo */}
      <img
        src="/images/logotuber1.png"
        alt="Tuber X Akademi Logo"
        className="h-8 w-auto rounded-lg select-none mr-4"
        style={{objectFit: 'contain'}}
      />
      <div className="ml-auto flex items-center space-x-2 pr-6">
        {/* Tema Değiştirme Butonu */}
        <button
          onClick={toggleDarkMode}
          className="p-2 text-gray-500 dark:text-gray-300 hover:text-tuber-pink dark:hover:text-tuber-pink hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg focus:outline-none transition-all"
          aria-label={darkMode ? 'Light mode' : 'Dark mode'}
        >
          {darkMode ? <SunIcon className="h-5 w-5" /> : <MoonIcon className="h-5 w-5" />}
        </button>

        {/* Bildirim Butonu */}
        <div className="relative" ref={notificationsRef}>
          <button
            onClick={() => setNotificationsOpen(!notificationsOpen)}
            className="relative p-2 text-gray-300 hover:text-tuber-pink hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg focus:outline-none transition-all"
            aria-label="Notifications"
          >
            {unreadCount > 0 ? (
              <BellAlertIcon className="h-5 w-5" />
            ) : (
              <BellIcon className="h-5 w-5" />
            )}

            {unreadCount > 0 && (
              <span className="absolute top-0 right-0 h-4 w-4 rounded-full bg-red-500 flex items-center justify-center text-xs text-white font-medium">
                {unreadCount}
              </span>
            )}
          </button>

          {/* Bildirimler Dropdown */}
          <AnimatePresence>
            {notificationsOpen && (
              <motion.div
                initial={{ opacity: 0, y: 10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: 10, scale: 0.95 }}
                transition={{ type: "spring", duration: 0.2, stiffness: 500, damping: 30 }}
                className="absolute right-0 mt-2 w-80 rounded-xl bg-white dark:bg-gray-800 shadow-lg ring-1 ring-black/5 dark:ring-white/10 overflow-hidden z-50"
              >
                <div className="p-3 border-b border-gray-100 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 flex items-center justify-between">
                  <h3 className="font-medium text-sm text-gray-900 dark:text-white">Bildirimler</h3>
                  {unreadCount > 0 && (
                    <button onClick={markAllAsRead} className="text-xs text-tuber-pink dark:text-pink-400 hover:underline font-medium">Tümünü Okundu Olarak İşaretle</button>
                  )}
                </div>

                {renderNotificationContent()}

                {notifications.length > 0 && (
                  <div className="p-2 border-t border-gray-100 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 text-center">
                    <button className="text-xs font-medium text-tuber-pink dark:text-pink-400 hover:underline" onClick={() => window.location.href='/dashboard/notifications'}>
                      Tüm bildirimleri görüntüle
                    </button>
                  </div>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Profil Menüsü */}
        <div className="relative" ref={profileMenuRef}>
          <button
            onClick={() => setProfileMenuOpen(!profileMenuOpen)}
            className="flex items-center focus:outline-none"
            aria-label="Open user menu"
          >
            <div className="relative">
              {renderUserAvatar()}
            </div>
          </button>

          {/* Açılır Profil Menüsü */}
          <AnimatePresence>
            {profileMenuOpen && (
              <motion.div
                initial={{ opacity: 0, y: 10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: 10, scale: 0.95 }}
                transition={{ type: "spring", duration: 0.2, stiffness: 500, damping: 30 }}
                className="absolute right-0 mt-2 w-60 rounded-xl bg-white dark:bg-gray-800 shadow-lg ring-1 ring-black/5 dark:ring-white/10 overflow-hidden z-50"
              >
                <div className="py-1">
                  <Link
                    to="/dashboard/profile"
                    className="flex items-center px-4 py-2.5 text-sm text-gray-700 dark:text-gray-200 hover:bg-pink-50 dark:hover:bg-gray-700 hover:text-tuber-pink dark:hover:text-tuber-pink transition-colors"
                  >
                    <UserCircleIcon className="mr-3 h-5 w-5 text-gray-500 dark:text-gray-400" />
                    Profilim
                  </Link>
                  <hr className="my-1 border-gray-100 dark:border-gray-700" />
                  <button
                    onClick={async () => {
                      await logout();
                      if (onLogout) onLogout();
                    }}
                    className="flex w-full items-center px-4 py-2.5 text-sm text-gray-700 dark:text-gray-200 hover:bg-pink-50 dark:hover:bg-gray-700 hover:text-red-600 dark:hover:text-red-400 transition-colors text-left"
                  >
                    <ArrowRightStartOnRectangleIcon className="mr-3 h-5 w-5 text-gray-500 dark:text-gray-400" />
                    Çıkış Yap
                  </button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </header>
  );
};

export default Topbar;