"""
Application-wide constants and configuration settings.
Handles environment variables, paths, database and scraper configuration,
and provides centralized configuration management with enhanced error handling.
"""

import os
import sys
import json
import logging
import platform
from enum import Enum
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import time, datetime, timezone
from dataclasses import dataclass, asdict
from dotenv import load_dotenv

# ---------------- Logging Setup ----------------

def setup_logging() -> logging.Logger:
    """
    Configure and return a logger with enhanced formatting and handlers.
    Creates a 'logs' directory if it does not exist and sets up file and console logging.
    """
    log_format = (
        '%(asctime)s [%(levelname)s] %(name)s - %(message)s '
        '(%(filename)s:%(lineno)d)'
    )
    log_dir = Path('logs')
    log_dir.mkdir(exist_ok=True)
    timestamp = datetime.now().strftime('%Y%m%d')
    log_file = log_dir / f'config_{timestamp}.log'
    
    # Clear any existing handlers to avoid duplicate logs
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)
    
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger('Config')

logger: logging.Logger = setup_logging()

# ---------------- Environment Variables ----------------

try:
    load_dotenv()
   
except Exception as e:
    logger.error(f"Failed to load environment variables: {e}")
    sys.exit(1)

# ---------------- Custom Exceptions ----------------

class ConfigError(Exception):
    """Custom exception for configuration-related errors."""
    pass

# ---------------- Environment Enumeration ----------------

class Environment(Enum):
    """Environment enumeration for configuration management."""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    
    @classmethod
    def get_current(cls) -> 'Environment':
        """
        Get the current environment based on the ENV variable.
        Defaults to DEVELOPMENT if not specified.
        """
        env = os.getenv("ENV", "development").lower()
        try:
            return cls(env)
        except ValueError:
            logger.warning(f"Unrecognized environment '{env}', defaulting to DEVELOPMENT.")
            return cls.DEVELOPMENT

# ---------------- System Information ----------------

@dataclass
class SystemInfo:
    """Container for basic system information."""
    python_version: str = platform.python_version()
    platform_system: str = platform.system()
    platform_release: str = platform.release()
    timezone: str = datetime.now(timezone.utc).astimezone().tzname()

# ---------------- Path Configuration ----------------

class PathConfig:
    """Enhanced path configuration and validation."""
    
    def __init__(self) -> None:
        self.BASE_DIR: Path = Path(__file__).resolve().parent
        self.SAVE_FOLDER: Path = self.BASE_DIR / "Ekran Görüntüleri"
        self.EXCEL_FILENAME: Path = self.BASE_DIR / "Bulunan_Yayıncılar.xlsx"
        self.CHROME_DRIVER_PATH: str = os.getenv('CHROMEDRIVER_PATH', r"C:\chromedriver.exe")
        self.CHROME_PROFILE_BASE: Path = Path(os.getenv('CHROME_PROFILE_BASE', os.getenv('LOCALAPPDATA', ''))) / "Google" / "Chrome" / "User Data"
        
        # Firefox yolları (testlerde kullanılan başarılı yollarla güncellendi)
        self.GECKO_DRIVER_PATH: str = "C:/geckodriver.exe" 
        self.FIREFOX_PROFILE_PATH: str = "C:/Users/<USER>/AppData/Roaming/Mozilla/Firefox/Profiles/u9ax4b3e.TikTok"
        
        self.BACKUP_DIR: Path = self.BASE_DIR / "backups"
        self.TEMP_DIR: Path = self.BASE_DIR / "temp"

    def get_chrome_profile_path(self, thread_name: str) -> str:
        """
        Thread'e özel Chrome profil yolu döndürür.
        Her thread için ayrı profil kullanarak çakışmaları önler.
        """
        profile_dir = self.CHROME_PROFILE_BASE / f"Profile_{thread_name}"
        profile_dir.mkdir(parents=True, exist_ok=True)
        return str(profile_dir)
    
    def validate(self) -> bool:
        """
        Validates critical paths.
        Returns True if all paths are valid; otherwise, logs an error and returns False.
        """
        validations = [
            (self.CHROME_DRIVER_PATH, "ChromeDriver"), # Chrome'u hala kullanıyorsanız kalsın
            (self.CHROME_PROFILE_PATH, "Chrome profile"), # Chrome'u hala kullanıyorsanız kalsın
            (self.GECKO_DRIVER_PATH, "GeckoDriver"),
            (self.FIREFOX_PROFILE_PATH, "Firefox profile"),
            (self.BASE_DIR, "Base directory"),
            (self.SAVE_FOLDER, "Screenshots folder"),
            (self.BACKUP_DIR, "Backup directory"),
            (self.TEMP_DIR, "Temporary directory")
        ]
        for path, name in validations:
            try:
                Path(path).resolve()
                
            except Exception as e:
                logger.error(f"Invalid {name.lower()} path: {path} - {e}")
                return False
        return True

# ---------------- Database Configuration ----------------

class DatabaseConfig:
    """Enhanced database configuration with validation and connection string generation."""
    
    def __init__(self) -> None:
        self.HOST: str = os.getenv("MYSQL_HOST", "localhost")
        self.USER: str = os.getenv("MYSQL_USER", "root")
        self.PASSWORD: str = os.getenv("MYSQL_PASSWORD", "")
        self.DATABASE: str = os.getenv("MYSQL_DATABASE", "tiktok_live_data")
        self.TABLE: str = os.getenv("MYSQL_TABLE", "live_data")
        self.PORT: int = int(os.getenv("MYSQL_PORT", "3306"))
        self.POOL_SIZE: int = int(os.getenv("MYSQL_POOL_SIZE", "5"))
        self.POOL_TIMEOUT: int = int(os.getenv("MYSQL_POOL_TIMEOUT", "30"))
        
    def get_connection_string(self) -> str:
        """Generate a MySQL connection string."""
        return f"mysql://{self.USER}:{self.PASSWORD}@{self.HOST}:{self.PORT}/{self.DATABASE}"
    
    def validate(self) -> bool:
        """
        Validates required database configuration fields.
        Returns True if configuration is valid; otherwise, logs errors and returns False.
        """
        required_fields = {
            "host": self.HOST,
            "user": self.USER,
            "password": self.PASSWORD,
            "database": self.DATABASE,
            "table": self.TABLE
        }
        for field, value in required_fields.items():
            if not value:
                logger.error(f"Missing required database field: {field}")
                return False
        if not (0 < self.PORT <= 65535):
            logger.error(f"Invalid port number: {self.PORT}")
            return False
        if not (0 < self.POOL_SIZE <= 20):
            logger.error(f"Invalid pool size: {self.POOL_SIZE}")
            return False
        return True

# ---------------- Scraper Configuration ----------------

@dataclass
class ScraperConfig:
    """Enhanced scraper configuration with validation."""
    EXCEL_SHEETNAME: str = "Sayfa1"
    SCRAPING_DURATION_SEC: int = 5 * 60
    AUTOMATION_START_HOUR: time = time(10, 0)
    AUTOMATION_STOP_HOUR: time = time(22, 0)
    MAX_RETRIES: int = 3
    RETRY_DELAY: float = 5.0
    REQUEST_TIMEOUT: int = 30
    USER_AGENT: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    
    def validate(self) -> bool:
        """
        Validates scraper configuration.
        Returns True if all settings are valid; otherwise, logs errors and returns False.
        """
        if not isinstance(self.SCRAPING_DURATION_SEC, int) or self.SCRAPING_DURATION_SEC <= 0:
            logger.error(f"Invalid scraping duration: {self.SCRAPING_DURATION_SEC}")
            return False
        if not isinstance(self.MAX_RETRIES, int) or self.MAX_RETRIES < 0:
            logger.error(f"Invalid max retries: {self.MAX_RETRIES}")
            return False
        if not isinstance(self.RETRY_DELAY, (int, float)) or self.RETRY_DELAY < 0:
            logger.error(f"Invalid retry delay: {self.RETRY_DELAY}")
            return False
        if not isinstance(self.REQUEST_TIMEOUT, int) or self.REQUEST_TIMEOUT <= 0:
            logger.error(f"Invalid request timeout: {self.REQUEST_TIMEOUT}")
            return False
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert scraper configuration to a dictionary."""
        return asdict(self)

# ---------------- Configuration Manager ----------------

class ConfigManager:
    """
    Centralized configuration manager that aggregates all configurations.
    Validates and exports configuration settings.
    """
    def __init__(self) -> None:
        self.environment: Environment = Environment.get_current()
        self.system_info: SystemInfo = SystemInfo()
        self.paths: PathConfig = PathConfig()
        self.database: DatabaseConfig = DatabaseConfig()
        self.scraper: ScraperConfig = ScraperConfig()       
    
    def validate_all(self) -> bool:
        """Validate all configuration components."""
        validations = [
            self.paths.validate(),
            self.database.validate(),
            self.scraper.validate()
        ]
        valid = all(validations)
        if not valid:
            logger.error("One or more configuration validations failed.")
        return valid
    
    def export_config(self, filepath: Path) -> None:
        """
        Export configuration to a JSON file.
        Sensitive fields like passwords are omitted.
        """
        try:
            config_dict = {
                "environment": self.environment.value,
                "system_info": asdict(self.system_info),
                "scraper": self.scraper.to_dict(),
                "database": {k: v for k, v in vars(self.database).items() if k != "PASSWORD"}
            }
            with filepath.open('w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=4, ensure_ascii=False)
            logger.info(f"Configuration exported to: {filepath}")
        except Exception as e:
            logger.error(f"Failed to export configuration: {e}")
            raise ConfigError(f"Configuration export failed: {e}")

# ---------------- Timeout Configuration ----------------

# Scraper ayarları
SCRAPER_DURATION = 1  # dakika
SCRAPER_HEADLESS = False
SCRAPER_TIMEOUT = 15  # saniye (30'dan 15'e düşürüldü)

# Timeout ayarları
PAGE_LOAD_TIMEOUT = 20  # saniye
ELEMENT_WAIT_TIMEOUT = 10  # saniye
CLICK_TIMEOUT = 5  # saniye

# Thread ayarları
THREAD_MONITOR_INTERVAL = 5  # saniye
THREAD_TIMEOUT = 300  # 5 dakika

# ---------------- Final Initialization ----------------

config_manager: ConfigManager = ConfigManager()

if not config_manager.validate_all():
    logger.error("Configuration validation failed.")
    raise ConfigError("Invalid configuration detected.")

__all__ = [
    'ConfigManager',
    'PathConfig',
    'DatabaseConfig',
    'ScraperConfig',
    'Environment',
    'SystemInfo',
    'ConfigError',
    'config_manager'
]
