/**
 * <PERSON><PERSON><PERSON><PERSON> Yapılandırma Dosyası
 * Tüm proje genelinde kullanılacak yapılandırma değerlerini içerir
 */

// API URL'sini .env dosyasından al
// Eğer .env'de VITE_API_URL değişkeni yoksa varsayılan değerleri kullan
const envApiUrl = import.meta.env.VITE_API_URL;

// Dinamik olarak BASE_URL hesaplama - canlı ortam için optimize edildi
const getApiBaseUrl = () => {
  // Doğru backend yolu: x.tuberajans.com/backend/x-site
  return 'https://x.tuberajans.com/backend/x-site';
};

// TUTARLI API URL'LERİ - Tüm frontend'de aynı URL kullanılacak
const X_SITE_BASE_URL = 'https://x.tuberajans.com/backend/x-site';
const SITE_BASE_URL = 'https://x.tuberajans.com/backend/site';

// API URL'leri
export const API_CONFIG = {
  X_SITE_BASE_URL,
  SITE_BASE_URL,
  // Ana API URL - canlı ortam için optimize edildi
  BASE_URL: getApiBaseUrl(),

  // Akademi API URL'i
  AKADEMI_BASE_URL: 'https://akademi.tuberajans.com/backend/akademi',

  // Test URL'i - API bağlantı testi için
  TEST_URL: `${getApiBaseUrl()}/db-status.php`,

  // Mock veri kullanımı kapatıldı (canlı veriler kullanılacak)
  ALWAYS_USE_MOCK_IN_DEV: false,

  // API istek zaman aşımı süresi (30 saniye)
  TIMEOUT: 30000,

  // API Endpoint'leri
  ENDPOINTS: {
    // x-site endpointleri
    AUTH: '/auth.php',
    LOGOUT: '/auth.php?action=logout',
    DASHBOARD: '/dashboard.php',
    DASHBOARD_METRICS: '/dashboard-metrics.php',
    PUBLISHERS: '/publishers.php',
    REPORTS: '/reports.php',
    INFLUENCERS: '/influencers.php',
    USERS: '/user-management.php',

    // Etkinlik ve görev yönetimi
    EVENTS: '/events.php',
    TASKS: '/tasks.php',
    WEEKLY_TASKS: '/weekly_tasks.php',
    WEEKLY_ARCHIVE: '/weekly_archive.php',
    FETCH_WEEKLY_DATA: '/fetch_weekly_data.php',
    AUTO_ASSIGN_TASKS: '/auto-assign-tasks.php',

    // Yayıncı ve performans izleme
    UPDATE_FOLLOWERS: '/update-followers.php',
    PERFORMANCE: '/performance.php',
    PERFORMANCE_COMPARISON: '/performance_comparison.php',
    LOGIN_LOGS: '/login-logs.php',

    // Akademi endpoint'leri
    AKADEMI_DASHBOARD: '/akademi-dashboard.php',
    AKADEMI_DUYURULAR: '/akademi-duyurular.php',
    AKADEMI_EGITIMLER: '/akademi-egitimler.php',
    AKADEMI_DESTEK: '/akademi-destek.php',
    AKADEMI_KULLANICILAR: '/akademi-kullanicilar.php',
    AKADEMI_YAPAY_ZEKA: '/akademi-yapay-zeka.php',

    // site endpointleri
    SITE_DASHBOARD: '/dashboard.php',
    SITE_ANASAYFA: '/anasayfa.php',
    SITE_YAYINCILAR: '/yayincilar.php',
    SITE_BASVURULAR: '/basvurular.php',
    SITE_ILETISIM_TALEPLERI: '/iletisimtalepleri.php',
    SITE_GERIARAMA_TALEPLERI: '/geriaramatalep.php',
    SITE_TOPLANTI_TALEPLERI: '/onlinetoplantitalep.php',
    SITE_TOPLU_SMS: '/toplu-sms.php',
    SITE_BLOG: '/blog.php',

    // ETSY Operasyonu endpoint'leri
    ETSY_DASHBOARD: '/etsy-dashboard.php',
    ETSY_TASARIMLAR: '/etsy-tasarimlar.php',
    ETSY_URUNLER: '/etsy-urunler.php',
    ETSY_AYARLAR: '/etsy-ayarlar.php',

    // WhatsApp Business API endpoint'leri
    WHATSAPP_CONTACTS: '/whatsapp-contacts.php',
    WHATSAPP_MESSAGES: '/whatsapp-messages.php',
    WHATSAPP_TEMPLATES: '/whatsapp-templates.php',
    WHATSAPP_STATS: '/whatsapp-stats.php',
    WHATSAPP_SEND_MESSAGE: '/whatsapp-send.php',
    WHATSAPP_SETTINGS: '/whatsapp-settings.php',

    // Yayıncı Keşfi endpoint'leri
    PUBLISHER_DISCOVERY: '/publisher-discovery.php',
    PUBLISHER_DISCOVERY_STATUS: '/publisher-discovery-status.php',
    PUBLISHER_DISCOVERY_CONTROL: '/publisher-discovery-control.php',
    PUBLISHER_DISCOVERY_QUERY: '/publisher-discovery-query.php',
    PUBLISHER_DISCOVERY_SEND_MESSAGE: '/publisher-discovery-send-message.php',

    // E-posta endpoint'leri
    EMAIL_TEMPLATES: '/email-templates.php',
    SEND_EMAIL: '/send-email.php'
  }
};

// Uygulama genel ayarları
export const APP_CONFIG = {
  // Uygulama adı
  NAME: 'Tuber Ajans',

  // Uygulama versiyonu
  VERSION: '1.0.0',

  // Tema ayarları
  THEME: {
    // Varsayılan tema modu
    DEFAULT_MODE: 'light',

    // Tema renkleri
    COLORS: {
      PRIMARY: '#1890ff',
      SUCCESS: '#10B981',
      WARNING: '#F59E0B',
      ERROR: '#EF4444',
      INFO: '#3B82F6'
    }
  },

  // Sayfalama varsayılan ayarları
  PAGINATION: {
    DEFAULT_PAGE_SIZE: 10,
    PAGE_SIZE_OPTIONS: [10, 20, 50, 100]
  },

  // Router ayarları
  ROUTER: {
    // Subdomain kullandığımız için artık BASE_PATH '/' olmalı
    BASE_PATH: '/',
  },

  // Mock verilerin tamamen kapatılması
  MOCK: {
    // Mock veri kullanılsın mı? - HAYIR
    ENABLED: false,

    // Mock veri yüklenme gecikmesi (kullanılmayacak)
    DELAY: 0
  }
};

// Güvenlik ayarları
export const SECURITY_CONFIG = {
  // Token localStorage anahtarı
  TOKEN_KEY: 'x_tuber_token',

  // Kullanıcı localStorage anahtarı
  USER_KEY: 'x_tuber_user',

  // Tema localStorage anahtarı
  THEME_KEY: 'x_tuber_theme'
};

// Tablo ve liste yapılandırmaları
export const TABLE_CONFIG = {
  PAGE_SIZE: 10,
  DEFAULT_SORT_FIELD: 'id',
  DEFAULT_SORT_ORDER: 'descend' as 'ascend' | 'descend',
};

// Yayıncı durumları
export const PUBLISHER_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  PENDING: 'pending'
};

// Varsayılan dışa aktarma
export default {
  API: API_CONFIG,
  APP: APP_CONFIG,
  SECURITY: SECURITY_CONFIG
};