2025-06-04 03:20:37,409 [INFO] __main__ - Otomasyon worker ba<PERSON>latılıyor... (automation_worker.py:189)
2025-06-04 03:20:37,425 [INFO] __main__ - <PERSON><PERSON> komut bulundu: check_status (automation_worker.py:148)
2025-06-04 03:20:37,425 [INFO] __main__ - 300 kullanıcı için durum sorgulama başlatılıyor: ['emel.ce', 'gizemin.tilsimi', 'gamzelimm870', 'aylinim40', 'alev.ates7676', 'sudesiww_', 'ipekckr21', 'idotheunicorn', 'tugba.canpolatt', '_oyle_istee_', 'lured_in', 'harbi_elif_', 'aybuke..0', '_04_leyla._', '_naz_mavi', 'engelsizyetenekler', 'xx._arzu_.04.04', 'ruveyyyydaa', 'ewin<PERSON>_dur_34__65', 'cyln_ylc', 'berkopits', 'yldz495', 'bennal<PERSON>', '_eemell', 'tutank11', 'nejma_436', 'busecan176', 'sevdiksevilmedik35', 'fatooo717171', 'betulmu', 'gokces74', 'aysealpsln_', 'pamukprenses1903', 'essmanurr07', 'hale.hale1102', '_avsar__', 'esmerim71.71', '62sevilll', 'leyla06001', '.onurhoca', 'hira.nur689', 'donis.terapitarot', 'fatmanurrccyedek', 'nurdiyyy', 'yarabenim_04', 'kablel', 'bggffvnjuu', 'haticeboyraz79', 'dark_quenn_09', 'razan_______90', 'tarot.aysee', 'papatyacick', '_alyona_2_', 'tokat.60343', 'asli276327.com', 'gamze_cimm', 'nur.yldmm', 'nursen3435', 'bano.lkateb', 'minee48_', 'faia.1', 'asalet236', 'ummutan1', 'osmanbardak8', 'kadir55.55.55', 'blackqueenbjk', 'tarottneptun', 'beyzaabatu', 'drucumm', '1melizpus', 'nefes656565', 'mardin47_63', 'asena_bozkurt_10', '33_moto_eymen', 'sadebiryasam.atarlihatun', 'muruvet0706', 'giizemmpolatt', 'eceee214', 'berivann0434', 'mrarosaa', 'mersinlikizz33', 'belizaks', '_incisu_11ww', '_tuc___', 'tugcedoganeer', 'zzeyzeys', 'beyzatime1', 'justsevcann', 'damlaskiim', 'ozlemaltinkara4148188', 'ednan72', 'sonsuzsongul', 'armina.korkusuz', 'istanbul.beyfendisii0', 'trend.girls34', 'fy_1004', 'sincapkizz1', 'bemoonfe', 'dileek_61', 'sevda_72.73', 'au123jh', 'durduyildiz231', 'jaba.l.1996', '57dilan_dilan34', '__..daye..__', 'remo......20', 'garam_alr', 'aksi_asi06', 'bellanr4', 'selinniles0671', 'sermin.6', 'yasargulcansu11_', 'homsiya.1', '49_yoksul_16', 'mavis.4501', 'enima982', 'delilaz_kizi', 'muhacirrct7', 'boss_pmg1', 'mardinli.47_472', 'kertenkelemustafa7467', '42aadfg', 'kerim_musicc', 'diva.yrgun', 'huzuricinde55.0', 'aydeniz5365', 'nurrolex_', 'o_1_kralice', 'tunanut01', 'kaderrr.a', 'ismailsonmezzzzz', 'melisanur0716', 'beyza_aytkn0', 'dilankuzu', 'narema31', 'busem_3428', 'benazir_74', 'efeadabas', 'ismetatabay53', 'yaren_gzl5934', 'cicekkkk35', 'sudemardinlimidyatli347', 'bayan_dilos_ile_cita35', 'sakirbagci_06', 'mediiw0', 'ssevda.sev', 'taokahve', 'meliks', 'tubatutii', 'ugur01284', 'muhammedalii.10', 'zeyno_zeyno73', '002cerenn', 'asmin.can56', 'mavi00.35', '21_34sananeeeeee', 'aklazarar428', 'gizli_sevdam.m', 'yaren.521', 'orkide_0010', 'bahar583434', 'cekirdek_aile63', 'vuslatreyhan', 'melike.er7', 'z.i.b.a_t.e.n.h.a.175', 'gll8124', 'gunesin_yildizi52', 'tesbihciniz0320', 'mehlikaactn_', 'seloman0001', 'gulseniniiz', 'berroski0', 'inst.selin', 'betulbenn_', 'mardinlitesbihci47', 'kammar9', 'lady_lucky19', 'lattelendinizz0', 'siirradyoda', 'bay_kurdo3634', 'yusenizim', 'sultan_erdal_', 'barby__htn', 'can_kiz_4105', 'mavikalp663', 'kuran_dersi', 'ssselcannn', '0nazl1_', 'stella_m.2003', 'fatma___0606', 'servan_pasa35_13', 'ferhat420642', 'sultan.aydndag', 'uzayrengi', 'sedra9966', 'sereen_508', 'moonlight_serpil', 'hasibe07070antalyali', 'caresiz088', 'tugcemalatya44', '.belmando', 'cyrineanour.91', 'beren026', 'damla44.40', 'seyranl65', '._selvi_', 'elin0123', 'gizemss345', 'ranos727', 'gamzeli_nida', 'nedensiz099', 'nurkader3465', 'dersimli2334', '52garaaydinn', 'nnicolenkiru', 'filizkoral1', 'ta__algzrawi', '00__________pp', '_yunus_04_04_', 'patronice_pinar56', 'nazlinazli16', 'yagmurcloud', '3irembal3', 'cicek_haleb', 'lonely_blue_butterfly365', 'sudehzl', 'masaalhlak', 'nazzoben', 'muzaffers43', 'sylemez.27.27', 'chiron2215', 'ozlem_4646', 'cacikkoo', 'senaselcan06', 'cagla1sanlier', 'semihmumaay', 'allah_bana_yeter_55', 'uyumsuz.29', 'nurselsimsekk', 'tarologvenus1', 'gece_21_35', 'metinkoc001', '_humeysaca', 'esmanurr.elmas', 'abdmamo11', 'cesur.enc', 'recebin_psikologu', 'sinem6534', 'ofmira', 'emrah_ucar36', 'gul.antep27', 'ayarsizz.mt2', 'emrelmas', 'adel0193', 'nurevizyon', 'okanmermervip', 'snglermn_', 'erenimofficial', 'sedaa_bakr_42', 'rawan_____pubg', 'hazalldln1903', 'yyhvb90ux1589', 'sazbendayhan', 'znur.aye.turgut', 'muratsutayy', 'gamereplay_mlbb', 'su.ckl', 'instagram.yorumlama', 'nisanurlive21', 'tubaaturan', 'rohatkaydd7', 'juga_x', 'hamza_muratti', 'baba.212134', 'ivano9934', 'abnxgeneral0', 'ceylan_senturk177', 'kaplanesport', '_kemal_61', 'jassed90', 'atlantis.queens', 'userr911937282910101', 'bato.mardini', 'hanla90lar', 'evrim0715', '_rapunzel_252', 'buluut.38', 'huseynncan61', 'naz2ella', 'krtbyy', 'dokhtekandahar', 'pubgmobile_tur_espor', 'mobilelegendstrofficial', 'sudepksy1', 'dilakilavuz', '1.sedoss', 'kayabrmusicc', 'cansatan34', 'faldunyasi20', 'mturanw'] (automation_worker.py:89)
2025-06-04 03:20:37,425 [INFO] StatusChecker - 🔍 Chrome StatusChecker başlıyor... (status_checker.py:87)
2025-06-04 03:20:37,425 [INFO] StatusChecker - Diğer Chrome işlemlerinin kapanması bekleniyor... (status_checker.py:87)
2025-06-04 03:20:37,425 [INFO] StatusChecker - status_checker diğer thread'lerin bitmesini bekliyor... (status_checker.py:87)
2025-06-04 03:20:37,425 [INFO] StatusChecker - Tüm diğer thread'ler tamamlandı, status_checker başlayabilir. (status_checker.py:87)
2025-06-04 03:20:37,425 [INFO] StatusChecker - Chrome WebDriver kurulumu başlıyor... (status_checker.py:87)
2025-06-04 03:20:37,425 [INFO] StatusChecker - Kullanılacak ChromeDriver yolu: chromedriver.exe (status_checker.py:87)
2025-06-04 03:20:37,425 [INFO] StatusChecker - Kullanılacak Chrome profil ana yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:87)
2025-06-04 03:20:37,425 [INFO] StatusChecker - Kullanılacak Chrome profil klasör adı: Profile 1 (status_checker.py:87)
2025-06-04 03:20:37,425 [INFO] StatusChecker - Kullanılacak Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64\chrome-win64\chrome.exe (status_checker.py:87)
2025-06-04 03:20:37,425 [INFO] StatusChecker - Chrome tercihleri ayarlandı. ChromeDriver servisi başlatılıyor... (status_checker.py:87)
2025-06-04 03:20:44,909 [ERROR] StatusChecker - ❌ Chrome WebDriverException: Message: session not created: Chrome failed to start: crashed.
  (chrome not reachable)
  (The process started from chrome location C:\Users\<USER>\Downloads\chrome-win64\chrome-win64\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
	GetHandleVerifier [0x00007FF6B366CF25+75717]
	GetHandleVerifier [0x00007FF6B366CF80+75808]
	(No symbol) [0x00007FF6B3438F9A]
	(No symbol) [0x00007FF6B3475D55]
	(No symbol) [0x00007FF6B3471ADD]
	(No symbol) [0x00007FF6B34C52D8]
	(No symbol) [0x00007FF6B34C48B0]
	(No symbol) [0x00007FF6B34B7153]
	(No symbol) [0x00007FF6B3480421]
	(No symbol) [0x00007FF6B34811B3]
	GetHandleVerifier [0x00007FF6B396D6FD+3223453]
	GetHandleVerifier [0x00007FF6B3967CA2+3200322]
	GetHandleVerifier [0x00007FF6B3985AD3+3322739]
	GetHandleVerifier [0x00007FF6B36869FA+180890]
	GetHandleVerifier [0x00007FF6B368E0FF+211359]
	GetHandleVerifier [0x00007FF6B3675274+109332]
	GetHandleVerifier [0x00007FF6B3675422+109762]
	GetHandleVerifier [0x00007FF6B365BA39+4825]
	BaseThreadInitThunk [0x00007FFE4D4A7374+20]
	RtlUserThreadStart [0x00007FFE4D69CC91+33]
 (status_checker.py:91)
2025-06-04 03:20:48,987 [INFO] StatusChecker - ⚠ Chrome WebDriver başlatılamadı, işlem sonlandırılıyor. (status_checker.py:87)
2025-06-04 03:20:48,987 [INFO] __main__ - Durum sorgulama tamamlandı. (automation_worker.py:97)
2025-06-04 03:20:51,003 [INFO] __main__ - Yeni komut bulundu: stop (automation_worker.py:148)
2025-06-04 03:20:51,007 [INFO] __main__ - Otomasyon zaten durdurulmuş. (automation_worker.py:81)
2025-06-04 03:20:53,034 [INFO] __main__ - Yeni komut bulundu: stop (automation_worker.py:148)
2025-06-04 03:20:53,034 [INFO] __main__ - Otomasyon zaten durdurulmuş. (automation_worker.py:81)
2025-06-04 03:20:55,050 [INFO] __main__ - Yeni komut bulundu: start (automation_worker.py:148)
2025-06-04 03:20:55,050 [INFO] __main__ - Otomasyon başlatılıyor. Süre: 3 dk, Headless: False, MessageMode: both (automation_worker.py:65)
2025-06-04 03:20:57,081 [INFO] __main__ - Yeni komut bulundu: start (automation_worker.py:148)
2025-06-04 03:20:57,081 [INFO] __main__ - Otomasyon zaten çalışıyor. (automation_worker.py:73)
2025-06-04 03:21:01,800 [ERROR] scraper_thread - Chrome başlatılırken hata: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: Unable to receive message from renderer
  (Session info: chrome=136.0.7103.92)
Stacktrace:
	GetHandleVerifier [0x00007FF6B366CF25+75717]
	GetHandleVerifier [0x00007FF6B366CF80+75808]
	(No symbol) [0x00007FF6B3438F9A]
	(No symbol) [0x00007FF6B34264EC]
	(No symbol) [0x00007FF6B34261DA]
	(No symbol) [0x00007FF6B3423E8A]
	(No symbol) [0x00007FF6B342483F]
	(No symbol) [0x00007FF6B343E4F1]
	(No symbol) [0x00007FF6B343F660]
	(No symbol) [0x00007FF6B3443D51]
	(No symbol) [0x00007FF6B34E05F8]
	(No symbol) [0x00007FF6B34B737A]
	(No symbol) [0x00007FF6B34DF39C]
	(No symbol) [0x00007FF6B34B7153]
	(No symbol) [0x00007FF6B3480421]
	(No symbol) [0x00007FF6B34811B3]
	GetHandleVerifier [0x00007FF6B396D6FD+3223453]
	GetHandleVerifier [0x00007FF6B3967CA2+3200322]
	GetHandleVerifier [0x00007FF6B3985AD3+3322739]
	GetHandleVerifier [0x00007FF6B36869FA+180890]
	GetHandleVerifier [0x00007FF6B368E0FF+211359]
	GetHandleVerifier [0x00007FF6B3675274+109332]
	GetHandleVerifier [0x00007FF6B3675422+109762]
	GetHandleVerifier [0x00007FF6B365BA39+4825]
	BaseThreadInitThunk [0x00007FFE4D4A7374+20]
	RtlUserThreadStart [0x00007FFE4D69CC91+33]
 (scraper_thread.py:264)
2025-06-04 03:21:03,815 [ERROR] scraper_thread - Chrome başlatılamadı! İşlem sonlandırılıyor. (scraper_thread.py:264)
2025-06-04 03:21:07,300 [INFO] main - Önceki scraper thread temizlendi (main.py:238)
2025-06-04 03:21:16,987 [ERROR] scraper_thread - Chrome başlatılırken hata: Message: session not created: Chrome failed to start: crashed.
  (chrome not reachable)
  (The process started from chrome location C:\Users\<USER>\Downloads\chrome-win64\chrome-win64\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
	GetHandleVerifier [0x00007FF6B366CF25+75717]
	GetHandleVerifier [0x00007FF6B366CF80+75808]
	(No symbol) [0x00007FF6B3438F9A]
	(No symbol) [0x00007FF6B3475D55]
	(No symbol) [0x00007FF6B3471ADD]
	(No symbol) [0x00007FF6B34C52D8]
	(No symbol) [0x00007FF6B34C48B0]
	(No symbol) [0x00007FF6B34B7153]
	(No symbol) [0x00007FF6B3480421]
	(No symbol) [0x00007FF6B34811B3]
	GetHandleVerifier [0x00007FF6B396D6FD+3223453]
	GetHandleVerifier [0x00007FF6B3967CA2+3200322]
	GetHandleVerifier [0x00007FF6B3985AD3+3322739]
	GetHandleVerifier [0x00007FF6B36869FA+180890]
	GetHandleVerifier [0x00007FF6B368E0FF+211359]
	GetHandleVerifier [0x00007FF6B3675274+109332]
	GetHandleVerifier [0x00007FF6B3675422+109762]
	GetHandleVerifier [0x00007FF6B365BA39+4825]
	BaseThreadInitThunk [0x00007FFE4D4A7374+20]
	RtlUserThreadStart [0x00007FFE4D69CC91+33]
 (scraper_thread.py:264)
2025-06-04 03:21:21,065 [ERROR] scraper_thread - Chrome başlatılamadı! İşlem sonlandırılıyor. (scraper_thread.py:264)
2025-06-04 03:21:24,534 [INFO] main - Önceki scraper thread temizlendi (main.py:238)
2025-06-04 03:21:34,690 [ERROR] scraper_thread - Chrome başlatılırken hata: Message: session not created: Chrome failed to start: crashed.
  (chrome not reachable)
  (The process started from chrome location C:\Users\<USER>\Downloads\chrome-win64\chrome-win64\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
	GetHandleVerifier [0x00007FF6B366CF25+75717]
	GetHandleVerifier [0x00007FF6B366CF80+75808]
	(No symbol) [0x00007FF6B3438F9A]
	(No symbol) [0x00007FF6B3475D55]
	(No symbol) [0x00007FF6B3471ADD]
	(No symbol) [0x00007FF6B34C52D8]
	(No symbol) [0x00007FF6B34C48B0]
	(No symbol) [0x00007FF6B34B7153]
	(No symbol) [0x00007FF6B3480421]
	(No symbol) [0x00007FF6B34811B3]
	GetHandleVerifier [0x00007FF6B396D6FD+3223453]
	GetHandleVerifier [0x00007FF6B3967CA2+3200322]
	GetHandleVerifier [0x00007FF6B3985AD3+3322739]
	GetHandleVerifier [0x00007FF6B36869FA+180890]
	GetHandleVerifier [0x00007FF6B368E0FF+211359]
	GetHandleVerifier [0x00007FF6B3675274+109332]
	GetHandleVerifier [0x00007FF6B3675422+109762]
	GetHandleVerifier [0x00007FF6B365BA39+4825]
	BaseThreadInitThunk [0x00007FFE4D4A7374+20]
	RtlUserThreadStart [0x00007FFE4D69CC91+33]
 (scraper_thread.py:264)
2025-06-04 03:21:38,768 [ERROR] scraper_thread - Chrome başlatılamadı! İşlem sonlandırılıyor. (scraper_thread.py:264)
2025-06-04 03:22:06,128 [INFO] main - Önceki scraper thread temizlendi (main.py:238)
2025-06-04 03:22:15,987 [ERROR] scraper_thread - Chrome başlatılırken hata: Message: session not created: Chrome failed to start: crashed.
  (chrome not reachable)
  (The process started from chrome location C:\Users\<USER>\Downloads\chrome-win64\chrome-win64\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
	GetHandleVerifier [0x00007FF6B366CF25+75717]
	GetHandleVerifier [0x00007FF6B366CF80+75808]
	(No symbol) [0x00007FF6B3438F9A]
	(No symbol) [0x00007FF6B3475D55]
	(No symbol) [0x00007FF6B3471ADD]
	(No symbol) [0x00007FF6B34C52D8]
	(No symbol) [0x00007FF6B34C48B0]
	(No symbol) [0x00007FF6B34B7153]
	(No symbol) [0x00007FF6B3480421]
	(No symbol) [0x00007FF6B34811B3]
	GetHandleVerifier [0x00007FF6B396D6FD+3223453]
	GetHandleVerifier [0x00007FF6B3967CA2+3200322]
	GetHandleVerifier [0x00007FF6B3985AD3+3322739]
	GetHandleVerifier [0x00007FF6B36869FA+180890]
	GetHandleVerifier [0x00007FF6B368E0FF+211359]
	GetHandleVerifier [0x00007FF6B3675274+109332]
	GetHandleVerifier [0x00007FF6B3675422+109762]
	GetHandleVerifier [0x00007FF6B365BA39+4825]
	BaseThreadInitThunk [0x00007FFE4D4A7374+20]
	RtlUserThreadStart [0x00007FFE4D69CC91+33]
 (scraper_thread.py:264)
2025-06-04 03:22:20,065 [ERROR] scraper_thread - Chrome başlatılamadı! İşlem sonlandırılıyor. (scraper_thread.py:264)
2025-06-04 03:22:23,347 [INFO] main - Önceki scraper thread temizlendi (main.py:238)
2025-06-04 03:22:31,503 [ERROR] scraper_thread - Chrome başlatılırken hata: Message: session not created: Chrome failed to start: crashed.
  (session not created: Could not read in devtools port number)
  (The process started from chrome location C:\Users\<USER>\Downloads\chrome-win64\chrome-win64\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
	GetHandleVerifier [0x00007FF6B366CF25+75717]
	GetHandleVerifier [0x00007FF6B366CF80+75808]
	(No symbol) [0x00007FF6B3438F9A]
	(No symbol) [0x00007FF6B3475D55]
	(No symbol) [0x00007FF6B3471ADD]
	(No symbol) [0x00007FF6B34C52D8]
	(No symbol) [0x00007FF6B34C48B0]
	(No symbol) [0x00007FF6B34B7153]
	(No symbol) [0x00007FF6B3480421]
	(No symbol) [0x00007FF6B34811B3]
	GetHandleVerifier [0x00007FF6B396D6FD+3223453]
	GetHandleVerifier [0x00007FF6B3967CA2+3200322]
	GetHandleVerifier [0x00007FF6B3985AD3+3322739]
	GetHandleVerifier [0x00007FF6B36869FA+180890]
	GetHandleVerifier [0x00007FF6B368E0FF+211359]
	GetHandleVerifier [0x00007FF6B3675274+109332]
	GetHandleVerifier [0x00007FF6B3675422+109762]
	GetHandleVerifier [0x00007FF6B365BA39+4825]
	BaseThreadInitThunk [0x00007FFE4D4A7374+20]
	RtlUserThreadStart [0x00007FFE4D69CC91+33]
 (scraper_thread.py:264)
2025-06-04 03:22:35,534 [ERROR] scraper_thread - Chrome başlatılamadı! İşlem sonlandırılıyor. (scraper_thread.py:264)
2025-06-04 03:22:39,534 [INFO] main - Önceki scraper thread temizlendi (main.py:238)
2025-06-04 03:22:47,597 [INFO] __main__ - Yeni komut bulundu: stop (automation_worker.py:148)
2025-06-04 03:22:47,600 [INFO] __main__ - Otomasyon durduruluyor. (automation_worker.py:77)
2025-06-04 03:22:47,600 [INFO] main - Otomasyon durdurma sinyali gönderildi (main.py:204)
2025-06-04 03:22:47,600 [INFO] main - Scraper thread durduruldu (main.py:209)
2025-06-04 03:22:47,600 [INFO] main - Tüm thread'ler durduruldu (main.py:219)
2025-06-04 03:22:47,768 [INFO] main - Thread monitörü durduruldu (main.py:192)
2025-06-04 03:22:49,612 [INFO] __main__ - Yeni komut bulundu: stop (automation_worker.py:148)
2025-06-04 03:22:49,612 [INFO] __main__ - Otomasyon zaten durdurulmuş. (automation_worker.py:81)
2025-06-04 03:22:49,628 [ERROR] scraper_thread - Chrome başlatılırken hata: Message: session not created: Chrome failed to start: crashed.
  (chrome not reachable)
  (The process started from chrome location C:\Users\<USER>\Downloads\chrome-win64\chrome-win64\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
	GetHandleVerifier [0x00007FF6B366CF25+75717]
	GetHandleVerifier [0x00007FF6B366CF80+75808]
	(No symbol) [0x00007FF6B3438F9A]
	(No symbol) [0x00007FF6B3475D55]
	(No symbol) [0x00007FF6B3471ADD]
	(No symbol) [0x00007FF6B34C52D8]
	(No symbol) [0x00007FF6B34C48B0]
	(No symbol) [0x00007FF6B34B7153]
	(No symbol) [0x00007FF6B3480421]
	(No symbol) [0x00007FF6B34811B3]
	GetHandleVerifier [0x00007FF6B396D6FD+3223453]
	GetHandleVerifier [0x00007FF6B3967CA2+3200322]
	GetHandleVerifier [0x00007FF6B3985AD3+3322739]
	GetHandleVerifier [0x00007FF6B36869FA+180890]
	GetHandleVerifier [0x00007FF6B368E0FF+211359]
	GetHandleVerifier [0x00007FF6B3675274+109332]
	GetHandleVerifier [0x00007FF6B3675422+109762]
	GetHandleVerifier [0x00007FF6B365BA39+4825]
	BaseThreadInitThunk [0x00007FFE4D4A7374+20]
	RtlUserThreadStart [0x00007FFE4D69CC91+33]
 (scraper_thread.py:264)
2025-06-04 03:22:53,690 [ERROR] scraper_thread - Chrome başlatılamadı! İşlem sonlandırılıyor. (scraper_thread.py:264)
2025-06-04 03:23:21,753 [INFO] __main__ - Yeni komut bulundu: start (automation_worker.py:148)
2025-06-04 03:23:21,758 [INFO] __main__ - Otomasyon başlatılıyor. Süre: 1 dk, Headless: False, MessageMode: both (automation_worker.py:65)
2025-06-04 03:23:31,581 [ERROR] scraper_thread - Chrome başlatılırken hata: Message: session not created: Chrome failed to start: crashed.
  (chrome not reachable)
  (The process started from chrome location C:\Users\<USER>\Downloads\chrome-win64\chrome-win64\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
	GetHandleVerifier [0x00007FF6B366CF25+75717]
	GetHandleVerifier [0x00007FF6B366CF80+75808]
	(No symbol) [0x00007FF6B3438F9A]
	(No symbol) [0x00007FF6B3475D55]
	(No symbol) [0x00007FF6B3471ADD]
	(No symbol) [0x00007FF6B34C52D8]
	(No symbol) [0x00007FF6B34C48B0]
	(No symbol) [0x00007FF6B34B7153]
	(No symbol) [0x00007FF6B3480421]
	(No symbol) [0x00007FF6B34811B3]
	GetHandleVerifier [0x00007FF6B396D6FD+3223453]
	GetHandleVerifier [0x00007FF6B3967CA2+3200322]
	GetHandleVerifier [0x00007FF6B3985AD3+3322739]
	GetHandleVerifier [0x00007FF6B36869FA+180890]
	GetHandleVerifier [0x00007FF6B368E0FF+211359]
	GetHandleVerifier [0x00007FF6B3675274+109332]
	GetHandleVerifier [0x00007FF6B3675422+109762]
	GetHandleVerifier [0x00007FF6B365BA39+4825]
	BaseThreadInitThunk [0x00007FFE4D4A7374+20]
	RtlUserThreadStart [0x00007FFE4D69CC91+33]
 (scraper_thread.py:264)
2025-06-04 03:23:35,643 [ERROR] scraper_thread - Chrome başlatılamadı! İşlem sonlandırılıyor. (scraper_thread.py:264)
2025-06-04 03:23:38,972 [INFO] main - Önceki scraper thread temizlendi (main.py:238)
2025-06-04 03:23:45,329 [ERROR] scraper_thread - Chrome başlatılırken hata: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: Unable to receive message from renderer
  (Session info: chrome=136.0.7103.92)
Stacktrace:
	GetHandleVerifier [0x00007FF6B366CF25+75717]
	GetHandleVerifier [0x00007FF6B366CF80+75808]
	(No symbol) [0x00007FF6B3438F9A]
	(No symbol) [0x00007FF6B34264EC]
	(No symbol) [0x00007FF6B34261DA]
	(No symbol) [0x00007FF6B342503E]
	(No symbol) [0x00007FF6B3448FD1]
	(No symbol) [0x00007FF6B34DF276]
	(No symbol) [0x00007FF6B34B7153]
	(No symbol) [0x00007FF6B3480421]
	(No symbol) [0x00007FF6B34811B3]
	GetHandleVerifier [0x00007FF6B396D6FD+3223453]
	GetHandleVerifier [0x00007FF6B3967CA2+3200322]
	GetHandleVerifier [0x00007FF6B3985AD3+3322739]
	GetHandleVerifier [0x00007FF6B36869FA+180890]
	GetHandleVerifier [0x00007FF6B368E0FF+211359]
	GetHandleVerifier [0x00007FF6B3675274+109332]
	GetHandleVerifier [0x00007FF6B3675422+109762]
	GetHandleVerifier [0x00007FF6B365BA39+4825]
	BaseThreadInitThunk [0x00007FFE4D4A7374+20]
	RtlUserThreadStart [0x00007FFE4D69CC91+33]
 (scraper_thread.py:264)
2025-06-04 03:23:48,362 [ERROR] scraper_thread - Chrome başlatılamadı! İşlem sonlandırılıyor. (scraper_thread.py:264)
2025-06-04 03:23:52,222 [INFO] main - Önceki scraper thread temizlendi (main.py:238)
2025-06-04 03:24:48,909 [ERROR] scraper_thread - Chrome başlatılırken hata: Message: session not created: Chrome failed to start: crashed.
  (chrome not reachable)
  (The process started from chrome location C:\Users\<USER>\Downloads\chrome-win64\chrome-win64\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
	GetHandleVerifier [0x00007FF6B366CF25+75717]
	GetHandleVerifier [0x00007FF6B366CF80+75808]
	(No symbol) [0x00007FF6B3438F9A]
	(No symbol) [0x00007FF6B3475D55]
	(No symbol) [0x00007FF6B3471ADD]
	(No symbol) [0x00007FF6B34C52D8]
	(No symbol) [0x00007FF6B34C48B0]
	(No symbol) [0x00007FF6B34B7153]
	(No symbol) [0x00007FF6B3480421]
	(No symbol) [0x00007FF6B34811B3]
	GetHandleVerifier [0x00007FF6B396D6FD+3223453]
	GetHandleVerifier [0x00007FF6B3967CA2+3200322]
	GetHandleVerifier [0x00007FF6B3985AD3+3322739]
	GetHandleVerifier [0x00007FF6B36869FA+180890]
	GetHandleVerifier [0x00007FF6B368E0FF+211359]
	GetHandleVerifier [0x00007FF6B3675274+109332]
	GetHandleVerifier [0x00007FF6B3675422+109762]
	GetHandleVerifier [0x00007FF6B365BA39+4825]
	BaseThreadInitThunk [0x00007FFE4D4A7374+20]
	RtlUserThreadStart [0x00007FFE4D69CC91+33]
 (scraper_thread.py:264)
2025-06-04 03:24:52,487 [INFO] __main__ - Program kullanıcı tarafından durduruldu. (automation_worker.py:192)
2025-06-04 03:24:52,487 [INFO] __main__ - Program sonlandırılıyor... (automation_worker.py:197)
2025-06-04 03:24:58,034 [INFO] __main__ - Otomasyon worker başlatılıyor... (automation_worker.py:189)
2025-06-04 03:25:58,284 [INFO] __main__ - Yeni komut bulundu: start (automation_worker.py:148)
2025-06-04 03:25:58,288 [INFO] __main__ - Otomasyon başlatılıyor. Süre: 1 dk, Headless: False, MessageMode: both (automation_worker.py:65)
2025-06-04 03:26:08,143 [ERROR] scraper_thread - Chrome başlatılırken hata: Message: session not created: Chrome failed to start: crashed.
  (chrome not reachable)
  (The process started from chrome location C:\Users\<USER>\Downloads\chrome-win64\chrome-win64\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
	GetHandleVerifier [0x00007FF6B366CF25+75717]
	GetHandleVerifier [0x00007FF6B366CF80+75808]
	(No symbol) [0x00007FF6B3438F9A]
	(No symbol) [0x00007FF6B3475D55]
	(No symbol) [0x00007FF6B3471ADD]
	(No symbol) [0x00007FF6B34C52D8]
	(No symbol) [0x00007FF6B34C48B0]
	(No symbol) [0x00007FF6B34B7153]
	(No symbol) [0x00007FF6B3480421]
	(No symbol) [0x00007FF6B34811B3]
	GetHandleVerifier [0x00007FF6B396D6FD+3223453]
	GetHandleVerifier [0x00007FF6B3967CA2+3200322]
	GetHandleVerifier [0x00007FF6B3985AD3+3322739]
	GetHandleVerifier [0x00007FF6B36869FA+180890]
	GetHandleVerifier [0x00007FF6B368E0FF+211359]
	GetHandleVerifier [0x00007FF6B3675274+109332]
	GetHandleVerifier [0x00007FF6B3675422+109762]
	GetHandleVerifier [0x00007FF6B365BA39+4825]
	BaseThreadInitThunk [0x00007FFE4D4A7374+20]
	RtlUserThreadStart [0x00007FFE4D69CC91+33]
 (scraper_thread.py:264)
2025-06-04 03:26:12,315 [ERROR] scraper_thread - Chrome başlatılamadı! İşlem sonlandırılıyor. (scraper_thread.py:264)
2025-06-04 03:26:18,612 [INFO] main - Önceki scraper thread temizlendi (main.py:238)
2025-06-04 03:29:38,237 [INFO] __main__ - Otomasyon worker başlatılıyor... (automation_worker.py:189)
2025-06-04 03:30:00,331 [INFO] __main__ - Yeni komut bulundu: start (automation_worker.py:148)
2025-06-04 03:30:00,331 [INFO] __main__ - Otomasyon başlatılıyor. Süre: 1 dk, Headless: False, MessageMode: both (automation_worker.py:65)
2025-06-04 03:30:10,159 [ERROR] scraper_thread - Chrome başlatılırken hata: Message: session not created: Chrome failed to start: crashed.
  (chrome not reachable)
  (The process started from chrome location C:\Users\<USER>\Downloads\chrome-win64\chrome-win64\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
	GetHandleVerifier [0x00007FF6B366CF25+75717]
	GetHandleVerifier [0x00007FF6B366CF80+75808]
	(No symbol) [0x00007FF6B3438F9A]
	(No symbol) [0x00007FF6B3475D55]
	(No symbol) [0x00007FF6B3471ADD]
	(No symbol) [0x00007FF6B34C52D8]
	(No symbol) [0x00007FF6B34C48B0]
	(No symbol) [0x00007FF6B34B7153]
	(No symbol) [0x00007FF6B3480421]
	(No symbol) [0x00007FF6B34811B3]
	GetHandleVerifier [0x00007FF6B396D6FD+3223453]
	GetHandleVerifier [0x00007FF6B3967CA2+3200322]
	GetHandleVerifier [0x00007FF6B3985AD3+3322739]
	GetHandleVerifier [0x00007FF6B36869FA+180890]
	GetHandleVerifier [0x00007FF6B368E0FF+211359]
	GetHandleVerifier [0x00007FF6B3675274+109332]
	GetHandleVerifier [0x00007FF6B3675422+109762]
	GetHandleVerifier [0x00007FF6B365BA39+4825]
	BaseThreadInitThunk [0x00007FFE4D4A7374+20]
	RtlUserThreadStart [0x00007FFE4D69CC91+33]
 (scraper_thread.py:264)
2025-06-04 03:30:14,237 [ERROR] scraper_thread - Chrome başlatılamadı! İşlem sonlandırılıyor. (scraper_thread.py:264)
2025-06-04 03:30:17,565 [INFO] main - Önceki scraper thread temizlendi (main.py:238)
2025-06-04 03:30:25,003 [ERROR] scraper_thread - Chrome başlatılırken hata: Message: session not created: Chrome failed to start: crashed.
  (chrome not reachable)
  (The process started from chrome location C:\Users\<USER>\Downloads\chrome-win64\chrome-win64\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
	GetHandleVerifier [0x00007FF6B366CF25+75717]
	GetHandleVerifier [0x00007FF6B366CF80+75808]
	(No symbol) [0x00007FF6B3438F9A]
	(No symbol) [0x00007FF6B3475D55]
	(No symbol) [0x00007FF6B3471ADD]
	(No symbol) [0x00007FF6B34C52D8]
	(No symbol) [0x00007FF6B34C48B0]
	(No symbol) [0x00007FF6B34B7153]
	(No symbol) [0x00007FF6B3480421]
	(No symbol) [0x00007FF6B34811B3]
	GetHandleVerifier [0x00007FF6B396D6FD+3223453]
	GetHandleVerifier [0x00007FF6B3967CA2+3200322]
	GetHandleVerifier [0x00007FF6B3985AD3+3322739]
	GetHandleVerifier [0x00007FF6B36869FA+180890]
	GetHandleVerifier [0x00007FF6B368E0FF+211359]
	GetHandleVerifier [0x00007FF6B3675274+109332]
	GetHandleVerifier [0x00007FF6B3675422+109762]
	GetHandleVerifier [0x00007FF6B365BA39+4825]
	BaseThreadInitThunk [0x00007FFE4D4A7374+20]
	RtlUserThreadStart [0x00007FFE4D69CC91+33]
 (scraper_thread.py:264)
2025-06-04 03:30:29,050 [ERROR] scraper_thread - Chrome başlatılamadı! İşlem sonlandırılıyor. (scraper_thread.py:264)
2025-06-04 03:32:02,856 [INFO] __main__ - Yeni komut bulundu: stop (automation_worker.py:148)
2025-06-04 03:32:09,597 [INFO] __main__ - Otomasyon durduruluyor. (automation_worker.py:77)
2025-06-04 03:32:09,612 [INFO] main - Otomasyon durdurma sinyali gönderildi (main.py:204)
2025-06-04 03:32:09,612 [INFO] main - Scraper thread durduruldu (main.py:209)
2025-06-04 03:32:09,612 [INFO] main - Tüm thread'ler durduruldu (main.py:219)
2025-06-04 03:32:10,284 [INFO] main - Thread monitörü durduruldu (main.py:192)
2025-06-04 03:32:17,643 [INFO] __main__ - Yeni komut bulundu: stop (automation_worker.py:148)
2025-06-04 03:32:17,643 [INFO] __main__ - Otomasyon zaten durdurulmuş. (automation_worker.py:81)
2025-06-05 02:58:55,948 [INFO] __main__ - Yeni komut bulundu: start (automation_worker.py:148)
2025-06-05 02:59:00,800 [INFO] __main__ - Otomasyon başlatılıyor. Süre: 3 dk, Headless: False, MessageMode: none (automation_worker.py:65)
2025-06-05 02:59:26,487 [ERROR] scraper_thread - Chrome başlatılırken hata: Message: session not created: Chrome failed to start: crashed.
  (session not created: Could not read in devtools port number)
  (The process started from chrome location C:\Users\<USER>\Downloads\chrome-win64\chrome-win64\chrome.exe is no longer running, so ChromeDriver is assuming that Chrome has crashed.)
Stacktrace:
	GetHandleVerifier [0x00007FF655F7CF25+75717]
	GetHandleVerifier [0x00007FF655F7CF80+75808]
	(No symbol) [0x00007FF655D48F9A]
	(No symbol) [0x00007FF655D85D55]
	(No symbol) [0x00007FF655D81ADD]
	(No symbol) [0x00007FF655DD52D8]
	(No symbol) [0x00007FF655DD48B0]
	(No symbol) [0x00007FF655DC7153]
	(No symbol) [0x00007FF655D90421]
	(No symbol) [0x00007FF655D911B3]
	GetHandleVerifier [0x00007FF65627D6FD+3223453]
	GetHandleVerifier [0x00007FF656277CA2+3200322]
	GetHandleVerifier [0x00007FF656295AD3+3322739]
	GetHandleVerifier [0x00007FF655F969FA+180890]
	GetHandleVerifier [0x00007FF655F9E0FF+211359]
	GetHandleVerifier [0x00007FF655F85274+109332]
	GetHandleVerifier [0x00007FF655F85422+109762]
	GetHandleVerifier [0x00007FF655F6BA39+4825]
	BaseThreadInitThunk [0x00007FFE4D4A7374+20]
	RtlUserThreadStart [0x00007FFE4D69CC91+33]
 (scraper_thread.py:264)
2025-06-05 02:59:30,519 [ERROR] scraper_thread - Chrome başlatılamadı! İşlem sonlandırılıyor. (scraper_thread.py:264)
