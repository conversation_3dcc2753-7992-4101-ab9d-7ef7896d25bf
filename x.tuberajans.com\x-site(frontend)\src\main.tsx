import React, { lazy, Suspense, useState, useEffect } from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { APP_CONFIG } from './config';
import { logger } from './utils/logger';
import './index.css';

// Lazy-load App bileşeni
const App = lazy(() => import('./App'));

// Suppress debug logs in production
if (import.meta.env.PROD) {
  console.log = () => {};
  console.debug = () => {};
  console.info = () => {};
}

// Yükleme ekranı bileşeni
const LoadingScreen = () => (
  <div className="loading-container" style={{
    width: '100%',
    height: '100vh',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    background: '#f5f5f5',
  }}>
    <div className="spinner" style={{
      width: '40px',
      height: '40px',
      border: '4px solid rgba(0, 0, 0, 0.1)',
      borderRadius: '50%',
      borderTop: '4px solid #1890ff',
      animation: 'spin 1s linear infinite',
    }} />
    <style>{`
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `}</style>
  </div>
);

// Global hata yakalayıcı
const ErrorFallback = () => {
  return (
    <div className="error-container" style={{
      width: '100%',
      height: '100vh',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      background: '#f5f5f5',
      color: '#333'
    }}>
      <div style={{ textAlign: 'center', maxWidth: '80%' }}>
        <h1 style={{ fontSize: '2rem', marginBottom: '1rem' }}>Uygulama Yüklenirken Bir Sorun Oluştu</h1>
        <p style={{ fontSize: '1rem', marginBottom: '2rem' }}>Lütfen sayfayı yenileyin veya daha sonra tekrar deneyin.</p>
        <button
          onClick={() => window.location.reload()}
          style={{
            padding: '10px 20px',
            backgroundColor: '#1890ff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '1rem'
          }}
        >
          Sayfayı Yenile
        </button>
      </div>
    </div>
  );
};

// Lazy loading wrapper with error boundary
const SafeAppLoader = () => {
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Global hata yakalayıcı
    const handleError = (event: ErrorEvent) => {
      setHasError(true);
      logger.error('Global application error caught during loading', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      }, event.error);
    };

    // Performans ölçümü başlat
    performance.mark('app-start');

    window.addEventListener('error', handleError);

    // Doğrudan yükleme durumunu değiştir - load event kullanma
    // Kısa bir gecikme ekleyerek yükleme animasyonunun görünmesini sağla
    setTimeout(() => {
      setIsLoading(false);
      performance.mark('app-loaded');
      performance.measure('app-loading-time', 'app-start', 'app-loaded');
    }, 500);

    return () => {
      window.removeEventListener('error', handleError);
    };
  }, []);

  if (hasError) {
    return <ErrorFallback />;
  }

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <Suspense fallback={<LoadingScreen />}>
      <BrowserRouter basename={APP_CONFIG.ROUTER.BASE_PATH}>
        <App />
      </BrowserRouter>
    </Suspense>
  );
};

const rootElement = document.getElementById('root');

if (!rootElement) {
  throw new Error('Root element bulunamadı! #root ID\'li element mevcut değil.');
}

// Hydration için optimizasyon - React 18 concurrent mode
const root = ReactDOM.createRoot(rootElement);
root.render(
  <React.StrictMode>
    <SafeAppLoader />
  </React.StrictMode>
);

// Cache yönetimi import
import CacheManager from './lib/cacheUtils';

// Servis çalışanı kayıt ve cache yönetimi - Optimize edilmiş
if ('serviceWorker' in navigator && import.meta.env.PROD) {
  window.addEventListener('load', async () => {
    try {
      // Mevcut service worker'ları kontrol et
      const registrations = await navigator.serviceWorker.getRegistrations();

      // Eski service worker'ları temizle
      for (const registration of registrations) {
        if (registration.scope.includes('x-tuber-cache-v1')) {
          console.log('Eski service worker temizleniyor...');
          await registration.unregister();
        }
      }

      // Cache'leri temizle
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName.includes('x-tuber-cache-v1')) {
              console.log('Eski cache temizleniyor:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }

      // Yeni service worker'ı kaydet - updateViaCache ile
      const registration = await navigator.serviceWorker.register('/service-worker.js', {
        updateViaCache: 'none', // Cache bypass
        scope: '/'
      });

      console.log('Service Worker kaydedildi:', registration);

      // Hemen güncelleme kontrolü yap
      await registration.update();

      // Service worker güncellemelerini dinle
      registration.addEventListener('updatefound', () => {
        console.log('Service Worker güncellemesi bulundu');
        const newWorker = registration.installing;

        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              console.log('Yeni Service Worker yüklendi');
              // Otomatik yenileme yerine kullanıcıya bildir
              if (confirm('Yeni güncelleme mevcut. Sayfayı yenilemek ister misiniz?')) {
                window.location.reload();
              }
            }
          });
        }
      });

    } catch (error) {
      console.error('Service Worker kayıt hatası:', error);
    }
  });
}

// Development modunda cache temizleme kısayolu
if (import.meta.env.DEV) {
  // Ctrl+Shift+R ile cache temizleme
  window.addEventListener('keydown', (e) => {
    if (e.ctrlKey && e.shiftKey && e.key === 'R') {
      e.preventDefault();
      console.log('Cache temizleme kısayolu tetiklendi');
      CacheManager.fullCacheReset();
    }
  });

  console.log('🔧 Development modu aktif');
  console.log('💡 Cache temizlemek için Ctrl+Shift+R tuşlarına basın');
  console.log('💡 Console\'da window.CacheManager kullanarak cache yönetimi yapabilirsiniz');
}