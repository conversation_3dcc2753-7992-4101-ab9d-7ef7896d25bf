<?php
// SendGrid Bulk Email API
ini_set('display_errors', 1);
error_reporting(E_ALL);
header('Content-Type: application/json; charset=utf-8');

// CORS headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../config/config.php';

// Get SendGrid API key from environment or config
$sendgridApiKey = getenv('SENDGRID_API_KEY') ?: (defined('SENDGRID_API_KEY') ? SENDGRID_API_KEY : null);
error_log("SendGrid API Key check: " . ($sendgridApiKey ? "Found" : "Not found"));
if (!$sendgridApiKey) {
    jsonResponse(['success' => false, 'error' => 'SendGrid API anahtarı yapılandırılmadı'], 500);
}

// Read input
$rawInput = file_get_contents('php://input');
error_log("Raw input: " . $rawInput);
$input = json_decode($rawInput, true);
error_log("Decoded input: " . print_r($input, true));

if (!$input || !isset($input['emails'], $input['subject'], $input['content'])) {
    jsonResponse(['success' => false, 'error' => 'Eksik parametre: emails, subject, content gerekli'], 400);
}

// Gönderen e-posta adresi seçimi
$allowedFromEmails = [
    '<EMAIL>' => 'Tuber Ajans | İş Birliği Ekibi',
    '<EMAIL>' => 'Tuber Ajans | Rapor Ekibi',
    '<EMAIL>' => 'Tuber Ajans | Destek Ekibi',
    '<EMAIL>' => 'Tuber Ajans | Satış Ekibi',
    '<EMAIL>' => 'Tuber Ajans | Pazarlama Ekibi',
    '<EMAIL>' => 'Tuber Ajans | Yönetim Ekibi'
];

$fromEmail = isset($input['fromEmail']) ? $input['fromEmail'] : '<EMAIL>';
$fromName = isset($allowedFromEmails[$fromEmail]) ? $allowedFromEmails[$fromEmail] : 'Tuber Ajans';

// Güvenlik kontrolü
if (!array_key_exists($fromEmail, $allowedFromEmails)) {
    $fromEmail = '<EMAIL>';
    $fromName = 'Tuber Ajans | İş Birliği Ekibi';
}

$emails = (array) $input['emails'];
$subject = trim($input['subject']);
$content = $input['content'];
$attachments = isset($input['attachments']) ? (array) $input['attachments'] : [];

// Build payload
$personalizations = [];
$toList = [];
foreach ($emails as $email) {
    $toList[] = ['email' => $email];
}
$personalizations[] = ['to' => $toList];

$payload = [
    'personalizations' => $personalizations,
    'from' => ['email' => $fromEmail, 'name' => $fromName],
    'subject' => $subject,
    'content' => [['type' => 'text/html', 'value' => $content]]
];

// Add attachments if present
if (!empty($attachments)) {
    $payload['attachments'] = [];
    
    foreach ($attachments as $attachment) {
        if (isset($attachment['filename'], $attachment['content'], $attachment['contentType'])) {
            $payload['attachments'][] = [
                'filename' => $attachment['filename'],
                'content' => $attachment['content'],
                'type' => $attachment['contentType'],
                'disposition' => 'attachment'
            ];
        }
    }
}

// Send request
error_log("Sending to SendGrid with payload: " . json_encode($payload));
$ch = curl_init('https://api.sendgrid.com/v3/mail/send');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . $sendgridApiKey,
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

error_log("SendGrid response code: " . $httpCode);
error_log("SendGrid response: " . $response);

if ($response === false || $httpCode >= 400) {
    $errorMsg = $curlError ?: "HTTP $httpCode";
    error_log("SendGrid error: " . $errorMsg . " Response: " . $response);
    jsonResponse(['success' => false, 'error' => 'SendGrid hatası: ' . $errorMsg], 500);
}

error_log("SendGrid success: " . $response);
jsonResponse(['success' => true]);