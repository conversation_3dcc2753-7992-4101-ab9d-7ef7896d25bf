<?php
// E-posta Şablon Yönetimi API
ini_set('display_errors', 1);
error_reporting(E_ALL);
header('Content-Type: application/json; charset=utf-8');

// CORS headers
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../config/config.php';

function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

// Veritabanı bağlantısı
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
} catch (PDOException $e) {
    jsonResponse(['success' => false, 'error' => 'Veritabanı bağlantı hatası: ' . $e->getMessage()], 500);
}

// Tabloyu oluştur (yoksa)
$createTableSQL = "
CREATE TABLE IF NOT EXISTS email_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) DEFAULT 'custom',
    subject VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

try {
    $pdo->exec($createTableSQL);

    // Varsayılan şablonları kontrol et ve ekle
    $checkDefault = $pdo->query("SELECT COUNT(*) as count FROM email_templates WHERE is_default = 1");
    $defaultCount = $checkDefault->fetch()['count'];

    if ($defaultCount == 0) {
        $defaultTemplates = [
            ['Genel Mesaj', 'general', 'Tuber Ajans - İletişim', "Merhaba {name},\n\nTuber Ajans olarak sizinle iletişime geçmekten mutluluk duyuyoruz.\n\nİyi günler dileriz.\n\nSaygılarımızla,\nTuber Ajans"],
            ['Haftalık Rapor', 'weekly_report', 'Haftalık Performans Raporu', "Merhaba {name},\n\nBu hafta sizin için hazırladığımız performans raporunuz hazır.\n\nDetaylar için lütfen ekte bulunan raporu inceleyiniz.\n\nSaygılarımızla,\nTuber Ajans"],
            ['Özel Mesaj', 'custom', 'Tuber Ajans - Özel Mesaj', "Merhaba {name},\n\n{custom_message}\n\nSaygılarımızla,\nTuber Ajans"]
        ];

        $stmt = $pdo->prepare("INSERT INTO email_templates (name, type, subject, content, is_default) VALUES (?, ?, ?, ?, 1)");
        foreach ($defaultTemplates as $template) {
            $stmt->execute($template);
        }
    }
} catch (PDOException $e) {
    jsonResponse(['success' => false, 'error' => 'Tablo oluşturma hatası: ' . $e->getMessage()], 500);
}

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        // Tüm şablonları getir
        try {
            $stmt = $pdo->query("SELECT * FROM email_templates ORDER BY is_default DESC, created_at DESC");
            $templates = $stmt->fetchAll();
            jsonResponse(['success' => true, 'templates' => $templates]);
        } catch (PDOException $e) {
            jsonResponse(['success' => false, 'error' => 'Şablonları getirme hatası: ' . $e->getMessage()], 500);
        }
        break;

    case 'POST':
        // Yeni şablon ekle
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || !isset($input['name'], $input['subject'], $input['content'])) {
            jsonResponse(['success' => false, 'error' => 'Eksik parametre: name, subject, content gerekli'], 400);
        }

        try {
            $stmt = $pdo->prepare("INSERT INTO email_templates (name, type, subject, content) VALUES (?, ?, ?, ?)");
            $stmt->execute([
                trim($input['name']),
                isset($input['type']) ? $input['type'] : 'custom',
                trim($input['subject']),
                trim($input['content'])
            ]);

            $newId = $pdo->lastInsertId();
            $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = ?");
            $stmt->execute([$newId]);
            $newTemplate = $stmt->fetch();

            jsonResponse(['success' => true, 'template' => $newTemplate]);
        } catch (PDOException $e) {
            jsonResponse(['success' => false, 'error' => 'Şablon kaydedilemedi: ' . $e->getMessage()], 500);
        }
        break;

    case 'PUT':
        // Şablon güncelle
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || !isset($input['id'], $input['name'], $input['subject'], $input['content'])) {
            jsonResponse(['success' => false, 'error' => 'Eksik parametre: id, name, subject, content gerekli'], 400);
        }

        try {
            // Şablonun var olup olmadığını kontrol et
            $stmt = $pdo->prepare("SELECT id FROM email_templates WHERE id = ?");
            $stmt->execute([$input['id']]);
            $template = $stmt->fetch();

            if (!$template) {
                jsonResponse(['success' => false, 'error' => 'Şablon bulunamadı'], 404);
            }

            $stmt = $pdo->prepare("UPDATE email_templates SET name = ?, subject = ?, content = ? WHERE id = ?");
            $stmt->execute([
                trim($input['name']),
                trim($input['subject']),
                trim($input['content']),
                $input['id']
            ]);

            $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = ?");
            $stmt->execute([$input['id']]);
            $updatedTemplate = $stmt->fetch();

            jsonResponse(['success' => true, 'template' => $updatedTemplate]);
        } catch (PDOException $e) {
            jsonResponse(['success' => false, 'error' => 'Şablon güncellenemedi: ' . $e->getMessage()], 500);
        }
        break;

    case 'DELETE':
        // Şablon sil
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || !isset($input['id'])) {
            jsonResponse(['success' => false, 'error' => 'Eksik parametre: id gerekli'], 400);
        }

        try {
            // Varsayılan şablonları silmeyi engelle
            $stmt = $pdo->prepare("SELECT is_default FROM email_templates WHERE id = ?");
            $stmt->execute([$input['id']]);
            $template = $stmt->fetch();

            if (!$template) {
                jsonResponse(['success' => false, 'error' => 'Şablon bulunamadı'], 404);
            }

            if ($template['is_default']) {
                jsonResponse(['success' => false, 'error' => 'Varsayılan şablonlar silinemez'], 400);
            }

            $stmt = $pdo->prepare("DELETE FROM email_templates WHERE id = ?");
            $stmt->execute([$input['id']]);

            jsonResponse(['success' => true, 'message' => 'Şablon silindi']);
        } catch (PDOException $e) {
            jsonResponse(['success' => false, 'error' => 'Şablon silinemedi: ' . $e->getMessage()], 500);
        }
        break;

    default:
        jsonResponse(['success' => false, 'error' => 'Desteklenmeyen HTTP metodu'], 405);
        break;
}
