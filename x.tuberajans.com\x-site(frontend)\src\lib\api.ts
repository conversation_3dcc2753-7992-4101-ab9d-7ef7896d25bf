import { API_CONFIG, SECURITY_CONFIG } from '../config';
import { PUBLISHERS, MOCK_WEEKLY_ARCHIVE, INFLUENCERS } from './mockData';
import { logger } from '../utils/logger';
import { useMockData, isDevelopment } from '../config/environment';

/* -------------------------------------------------
 *  MOCK_DATA – sadece development sırasında kullanılır
 * ------------------------------------------------*/
const MOCK_DATA = {
  users: [
    { id: 1, name: 'Dev Admin', email: '<EMAIL>', role: 'admin' },
  ],
  publishers: [],
  reports: [],
  influencers: [],
  dashboard: {},
  notifications: [],
} as const;

type MockKey = keyof typeof MOCK_DATA;


/* -------------------------------------------------
 *  1) Temel URL – TUTARLI API URL KULLAN
 * ------------------------------------------------- */
const API_URL = API_CONFIG.X_SITE_BASE_URL;

/* -------------------------------------------------
 *  2) Yardımcılar
 * ------------------------------------------------- */
const getToken = (): string | null => {
  try {
    const raw = localStorage.getItem('user');
    return raw ? JSON.parse(raw).token ?? null : null;
  } catch {
    return null;
  }
};

const getHeaders = (auth = false): HeadersInit => {
  const h: HeadersInit = { 'Content-Type': 'application/json' };
  if (auth) {
    const t = getToken();
    if (t) h['Authorization'] = `Bearer ${t}`;
  }
  return h;
};

/* -------------------------------------------------
 *  3) Genel istek sarmalı
 * ------------------------------------------------- */
const apiRequest = async (endpoint: string, options: RequestInit = {}) => {
  try {
    // URL doğru şekilde birleştir
    // Şimdilik /api'nin BASE URL'de olduğunu varsayıyoruz.

    // Eğer API_URL sonunda /api yoksa ve endpoint başında /api yoksa:
    // const url = `${API_URL}/api${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;
    // Eğer API_URL sonunda /api VARSA ve endpoint başında / yoksa:
    // const url = `${API_URL}${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;
    // Şimdilik en basit haliyle bırakalım, API_URL'in doğru olduğunu varsayalım:
    const url = `${API_URL}${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;
    // Debugging için daha açıklayıcı loglar ekleyelim
    console.log(`[DEBUG] API İsteği Yapılıyor:
     - API_URL: ${API_URL}
     - Endpoint: ${endpoint}
     - Tam URL: ${url}
     - Method: ${options.method || 'GET'}
     - Token: ${getToken() ? 'Var' : 'Yok'}`);

    if (options.method === 'POST' && options.body) {
      try {
        console.log('Request Body:', JSON.parse(options.body as string));
      } catch (e) {
        console.log('Request Body (raw):', options.body);
      }
    }

    const token = getToken(); // Doğru token alma fonksiyonunu kullan

    // Temel header'ları oluştur
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    // Token varsa ekle
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // Özel header'lar varsa ekle
    if (options.headers) {
      Object.entries(options.headers).forEach(([key, value]) => {
        headers[key] = value;
      });
    }

    const requestOptions: RequestInit = {
      method: options.method || 'GET',
      headers,
      body: options.body,
      // CORS sorunlarını önlemek için aynı origin kullan - 403 hataları için önemli
      credentials: 'same-origin',
    };

    console.log('[DEBUG] Request headers:', headers);

    const response = await fetch(url, requestOptions);

    if (!response.ok) {
      // Daha detaylı hata yönetimi
      const contentType = response.headers.get('content-type');
      let errorDetails = '';

      try {
        if (contentType && contentType.includes('application/json')) {
          // JSON hatası
          const errorJson = await response.json();
          console.error(`API Hata Yanıtı (${response.status}):`, errorJson);
          errorDetails =
            errorJson.error ||
            errorJson.message ||
            (typeof errorJson === 'string' ? errorJson : JSON.stringify(errorJson));
        } else {
          // Text hatası
          const errorText = await response.text();
          console.error(`API Hata Yanıtı (${response.status}):`, errorText);
          errorDetails = errorText || 'Hata detayı yok';
        }
      } catch (parseError) {
        console.error('Hata yanıtı ayrıştırılamadı:', parseError);
        errorDetails = 'Hata yanıtı alınamadı';
      }

      throw new Error(`API request failed: Error: HTTP error! status: ${response.status} - ${errorDetails}`);
    }

    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      const jsonData = await response.json();
      console.log('API Response:', jsonData);
      return jsonData;
    } else {
      const textData = await response.text();
      console.log('API Response (text):', textData);
      return textData;
    }
  } catch (error) {
    logger.error('API request failed', { endpoint, error: error instanceof Error ? error.message : String(error) }, error instanceof Error ? error : undefined);

    // Development ortamında ve mock data flag'i aktifse mockData kullan
    if (useMockData) {
      logger.warn('Using mock data due to API error', { endpoint, useMockData });

      if (endpoint.includes('dashboard')) {
        return getDashboardTestData();
      }

      if (endpoint.includes('publishers')) {
        return { data: PUBLISHERS };
      }

      if (endpoint.includes('influencers')) {
        return { data: INFLUENCERS };
      }

      if (endpoint.includes('reports')) {
        return { data: MOCK_WEEKLY_ARCHIVE };
      }
    }

    if (endpoint.includes('auth')) {
      logger.error('Authentication API error', { endpoint });
      // Auth hatalarını yükselt - kullanıcı bilgilendirilmeli
      throw error;
    }

    // Diğer durumlarda boş array veya nesne döndür
    return endpoint.includes('reports') ||
           endpoint.includes('publishers') ||
           endpoint.includes('influencers') ?
           { data: [] } :
           {};
  }
};

// Dashboard için güvenilir test verisi
function getDashboardTestData() {
  return {
    overview: {
      total_publishers: 45,
      total_tasks: 120,
      completed_tasks: 85,
      total_followers: 250000,
      total_diamonds: 1500000,
      completion_rate: 70.8
    },
    trend_data: [
      {
        week_start: '2023-04-01',
        total_followers: 220000,
        total_diamonds: 1300000
      },
      {
        week_start: '2023-04-08',
        total_followers: 230000,
        total_diamonds: 1350000
      },
      {
        week_start: '2023-04-15',
        total_followers: 240000,
        total_diamonds: 1400000
      }
    ],
    recent_events: [
      {
        id: 1,
        title: 'Test Etkinlik 1',
        date: new Date().toISOString().split('T')[0],
        status: 'active'
      },
      {
        id: 2,
        title: 'Test Etkinlik 2',
        date: new Date(Date.now() + 86400000).toISOString().split('T')[0],
        status: 'pending'
      }
    ]
  };
}

export async function loginUser(email: string, password: string) {
  try {
    // URL'yi tam olarak göster ve debugla
    const loginUrl = `${API_URL}/auth.php`;
    console.log('Login URL:', loginUrl);

    const response = await fetch(loginUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify({ email, password })
    });

    const responseText = await response.text();
    console.log('Raw Server Response:', responseText);

    if (!response.ok) {
      const errorData = JSON.parse(responseText);
      throw new Error(errorData.error || 'Giriş başarısız');
    }

    const responseData = JSON.parse(responseText);

    // SADECE VERİYİ DÖNDÜR, localStorage'a KAYDETME
    // auth.ts içindeki login fonksiyonu bunu yapacak
    return {
      user: responseData.user,
      token: responseData.token
    };

  } catch (error) {
    console.error('Login process error:', error);
    throw error;
  }
}


export async function logoutUser(token: string) {
  // Geliştirme ortamında simüle edilmiş logout
  if (process.env.NODE_ENV === 'development') {
    console.log('[DEV] Mock Logout');
    return { success: true };
  }

  // Normal logout
  try {
    const logoutUrl = `${API_URL}/auth.php?action=logout`;
    console.log('Logout URL:', logoutUrl);

    const response = await fetch(logoutUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      let errorText = await response.text();
      console.error('Logout Error Response:', errorText);

      try {
        const errorData = JSON.parse(errorText);
        throw new Error(
          errorData.error ||
          errorData.message ||
          'Çıkış başarısız'
        );
      } catch {
        throw new Error(errorText || 'Çıkış sırasında bilinmeyen bir hata oluştu');
      }
    }

    return await response.json();
  } catch (error) {
    console.error('Logout process error:', error);
    throw error;
  }
}

export async function validateToken(token: string) {
  // Geliştirme ortamında token her zaman geçerli
  if (process.env.NODE_ENV === 'development') {
    console.log('[DEV] Mock Token Validation');
    return { user: { id: 1, name: 'Test User', email: '<EMAIL>', role: 'admin' } };
  }

  // Normal token doğrulama
  try {
    const validateUrl = `${API_URL}/auth.php`;
    console.log('Token validation URL:', validateUrl);

    const response = await fetch(validateUrl, {
      method: 'GET',
      headers: { 'Authorization': `Bearer ${token}` },
      credentials: 'include'
    });

    if (!response.ok) {
      let errorText = await response.text();
      console.error('Token Validation Error Response:', errorText);

      try {
        const errorData = JSON.parse(errorText);
        throw new Error(
          errorData.error ||
          errorData.message ||
          'Token doğrulama başarısız'
        );
      } catch {
        throw new Error(errorText || 'Token doğrulama sırasında bilinmeyen bir hata oluştu');
      }
    }

    return await response.json();
  } catch (error) {
    console.error('Token validation process error:', error);
    throw error;
  }
}

// Mock veri kullanımı için yardımcı fonksiyon
function useMockData<T>(mockData: T, delay = 500): Promise<T> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockData);
    }, delay);
  });
}

// Yayıncı API
export interface Publisher {
  id?: number;
  username: string;
  isim_soyisim: string;
  telefon: string;
  mail: string;
  dogum_tarihi: string;
  sehir: string;
  meslek: string;
  kayit_tarihi?: string;
}

export async function getPublishers() {
  if (import.meta.env.DEV && API_CONFIG.ALWAYS_USE_MOCK_IN_DEV) {
    console.log('[DEV] Mock Publisher verileri kullanılıyor');
    return useMockData({ data: PUBLISHERS });
  }
  try {
    const response = await apiRequest('/db-query.php', {
      method: 'POST',
      body: JSON.stringify({
        query: 'SELECT * FROM publisher_info'
      })
    });
    return response;
  } catch (error) {
    console.error('[HATA] Publishers verileri alınamadı:', error);
    // Hata durumunda geliştirme ortamında mock veri kullan
    if (import.meta.env.DEV) {
      console.warn('Hata sonrası mock veri kullanılıyor');
      return { data: PUBLISHERS };
    }
    // Production'da boş dizi döndür
    return { data: [] };
  }
}

export async function getPublisherById(username: string) {
  return apiRequest(`/publishers.php?username=${username}`);
}

export async function createPublisher(data: Partial<Publisher>) {
  return apiRequest('/publishers.php', { method: 'POST', body: JSON.stringify(data) });
}

export async function updatePublisher(publisherData: Partial<Publisher>) {
  return apiRequest('/publishers.php', { method: 'PUT', body: JSON.stringify(publisherData) });
}

export async function deletePublisher(id: number) {
  return apiRequest('/publishers.php', { method: 'DELETE', body: JSON.stringify({ id }) });
}

// Rapor API
export interface WeeklyReport {
  id?: number;
  kullanici_adi: string;
  hafta_baslangici: string;
  hafta_bitisi: string;
  canli_yayin_gunu: number;
  yayin_suresi: number;
  elmaslar: number;
  yeni_takipciler: number;
  aboneler: number;
  maclar: number;
}

export async function getReports() {
  return apiRequest('/reports.php');
}

export async function getReportsByDateRange(startDate: string, endDate: string) {
  if (import.meta.env.DEV && API_CONFIG.ALWAYS_USE_MOCK_IN_DEV) {
    console.log('DEV modunda mock veri kullanılıyor...');
    const mockReports = MOCK_WEEKLY_ARCHIVE.filter(report => {
      const reportDate = new Date(report.hafta_baslangici);
      const start = new Date(startDate);
      const end = new Date(endDate);
      return reportDate >= start && reportDate <= end;
    });

    return useMockData({ data: mockReports });
  }

  const endpoint = `${API_CONFIG.ENDPOINTS.WEEKLY_ARCHIVE}?start_date=${startDate}&end_date=${endDate}`;
  console.log(`Weekly Archive için endpoint: ${endpoint}`);
  console.log(`API Base URL: ${API_CONFIG.BASE_URL}`);
  console.log(`Tam URL: ${API_CONFIG.BASE_URL}/${endpoint.replace(/^\//, '')}`);

  try {
    const response = await apiRequest(endpoint);
    console.log('Weekly Archive API yanıtı:', response);
    return response;
  } catch (error) {
    console.error('Weekly Archive hatası:', error);
    // Hata durumunda boş veri döndür
    return { data: [] };
  }
}

export async function getReportsByUsername(username: string) {
  return apiRequest(`/reports.php?username=${username}`);
}

export async function getReportsByUsernameAndDateRange(username: string, startDate: string, endDate: string) {
  return apiRequest(`/reports.php?username=${username}&start_date=${startDate}&end_date=${endDate}`);
}

// Influencer API
export interface Influencer {
  id?: number;
  username: string;
  email: string;
  followers?: number;
  followers_range?: string;
  category?: string;
  location?: string;
  status?: string;
  notes?: string;
  last_contact?: string;
  sosyal_medya?: string;
  telefon?: string;
  kampanya_gecmisi?: string;
  profile_image?: string;
}

export async function getInfluencers() {
  if (import.meta.env.DEV && API_CONFIG.ALWAYS_USE_MOCK_IN_DEV) {
    console.log('DEV modunda mock veri kullanılıyor...');
    return useMockData({ data: INFLUENCERS });
  }
  try {
    // API_CONFIG'den doğru endpoint'i al
    const endpoint = API_CONFIG.ENDPOINTS.INFLUENCERS;
    console.log(`Influencer listesi için endpoint kullanılıyor: ${endpoint}`);

    // Doğru endpoint'e GET isteği gönder
    const response = await apiRequest(endpoint, {
      method: 'GET',
    });

    console.log('Influencer API yanıtı:', response);

    return response;
  } catch (error) {
    console.error('getInfluencers error:', error);
    // Hata durumunda boş bir veri dizisi döndür
    return { data: [] };
  }
}

export async function getInfluencerById(id: number) {
  if (import.meta.env.DEV) {
    return useMockData({
      data: INFLUENCERS.find(inf => inf.id === id) || null
    });
  }

  try {
    // API_CONFIG'den doğru endpoint'i al
    const endpoint = `${API_CONFIG.ENDPOINTS.INFLUENCERS}?id=${id}`;
    console.log(`Influencer detayı için endpoint kullanılıyor: ${endpoint}`);

    if (!id) {
      throw new Error('ID parametresi gereklidir');
    }

    // Doğru endpoint'e GET isteği gönder
    return apiRequest(endpoint, {
      method: 'GET'
    });
  } catch (error) {
    console.error('getInfluencerById error:', error);
    throw error;
  }
}

export async function updateInfluencer(id: number, influencerData: Partial<Influencer>) {
  if (import.meta.env.DEV && (!influencerData || Object.keys(influencerData).length === 0)) {
    return useMockData({ success: true, updated: true, id });
  }

  console.log('updateInfluencer çağrıldı:', id, influencerData);

  try {
    // API_CONFIG'den doğru endpoint'i al
    const endpoint = API_CONFIG.ENDPOINTS.INFLUENCERS;
    console.log(`Influencer güncellemek için endpoint kullanılıyor: ${endpoint}`);

    if (!id) {
      throw new Error('Güncelleme için ID gereklidir');
    }

    // Veriyi temizle - undefined ve boş değerleri kaldır
    const cleanData: Record<string, any> = Object.entries(influencerData)
      .filter(([_, value]) => value !== undefined && value !== null)
      .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

    // ID'yi temiz verilere ekle
    cleanData.id = id;

    console.log('Backend\'e gönderilecek güncelleme verisi:', cleanData);

    // Doğru endpoint'e PUT isteği gönder
    return apiRequest(endpoint, {
      method: 'PUT',
      body: JSON.stringify({
        action: 'update',
        data: cleanData
      })
    });
  } catch (error) {
    console.error('updateInfluencer error:', error);
    throw error;
  }
}

export async function createInfluencer(influencerData: Partial<Influencer>) {
  if (import.meta.env.DEV && (!influencerData || Object.keys(influencerData).length === 0)) {
    return useMockData({
      success: true,
      id: Math.floor(Math.random() * 1000) + 1000
    });
  }

  console.log('createInfluencer çağrıldı:', influencerData);

  try {
    // Zorunlu alanları kontrol et
    if (!influencerData.username || !influencerData.email) {
      throw new Error('Kullanıcı adı ve email zorunludur');
    }

    // API_CONFIG'den doğru endpoint'i al
    const endpoint = API_CONFIG.ENDPOINTS.INFLUENCERS;
    console.log(`Influencer eklemek için endpoint kullanılıyor: ${endpoint}`);

    // Veriyi temizle - undefined ve boş değerleri kaldır
    const cleanData = Object.entries(influencerData)
      .filter(([_, value]) => value !== undefined && value !== null && value !== '')
      .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

    console.log('Backend\'e gönderilecek veri:', cleanData);

    // Doğru endpoint'e POST isteği gönder
    return apiRequest(endpoint, {
      method: 'POST',
      body: JSON.stringify({
        action: 'create',
        data: cleanData
      })
    });
  } catch (error) {
    console.error('createInfluencer error:', error);
    throw error;
  }
}

export async function deleteInfluencer(id: number) {
  if (import.meta.env.DEV) {
    return useMockData({ success: true });
  }

  try {
    // API_CONFIG'den doğru endpoint'i al
    const endpoint = API_CONFIG.ENDPOINTS.INFLUENCERS;
    console.log(`Influencer silme için endpoint kullanılıyor: ${endpoint}`);

    if (!id) {
      throw new Error('Silme işlemi için ID gereklidir');
    }

    // Doğru endpoint'e DELETE isteği gönder
    return apiRequest(endpoint, {
      method: 'DELETE',
      body: JSON.stringify({
        action: 'delete',
        id
      })
    });
  } catch (error) {
    console.error('deleteInfluencer error:', error);
    throw error;
  }
}

export async function sendEmailToInfluencers(emails: string[], subject: string, content: string, fromEmail?: string) {
  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}/send-email.php`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem(SECURITY_CONFIG.TOKEN_KEY)}`
      },
      body: JSON.stringify({
        emails: emails,
        subject: subject,
        content: content,
        fromEmail: fromEmail
      })
    });

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'E-posta gönderimi başarısız');
    }

    return result;
  } catch (error) {
    console.error('SendGrid e-posta gönderme hatası:', error);
    throw error;
  }
}

// Dashboard API
export async function getDashboardData() {
  try {
    const response = await apiRequest('/dashboard.php?type=full');
    if (typeof response === 'object' && response !== null) {
      console.log('Dashboard verisi alındı:', response);
      return response;
    }
    throw new Error('Geçersiz dashboard verisi');
  } catch (error) {
    console.error('Dashboard verisi alınamadı:', error);
    return getDashboardTestData();
  }
}

export async function getDashboardStats() {
  try {
    const response = await apiRequest('/dashboard.php?type=stats');
    return response;
  } catch (error) {
    console.error('Dashboard istatistik verisi alınamadı:', error);
    return getDashboardTestData().overview;
  }
}

export async function getWeeklyTrends(timeRange: string = 'weekly') {
  try {
    const response = await apiRequest(`/dashboard.php?type=trends&range=${timeRange}`);
    return response;
  } catch (error) {
    console.error('Trend verisi alınamadı:', error);
    return getDashboardTestData().trend_data;
  }
}

export async function getTopPerformers() {
  try {
    const response = await getDashboardData();
    if (response && response.data && response.data.top_performers) {
      return { data: response.data.top_performers };
    }
    throw new Error('Geçerli top performers verisi bulunamadı');
  } catch (error) {
    console.error('Top performers verisi alınamadı:', error);
    // Varsayılan test verisi döndür
    return {
      data: [
        { id: "test1", username: "test_user1", growth: 23, diamonds: 25000 },
        { id: "test2", username: "test_user2", growth: 18, diamonds: 20000 },
        { id: "test3", username: "test_user3", growth: 15, diamonds: 18000 }
      ]
    };
  }
}

export async function getNotifications() {
  try {
    const response = await apiRequest('/notifications.php');
    return response;
  } catch (error) {
    console.error('Bildirimler alınamadı:', error);
    // Varsayılan boş bildirim dizisi döndür
    return {
      data: [],
      unread_count: 0
    };
  }
}

// Kullanıcı API
export interface User {
  id: number;
  name: string;
  email: string;
  role: 'admin' | 'editor' | 'viewer';
  created_at?: string;
}

export async function getUsers() {
  if (import.meta.env.DEV) {
    return useMockData([
      {
        id: 1,
        name: 'Admin',
        email: '<EMAIL>',
        role: 'admin',
        created_at: new Date().toISOString()
      },
      {
        id: 2,
        name: 'Editör',
        email: '<EMAIL>',
        role: 'editor',
        created_at: new Date().toISOString()
      },
      {
        id: 3,
        name: 'İzleyici',
        email: '<EMAIL>',
        role: 'viewer',
        created_at: new Date().toISOString()
      }
    ]);
  }
  return apiRequest('/user-management.php');
}

export async function createUser(userData: Omit<User, 'id' | 'created_at'> & { password: string }) {
  if (import.meta.env.DEV) {
    return useMockData({
      ...userData,
      id: Math.floor(Math.random() * 1000) + 4,
      created_at: new Date().toISOString()
    });
  }
  return apiRequest('/user-management.php', { method: 'POST', body: JSON.stringify(userData) });
}

export async function updateUser(userData: Partial<User> & { password?: string }) {
  if (import.meta.env.DEV) {
    return useMockData({
      ...userData,
      updated_at: new Date().toISOString()
    });
  }
  return apiRequest(`/user-management.php?id=${userData.id}`, { method: 'PUT', body: JSON.stringify(userData) });
}

export async function deleteUser(id: number) {
  if (import.meta.env.DEV) {
    return useMockData({ message: 'Kullanıcı başarıyla silindi' });
  }
  return apiRequest(`/user-management.php?id=${id}`, { method: 'DELETE' });
}

interface LoadDataResponse {
  users: User[];
  publishers: Publisher[];
  reports: WeeklyReport[];
}

export const loadData = async () => {
  try {
    const response = await apiRequest('/dashboard.php', { method: 'GET' });

    if (process.env.NODE_ENV === 'development' && !response) {
      return {
        data: {
          users: MOCK_DATA.users,
          publishers: MOCK_DATA.publishers,
          reports: MOCK_DATA.reports,
          // AI Advisor için gerekli veriler
          topPerformers: [
            { id: "1", username: "yayinci1", growth: 23, diamonds: 25000 },
            { id: "2", username: "yayinci2", growth: 18, diamonds: 20000 },
            { id: "3", username: "yayinci3", growth: 15, diamonds: 18000 }
          ],
          contentTypePerformance: [
            { type: "Müzik", performance: 85 },
            { type: "Eğlence", performance: 70 },
            { type: "Sohbet", performance: 60 }
          ],
          optimalTimeSlots: [
            { activity: "Müzik Yayınları", timeSlot: "20:00 - 22:00" },
            { activity: "Sohbet", timeSlot: "18:00 - 20:00" }
          ],
          currentMonthRevenue: 25000,
          previousMonthRevenue: 20000,
          targetRevenue: 35000,
          targetGrowth: 40
        },
        error: null
      };
    }

    return {
      data: response,
      error: null
    };
  } catch (error) {
    console.error('Data loading error:', error);

    if (process.env.NODE_ENV === 'development') {
      console.warn('Veri yükleme hatası nedeniyle mock veri kullanılıyor');
      return {
        data: {
          users: MOCK_DATA.users,
          publishers: MOCK_DATA.publishers,
          reports: MOCK_DATA.reports,
          // AI Advisor için gerekli veriler
          topPerformers: [
            { id: "1", username: "yayinci1", growth: 23, diamonds: 25000 },
            { id: "2", username: "yayinci2", growth: 18, diamonds: 20000 },
            { id: "3", username: "yayinci3", growth: 15, diamonds: 18000 }
          ],
          contentTypePerformance: [
            { type: "Müzik", performance: 85 },
            { type: "Eğlence", performance: 70 },
            { type: "Sohbet", performance: 60 }
          ],
          optimalTimeSlots: [
            { activity: "Müzik Yayınları", timeSlot: "20:00 - 22:00" },
            { activity: "Sohbet", timeSlot: "18:00 - 20:00" }
          ],
          currentMonthRevenue: 25000,
          previousMonthRevenue: 20000,
          targetRevenue: 35000,
          targetGrowth: 40
        },
        error: null
      };
    }

    const emptyData: LoadDataResponse = {
      users: [],
      publishers: [],
      reports: []
    };

    return {
      data: process.env.NODE_ENV === 'development' ? emptyData : null,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};