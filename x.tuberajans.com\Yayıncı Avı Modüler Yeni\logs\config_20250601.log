2025-06-01 18:38:25,922 [INFO] __main__ - Otomasyon worker ba<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>... (automation_worker.py:189)
2025-06-03 15:43:47,034 [INFO] __main__ - <PERSON><PERSON> komut bulundu: start (automation_worker.py:148)
2025-06-03 15:43:54,753 [INFO] __main__ - <PERSON><PERSON><PERSON><PERSON> başlatılıyor. Süre: 1 dk, Headless: <PERSON><PERSON><PERSON>, MessageMode: elite (automation_worker.py:65)
2025-06-03 15:44:01,943 [INFO] scraper_thread - TikTok Live sayfasına gidiliyor... (scraper_thread.py:260)
2025-06-03 15:44:09,602 [INFO] scraper_thread - <PERSON>raper ba<PERSON><PERSON><PERSON>, süre: 1.0 dk (scraper_thread.py:260)
2025-06-03 15:44:14,539 [INFO] scraper_thread - mehmetalierbilofficial | 501 (scraper_thread.py:260)
2025-06-03 15:44:20,175 [INFO] scraper_thread - elbor<PERSON>ua94 | 0 (scraper_thread.py:260)
2025-06-03 15:44:25,424 [INFO] scraper_thread - panjshiri756 | 2 (scraper_thread.py:260)
2025-06-03 15:44:31,451 [INFO] scraper_thread - solovelvet138 | 1 (scraper_thread.py:260)
2025-06-03 15:44:36,208 [INFO] scraper_thread - trapboy699 | 1 (scraper_thread.py:260)
2025-06-03 15:44:40,680 [INFO] scraper_thread - aliyev_emrah_044 | 3 (scraper_thread.py:260)
2025-06-03 15:44:46,102 [INFO] scraper_thread - cassidyp222 | 4 (scraper_thread.py:260)
2025-06-03 15:44:51,618 [INFO] scraper_thread - saatra2.0 | 3 (scraper_thread.py:260)
2025-06-03 15:44:56,389 [INFO] scraper_thread - uhhhwhit | 2 (scraper_thread.py:260)
2025-06-03 15:45:01,882 [INFO] scraper_thread - user2043105632337 | 5 (scraper_thread.py:260)
2025-06-03 15:45:06,121 [INFO] scraper_thread - wesleywscavaladoidao | 1 (scraper_thread.py:260)
2025-06-03 15:45:09,930 [INFO] scraper_thread - Toplam 11 benzersiz kullanıcı bulundu (scraper_thread.py:260)
2025-06-03 15:45:18,069 [INFO] main - MONİTÖR: Veritabanından alınan son 1 dakikadaki kullanıcı sayısı: 10 (main.py:110)
2025-06-03 15:45:18,071 [INFO] main - MONİTÖR: Örnek kayıtlar: [{'username': 'wesleywscavaladoidao', 'timestamp': datetime.datetime(2025, 6, 3, 15, 45, 6), 'status': 'Bekleniyor'}, {'username': 'user2043105632337', 'timestamp': datetime.datetime(2025, 6, 3, 15, 45, 1), 'status': 'Bekleniyor'}, {'username': 'uhhhwhit', 'timestamp': datetime.datetime(2025, 6, 3, 15, 44, 56), 'status': 'Bekleniyor'}] (main.py:111)
2025-06-03 15:45:21,086 [INFO] main - MONİTÖR: Status checker için 10 kullanıcı hazırlandı (main.py:125)
2025-06-03 15:45:21,097 [INFO] StatusChecker - 🔍 Chrome StatusChecker başlıyor... (status_checker.py:87)
2025-06-03 15:45:21,101 [INFO] StatusChecker - Diğer Chrome işlemlerinin kapanması bekleniyor... (status_checker.py:87)
2025-06-03 15:45:21,101 [INFO] StatusChecker - status_checker diğer thread'lerin bitmesini bekliyor... (status_checker.py:87)
2025-06-03 15:45:21,101 [INFO] StatusChecker - Tüm diğer thread'ler tamamlandı, status_checker başlayabilir. (status_checker.py:87)
2025-06-03 15:45:21,101 [INFO] StatusChecker - Chrome WebDriver kurulumu başlıyor... (status_checker.py:87)
2025-06-03 15:45:21,102 [INFO] StatusChecker - Kullanılacak ChromeDriver yolu: chromedriver.exe (status_checker.py:87)
2025-06-03 15:45:21,103 [INFO] StatusChecker - Kullanılacak Chrome profil ana yolu: C:\Users\<USER>\AppData\Local\Google\Chrome for Testing\User Data (status_checker.py:87)
2025-06-03 15:45:21,103 [INFO] StatusChecker - Kullanılacak Chrome profil klasör adı: Profile 1 (status_checker.py:87)
2025-06-03 15:45:21,104 [INFO] StatusChecker - Kullanılacak Chrome binary yolu: C:\Users\<USER>\Downloads\chrome-win64\chrome-win64\chrome.exe (status_checker.py:87)
2025-06-03 15:45:21,105 [INFO] StatusChecker - Chrome tercihleri ayarlandı. ChromeDriver servisi başlatılıyor... (status_checker.py:87)
2025-06-03 15:45:23,328 [INFO] StatusChecker - ✅ Chrome WebDriver başarıyla başlatıldı. (status_checker.py:87)
2025-06-03 15:45:23,449 [INFO] StatusChecker - JavaScript ile navigator.webdriver maskelendi. (status_checker.py:87)
2025-06-03 15:45:23,450 [INFO] StatusChecker - 🌐 TikTok Backstage sayfasına gidiliyor... (status_checker.py:87)
2025-06-03 15:45:23,453 [INFO] StatusChecker - ⏳ URL yükleniyor: https://live-backstage.tiktok.com/portal (status_checker.py:87)
2025-06-03 15:45:24,503 [ERROR] StatusChecker - ⚠ Sayfaya erişim hatası: Message: unknown error: net::ERR_NAME_NOT_RESOLVED
  (Session info: chrome=136.0.7103.92)
Stacktrace:
	GetHandleVerifier [0x00007FF62A2ECF25+75717]
	GetHandleVerifier [0x00007FF62A2ECF80+75808]
	(No symbol) [0x00007FF62A0B8F9A]
	(No symbol) [0x00007FF62A0B59F4]
	(No symbol) [0x00007FF62A0A6789]
	(No symbol) [0x00007FF62A0A84F8]
	(No symbol) [0x00007FF62A0A6A96]
	(No symbol) [0x00007FF62A0A6516]
	(No symbol) [0x00007FF62A0A61DA]
	(No symbol) [0x00007FF62A0A3E8A]
	(No symbol) [0x00007FF62A0A465C]
	(No symbol) [0x00007FF62A0BCF3A]
	(No symbol) [0x00007FF62A16013E]
	(No symbol) [0x00007FF62A13737A]
	(No symbol) [0x00007FF62A15F39C]
	(No symbol) [0x00007FF62A137153]
	(No symbol) [0x00007FF62A100421]
	(No symbol) [0x00007FF62A1011B3]
	GetHandleVerifier [0x00007FF62A5ED6FD+3223453]
	GetHandleVerifier [0x00007FF62A5E7CA2+3200322]
	GetHandleVerifier [0x00007FF62A605AD3+3322739]
	GetHandleVerifier [0x00007FF62A3069FA+180890]
	GetHandleVerifier [0x00007FF62A30E0FF+211359]
	GetHandleVerifier [0x00007FF62A2F5274+109332]
	GetHandleVerifier [0x00007FF62A2F5422+109762]
	GetHandleVerifier [0x00007FF62A2DBA39+4825]
	BaseThreadInitThunk [0x00007FFE4D4A7374+20]
	RtlUserThreadStart [0x00007FFE4D69CC91+33]
 (status_checker.py:91)
2025-06-03 15:45:26,425 [INFO] __main__ - Program kullanıcı tarafından durduruldu. (automation_worker.py:192)
2025-06-03 15:45:26,425 [INFO] __main__ - Program sonlandırılıyor... (automation_worker.py:197)
