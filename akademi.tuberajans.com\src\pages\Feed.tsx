import React, { useState, useRef, useEffect, useContext, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaHeart, FaComment, FaEllipsisH, FaSmile, FaTimes, FaPencilAlt, FaTrash, FaStream } from 'react-icons/fa';
import axios from 'axios';
import { useAuth } from '../hooks/useAuth';
import { SidebarContext } from '../contexts/SidebarContext';
import { useTikTok } from '../hooks/useTikTok';
import { formatPostTime } from '../utils/dateUtils';


interface UserInfo {
  username: string;
  name?: string;
  profile_image?: string;
  avatar_url?: string;
  role?: string;
}

interface FeedItem {
  id: number;
  author: {
    avatar: string | null;
    username?: string;
    name?: string;
    role?: string;
  };
  content: string;
  media?: string | null;
  createdAt: Date;
  likes: number;
  comments: number;
  shares: number;
  isLiked: boolean;
  comments_data?: Array<{
    id: number;
    content: string;
    author: string;
    username?: string;
    profile_image?: string;
    created_at: string;
  }>;
}

// API Base URL'i ayarla
const API_BASE_URL = import.meta.env.PROD 
  ? 'https://akademi.tuberajans.com/backend/api' 
  : '/backend/api';

const Feed: React.FC = () => {
  // Development mode kontrolü - Production mode'a geçtik
  const isDevMode = false; // Production mode'u zorla

  // Sidebar context'inden isMobile ve isSidebarOpen değerlerini al
  const { isMobile } = useContext(SidebarContext);

  // AuthContext'i her zaman kullan
  const authContext = useAuth();
  let user = authContext.user;

  // Fallback: localStorage'dan kullanıcı bilgisini al
  if (!user) {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      user = JSON.parse(storedUser);
    }
  }
  const [postText, setPostText] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [openMenuId, setOpenMenuId] = useState<number | null>(null);
  const [editingPostId, setEditingPostId] = useState<number | null>(null);
  const [editingText, setEditingText] = useState('');
  const [editingPreviewUrl, setEditingPreviewUrl] = useState<string | null>(null);

  const [feedItems, setFeedItems] = useState<FeedItem[]>([]);
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const menuRef = useRef<HTMLDivElement>(null);
  const [commentTexts, setCommentTexts] = useState<Record<number, string>>({});

  // TikTok Context'ten verileri al
  const { tiktokUser } = useTikTok();

  // Dosya seçme işlevi
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    if (file) {
      // Sadece resim dosyalarını kabul et
      if (!/image\/(png|jpg|jpeg)/.test(file.type)) {
        alert('Lütfen yalnızca PNG, JPG veya JPEG formatında dosya yükleyin.');
        return;
      }

      setSelectedFile(file);

      // Dosya ön izlemesi için URL oluştur
      const fileReader = new FileReader();
      fileReader.onload = () => {
        setPreviewUrl(fileReader.result as string);
      };
      fileReader.readAsDataURL(file);
    }
  };

  // Dosya yükleme butonuna tıklama
  const handleMediaButtonClick = () => {
    fileInputRef.current?.click();
  };

  // Eklenen dosyayı kaldırma
  const handleRemoveFile = () => {
    setSelectedFile(null);
    setPreviewUrl(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Emoji ekleme
  const handleEmojiSelect = (emojiData: { emoji: string }) => {
    if (editingPostId) {
      setEditingText(prevText => prevText + emojiData.emoji);
    } else {
      setPostText(prevText => prevText + emojiData.emoji);
    }
  };

  // Akış verilerini API'den çek
  const fetchFeedItems = useCallback(async () => {
    setLoading(true);
    try {

      // Mock veri kullanımı kaldırıldı - sadece API'den veri al



      // Production modunda gerçek API çağrısı
      const response = await axios.get(`${API_BASE_URL}/api_data.php`, {
        params: {
          endpoint: 'feed'
        },
        timeout: 10000 // 10 saniye timeout
      });

      if (response.data && response.data.status === 'success') {
        if (Array.isArray(response.data.data)) {
          // API'den gelen verileri formatla
          const formattedFeedItems = response.data.data.map((item: {
            id: number;
            username?: string;
            profile_image?: string;
            role?: string;
            content: string;
            created_at: string;
            likes_count?: number | string;
            comments_count?: number | string;
            is_liked?: boolean;
            media_url?: string;
            comments_data?: Array<unknown>;
          }) => ({
            id: item.id,
            author: {
              name: item.username || 'Kullanıcı',
              avatar: item.profile_image || null,
              role: item.role || 'Ajans Grubu',
            },
            content: item.content,
            media: item.media_url,
            createdAt: new Date(item.created_at),
            likes: parseInt(String(item.likes_count || 0)) || 0,
            comments: parseInt(String(item.comments_count || 0)) || 0,
            shares: 0,
            isLiked: Boolean(item.is_liked),
            comments_data: item.comments_data || []
          }));

          setFeedItems(formattedFeedItems);
        } else {
          setFeedItems([]);
        }
      } else {
        console.error('API başarısız yanıt döndü:', response.data);
        setFeedItems([]);
      }
    } catch (err: unknown) {
      console.error('Feed verileri yüklenirken hata:', err);
      setFeedItems([]);
    } finally {
      setLoading(false);
    }
  }, [isDevMode, tiktokUser, userInfo]); // useCallback dependency array

  // Kullanıcı bilgilerini getir
  const fetchUserInfo = useCallback(async () => {
    try {
      // Gerçek API çağrısı
      const response = await axios.get(`${API_BASE_URL}/user_session.php`);
      if (response.data.status === 'success') {
        setUserInfo(response.data.data);
      } else {
        console.error('Kullanıcı bilgileri alınamadı:', response.data.message);
      }
    } catch (err) {
      console.error('Kullanıcı bilgileri yüklenirken hata:', err);
    }
  }, []); // useCallback dependency array

  // Sayfa yüklendiğinde akış verilerini ve kullanıcı bilgilerini çek
  useEffect(() => {
    // API test etmek için
    console.log('Feed componenti yüklendi. API test ediliyor...');
    console.log('Base URL:', window.location.origin);
    console.log('API_BASE_URL:', API_BASE_URL);
    console.log('Full API URL:', `${API_BASE_URL}/api_data.php`);

    fetchFeedItems();
    fetchUserInfo();
  }, []); // Sadece component mount olduğunda çalışsın

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setOpenMenuId(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Like işlevi
  const handleLike = async (id: number) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api_data.php`, {
        endpoint: 'like_post',
        post_id: id,
        user_id: user?.id || 1
      });

      if (response.data.status === 'success') {
        // UI'ı güncelle
        setFeedItems(prevItems =>
          prevItems.map(item =>
            item.id === id
              ? { ...item, isLiked: !item.isLiked, likes: item.isLiked ? item.likes - 1 : item.likes + 1 }
              : item
          )
        );
      } else {
        console.error('Beğeni işlemi başarısız:', response.data.message);
        alert('Beğeni işlemi başarısız: ' + response.data.message);
      }
    } catch (err: unknown) {
      console.error('Beğeni işlemi sırasında hata oluştu:', err);
      if (err instanceof Error) {
        alert('Beğeni işlemi sırasında hata oluştu: ' + err.message);
      } else {
        alert('Beğeni işlemi sırasında bilinmeyen bir hata oluştu');
      }
    }
  };

  // Menü açma/kapama
  const toggleMenu = (id: number) => {
    setOpenMenuId(openMenuId === id ? null : id);
  };

  // Yorumları açma/kapama

  // Düzenleme başlatma
  const handleEdit = (item: FeedItem) => {
    // Paylaşım 30 dakikadan eski mi kontrolü
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
    if (item.createdAt < thirtyMinutesAgo) {
      alert('Bu paylaşım yalnızca ilk 30 dakika içinde düzenlenebilir.');
      return;
    }

    setEditingPostId(item.id);
    setEditingText(item.content);
    setEditingPreviewUrl(item.media || null);
    setOpenMenuId(null);
  };

  // Düzenlemeyi kaydetme
  const handleSaveEdit = () => {
    if (!editingText.trim()) {
      alert('Paylaşım içeriği boş olamaz.');
      return;
    }

    setFeedItems(prevItems =>
      prevItems.map(item =>
        item.id === editingPostId
          ? { ...item, content: editingText, media: editingPreviewUrl }
          : item
      )
    );

    // Düzenleme modunu kapat
    setEditingPostId(null);
    setEditingText('');
    setEditingPreviewUrl(null);
  };

  // Düzenlemeyi iptal etme
  const handleCancelEdit = () => {
    setEditingPostId(null);
    setEditingText('');
    setEditingPreviewUrl(null);
  };

  // Paylaşım silme
  const handleDelete = async (id: number) => {
    if (window.confirm('Bu paylaşımı silmek istediğinize emin misiniz?')) {
      try {
        // Backend'e silme isteği gönder
        const response = await axios.post(`${API_BASE_URL}/api_data.php`, {
          endpoint: 'delete_post',
          post_id: id,
          user_id: user?.id || 1
        });

        if (response.data.status === 'success') {
          // UI'dan kaldır
          setFeedItems(prevItems => prevItems.filter(item => item.id !== id));
        } else {
          alert('Gönderi silinirken bir hata oluştu: ' + response.data.message);
        }
      } catch (err) {
        console.error('Gönderi silinirken hata oluştu:', err);
        alert('Gönderi silinirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.');
      }
    }
    setOpenMenuId(null);
  };

  // Gönderi paylaşma
  const handleShare = async () => {
    if (!postText.trim() && !selectedFile) {
      alert('Lütfen bir metin yazın veya bir medya dosyası ekleyin.');
      return;
    }

    try {
      // Form verisi oluştur
      const formData = new FormData();
      formData.append('endpoint', 'create_post');
      formData.append('user_id', user?.id?.toString() || '1');
      formData.append('content', postText);

      if (selectedFile) {
        formData.append('media', selectedFile);
      }

      const response = await axios.post(`${API_BASE_URL}/api_data.php`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      if (response.data.status === 'success') {
        // Debug: Avatar URL'ini kontrol et
        const avatarUrl = tiktokUser?.avatar_url || userInfo?.profile_image || userInfo?.avatar_url;
        console.log('Yeni gönderi avatar debug:', {
          tiktokUser_avatar: tiktokUser?.avatar_url,
          userInfo_profile_image: userInfo?.profile_image,
          userInfo_avatar_url: userInfo?.avatar_url,
          final_avatar: avatarUrl
        });
        
        // Yeni gönderiyi ekle
        const newPost = {
          id: response.data.post_id || Math.max(0, ...feedItems.map(item => item.id)) + 1,
          author: {
            name: tiktokUser?.display_name || userInfo?.username || 'tuberajans',
            avatar: tiktokUser?.avatar_url || userInfo?.profile_image || userInfo?.avatar_url || null,
            role: userInfo?.role || 'Ajans Grubu',
          },
          content: postText,
          media: previewUrl,
          createdAt: new Date(),
          likes: 0,
          comments: 0,
          shares: 0,
          isLiked: false,
        };

        setFeedItems([newPost, ...feedItems]);

        // Paylaşım sonrası sıfırlama
        setPostText('');
        setSelectedFile(null);
        setPreviewUrl(null);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      } else {
        alert('Gönderi paylaşılırken bir hata oluştu: ' + response.data.message);
      }
    } catch (err) {
      console.error('Gönderi paylaşılırken hata oluştu:', err);
      if (axios.isAxiosError(err)) {
        console.error('API Hatası:', err.response?.status, err.response?.data);
        console.error('İstek URL:', err.config?.url);
        console.error('İstek Method:', err.config?.method);
        console.error('Base URL:', window.location.origin);
        
        if (err.response?.status === 404) {
          alert('API endpoint bulunamadı (404). Lütfen backend ayarlarını kontrol edin.\nURL: ' + err.config?.url);
        } else {
          alert(`Gönderi paylaşılamadı. Hata: ${err.response?.status || 'Bilinmeyen hata'}\nDetay: ${err.response?.data?.message || err.message}`);
        }
      } else {
        alert('Gönderi paylaşılırken bir hata oluştu. Lütfen daha sonra tekrar deneyin.');
      }
    }
  };

  // Yorum gönderme fonksiyonu
  const handleSubmitComment = async (postId: number) => {
    const commentText = commentTexts[postId];
    if (!commentText?.trim()) {
      return;
    }

    try {
      const response = await axios.post(`${API_BASE_URL}/api_data.php`, {
        endpoint: 'add_comment',
        post_id: postId,
        user_id: user?.id || 1,
        content: commentText.trim()
      });

      if (response.data.status === 'success') {
        // Yorum input'unu temizle
        setCommentTexts(prev => ({
          ...prev,
          [postId]: ''
        }));

        // Feed'i yenile ki yeni yorum görünsün
        await fetchFeedItems();

        console.log('Yorum başarıyla gönderildi');
      } else {
        alert('Yorum gönderilemedi: ' + response.data.message);
      }
    } catch (err) {
      console.error('Yorum gönderme hatası:', err);
      alert('Yorum gönderilirken bir hata oluştu.');
    }
  };

  // Paylaşımın düzenlenebilir olup olmadığını kontrol etme
  const isEditable = (date: Date) => {
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
    return date > thirtyMinutesAgo;
  };

  return (
    <div className="container" style={{
      maxWidth: isMobile ? '100%' : 'none',
      width: isMobile ? '100%' : 'auto',
      overflowX: 'hidden',
      position: 'relative',
      left: 0,
      marginLeft: 0,
      paddingTop: isMobile ? '0.5rem' : '0.75rem',
      paddingLeft: isMobile ? '0.25rem' : '0rem',
      paddingRight: isMobile ? '0.25rem' : '1rem',
      boxSizing: 'border-box'
    }}>
        <div className="flex gap-0 xl:gap-6 w-full">
          {/* Sol Sütun - Ana Akış */}
          <div className="flex-1 w-full">
            {/* Gönderi Oluştur */}
            <div className="bg-white dark:bg-[#16151c] rounded-lg shadow-sm overflow-hidden mb-6 p-4 sm:p-6">
              <div className="flex items-center mb-3 sm:mb-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-700 dark:text-gray-300 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                <h2 className="text-base font-semibold text-gray-800 dark:text-white">Yeni Paylaşım</h2>
              </div>
              <div className="p-3">
                <div className="flex space-x-1.5 sm:space-x-2">
                  <div className="flex-shrink-0">
                    {(tiktokUser?.avatar_url || userInfo?.profile_image || userInfo?.avatar_url) ? (
                      <img
                        src={tiktokUser?.avatar_url || userInfo?.profile_image || userInfo?.avatar_url}
                        alt={tiktokUser?.display_name || userInfo?.username || 'Kullanıcı'}
                        className="w-7 h-7 sm:w-8 sm:h-8 rounded-full object-cover border border-gray-200 dark:border-gray-600"
                      />
                    ) : (
                      <div className="w-7 h-7 sm:w-8 sm:h-8 rounded-full bg-[#FF3E71]/10 dark:bg-[#FF3E71]/20 flex items-center justify-center text-[#FF3E71]">
                        <span className="font-medium text-xs sm:text-sm">{(tiktokUser?.display_name || userInfo?.username || 'T').charAt(0).toUpperCase()}</span>
                      </div>
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="relative">
                      <textarea
                        className="w-full p-2 pr-8 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 rounded-lg focus:ring-[#FF3E71] focus:border-[#FF3E71] text-xs sm:text-sm text-gray-700 dark:text-gray-200 placeholder-gray-400 dark:placeholder-gray-500 resize-none"
                        placeholder="Topluluğa bir şeyler paylaş..."
                        rows={2}
                        value={postText}
                        onChange={(e) => setPostText(e.target.value)}
                      ></textarea>
                      <div className="absolute top-3 right-3">
                        <button
                          className="text-gray-400 hover:text-[#FF3E71] transition-colors p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
                          onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                          title="Emoji ekle"
                        >
                          <FaSmile className="h-5 w-5" />
                        </button>

                        {showEmojiPicker && (
                          <div className="absolute bottom-10 right-0 z-50 shadow-xl border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden bg-white dark:bg-gray-800 p-2 w-[280px] max-h-48 overflow-y-auto">
                            <button
                              className="fixed inset-0 border-none bg-transparent cursor-pointer z-40"
                              onClick={() => setShowEmojiPicker(false)}
                              onKeyDown={(e) => {
                                if (e.key === 'Escape') {
                                  setShowEmojiPicker(false);
                                }
                              }}
                              aria-label="Emoji picker'ı kapat"
                            ></button>
                            <div className="relative z-50">
                              <div className="grid grid-cols-8 gap-1">
                                {[
                                  "😀", "😃", "😄", "😁", "😆", "😅", "🤣", "😂",
                                  "🙂", "🙃", "😉", "😊", "😇", "😍", "🥰", "😘",
                                  "😗", "☺️", "😚", "😙", "😋", "😛", "😜", "🤪",
                                  "😝", "🤑", "🤗", "🤭", "🤫", "🤔", "🤐", "🤨",
                                  "😐", "😑", "😶", "😏", "😒", "🙄", "😬", "🤥",
                                  "😌", "😔", "😪", "🤤", "😴", "😷", "🤒", "🤕",
                                  "🤢", "🤮", "🤧", "🥵", "🥶", "🥴", "😵", "🤯",
                                  "🤠", "🥳", "😎", "🤓", "🧐", "😕", "😟", "🙁",
                                  "☹️", "😮", "😯", "😲", "😳", "🥺", "😦", "😧",
                                  "😨", "😰", "😥", "😢", "😭", "😱", "😖", "😣",
                                  "😞", "😓", "😩", "😫", "😤", "😡", "😠", "🤬",
                                  "👍", "👎", "👏", "🙌", "👌", "🤝", "❤️", "👋"
                                ].map((emoji) => (
                                  <button
                                    key={emoji}
                                    className="text-lg hover:bg-gray-100 dark:hover:bg-gray-700 p-1 rounded"
                                    onClick={() => {
                                      handleEmojiSelect({ emoji });
                                      setShowEmojiPicker(false);
                                    }}
                                  >
                                    {emoji}
                                  </button>
                                ))}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Medya ön izleme */}
                    {previewUrl && (
                      <div className="mt-3 relative">
                        <div className="rounded-lg overflow-hidden relative bg-gray-100 dark:bg-gray-800">
                          <img
                            src={previewUrl}
                            alt="Seçilen görsel"
                            className="w-full max-h-64 object-contain"
                            style={{ aspectRatio: 'auto' }}
                          />
                          <button
                            className="absolute top-2 right-2 bg-gray-800 bg-opacity-50 text-white rounded-full p-1 hover:bg-opacity-70 transition-all"
                            onClick={handleRemoveFile}
                          >
                            <FaTimes size={16} />
                          </button>
                        </div>
                        <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                          {selectedFile?.name} ({(selectedFile?.size ? selectedFile.size / 1024 : 0).toFixed(1)} KB)
                        </div>
                      </div>
                    )}

                    {/* Görünmeyen dosya girişi */}
                    <input
                      type="file"
                      ref={fileInputRef}
                      className="hidden"
                      accept="image/png, image/jpeg, image/jpg"
                      onChange={handleFileSelect}
                    />

                    <div className="mt-3 sm:mt-4 flex justify-between items-center">
                      <div className="flex space-x-2">
                        <button
                          className="inline-flex items-center px-4 py-2 border border-gray-200 dark:border-gray-600 rounded-full text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors touch-manipulation"
                          onClick={handleMediaButtonClick}
                        >
                          <svg className="h-4 w-4 mr-2 text-[#FF3E71]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          <span>Medya</span>
                        </button>
                      </div>
                      <button
                        className="inline-flex items-center px-6 py-2 border border-transparent rounded-full shadow-sm text-sm font-medium text-white bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] hover:from-[#FF5F87] hover:to-[#FF3E71] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF3E71] transition-all duration-300 touch-manipulation"
                        onClick={handleShare}
                      >
                        Paylaş
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* Akış Listesi */}
            <div className="space-y-1 xl:space-y-4">
              <AnimatePresence>
                {feedItems.map((item, index) => (
                  <motion.div
                    key={item.id}
                    className="bg-white dark:bg-[#16151c] rounded-none xl:rounded-lg shadow-sm overflow-hidden p-3"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    {/* Düzenleme Modu */}
                    {editingPostId === item.id ? (
                      <div className="p-4">
                        <div className="flex items-center mb-4">
                          {(item.author.avatar || tiktokUser?.avatar_url || userInfo?.profile_image || userInfo?.avatar_url) ? (
                            <img
                              src={item.author.avatar || tiktokUser?.avatar_url || userInfo?.profile_image || userInfo?.avatar_url}
                              alt={item.author.name || item.author.username}
                              className="w-10 h-10 rounded-full object-cover"
                            />
                          ) : (
                            <div className="w-10 h-10 rounded-full bg-[#FF3E71]/10 dark:bg-[#FF3E71]/20 flex items-center justify-center text-[#FF3E71]">
                              <span className="font-medium text-sm">{(item.author.name || item.author.username || 'U').charAt(0).toUpperCase()}</span>
                            </div>
                          )}
                          <div className="ml-3">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">{item.author.name || item.author.username}</div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">{item.author.role}</div>
                          </div>
                        </div>

                        <textarea
                          className="w-full p-3 border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 rounded-lg focus:ring-tuber-pink focus:border-tuber-pink text-sm text-gray-700 dark:text-gray-200 placeholder-gray-400 dark:placeholder-gray-500"
                          rows={3}
                          value={editingText}
                          onChange={(e) => setEditingText(e.target.value)}
                        ></textarea>

                        {editingPreviewUrl && (
                          <div className="mt-3 relative">
                            <div className="rounded-lg overflow-hidden relative">
                              <img
                                src={editingPreviewUrl}
                                alt="Paylaşım görseli"
                                className="max-h-48 w-auto mx-auto object-contain"
                              />
                            </div>
                          </div>
                        )}

                        <div className="mt-3 flex justify-end gap-2">
                          <button
                            className="inline-flex items-center px-3 py-1.5 border border-gray-200 dark:border-gray-600 rounded-full text-xs font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
                            onClick={handleCancelEdit}
                          >
                            İptal
                          </button>
                          <button
                            className="inline-flex items-center px-3 py-1.5 border border-transparent rounded-full shadow-sm text-xs font-medium text-white bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] hover:from-[#FF5F87] hover:to-[#FF3E71] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF3E71] transition-all duration-300"
                            onClick={handleSaveEdit}
                          >
                            Kaydet
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div className="p-1 sm:p-3 md:p-4">
                        {/* Gönderi Başlığı */}
                        <div className="flex justify-between">
                          <div className="flex items-center">
                            {(item.author.avatar || tiktokUser?.avatar_url || userInfo?.profile_image || userInfo?.avatar_url) ? (
                              <img
                                src={item.author.avatar || tiktokUser?.avatar_url || userInfo?.profile_image || userInfo?.avatar_url}
                                alt={item.author.name || item.author.username}
                                className="w-7 h-7 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full object-cover"
                              />
                            ) : (
                              <div className="w-7 h-7 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full bg-[#FF3E71]/10 dark:bg-[#FF3E71]/20 flex items-center justify-center text-[#FF3E71]">
                                <span className="font-medium text-xs sm:text-sm">{(item.author.name || item.author.username || 'U').charAt(0).toUpperCase()}</span>
                              </div>
                            )}
                            <div className="ml-1.5 sm:ml-2 md:ml-3">
                              <div className="text-xs sm:text-sm font-medium text-gray-900 dark:text-white">{item.author.name || item.author.username}</div>
                              <div className="text-[10px] sm:text-xs text-gray-500 dark:text-gray-400">
                                {formatPostTime(item.createdAt)}
                              </div>
                            </div>
                          </div>
                          <div className="relative" ref={menuRef}>
                            <button
                              className="text-gray-400 dark:text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 p-1"
                              onClick={() => toggleMenu(item.id)}
                              aria-label="Paylaşım seçenekleri"
                            >
                              <FaEllipsisH />
                            </button>

                            {/* Açılır menü */}
                            {openMenuId === item.id && (
                              <div className="absolute right-0 top-8 w-36 bg-white dark:bg-[#1e1d26] rounded-lg shadow-lg z-10 py-1 border border-gray-100 dark:border-gray-700">
                                {isEditable(item.createdAt) ? (
                                  <button
                                    className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center"
                                    onClick={() => handleEdit(item)}
                                  >
                                    <FaPencilAlt className="mr-2 text-[#FF3E71]" />
                                    Düzenle
                                  </button>
                                ) : (
                                  <button
                                    className="w-full text-left px-4 py-2 text-sm text-gray-400 dark:text-gray-500 flex items-center cursor-not-allowed"
                                    title="Paylaşımlar yalnızca ilk 30 dakika içinde düzenlenebilir."
                                  >
                                    <FaPencilAlt className="mr-2" />
                                    Düzenle
                                  </button>
                                )}
                                <button
                                  className="w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center"
                                  onClick={() => handleDelete(item.id)}
                                >
                                  <FaTrash className="mr-2" />
                                  Sil
                                </button>
                              </div>
                            )}
                          </div>
                        </div>
                        {/* Gönderi İçeriği */}
                        <div className="mt-2 sm:mt-3">
                          <p className="text-xs sm:text-sm md:text-base text-gray-800 dark:text-gray-200 line-clamp-3">{item.content}</p>
                          {item.media && (
                            <div className="mt-2 sm:mt-3 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800">
                              <img
                                src={item.media}
                                alt="Post media"
                                className="w-full max-h-64 sm:max-h-72 md:max-h-80 object-contain"
                                style={{ aspectRatio: 'auto' }}
                              />
                            </div>
                          )}
                        </div>
                        {/* Etkileşim Butonları */}
                        <div className="mt-3 sm:mt-4 flex items-center justify-between border-t border-gray-100 dark:border-gray-700 pt-2 sm:pt-3">
                          {/* Beğeni ve Yorum butonları yan yana */}
                          <div className="flex items-center space-x-1 sm:space-x-2">
                            <button
                              className={`flex items-center text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-1.5 rounded-full transition-colors touch-manipulation ${
                                item.isLiked
                                  ? 'text-white bg-[#FF3E71] dark:bg-[#FF3E71]'
                                  : 'text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600'
                              }`}
                              onClick={() => handleLike(item.id)}
                            >
                              <FaHeart className="mr-1 sm:mr-1.5 text-xs" />
                              <span>{item.likes}</span>
                            </button>
                            <button
                              className="flex items-center text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-1.5 rounded-full text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors touch-manipulation"
                              onClick={() => document.getElementById(`comment-input-${item.id}`)?.focus()}
                            >
                              <FaComment className="mr-1 sm:mr-1.5 text-xs" />
                              <span>{item.comments}</span>
                            </button>
                          </div>
                        </div>

                        {/* Yorumlar Bölümü - Her zaman görünür */}
                        <div className="mt-2 sm:mt-3 border-t border-gray-100 dark:border-gray-700 pt-2 sm:pt-3 px-0 sm:px-2 md:px-3">
                          {/* Gerçek yorumlar - API'den gelen veriler */}
                          <div className="space-y-2 sm:space-y-3">
                            {item.comments_data && item.comments_data.length > 0 ? (
                              item.comments_data.map((comment) => (
                                <div key={comment.id} className="flex space-x-1.5 sm:space-x-2">
                                  {(comment.profile_image || tiktokUser?.avatar_url || userInfo?.profile_image || userInfo?.avatar_url) ? (
                                    <img 
                                      src={comment.profile_image || tiktokUser?.avatar_url || userInfo?.profile_image || userInfo?.avatar_url} 
                                      alt={comment.username} 
                                      className="w-6 h-6 sm:w-7 sm:h-7 rounded-full flex-shrink-0" 
                                    />
                                  ) : (
                                    <div className="w-6 h-6 sm:w-7 sm:h-7 rounded-full bg-[#FF3E71]/10 dark:bg-[#FF3E71]/20 flex items-center justify-center text-[#FF3E71] flex-shrink-0">
                                      <span className="font-medium text-[10px] sm:text-xs">{comment.username?.charAt(0).toUpperCase() || 'U'}</span>
                                    </div>
                                  )}
                                  <div className="flex-1 bg-gray-50 dark:bg-gray-800/50 rounded-lg p-1.5 sm:p-2">
                                    <div className="flex justify-between items-start">
                                      <div className="text-[10px] sm:text-xs font-medium text-gray-900 dark:text-white">{comment.username}</div>
                                      <div className="text-[9px] sm:text-[10px] text-gray-500">{formatPostTime(new Date(comment.created_at))}</div>
                                    </div>
                                    <p className="text-[10px] sm:text-xs text-gray-700 dark:text-gray-300 mt-0.5 sm:mt-1">{comment.content}</p>
                                  </div>
                                </div>
                              ))
                            ) : (
                              <div className="text-xs text-gray-500 dark:text-gray-400 text-center py-2">Henüz yorum yapılmamış.</div>
                            )}
                          </div>

                          {/* Yorum yapma alanı - Her zaman görünür */}
                          <div className="mt-2 flex space-x-1.5 px-0">
                            {(tiktokUser?.avatar_url || userInfo?.profile_image || userInfo?.avatar_url) ? (
                              <img
                                src={tiktokUser?.avatar_url || userInfo?.profile_image || userInfo?.avatar_url}
                                alt={tiktokUser?.display_name || userInfo?.username || 'Kullanıcı'}
                                className="w-6 h-6 rounded-full object-cover border border-gray-200 dark:border-gray-600"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  // Fallback avatar
                                  target.src = 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face&auto=format&q=80';
                                }}
                              />
                            ) : (
                              <div className="w-6 h-6 rounded-full bg-[#FF3E71]/10 dark:bg-[#FF3E71]/20 flex items-center justify-center text-[#FF3E71]">
                                <span className="font-medium text-[10px]">{(tiktokUser?.display_name || userInfo?.username || 'T').charAt(0).toUpperCase()}</span>
                              </div>
                            )}
                            <div className="flex-1 relative">
                              <input
                                id={`comment-input-${item.id}`}
                                type="text"
                                className="w-full p-1.5 text-[10px] sm:text-xs border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 rounded-full focus:ring-[#FF3E71] focus:border-[#FF3E71] text-gray-700 dark:text-gray-200 placeholder-gray-400 dark:placeholder-gray-500"
                                placeholder="Yorum yaz..."
                                value={commentTexts[item.id] || ''}
                                onChange={(e) => setCommentTexts(prev => ({
                                  ...prev,
                                  [item.id]: e.target.value
                                }))}
                                onKeyDown={(e) => {
                                  if (e.key === 'Enter') {
                                    handleSubmitComment(item.id);
                                  }
                                }}
                              />
                              <button 
                                className="absolute right-1.5 top-1/2 -translate-y-1/2 text-[#FF3E71] hover:text-[#FF5F87] transition-colors p-1 hover:bg-[#FF3E71]/10 rounded-full"
                                onClick={() => handleSubmitComment(item.id)}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="currentColor">
                                  <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                                </svg>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </motion.div>
                ))}
              </AnimatePresence>

              {feedItems.length === 0 && !loading && (
                <div className="text-center py-10 bg-white dark:bg-[#16151c] rounded-lg shadow-sm">
                  <div className="w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-300 rounded-full flex items-center justify-center mb-4">
                    <FaStream size={24} />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">Hiç İçerik Bulunamadı</h3>
                  <p className="mt-1 text-gray-500 dark:text-gray-400">Henüz bu akışta içerik bulunmamaktadır.</p>
                  <button
                    className="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-full shadow-sm text-sm font-medium text-white bg-gradient-to-r from-[#FF3E71] to-[#FF5F87] hover:from-[#FF5F87] hover:to-[#FF3E71] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF3E71] transition-all duration-300"
                  >
                    İlk Paylaşımı Yap
                  </button>
                </div>
              )}

              {loading && (
                <div className="text-center py-10 bg-white dark:bg-[#16151c] rounded-lg shadow-sm">
                  <div className="w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-300 rounded-full flex items-center justify-center mb-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#FF3E71]"></div>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">Akış Yükleniyor...</h3>
                  <p className="mt-1 text-gray-500 dark:text-gray-400">İçerikler getiriliyor, lütfen bekleyin.</p>
                </div>
              )}
            </div>
          </div>
          {/* Sağ Sütun - Yan Konteynerler - Sadece Desktop'ta Görünür */}
          <div className="hidden xl:block w-80 2xl:w-96 flex-shrink-0 space-y-6 pr-4 2xl:pr-6">
            {/* Faydalı Bağlantılar */}
            {/* ...Faydalı Bağlantılar içeriği... */}
          </div>
        </div>
    </div>
  );
};

export default Feed;