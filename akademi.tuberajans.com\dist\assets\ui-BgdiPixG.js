import{r as v}from"./vendor-BKU87Gzz.js";var Ms={exports:{}},Ht={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ar=v,lr=Symbol.for("react.element"),ur=Symbol.for("react.fragment"),cr=Object.prototype.hasOwnProperty,hr=ar.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,fr={key:!0,ref:!0,__self:!0,__source:!0};function Rs(t,e,n){var s,i={},o=null,r=null;n!==void 0&&(o=""+n),e.key!==void 0&&(o=""+e.key),e.ref!==void 0&&(r=e.ref);for(s in e)cr.call(e,s)&&!fr.hasOwnProperty(s)&&(i[s]=e[s]);if(t&&t.defaultProps)for(s in e=t.defaultProps,e)i[s]===void 0&&(i[s]=e[s]);return{$$typeof:lr,type:t,key:o,ref:r,props:i,_owner:hr.current}}Ht.Fragment=ur;Ht.jsx=Rs;Ht.jsxs=Rs;Ms.exports=Ht;var G=Ms.exports;const Re=v.createContext({});function Ee(t){const e=v.useRef(null);return e.current===null&&(e.current=t()),e.current}const Xt=v.createContext(null),Le=v.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});class dr extends v.Component{getSnapshotBeforeUpdate(e){const n=this.props.childRef.current;if(n&&e.isPresent&&!this.props.isPresent){const s=this.props.sizeRef.current;s.height=n.offsetHeight||0,s.width=n.offsetWidth||0,s.top=n.offsetTop,s.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function pr({children:t,isPresent:e}){const n=v.useId(),s=v.useRef(null),i=v.useRef({width:0,height:0,top:0,left:0}),{nonce:o}=v.useContext(Le);return v.useInsertionEffect(()=>{const{width:r,height:a,top:l,left:u}=i.current;if(e||!s.current||!r||!a)return;s.current.dataset.motionPopId=n;const c=document.createElement("style");return o&&(c.nonce=o),document.head.appendChild(c),c.sheet&&c.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${r}px !important;
            height: ${a}px !important;
            top: ${l}px !important;
            left: ${u}px !important;
          }
        `),()=>{document.head.removeChild(c)}},[e]),G.jsx(dr,{isPresent:e,childRef:s,sizeRef:i,children:v.cloneElement(t,{ref:s})})}const mr=({children:t,initial:e,isPresent:n,onExitComplete:s,custom:i,presenceAffectsLayout:o,mode:r})=>{const a=Ee(gr),l=v.useId(),u=v.useCallback(h=>{a.set(h,!0);for(const f of a.values())if(!f)return;s&&s()},[a,s]),c=v.useMemo(()=>({id:l,initial:e,isPresent:n,custom:i,onExitComplete:u,register:h=>(a.set(h,!1),()=>a.delete(h))}),o?[Math.random(),u]:[n,u]);return v.useMemo(()=>{a.forEach((h,f)=>a.set(f,!1))},[n]),v.useEffect(()=>{!n&&!a.size&&s&&s()},[n]),r==="popLayout"&&(t=G.jsx(pr,{isPresent:n,children:t})),G.jsx(Xt.Provider,{value:c,children:t})};function gr(){return new Map}function Es(t=!0){const e=v.useContext(Xt);if(e===null)return[!0,null];const{isPresent:n,onExitComplete:s,register:i}=e,o=v.useId();v.useEffect(()=>{t&&i(o)},[t]);const r=v.useCallback(()=>t&&s&&s(o),[o,s,t]);return!n&&s?[!1,r]:[!0]}const Ft=t=>t.key||"";function dn(t){const e=[];return v.Children.forEach(t,n=>{v.isValidElement(n)&&e.push(n)}),e}const Fe=typeof window<"u",Ls=Fe?v.useLayoutEffect:v.useEffect,Wu=({children:t,custom:e,initial:n=!0,onExitComplete:s,presenceAffectsLayout:i=!0,mode:o="sync",propagate:r=!1})=>{const[a,l]=Es(r),u=v.useMemo(()=>dn(t),[t]),c=r&&!a?[]:u.map(Ft),h=v.useRef(!0),f=v.useRef(u),d=Ee(()=>new Map),[p,m]=v.useState(u),[y,g]=v.useState(u);Ls(()=>{h.current=!1,f.current=u;for(let b=0;b<y.length;b++){const x=Ft(y[b]);c.includes(x)?d.delete(x):d.get(x)!==!0&&d.set(x,!1)}},[y,c.length,c.join("-")]);const T=[];if(u!==p){let b=[...u];for(let x=0;x<y.length;x++){const w=y[x],M=Ft(w);c.includes(M)||(b.splice(x,0,w),T.push(w))}o==="wait"&&T.length&&(b=T),g(dn(b)),m(u);return}const{forceRender:P}=v.useContext(Re);return G.jsx(G.Fragment,{children:y.map(b=>{const x=Ft(b),w=r&&!a?!1:u===y||c.includes(x),M=()=>{if(d.has(x))d.set(x,!0);else return;let A=!0;d.forEach(L=>{L||(A=!1)}),A&&(P==null||P(),g(f.current),r&&(l==null||l()),s&&s())};return G.jsx(mr,{isPresent:w,initial:!h.current||n?void 0:!1,custom:w?void 0:e,presenceAffectsLayout:i,mode:o,onExitComplete:w?void 0:M,children:b},x)})})},j=t=>t;let Fs=j;function Be(t){let e;return()=>(e===void 0&&(e=t()),e)}const ht=(t,e,n)=>{const s=e-t;return s===0?1:(n-t)/s},z=t=>t*1e3,H=t=>t/1e3,yr={useManualTiming:!1};function vr(t){let e=new Set,n=new Set,s=!1,i=!1;const o=new WeakSet;let r={delta:0,timestamp:0,isProcessing:!1};function a(u){o.has(u)&&(l.schedule(u),t()),u(r)}const l={schedule:(u,c=!1,h=!1)=>{const d=h&&s?e:n;return c&&o.add(u),d.has(u)||d.add(u),u},cancel:u=>{n.delete(u),o.delete(u)},process:u=>{if(r=u,s){i=!0;return}s=!0,[e,n]=[n,e],e.forEach(a),e.clear(),s=!1,i&&(i=!1,l.process(u))}};return l}const Bt=["read","resolveKeyframes","update","preRender","render","postRender"],xr=40;function Bs(t,e){let n=!1,s=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,r=Bt.reduce((g,T)=>(g[T]=vr(o),g),{}),{read:a,resolveKeyframes:l,update:u,preRender:c,render:h,postRender:f}=r,d=()=>{const g=performance.now();n=!1,i.delta=s?1e3/60:Math.max(Math.min(g-i.timestamp,xr),1),i.timestamp=g,i.isProcessing=!0,a.process(i),l.process(i),u.process(i),c.process(i),h.process(i),f.process(i),i.isProcessing=!1,n&&e&&(s=!1,t(d))},p=()=>{n=!0,s=!0,i.isProcessing||t(d)};return{schedule:Bt.reduce((g,T)=>{const P=r[T];return g[T]=(b,x=!1,w=!1)=>(n||p(),P.schedule(b,x,w)),g},{}),cancel:g=>{for(let T=0;T<Bt.length;T++)r[Bt[T]].cancel(g)},state:i,steps:r}}const{schedule:V,cancel:q,state:E,steps:ne}=Bs(typeof requestAnimationFrame<"u"?requestAnimationFrame:j,!0),ks=v.createContext({strict:!1}),pn={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ft={};for(const t in pn)ft[t]={isEnabled:e=>pn[t].some(n=>!!e[n])};function Tr(t){for(const e in t)ft[e]={...ft[e],...t[e]}}const Pr=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Ut(t){return t.startsWith("while")||t.startsWith("drag")&&t!=="draggable"||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||Pr.has(t)}let js=t=>!Ut(t);function Sr(t){t&&(js=e=>e.startsWith("on")?!Ut(e):t(e))}try{Sr(require("@emotion/is-prop-valid").default)}catch{}function Ar(t,e,n){const s={};for(const i in t)i==="values"&&typeof t.values=="object"||(js(i)||n===!0&&Ut(i)||!e&&!Ut(i)||t.draggable&&i.startsWith("onDrag"))&&(s[i]=t[i]);return s}function br(t){if(typeof Proxy>"u")return t;const e=new Map,n=(...s)=>t(...s);return new Proxy(n,{get:(s,i)=>i==="create"?t:(e.has(i)||e.set(i,t(i)),e.get(i))})}const Yt=v.createContext({});function bt(t){return typeof t=="string"||Array.isArray(t)}function qt(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}const ke=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],je=["initial",...ke];function Zt(t){return qt(t.animate)||je.some(e=>bt(t[e]))}function Os(t){return!!(Zt(t)||t.variants)}function wr(t,e){if(Zt(t)){const{initial:n,animate:s}=t;return{initial:n===!1||bt(n)?n:void 0,animate:bt(s)?s:void 0}}return t.inherit!==!1?e:{}}function Vr(t){const{initial:e,animate:n}=wr(t,v.useContext(Yt));return v.useMemo(()=>({initial:e,animate:n}),[mn(e),mn(n)])}function mn(t){return Array.isArray(t)?t.join(" "):t}const Cr=Symbol.for("motionComponentSymbol");function ot(t){return t&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function Dr(t,e,n){return v.useCallback(s=>{s&&t.onMount&&t.onMount(s),e&&(s?e.mount(s):e.unmount()),n&&(typeof n=="function"?n(s):ot(n)&&(n.current=s))},[e])}const Oe=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Mr="framerAppearId",Is="data-"+Oe(Mr),{schedule:Ie}=Bs(queueMicrotask,!1),Ns=v.createContext({});function Rr(t,e,n,s,i){var o,r;const{visualElement:a}=v.useContext(Yt),l=v.useContext(ks),u=v.useContext(Xt),c=v.useContext(Le).reducedMotion,h=v.useRef(null);s=s||l.renderer,!h.current&&s&&(h.current=s(t,{visualState:e,parent:a,props:n,presenceContext:u,blockInitialAnimation:u?u.initial===!1:!1,reducedMotionConfig:c}));const f=h.current,d=v.useContext(Ns);f&&!f.projection&&i&&(f.type==="html"||f.type==="svg")&&Er(h.current,n,i,d);const p=v.useRef(!1);v.useInsertionEffect(()=>{f&&p.current&&f.update(n,u)});const m=n[Is],y=v.useRef(!!m&&!(!((o=window.MotionHandoffIsComplete)===null||o===void 0)&&o.call(window,m))&&((r=window.MotionHasOptimisedAnimation)===null||r===void 0?void 0:r.call(window,m)));return Ls(()=>{f&&(p.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),Ie.render(f.render),y.current&&f.animationState&&f.animationState.animateChanges())}),v.useEffect(()=>{f&&(!y.current&&f.animationState&&f.animationState.animateChanges(),y.current&&(queueMicrotask(()=>{var g;(g=window.MotionHandoffMarkAsComplete)===null||g===void 0||g.call(window,m)}),y.current=!1))}),f}function Er(t,e,n,s){const{layoutId:i,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:u}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:Us(t.parent)),t.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!r||a&&ot(a),visualElement:t,animationType:typeof o=="string"?o:"both",initialPromotionConfig:s,layoutScroll:l,layoutRoot:u})}function Us(t){if(t)return t.options.allowProjection!==!1?t.projection:Us(t.parent)}function Lr({preloadedFeatures:t,createVisualElement:e,useRender:n,useVisualState:s,Component:i}){var o,r;t&&Tr(t);function a(u,c){let h;const f={...v.useContext(Le),...u,layoutId:Fr(u)},{isStatic:d}=f,p=Vr(u),m=s(u,d);if(!d&&Fe){Br();const y=kr(f);h=y.MeasureLayout,p.visualElement=Rr(i,m,f,e,y.ProjectionNode)}return G.jsxs(Yt.Provider,{value:p,children:[h&&p.visualElement?G.jsx(h,{visualElement:p.visualElement,...f}):null,n(i,u,Dr(m,p.visualElement,c),m,d,p.visualElement)]})}a.displayName=`motion.${typeof i=="string"?i:`create(${(r=(o=i.displayName)!==null&&o!==void 0?o:i.name)!==null&&r!==void 0?r:""})`}`;const l=v.forwardRef(a);return l[Cr]=i,l}function Fr({layoutId:t}){const e=v.useContext(Re).id;return e&&t!==void 0?e+"-"+t:t}function Br(t,e){v.useContext(ks).strict}function kr(t){const{drag:e,layout:n}=ft;if(!e&&!n)return{};const s={...e,...n};return{MeasureLayout:e!=null&&e.isEnabled(t)||n!=null&&n.isEnabled(t)?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}const jr=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Ne(t){return typeof t!="string"||t.includes("-")?!1:!!(jr.indexOf(t)>-1||/[A-Z]/u.test(t))}function gn(t){const e=[{},{}];return t==null||t.values.forEach((n,s)=>{e[0][s]=n.get(),e[1][s]=n.getVelocity()}),e}function Ue(t,e,n,s){if(typeof e=="function"){const[i,o]=gn(s);e=e(n!==void 0?n:t.custom,i,o)}if(typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"){const[i,o]=gn(s);e=e(n!==void 0?n:t.custom,i,o)}return e}const pe=t=>Array.isArray(t),Or=t=>!!(t&&typeof t=="object"&&t.mix&&t.toValue),Ir=t=>pe(t)?t[t.length-1]||0:t,B=t=>!!(t&&t.getVelocity);function Ot(t){const e=B(t)?t.get():t;return Or(e)?e.toValue():e}function Nr({scrapeMotionValuesFromProps:t,createRenderState:e,onUpdate:n},s,i,o){const r={latestValues:Ur(s,i,o,t),renderState:e()};return n&&(r.onMount=a=>n({props:s,current:a,...r}),r.onUpdate=a=>n(a)),r}const _s=t=>(e,n)=>{const s=v.useContext(Yt),i=v.useContext(Xt),o=()=>Nr(t,e,s,i);return n?o():Ee(o)};function Ur(t,e,n,s){const i={},o=s(t,{});for(const f in o)i[f]=Ot(o[f]);let{initial:r,animate:a}=t;const l=Zt(t),u=Os(t);e&&u&&!l&&t.inherit!==!1&&(r===void 0&&(r=e.initial),a===void 0&&(a=e.animate));let c=n?n.initial===!1:!1;c=c||r===!1;const h=c?a:r;if(h&&typeof h!="boolean"&&!qt(h)){const f=Array.isArray(h)?h:[h];for(let d=0;d<f.length;d++){const p=Ue(t,f[d]);if(p){const{transitionEnd:m,transition:y,...g}=p;for(const T in g){let P=g[T];if(Array.isArray(P)){const b=c?P.length-1:0;P=P[b]}P!==null&&(i[T]=P)}for(const T in m)i[T]=m[T]}}}return i}const pt=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],it=new Set(pt),Ks=t=>e=>typeof e=="string"&&e.startsWith(t),Ws=Ks("--"),_r=Ks("var(--"),_e=t=>_r(t)?Kr.test(t.split("/*")[0].trim()):!1,Kr=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,$s=(t,e)=>e&&typeof t=="number"?e.transform(t):t,X=(t,e,n)=>n>e?e:n<t?t:n,mt={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},wt={...mt,transform:t=>X(0,1,t)},kt={...mt,default:1},Mt=t=>({test:e=>typeof e=="string"&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),Y=Mt("deg"),K=Mt("%"),S=Mt("px"),Wr=Mt("vh"),$r=Mt("vw"),yn={...K,parse:t=>K.parse(t)/100,transform:t=>K.transform(t*100)},Gr={borderWidth:S,borderTopWidth:S,borderRightWidth:S,borderBottomWidth:S,borderLeftWidth:S,borderRadius:S,radius:S,borderTopLeftRadius:S,borderTopRightRadius:S,borderBottomRightRadius:S,borderBottomLeftRadius:S,width:S,maxWidth:S,height:S,maxHeight:S,top:S,right:S,bottom:S,left:S,padding:S,paddingTop:S,paddingRight:S,paddingBottom:S,paddingLeft:S,margin:S,marginTop:S,marginRight:S,marginBottom:S,marginLeft:S,backgroundPositionX:S,backgroundPositionY:S},zr={rotate:Y,rotateX:Y,rotateY:Y,rotateZ:Y,scale:kt,scaleX:kt,scaleY:kt,scaleZ:kt,skew:Y,skewX:Y,skewY:Y,distance:S,translateX:S,translateY:S,translateZ:S,x:S,y:S,z:S,perspective:S,transformPerspective:S,opacity:wt,originX:yn,originY:yn,originZ:S},vn={...mt,transform:Math.round},Ke={...Gr,...zr,zIndex:vn,size:S,fillOpacity:wt,strokeOpacity:wt,numOctaves:vn},Hr={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Xr=pt.length;function Yr(t,e,n){let s="",i=!0;for(let o=0;o<Xr;o++){const r=pt[o],a=t[r];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(r.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||n){const u=$s(a,Ke[r]);if(!l){i=!1;const c=Hr[r]||r;s+=`${c}(${u}) `}n&&(e[r]=u)}}return s=s.trim(),n?s=n(e,i?"":s):i&&(s="none"),s}function We(t,e,n){const{style:s,vars:i,transformOrigin:o}=t;let r=!1,a=!1;for(const l in e){const u=e[l];if(it.has(l)){r=!0;continue}else if(Ws(l)){i[l]=u;continue}else{const c=$s(u,Ke[l]);l.startsWith("origin")?(a=!0,o[l]=c):s[l]=c}}if(e.transform||(r||n?s.transform=Yr(e,t.transform,n):s.transform&&(s.transform="none")),a){const{originX:l="50%",originY:u="50%",originZ:c=0}=o;s.transformOrigin=`${l} ${u} ${c}`}}const qr={offset:"stroke-dashoffset",array:"stroke-dasharray"},Zr={offset:"strokeDashoffset",array:"strokeDasharray"};function Jr(t,e,n=1,s=0,i=!0){t.pathLength=1;const o=i?qr:Zr;t[o.offset]=S.transform(-s);const r=S.transform(e),a=S.transform(n);t[o.array]=`${r} ${a}`}function xn(t,e,n){return typeof t=="string"?t:S.transform(e+n*t)}function Qr(t,e,n){const s=xn(e,t.x,t.width),i=xn(n,t.y,t.height);return`${s} ${i}`}function $e(t,{attrX:e,attrY:n,attrScale:s,originX:i,originY:o,pathLength:r,pathSpacing:a=1,pathOffset:l=0,...u},c,h){if(We(t,u,h),c){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:f,style:d,dimensions:p}=t;f.transform&&(p&&(d.transform=f.transform),delete f.transform),p&&(i!==void 0||o!==void 0||d.transform)&&(d.transformOrigin=Qr(p,i!==void 0?i:.5,o!==void 0?o:.5)),e!==void 0&&(f.x=e),n!==void 0&&(f.y=n),s!==void 0&&(f.scale=s),r!==void 0&&Jr(f,r,a,l,!1)}const Ge=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),Gs=()=>({...Ge(),attrs:{}}),ze=t=>typeof t=="string"&&t.toLowerCase()==="svg";function zs(t,{style:e,vars:n},s,i){Object.assign(t.style,e,i&&i.getProjectionStyles(s));for(const o in n)t.style.setProperty(o,n[o])}const Hs=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Xs(t,e,n,s){zs(t,e,void 0,s);for(const i in e.attrs)t.setAttribute(Hs.has(i)?i:Oe(i),e.attrs[i])}const _t={};function to(t){Object.assign(_t,t)}function Ys(t,{layout:e,layoutId:n}){return it.has(t)||t.startsWith("origin")||(e||n!==void 0)&&(!!_t[t]||t==="opacity")}function He(t,e,n){var s;const{style:i}=t,o={};for(const r in i)(B(i[r])||e.style&&B(e.style[r])||Ys(r,t)||((s=n==null?void 0:n.getValue(r))===null||s===void 0?void 0:s.liveStyle)!==void 0)&&(o[r]=i[r]);return o}function qs(t,e,n){const s=He(t,e,n);for(const i in t)if(B(t[i])||B(e[i])){const o=pt.indexOf(i)!==-1?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i;s[o]=t[i]}return s}function eo(t,e){try{e.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{e.dimensions={x:0,y:0,width:0,height:0}}}const Tn=["x","y","width","height","cx","cy","r"],no={useVisualState:_s({scrapeMotionValuesFromProps:qs,createRenderState:Gs,onUpdate:({props:t,prevProps:e,current:n,renderState:s,latestValues:i})=>{if(!n)return;let o=!!t.drag;if(!o){for(const a in i)if(it.has(a)){o=!0;break}}if(!o)return;let r=!e;if(e)for(let a=0;a<Tn.length;a++){const l=Tn[a];t[l]!==e[l]&&(r=!0)}r&&V.read(()=>{eo(n,s),V.render(()=>{$e(s,i,ze(n.tagName),t.transformTemplate),Xs(n,s)})})}})},so={useVisualState:_s({scrapeMotionValuesFromProps:He,createRenderState:Ge})};function Zs(t,e,n){for(const s in e)!B(e[s])&&!Ys(s,n)&&(t[s]=e[s])}function io({transformTemplate:t},e){return v.useMemo(()=>{const n=Ge();return We(n,e,t),Object.assign({},n.vars,n.style)},[e])}function ro(t,e){const n=t.style||{},s={};return Zs(s,n,t),Object.assign(s,io(t,e)),s}function oo(t,e){const n={},s=ro(t,e);return t.drag&&t.dragListener!==!1&&(n.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),t.tabIndex===void 0&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=s,n}function ao(t,e,n,s){const i=v.useMemo(()=>{const o=Gs();return $e(o,e,ze(s),t.transformTemplate),{...o.attrs,style:{...o.style}}},[e]);if(t.style){const o={};Zs(o,t.style,t),i.style={...o,...i.style}}return i}function lo(t=!1){return(n,s,i,{latestValues:o},r)=>{const l=(Ne(n)?ao:oo)(s,o,r,n),u=Ar(s,typeof n=="string",t),c=n!==v.Fragment?{...u,...l,ref:i}:{},{children:h}=s,f=v.useMemo(()=>B(h)?h.get():h,[h]);return v.createElement(n,{...c,children:f})}}function uo(t,e){return function(s,{forwardMotionProps:i}={forwardMotionProps:!1}){const r={...Ne(s)?no:so,preloadedFeatures:t,useRender:lo(i),createVisualElement:e,Component:s};return Lr(r)}}function Js(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}function Jt(t,e,n){const s=t.getProps();return Ue(s,e,n!==void 0?n:s.custom,t)}const co=Be(()=>window.ScrollTimeline!==void 0);class ho{constructor(e){this.stop=()=>this.runAll("stop"),this.animations=e.filter(Boolean)}get finished(){return Promise.all(this.animations.map(e=>"finished"in e?e.finished:e))}getAll(e){return this.animations[0][e]}setAll(e,n){for(let s=0;s<this.animations.length;s++)this.animations[s][e]=n}attachTimeline(e,n){const s=this.animations.map(i=>{if(co()&&i.attachTimeline)return i.attachTimeline(e);if(typeof n=="function")return n(i)});return()=>{s.forEach((i,o)=>{i&&i(),this.animations[o].stop()})}}get time(){return this.getAll("time")}set time(e){this.setAll("time",e)}get speed(){return this.getAll("speed")}set speed(e){this.setAll("speed",e)}get startTime(){return this.getAll("startTime")}get duration(){let e=0;for(let n=0;n<this.animations.length;n++)e=Math.max(e,this.animations[n].duration);return e}runAll(e){this.animations.forEach(n=>n[e]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class fo extends ho{then(e,n){return Promise.all(this.animations).then(e).catch(n)}}function Xe(t,e){return t?t[e]||t.default||t:void 0}const me=2e4;function Qs(t){let e=0;const n=50;let s=t.next(e);for(;!s.done&&e<me;)e+=n,s=t.next(e);return e>=me?1/0:e}function Ye(t){return typeof t=="function"}function Pn(t,e){t.timeline=e,t.onfinish=null}const qe=t=>Array.isArray(t)&&typeof t[0]=="number",po={linearEasing:void 0};function mo(t,e){const n=Be(t);return()=>{var s;return(s=po[e])!==null&&s!==void 0?s:n()}}const Kt=mo(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),ti=(t,e,n=10)=>{let s="";const i=Math.max(Math.round(e/n),2);for(let o=0;o<i;o++)s+=t(ht(0,i-1,o))+", ";return`linear(${s.substring(0,s.length-2)})`};function ei(t){return!!(typeof t=="function"&&Kt()||!t||typeof t=="string"&&(t in ge||Kt())||qe(t)||Array.isArray(t)&&t.every(ei))}const yt=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,ge={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:yt([0,.65,.55,1]),circOut:yt([.55,0,1,.45]),backIn:yt([.31,.01,.66,-.59]),backOut:yt([.33,1.53,.69,.99])};function ni(t,e){if(t)return typeof t=="function"&&Kt()?ti(t,e):qe(t)?yt(t):Array.isArray(t)?t.map(n=>ni(n,e)||ge.easeOut):ge[t]}const _={x:!1,y:!1};function si(){return _.x||_.y}function go(t,e,n){var s;if(t instanceof Element)return[t];if(typeof t=="string"){let i=document;const o=(s=void 0)!==null&&s!==void 0?s:i.querySelectorAll(t);return o?Array.from(o):[]}return Array.from(t)}function ii(t,e){const n=go(t),s=new AbortController,i={passive:!0,...e,signal:s.signal};return[n,i,()=>s.abort()]}function Sn(t){return e=>{e.pointerType==="touch"||si()||t(e)}}function yo(t,e,n={}){const[s,i,o]=ii(t,n),r=Sn(a=>{const{target:l}=a,u=e(a);if(typeof u!="function"||!l)return;const c=Sn(h=>{u(h),l.removeEventListener("pointerleave",c)});l.addEventListener("pointerleave",c,i)});return s.forEach(a=>{a.addEventListener("pointerenter",r,i)}),o}const ri=(t,e)=>e?t===e?!0:ri(t,e.parentElement):!1,Ze=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1,vo=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function xo(t){return vo.has(t.tagName)||t.tabIndex!==-1}const vt=new WeakSet;function An(t){return e=>{e.key==="Enter"&&t(e)}}function se(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}const To=(t,e)=>{const n=t.currentTarget;if(!n)return;const s=An(()=>{if(vt.has(n))return;se(n,"down");const i=An(()=>{se(n,"up")}),o=()=>se(n,"cancel");n.addEventListener("keyup",i,e),n.addEventListener("blur",o,e)});n.addEventListener("keydown",s,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",s),e)};function bn(t){return Ze(t)&&!si()}function Po(t,e,n={}){const[s,i,o]=ii(t,n),r=a=>{const l=a.currentTarget;if(!bn(a)||vt.has(l))return;vt.add(l);const u=e(a),c=(d,p)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",f),!(!bn(d)||!vt.has(l))&&(vt.delete(l),typeof u=="function"&&u(d,{success:p}))},h=d=>{c(d,n.useGlobalTarget||ri(l,d.target))},f=d=>{c(d,!1)};window.addEventListener("pointerup",h,i),window.addEventListener("pointercancel",f,i)};return s.forEach(a=>{!xo(a)&&a.getAttribute("tabindex")===null&&(a.tabIndex=0),(n.useGlobalTarget?window:a).addEventListener("pointerdown",r,i),a.addEventListener("focus",u=>To(u,i),i)}),o}function So(t){return t==="x"||t==="y"?_[t]?null:(_[t]=!0,()=>{_[t]=!1}):_.x||_.y?null:(_.x=_.y=!0,()=>{_.x=_.y=!1})}const oi=new Set(["width","height","top","left","right","bottom",...pt]);let It;function Ao(){It=void 0}const W={now:()=>(It===void 0&&W.set(E.isProcessing||yr.useManualTiming?E.timestamp:performance.now()),It),set:t=>{It=t,queueMicrotask(Ao)}};function Je(t,e){t.indexOf(e)===-1&&t.push(e)}function Qe(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}class tn{constructor(){this.subscriptions=[]}add(e){return Je(this.subscriptions,e),()=>Qe(this.subscriptions,e)}notify(e,n,s){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](e,n,s);else for(let o=0;o<i;o++){const r=this.subscriptions[o];r&&r(e,n,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function ai(t,e){return e?t*(1e3/e):0}const wn=30,bo=t=>!isNaN(parseFloat(t));class wo{constructor(e,n={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(s,i=!0)=>{const o=W.now();this.updatedAt!==o&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(s),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=n.owner}setCurrent(e){this.current=e,this.updatedAt=W.now(),this.canTrackVelocity===null&&e!==void 0&&(this.canTrackVelocity=bo(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,n){this.events[e]||(this.events[e]=new tn);const s=this.events[e].add(n);return e==="change"?()=>{s(),V.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,n){this.passiveEffect=e,this.stopPassiveEffect=n}set(e,n=!0){!n||!this.passiveEffect?this.updateAndNotify(e,n):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,n,s){this.set(n),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-s}jump(e,n=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const e=W.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||e-this.updatedAt>wn)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,wn);return ai(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Vt(t,e){return new wo(t,e)}function Vo(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,Vt(n))}function Co(t,e){const n=Jt(t,e);let{transitionEnd:s={},transition:i={},...o}=n||{};o={...o,...s};for(const r in o){const a=Ir(o[r]);Vo(t,r,a)}}function Do(t){return!!(B(t)&&t.add)}function ye(t,e){const n=t.getValue("willChange");if(Do(n))return n.add(e)}function li(t){return t.props[Is]}const ui=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,Mo=1e-7,Ro=12;function Eo(t,e,n,s,i){let o,r,a=0;do r=e+(n-e)/2,o=ui(r,s,i)-t,o>0?n=r:e=r;while(Math.abs(o)>Mo&&++a<Ro);return r}function Rt(t,e,n,s){if(t===e&&n===s)return j;const i=o=>Eo(o,0,1,t,n);return o=>o===0||o===1?o:ui(i(o),e,s)}const ci=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,hi=t=>e=>1-t(1-e),fi=Rt(.33,1.53,.69,.99),en=hi(fi),di=ci(en),pi=t=>(t*=2)<1?.5*en(t):.5*(2-Math.pow(2,-10*(t-1))),nn=t=>1-Math.sin(Math.acos(t)),mi=hi(nn),gi=ci(nn),yi=t=>/^0[^.\s]+$/u.test(t);function Lo(t){return typeof t=="number"?t===0:t!==null?t==="none"||t==="0"||yi(t):!0}const Tt=t=>Math.round(t*1e5)/1e5,sn=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function Fo(t){return t==null}const Bo=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,rn=(t,e)=>n=>!!(typeof n=="string"&&Bo.test(n)&&n.startsWith(t)||e&&!Fo(n)&&Object.prototype.hasOwnProperty.call(n,e)),vi=(t,e,n)=>s=>{if(typeof s!="string")return s;const[i,o,r,a]=s.match(sn);return{[t]:parseFloat(i),[e]:parseFloat(o),[n]:parseFloat(r),alpha:a!==void 0?parseFloat(a):1}},ko=t=>X(0,255,t),ie={...mt,transform:t=>Math.round(ko(t))},nt={test:rn("rgb","red"),parse:vi("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+ie.transform(t)+", "+ie.transform(e)+", "+ie.transform(n)+", "+Tt(wt.transform(s))+")"};function jo(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}}const ve={test:rn("#"),parse:jo,transform:nt.transform},at={test:rn("hsl","hue"),parse:vi("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+K.transform(Tt(e))+", "+K.transform(Tt(n))+", "+Tt(wt.transform(s))+")"},F={test:t=>nt.test(t)||ve.test(t)||at.test(t),parse:t=>nt.test(t)?nt.parse(t):at.test(t)?at.parse(t):ve.parse(t),transform:t=>typeof t=="string"?t:t.hasOwnProperty("red")?nt.transform(t):at.transform(t)},Oo=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Io(t){var e,n;return isNaN(t)&&typeof t=="string"&&(((e=t.match(sn))===null||e===void 0?void 0:e.length)||0)+(((n=t.match(Oo))===null||n===void 0?void 0:n.length)||0)>0}const xi="number",Ti="color",No="var",Uo="var(",Vn="${}",_o=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Ct(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},i=[];let o=0;const a=e.replace(_o,l=>(F.test(l)?(s.color.push(o),i.push(Ti),n.push(F.parse(l))):l.startsWith(Uo)?(s.var.push(o),i.push(No),n.push(l)):(s.number.push(o),i.push(xi),n.push(parseFloat(l))),++o,Vn)).split(Vn);return{values:n,split:a,indexes:s,types:i}}function Pi(t){return Ct(t).values}function Si(t){const{split:e,types:n}=Ct(t),s=e.length;return i=>{let o="";for(let r=0;r<s;r++)if(o+=e[r],i[r]!==void 0){const a=n[r];a===xi?o+=Tt(i[r]):a===Ti?o+=F.transform(i[r]):o+=i[r]}return o}}const Ko=t=>typeof t=="number"?0:t;function Wo(t){const e=Pi(t);return Si(t)(e.map(Ko))}const Z={test:Io,parse:Pi,createTransformer:Si,getAnimatableNone:Wo},$o=new Set(["brightness","contrast","saturate","opacity"]);function Go(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[s]=n.match(sn)||[];if(!s)return t;const i=n.replace(s,"");let o=$o.has(e)?1:0;return s!==n&&(o*=100),e+"("+o+i+")"}const zo=/\b([a-z-]*)\(.*?\)/gu,xe={...Z,getAnimatableNone:t=>{const e=t.match(zo);return e?e.map(Go).join(" "):t}},Ho={...Ke,color:F,backgroundColor:F,outlineColor:F,fill:F,stroke:F,borderColor:F,borderTopColor:F,borderRightColor:F,borderBottomColor:F,borderLeftColor:F,filter:xe,WebkitFilter:xe},on=t=>Ho[t];function Ai(t,e){let n=on(t);return n!==xe&&(n=Z),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Xo=new Set(["auto","none","0"]);function Yo(t,e,n){let s=0,i;for(;s<t.length&&!i;){const o=t[s];typeof o=="string"&&!Xo.has(o)&&Ct(o).values.length&&(i=t[s]),s++}if(i&&n)for(const o of e)t[o]=Ai(n,i)}const Cn=t=>t===mt||t===S,Dn=(t,e)=>parseFloat(t.split(", ")[e]),Mn=(t,e)=>(n,{transform:s})=>{if(s==="none"||!s)return 0;const i=s.match(/^matrix3d\((.+)\)$/u);if(i)return Dn(i[1],e);{const o=s.match(/^matrix\((.+)\)$/u);return o?Dn(o[1],t):0}},qo=new Set(["x","y","z"]),Zo=pt.filter(t=>!qo.has(t));function Jo(t){const e=[];return Zo.forEach(n=>{const s=t.getValue(n);s!==void 0&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e}const dt={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:Mn(4,13),y:Mn(5,14)};dt.translateX=dt.x;dt.translateY=dt.y;const st=new Set;let Te=!1,Pe=!1;function bi(){if(Pe){const t=Array.from(st).filter(s=>s.needsMeasurement),e=new Set(t.map(s=>s.element)),n=new Map;e.forEach(s=>{const i=Jo(s);i.length&&(n.set(s,i),s.render())}),t.forEach(s=>s.measureInitialState()),e.forEach(s=>{s.render();const i=n.get(s);i&&i.forEach(([o,r])=>{var a;(a=s.getValue(o))===null||a===void 0||a.set(r)})}),t.forEach(s=>s.measureEndState()),t.forEach(s=>{s.suspendedScrollY!==void 0&&window.scrollTo(0,s.suspendedScrollY)})}Pe=!1,Te=!1,st.forEach(t=>t.complete()),st.clear()}function wi(){st.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(Pe=!0)})}function Qo(){wi(),bi()}class an{constructor(e,n,s,i,o,r=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...e],this.onComplete=n,this.name=s,this.motionValue=i,this.element=o,this.isAsync=r}scheduleResolve(){this.isScheduled=!0,this.isAsync?(st.add(this),Te||(Te=!0,V.read(wi),V.resolveKeyframes(bi))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:n,element:s,motionValue:i}=this;for(let o=0;o<e.length;o++)if(e[o]===null)if(o===0){const r=i==null?void 0:i.get(),a=e[e.length-1];if(r!==void 0)e[0]=r;else if(s&&n){const l=s.readValue(n,a);l!=null&&(e[0]=l)}e[0]===void 0&&(e[0]=a),i&&r===void 0&&i.set(e[0])}else e[o]=e[o-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),st.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,st.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const Vi=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),ta=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function ea(t){const e=ta.exec(t);if(!e)return[,];const[,n,s,i]=e;return[`--${n??s}`,i]}function Ci(t,e,n=1){const[s,i]=ea(t);if(!s)return;const o=window.getComputedStyle(e).getPropertyValue(s);if(o){const r=o.trim();return Vi(r)?parseFloat(r):r}return _e(i)?Ci(i,e,n+1):i}const Di=t=>e=>e.test(t),na={test:t=>t==="auto",parse:t=>t},Mi=[mt,S,K,Y,$r,Wr,na],Rn=t=>Mi.find(Di(t));class Ri extends an{constructor(e,n,s,i,o){super(e,n,s,i,o,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:n,name:s}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<e.length;l++){let u=e[l];if(typeof u=="string"&&(u=u.trim(),_e(u))){const c=Ci(u,n.current);c!==void 0&&(e[l]=c),l===e.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!oi.has(s)||e.length!==2)return;const[i,o]=e,r=Rn(i),a=Rn(o);if(r!==a)if(Cn(r)&&Cn(a))for(let l=0;l<e.length;l++){const u=e[l];typeof u=="string"&&(e[l]=parseFloat(u))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:n}=this,s=[];for(let i=0;i<e.length;i++)Lo(e[i])&&s.push(i);s.length&&Yo(e,s,n)}measureInitialState(){const{element:e,unresolvedKeyframes:n,name:s}=this;if(!e||!e.current)return;s==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=dt[s](e.measureViewportBox(),window.getComputedStyle(e.current)),n[0]=this.measuredOrigin;const i=n[n.length-1];i!==void 0&&e.getValue(s,i).jump(i,!1)}measureEndState(){var e;const{element:n,name:s,unresolvedKeyframes:i}=this;if(!n||!n.current)return;const o=n.getValue(s);o&&o.jump(this.measuredOrigin,!1);const r=i.length-1,a=i[r];i[r]=dt[s](n.measureViewportBox(),window.getComputedStyle(n.current)),a!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=a),!((e=this.removedTransforms)===null||e===void 0)&&e.length&&this.removedTransforms.forEach(([l,u])=>{n.getValue(l).set(u)}),this.resolveNoneKeyframes()}}const En=(t,e)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(Z.test(t)||t==="0")&&!t.startsWith("url("));function sa(t){const e=t[0];if(t.length===1)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}function ia(t,e,n,s){const i=t[0];if(i===null)return!1;if(e==="display"||e==="visibility")return!0;const o=t[t.length-1],r=En(i,e),a=En(o,e);return!r||!a?!1:sa(t)||(n==="spring"||Ye(n))&&s}const ra=t=>t!==null;function Qt(t,{repeat:e,repeatType:n="loop"},s){const i=t.filter(ra),o=e&&n!=="loop"&&e%2===1?0:i.length-1;return!o||s===void 0?i[o]:s}const oa=40;class Ei{constructor({autoplay:e=!0,delay:n=0,type:s="keyframes",repeat:i=0,repeatDelay:o=0,repeatType:r="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=W.now(),this.options={autoplay:e,delay:n,type:s,repeat:i,repeatDelay:o,repeatType:r,...a},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>oa?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&Qo(),this._resolved}onKeyframesResolved(e,n){this.resolvedAt=W.now(),this.hasAttemptedResolve=!0;const{name:s,type:i,velocity:o,delay:r,onComplete:a,onUpdate:l,isGenerator:u}=this.options;if(!u&&!ia(e,s,i,o))if(r)this.options.duration=0;else{l&&l(Qt(e,this.options,n)),a&&a(),this.resolveFinishedPromise();return}const c=this.initPlayback(e,n);c!==!1&&(this._resolved={keyframes:e,finalKeyframe:n,...c},this.onPostResolved())}onPostResolved(){}then(e,n){return this.currentFinishedPromise.then(e,n)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(e=>{this.resolveFinishedPromise=e})}}const C=(t,e,n)=>t+(e-t)*n;function re(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function aa({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,e/=100,n/=100;let i=0,o=0,r=0;if(!e)i=o=r=n;else{const a=n<.5?n*(1+e):n+e-n*e,l=2*n-a;i=re(l,a,t+1/3),o=re(l,a,t),r=re(l,a,t-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(r*255),alpha:s}}function Wt(t,e){return n=>n>0?e:t}const oe=(t,e,n)=>{const s=t*t,i=n*(e*e-s)+s;return i<0?0:Math.sqrt(i)},la=[ve,nt,at],ua=t=>la.find(e=>e.test(t));function Ln(t){const e=ua(t);if(!e)return!1;let n=e.parse(t);return e===at&&(n=aa(n)),n}const Fn=(t,e)=>{const n=Ln(t),s=Ln(e);if(!n||!s)return Wt(t,e);const i={...n};return o=>(i.red=oe(n.red,s.red,o),i.green=oe(n.green,s.green,o),i.blue=oe(n.blue,s.blue,o),i.alpha=C(n.alpha,s.alpha,o),nt.transform(i))},ca=(t,e)=>n=>e(t(n)),Et=(...t)=>t.reduce(ca),Se=new Set(["none","hidden"]);function ha(t,e){return Se.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function fa(t,e){return n=>C(t,e,n)}function ln(t){return typeof t=="number"?fa:typeof t=="string"?_e(t)?Wt:F.test(t)?Fn:ma:Array.isArray(t)?Li:typeof t=="object"?F.test(t)?Fn:da:Wt}function Li(t,e){const n=[...t],s=n.length,i=t.map((o,r)=>ln(o)(o,e[r]));return o=>{for(let r=0;r<s;r++)n[r]=i[r](o);return n}}function da(t,e){const n={...t,...e},s={};for(const i in n)t[i]!==void 0&&e[i]!==void 0&&(s[i]=ln(t[i])(t[i],e[i]));return i=>{for(const o in s)n[o]=s[o](i);return n}}function pa(t,e){var n;const s=[],i={color:0,var:0,number:0};for(let o=0;o<e.values.length;o++){const r=e.types[o],a=t.indexes[r][i[r]],l=(n=t.values[a])!==null&&n!==void 0?n:0;s[o]=l,i[r]++}return s}const ma=(t,e)=>{const n=Z.createTransformer(e),s=Ct(t),i=Ct(e);return s.indexes.var.length===i.indexes.var.length&&s.indexes.color.length===i.indexes.color.length&&s.indexes.number.length>=i.indexes.number.length?Se.has(t)&&!i.values.length||Se.has(e)&&!s.values.length?ha(t,e):Et(Li(pa(s,i),i.values),n):Wt(t,e)};function Fi(t,e,n){return typeof t=="number"&&typeof e=="number"&&typeof n=="number"?C(t,e,n):ln(t)(t,e)}const ga=5;function Bi(t,e,n){const s=Math.max(e-ga,0);return ai(n-t(s),e-s)}const D={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Bn=.001;function ya({duration:t=D.duration,bounce:e=D.bounce,velocity:n=D.velocity,mass:s=D.mass}){let i,o,r=1-e;r=X(D.minDamping,D.maxDamping,r),t=X(D.minDuration,D.maxDuration,H(t)),r<1?(i=u=>{const c=u*r,h=c*t,f=c-n,d=Ae(u,r),p=Math.exp(-h);return Bn-f/d*p},o=u=>{const h=u*r*t,f=h*n+n,d=Math.pow(r,2)*Math.pow(u,2)*t,p=Math.exp(-h),m=Ae(Math.pow(u,2),r);return(-i(u)+Bn>0?-1:1)*((f-d)*p)/m}):(i=u=>{const c=Math.exp(-u*t),h=(u-n)*t+1;return-.001+c*h},o=u=>{const c=Math.exp(-u*t),h=(n-u)*(t*t);return c*h});const a=5/t,l=xa(i,o,a);if(t=z(t),isNaN(l))return{stiffness:D.stiffness,damping:D.damping,duration:t};{const u=Math.pow(l,2)*s;return{stiffness:u,damping:r*2*Math.sqrt(s*u),duration:t}}}const va=12;function xa(t,e,n){let s=n;for(let i=1;i<va;i++)s=s-t(s)/e(s);return s}function Ae(t,e){return t*Math.sqrt(1-e*e)}const Ta=["duration","bounce"],Pa=["stiffness","damping","mass"];function kn(t,e){return e.some(n=>t[n]!==void 0)}function Sa(t){let e={velocity:D.velocity,stiffness:D.stiffness,damping:D.damping,mass:D.mass,isResolvedFromDuration:!1,...t};if(!kn(t,Pa)&&kn(t,Ta))if(t.visualDuration){const n=t.visualDuration,s=2*Math.PI/(n*1.2),i=s*s,o=2*X(.05,1,1-(t.bounce||0))*Math.sqrt(i);e={...e,mass:D.mass,stiffness:i,damping:o}}else{const n=ya(t);e={...e,...n,mass:D.mass},e.isResolvedFromDuration=!0}return e}function ki(t=D.visualDuration,e=D.bounce){const n=typeof t!="object"?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:s,restDelta:i}=n;const o=n.keyframes[0],r=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:u,mass:c,duration:h,velocity:f,isResolvedFromDuration:d}=Sa({...n,velocity:-H(n.velocity||0)}),p=f||0,m=u/(2*Math.sqrt(l*c)),y=r-o,g=H(Math.sqrt(l/c)),T=Math.abs(y)<5;s||(s=T?D.restSpeed.granular:D.restSpeed.default),i||(i=T?D.restDelta.granular:D.restDelta.default);let P;if(m<1){const x=Ae(g,m);P=w=>{const M=Math.exp(-m*g*w);return r-M*((p+m*g*y)/x*Math.sin(x*w)+y*Math.cos(x*w))}}else if(m===1)P=x=>r-Math.exp(-g*x)*(y+(p+g*y)*x);else{const x=g*Math.sqrt(m*m-1);P=w=>{const M=Math.exp(-m*g*w),A=Math.min(x*w,300);return r-M*((p+m*g*y)*Math.sinh(A)+x*y*Math.cosh(A))/x}}const b={calculatedDuration:d&&h||null,next:x=>{const w=P(x);if(d)a.done=x>=h;else{let M=0;m<1&&(M=x===0?z(p):Bi(P,x,w));const A=Math.abs(M)<=s,L=Math.abs(r-w)<=i;a.done=A&&L}return a.value=a.done?r:w,a},toString:()=>{const x=Math.min(Qs(b),me),w=ti(M=>b.next(x*M).value,x,30);return x+"ms "+w}};return b}function jn({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:u=.5,restSpeed:c}){const h=t[0],f={done:!1,value:h},d=A=>a!==void 0&&A<a||l!==void 0&&A>l,p=A=>a===void 0?l:l===void 0||Math.abs(a-A)<Math.abs(l-A)?a:l;let m=n*e;const y=h+m,g=r===void 0?y:r(y);g!==y&&(m=g-h);const T=A=>-m*Math.exp(-A/s),P=A=>g+T(A),b=A=>{const L=T(A),I=P(A);f.done=Math.abs(L)<=u,f.value=f.done?g:I};let x,w;const M=A=>{d(f.value)&&(x=A,w=ki({keyframes:[f.value,p(f.value)],velocity:Bi(P,A,f.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return M(0),{calculatedDuration:null,next:A=>{let L=!1;return!w&&x===void 0&&(L=!0,b(A),M(A)),x!==void 0&&A>=x?w.next(A-x):(!L&&b(A),f)}}}const Aa=Rt(.42,0,1,1),ba=Rt(0,0,.58,1),ji=Rt(.42,0,.58,1),wa=t=>Array.isArray(t)&&typeof t[0]!="number",Va={linear:j,easeIn:Aa,easeInOut:ji,easeOut:ba,circIn:nn,circInOut:gi,circOut:mi,backIn:en,backInOut:di,backOut:fi,anticipate:pi},On=t=>{if(qe(t)){Fs(t.length===4);const[e,n,s,i]=t;return Rt(e,n,s,i)}else if(typeof t=="string")return Va[t];return t};function Ca(t,e,n){const s=[],i=n||Fi,o=t.length-1;for(let r=0;r<o;r++){let a=i(t[r],t[r+1]);if(e){const l=Array.isArray(e)?e[r]||j:e;a=Et(l,a)}s.push(a)}return s}function Da(t,e,{clamp:n=!0,ease:s,mixer:i}={}){const o=t.length;if(Fs(o===e.length),o===1)return()=>e[0];if(o===2&&e[0]===e[1])return()=>e[1];const r=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=Ca(e,s,i),l=a.length,u=c=>{if(r&&c<t[0])return e[0];let h=0;if(l>1)for(;h<t.length-2&&!(c<t[h+1]);h++);const f=ht(t[h],t[h+1],c);return a[h](f)};return n?c=>u(X(t[0],t[o-1],c)):u}function Ma(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=ht(0,e,s);t.push(C(n,1,i))}}function Ra(t){const e=[0];return Ma(e,t.length-1),e}function Ea(t,e){return t.map(n=>n*e)}function La(t,e){return t.map(()=>e||ji).splice(0,t.length-1)}function $t({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=wa(s)?s.map(On):On(s),o={done:!1,value:e[0]},r=Ea(n&&n.length===e.length?n:Ra(e),t),a=Da(r,e,{ease:Array.isArray(i)?i:La(e,i)});return{calculatedDuration:t,next:l=>(o.value=a(l),o.done=l>=t,o)}}const Fa=t=>{const e=({timestamp:n})=>t(n);return{start:()=>V.update(e,!0),stop:()=>q(e),now:()=>E.isProcessing?E.timestamp:W.now()}},Ba={decay:jn,inertia:jn,tween:$t,keyframes:$t,spring:ki},ka=t=>t/100;class un extends Ei{constructor(e){super(e),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:l}=this.options;l&&l()};const{name:n,motionValue:s,element:i,keyframes:o}=this.options,r=(i==null?void 0:i.KeyframeResolver)||an,a=(l,u)=>this.onKeyframesResolved(l,u);this.resolver=new r(o,a,n,s,i),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(e){const{type:n="keyframes",repeat:s=0,repeatDelay:i=0,repeatType:o,velocity:r=0}=this.options,a=Ye(n)?n:Ba[n]||$t;let l,u;a!==$t&&typeof e[0]!="number"&&(l=Et(ka,Fi(e[0],e[1])),e=[0,100]);const c=a({...this.options,keyframes:e});o==="mirror"&&(u=a({...this.options,keyframes:[...e].reverse(),velocity:-r})),c.calculatedDuration===null&&(c.calculatedDuration=Qs(c));const{calculatedDuration:h}=c,f=h+i,d=f*(s+1)-i;return{generator:c,mirroredGenerator:u,mapPercentToKeyframes:l,calculatedDuration:h,resolvedDuration:f,totalDuration:d}}onPostResolved(){const{autoplay:e=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!e?this.pause():this.state=this.pendingPlayState}tick(e,n=!1){const{resolved:s}=this;if(!s){const{keyframes:A}=this.options;return{done:!0,value:A[A.length-1]}}const{finalKeyframe:i,generator:o,mirroredGenerator:r,mapPercentToKeyframes:a,keyframes:l,calculatedDuration:u,totalDuration:c,resolvedDuration:h}=s;if(this.startTime===null)return o.next(0);const{delay:f,repeat:d,repeatType:p,repeatDelay:m,onUpdate:y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-c/this.speed,this.startTime)),n?this.currentTime=e:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(e-this.startTime)*this.speed;const g=this.currentTime-f*(this.speed>=0?1:-1),T=this.speed>=0?g<0:g>c;this.currentTime=Math.max(g,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=c);let P=this.currentTime,b=o;if(d){const A=Math.min(this.currentTime,c)/h;let L=Math.floor(A),I=A%1;!I&&A>=1&&(I=1),I===1&&L--,L=Math.min(L,d+1),!!(L%2)&&(p==="reverse"?(I=1-I,m&&(I-=m/h)):p==="mirror"&&(b=r)),P=X(0,1,I)*h}const x=T?{done:!1,value:l[0]}:b.next(P);a&&(x.value=a(x.value));let{done:w}=x;!T&&u!==null&&(w=this.speed>=0?this.currentTime>=c:this.currentTime<=0);const M=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&w);return M&&i!==void 0&&(x.value=Qt(l,this.options,i)),y&&y(x.value),M&&this.finish(),x}get duration(){const{resolved:e}=this;return e?H(e.calculatedDuration):0}get time(){return H(this.currentTime)}set time(e){e=z(e),this.currentTime=e,this.holdTime!==null||this.speed===0?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.speed)}get speed(){return this.playbackSpeed}set speed(e){const n=this.playbackSpeed!==e;this.playbackSpeed=e,n&&(this.time=H(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:e=Fa,onPlay:n,startTime:s}=this.options;this.driver||(this.driver=e(o=>this.tick(o))),n&&n();const i=this.driver.now();this.holdTime!==null?this.startTime=i-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=i):this.startTime=s??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var e;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(e=this.currentTime)!==null&&e!==void 0?e:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:e}=this.options;e&&e()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}}const ja=new Set(["opacity","clipPath","filter","transform"]);function Oa(t,e,n,{delay:s=0,duration:i=300,repeat:o=0,repeatType:r="loop",ease:a="easeInOut",times:l}={}){const u={[e]:n};l&&(u.offset=l);const c=ni(a,i);return Array.isArray(c)&&(u.easing=c),t.animate(u,{delay:s,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:r==="reverse"?"alternate":"normal"})}const Ia=Be(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Gt=10,Na=2e4;function Ua(t){return Ye(t.type)||t.type==="spring"||!ei(t.ease)}function _a(t,e){const n=new un({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0});let s={done:!1,value:t[0]};const i=[];let o=0;for(;!s.done&&o<Na;)s=n.sample(o),i.push(s.value),o+=Gt;return{times:void 0,keyframes:i,duration:o-Gt,ease:"linear"}}const Oi={anticipate:pi,backInOut:di,circInOut:gi};function Ka(t){return t in Oi}class In extends Ei{constructor(e){super(e);const{name:n,motionValue:s,element:i,keyframes:o}=this.options;this.resolver=new Ri(o,(r,a)=>this.onKeyframesResolved(r,a),n,s,i),this.resolver.scheduleResolve()}initPlayback(e,n){let{duration:s=300,times:i,ease:o,type:r,motionValue:a,name:l,startTime:u}=this.options;if(!a.owner||!a.owner.current)return!1;if(typeof o=="string"&&Kt()&&Ka(o)&&(o=Oi[o]),Ua(this.options)){const{onComplete:h,onUpdate:f,motionValue:d,element:p,...m}=this.options,y=_a(e,m);e=y.keyframes,e.length===1&&(e[1]=e[0]),s=y.duration,i=y.times,o=y.ease,r="keyframes"}const c=Oa(a.owner.current,l,e,{...this.options,duration:s,times:i,ease:o});return c.startTime=u??this.calcStartTime(),this.pendingTimeline?(Pn(c,this.pendingTimeline),this.pendingTimeline=void 0):c.onfinish=()=>{const{onComplete:h}=this.options;a.set(Qt(e,this.options,n)),h&&h(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:s,times:i,type:r,ease:o,keyframes:e}}get duration(){const{resolved:e}=this;if(!e)return 0;const{duration:n}=e;return H(n)}get time(){const{resolved:e}=this;if(!e)return 0;const{animation:n}=e;return H(n.currentTime||0)}set time(e){const{resolved:n}=this;if(!n)return;const{animation:s}=n;s.currentTime=z(e)}get speed(){const{resolved:e}=this;if(!e)return 1;const{animation:n}=e;return n.playbackRate}set speed(e){const{resolved:n}=this;if(!n)return;const{animation:s}=n;s.playbackRate=e}get state(){const{resolved:e}=this;if(!e)return"idle";const{animation:n}=e;return n.playState}get startTime(){const{resolved:e}=this;if(!e)return null;const{animation:n}=e;return n.startTime}attachTimeline(e){if(!this._resolved)this.pendingTimeline=e;else{const{resolved:n}=this;if(!n)return j;const{animation:s}=n;Pn(s,e)}return j}play(){if(this.isStopped)return;const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.playState==="finished"&&this.updateFinishedPromise(),n.play()}pause(){const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:e}=this;if(!e)return;const{animation:n,keyframes:s,duration:i,type:o,ease:r,times:a}=e;if(n.playState==="idle"||n.playState==="finished")return;if(this.time){const{motionValue:u,onUpdate:c,onComplete:h,element:f,...d}=this.options,p=new un({...d,keyframes:s,duration:i,type:o,ease:r,times:a,isGenerator:!0}),m=z(this.time);u.setWithVelocity(p.sample(m-Gt).value,p.sample(m).value,Gt)}const{onStop:l}=this.options;l&&l(),this.cancel()}complete(){const{resolved:e}=this;e&&e.animation.finish()}cancel(){const{resolved:e}=this;e&&e.animation.cancel()}static supports(e){const{motionValue:n,name:s,repeatDelay:i,repeatType:o,damping:r,type:a}=e;if(!n||!n.owner||!(n.owner.current instanceof HTMLElement))return!1;const{onUpdate:l,transformTemplate:u}=n.owner.getProps();return Ia()&&s&&ja.has(s)&&!l&&!u&&!i&&o!=="mirror"&&r!==0&&a!=="inertia"}}const Wa={type:"spring",stiffness:500,damping:25,restSpeed:10},$a=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),Ga={type:"keyframes",duration:.8},za={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Ha=(t,{keyframes:e})=>e.length>2?Ga:it.has(t)?t.startsWith("scale")?$a(e[1]):Wa:za;function Xa({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}const cn=(t,e,n,s={},i,o)=>r=>{const a=Xe(s,t)||{},l=a.delay||s.delay||0;let{elapsed:u=0}=s;u=u-z(l);let c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:f=>{e.set(f),a.onUpdate&&a.onUpdate(f)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:i};Xa(a)||(c={...c,...Ha(t,c)}),c.duration&&(c.duration=z(c.duration)),c.repeatDelay&&(c.repeatDelay=z(c.repeatDelay)),c.from!==void 0&&(c.keyframes[0]=c.from);let h=!1;if((c.type===!1||c.duration===0&&!c.repeatDelay)&&(c.duration=0,c.delay===0&&(h=!0)),h&&!o&&e.get()!==void 0){const f=Qt(c.keyframes,a);if(f!==void 0)return V.update(()=>{c.onUpdate(f),c.onComplete()}),new fo([])}return!o&&In.supports(c)?new In(c):new un(c)};function Ya({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,s}function Ii(t,e,{delay:n=0,transitionOverride:s,type:i}={}){var o;let{transition:r=t.getDefaultTransition(),transitionEnd:a,...l}=e;s&&(r=s);const u=[],c=i&&t.animationState&&t.animationState.getState()[i];for(const h in l){const f=t.getValue(h,(o=t.latestValues[h])!==null&&o!==void 0?o:null),d=l[h];if(d===void 0||c&&Ya(c,h))continue;const p={delay:n,...Xe(r||{},h)};let m=!1;if(window.MotionHandoffAnimation){const g=li(t);if(g){const T=window.MotionHandoffAnimation(g,h,V);T!==null&&(p.startTime=T,m=!0)}}ye(t,h),f.start(cn(h,f,d,t.shouldReduceMotion&&oi.has(h)?{type:!1}:p,t,m));const y=f.animation;y&&u.push(y)}return a&&Promise.all(u).then(()=>{V.update(()=>{a&&Co(t,a)})}),u}function be(t,e,n={}){var s;const i=Jt(t,e,n.type==="exit"?(s=t.presenceContext)===null||s===void 0?void 0:s.custom:void 0);let{transition:o=t.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(o=n.transitionOverride);const r=i?()=>Promise.all(Ii(t,i,n)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(u=0)=>{const{delayChildren:c=0,staggerChildren:h,staggerDirection:f}=o;return qa(t,e,c+u,h,f,n)}:()=>Promise.resolve(),{when:l}=o;if(l){const[u,c]=l==="beforeChildren"?[r,a]:[a,r];return u().then(()=>c())}else return Promise.all([r(),a(n.delay)])}function qa(t,e,n=0,s=0,i=1,o){const r=[],a=(t.variantChildren.size-1)*s,l=i===1?(u=0)=>u*s:(u=0)=>a-u*s;return Array.from(t.variantChildren).sort(Za).forEach((u,c)=>{u.notify("AnimationStart",e),r.push(be(u,e,{...o,delay:n+l(c)}).then(()=>u.notify("AnimationComplete",e)))}),Promise.all(r)}function Za(t,e){return t.sortNodePosition(e)}function Ja(t,e,n={}){t.notify("AnimationStart",e);let s;if(Array.isArray(e)){const i=e.map(o=>be(t,o,n));s=Promise.all(i)}else if(typeof e=="string")s=be(t,e,n);else{const i=typeof e=="function"?Jt(t,e,n.custom):e;s=Promise.all(Ii(t,i,n))}return s.then(()=>{t.notify("AnimationComplete",e)})}const Qa=je.length;function Ni(t){if(!t)return;if(!t.isControllingVariants){const n=t.parent?Ni(t.parent)||{}:{};return t.props.initial!==void 0&&(n.initial=t.props.initial),n}const e={};for(let n=0;n<Qa;n++){const s=je[n],i=t.props[s];(bt(i)||i===!1)&&(e[s]=i)}return e}const tl=[...ke].reverse(),el=ke.length;function nl(t){return e=>Promise.all(e.map(({animation:n,options:s})=>Ja(t,n,s)))}function sl(t){let e=nl(t),n=Nn(),s=!0;const i=l=>(u,c)=>{var h;const f=Jt(t,c,l==="exit"?(h=t.presenceContext)===null||h===void 0?void 0:h.custom:void 0);if(f){const{transition:d,transitionEnd:p,...m}=f;u={...u,...m,...p}}return u};function o(l){e=l(t)}function r(l){const{props:u}=t,c=Ni(t.parent)||{},h=[],f=new Set;let d={},p=1/0;for(let y=0;y<el;y++){const g=tl[y],T=n[g],P=u[g]!==void 0?u[g]:c[g],b=bt(P),x=g===l?T.isActive:null;x===!1&&(p=y);let w=P===c[g]&&P!==u[g]&&b;if(w&&s&&t.manuallyAnimateOnMount&&(w=!1),T.protectedKeys={...d},!T.isActive&&x===null||!P&&!T.prevProp||qt(P)||typeof P=="boolean")continue;const M=il(T.prevProp,P);let A=M||g===l&&T.isActive&&!w&&b||y>p&&b,L=!1;const I=Array.isArray(P)?P:[P];let rt=I.reduce(i(g),{});x===!1&&(rt={});const{prevResolvedValues:hn={}}=T,or={...hn,...rt},fn=k=>{A=!0,f.has(k)&&(L=!0,f.delete(k)),T.needsAnimating[k]=!0;const $=t.getValue(k);$&&($.liveStyle=!1)};for(const k in or){const $=rt[k],te=hn[k];if(d.hasOwnProperty(k))continue;let ee=!1;pe($)&&pe(te)?ee=!Js($,te):ee=$!==te,ee?$!=null?fn(k):f.add(k):$!==void 0&&f.has(k)?fn(k):T.protectedKeys[k]=!0}T.prevProp=P,T.prevResolvedValues=rt,T.isActive&&(d={...d,...rt}),s&&t.blockInitialAnimation&&(A=!1),A&&(!(w&&M)||L)&&h.push(...I.map(k=>({animation:k,options:{type:g}})))}if(f.size){const y={};f.forEach(g=>{const T=t.getBaseTarget(g),P=t.getValue(g);P&&(P.liveStyle=!0),y[g]=T??null}),h.push({animation:y})}let m=!!h.length;return s&&(u.initial===!1||u.initial===u.animate)&&!t.manuallyAnimateOnMount&&(m=!1),s=!1,m?e(h):Promise.resolve()}function a(l,u){var c;if(n[l].isActive===u)return Promise.resolve();(c=t.variantChildren)===null||c===void 0||c.forEach(f=>{var d;return(d=f.animationState)===null||d===void 0?void 0:d.setActive(l,u)}),n[l].isActive=u;const h=r(l);for(const f in n)n[f].protectedKeys={};return h}return{animateChanges:r,setActive:a,setAnimateFunction:o,getState:()=>n,reset:()=>{n=Nn(),s=!0}}}function il(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!Js(e,t):!1}function Q(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Nn(){return{animate:Q(!0),whileInView:Q(),whileHover:Q(),whileTap:Q(),whileDrag:Q(),whileFocus:Q(),exit:Q()}}class J{constructor(e){this.isMounted=!1,this.node=e}update(){}}class rl extends J{constructor(e){super(e),e.animationState||(e.animationState=sl(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();qt(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:n}=this.node.prevProps||{};e!==n&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),(e=this.unmountControls)===null||e===void 0||e.call(this)}}let ol=0;class al extends J{constructor(){super(...arguments),this.id=ol++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:n}=this.node.presenceContext,{isPresent:s}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===s)return;const i=this.node.animationState.setActive("exit",!e);n&&!e&&i.then(()=>n(this.id))}mount(){const{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}const ll={animation:{Feature:rl},exit:{Feature:al}};function Dt(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}function Lt(t){return{point:{x:t.pageX,y:t.pageY}}}const ul=t=>e=>Ze(e)&&t(e,Lt(e));function Pt(t,e,n,s){return Dt(t,e,ul(n),s)}const Un=(t,e)=>Math.abs(t-e);function cl(t,e){const n=Un(t.x,e.x),s=Un(t.y,e.y);return Math.sqrt(n**2+s**2)}class Ui{constructor(e,n,{transformPagePoint:s,contextWindow:i,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const h=le(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,d=cl(h.offset,{x:0,y:0})>=3;if(!f&&!d)return;const{point:p}=h,{timestamp:m}=E;this.history.push({...p,timestamp:m});const{onStart:y,onMove:g}=this.handlers;f||(y&&y(this.lastMoveEvent,h),this.startEvent=this.lastMoveEvent),g&&g(this.lastMoveEvent,h)},this.handlePointerMove=(h,f)=>{this.lastMoveEvent=h,this.lastMoveEventInfo=ae(f,this.transformPagePoint),V.update(this.updatePoint,!0)},this.handlePointerUp=(h,f)=>{this.end();const{onEnd:d,onSessionEnd:p,resumeAnimation:m}=this.handlers;if(this.dragSnapToOrigin&&m&&m(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const y=le(h.type==="pointercancel"?this.lastMoveEventInfo:ae(f,this.transformPagePoint),this.history);this.startEvent&&d&&d(h,y),p&&p(h,y)},!Ze(e))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=s,this.contextWindow=i||window;const r=Lt(e),a=ae(r,this.transformPagePoint),{point:l}=a,{timestamp:u}=E;this.history=[{...l,timestamp:u}];const{onSessionStart:c}=n;c&&c(e,le(a,this.history)),this.removeListeners=Et(Pt(this.contextWindow,"pointermove",this.handlePointerMove),Pt(this.contextWindow,"pointerup",this.handlePointerUp),Pt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),q(this.updatePoint)}}function ae(t,e){return e?{point:e(t.point)}:t}function _n(t,e){return{x:t.x-e.x,y:t.y-e.y}}function le({point:t},e){return{point:t,delta:_n(t,_i(e)),offset:_n(t,hl(e)),velocity:fl(e,.1)}}function hl(t){return t[0]}function _i(t){return t[t.length-1]}function fl(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,s=null;const i=_i(t);for(;n>=0&&(s=t[n],!(i.timestamp-s.timestamp>z(e)));)n--;if(!s)return{x:0,y:0};const o=H(i.timestamp-s.timestamp);if(o===0)return{x:0,y:0};const r={x:(i.x-s.x)/o,y:(i.y-s.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}const Ki=1e-4,dl=1-Ki,pl=1+Ki,Wi=.01,ml=0-Wi,gl=0+Wi;function O(t){return t.max-t.min}function yl(t,e,n){return Math.abs(t-e)<=n}function Kn(t,e,n,s=.5){t.origin=s,t.originPoint=C(e.min,e.max,t.origin),t.scale=O(n)/O(e),t.translate=C(n.min,n.max,t.origin)-t.originPoint,(t.scale>=dl&&t.scale<=pl||isNaN(t.scale))&&(t.scale=1),(t.translate>=ml&&t.translate<=gl||isNaN(t.translate))&&(t.translate=0)}function St(t,e,n,s){Kn(t.x,e.x,n.x,s?s.originX:void 0),Kn(t.y,e.y,n.y,s?s.originY:void 0)}function Wn(t,e,n){t.min=n.min+e.min,t.max=t.min+O(e)}function vl(t,e,n){Wn(t.x,e.x,n.x),Wn(t.y,e.y,n.y)}function $n(t,e,n){t.min=e.min-n.min,t.max=t.min+O(e)}function At(t,e,n){$n(t.x,e.x,n.x),$n(t.y,e.y,n.y)}function xl(t,{min:e,max:n},s){return e!==void 0&&t<e?t=s?C(e,t,s.min):Math.max(t,e):n!==void 0&&t>n&&(t=s?C(n,t,s.max):Math.min(t,n)),t}function Gn(t,e,n){return{min:e!==void 0?t.min+e:void 0,max:n!==void 0?t.max+n-(t.max-t.min):void 0}}function Tl(t,{top:e,left:n,bottom:s,right:i}){return{x:Gn(t.x,n,i),y:Gn(t.y,e,s)}}function zn(t,e){let n=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,s]=[s,n]),{min:n,max:s}}function Pl(t,e){return{x:zn(t.x,e.x),y:zn(t.y,e.y)}}function Sl(t,e){let n=.5;const s=O(t),i=O(e);return i>s?n=ht(e.min,e.max-s,t.min):s>i&&(n=ht(t.min,t.max-i,e.min)),X(0,1,n)}function Al(t,e){const n={};return e.min!==void 0&&(n.min=e.min-t.min),e.max!==void 0&&(n.max=e.max-t.min),n}const we=.35;function bl(t=we){return t===!1?t=0:t===!0&&(t=we),{x:Hn(t,"left","right"),y:Hn(t,"top","bottom")}}function Hn(t,e,n){return{min:Xn(t,e),max:Xn(t,n)}}function Xn(t,e){return typeof t=="number"?t:t[e]||0}const Yn=()=>({translate:0,scale:1,origin:0,originPoint:0}),lt=()=>({x:Yn(),y:Yn()}),qn=()=>({min:0,max:0}),R=()=>({x:qn(),y:qn()});function U(t){return[t("x"),t("y")]}function $i({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}function wl({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function Vl(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}function ue(t){return t===void 0||t===1}function Ve({scale:t,scaleX:e,scaleY:n}){return!ue(t)||!ue(e)||!ue(n)}function tt(t){return Ve(t)||Gi(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function Gi(t){return Zn(t.x)||Zn(t.y)}function Zn(t){return t&&t!=="0%"}function zt(t,e,n){const s=t-n,i=e*s;return n+i}function Jn(t,e,n,s,i){return i!==void 0&&(t=zt(t,i,s)),zt(t,n,s)+e}function Ce(t,e=0,n=1,s,i){t.min=Jn(t.min,e,n,s,i),t.max=Jn(t.max,e,n,s,i)}function zi(t,{x:e,y:n}){Ce(t.x,e.translate,e.scale,e.originPoint),Ce(t.y,n.translate,n.scale,n.originPoint)}const Qn=.999999999999,ts=1.0000000000001;function Cl(t,e,n,s=!1){const i=n.length;if(!i)return;e.x=e.y=1;let o,r;for(let a=0;a<i;a++){o=n[a],r=o.projectionDelta;const{visualElement:l}=o.options;l&&l.props.style&&l.props.style.display==="contents"||(s&&o.options.layoutScroll&&o.scroll&&o!==o.root&&ct(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,zi(t,r)),s&&tt(o.latestValues)&&ct(t,o.latestValues))}e.x<ts&&e.x>Qn&&(e.x=1),e.y<ts&&e.y>Qn&&(e.y=1)}function ut(t,e){t.min=t.min+e,t.max=t.max+e}function es(t,e,n,s,i=.5){const o=C(t.min,t.max,i);Ce(t,e,n,o,s)}function ct(t,e){es(t.x,e.x,e.scaleX,e.scale,e.originX),es(t.y,e.y,e.scaleY,e.scale,e.originY)}function Hi(t,e){return $i(Vl(t.getBoundingClientRect(),e))}function Dl(t,e,n){const s=Hi(t,n),{scroll:i}=e;return i&&(ut(s.x,i.offset.x),ut(s.y,i.offset.y)),s}const Xi=({current:t})=>t?t.ownerDocument.defaultView:null,Ml=new WeakMap;class Rl{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=R(),this.visualElement=e}start(e,{snapToCursor:n=!1}={}){const{presenceContext:s}=this.visualElement;if(s&&s.isPresent===!1)return;const i=c=>{const{dragSnapToOrigin:h}=this.getProps();h?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(Lt(c).point)},o=(c,h)=>{const{drag:f,dragPropagation:d,onDragStart:p}=this.getProps();if(f&&!d&&(this.openDragLock&&this.openDragLock(),this.openDragLock=So(f),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),U(y=>{let g=this.getAxisMotionValue(y).get()||0;if(K.test(g)){const{projection:T}=this.visualElement;if(T&&T.layout){const P=T.layout.layoutBox[y];P&&(g=O(P)*(parseFloat(g)/100))}}this.originPoint[y]=g}),p&&V.postRender(()=>p(c,h)),ye(this.visualElement,"transform");const{animationState:m}=this.visualElement;m&&m.setActive("whileDrag",!0)},r=(c,h)=>{const{dragPropagation:f,dragDirectionLock:d,onDirectionLock:p,onDrag:m}=this.getProps();if(!f&&!this.openDragLock)return;const{offset:y}=h;if(d&&this.currentDirection===null){this.currentDirection=El(y),this.currentDirection!==null&&p&&p(this.currentDirection);return}this.updateAxis("x",h.point,y),this.updateAxis("y",h.point,y),this.visualElement.render(),m&&m(c,h)},a=(c,h)=>this.stop(c,h),l=()=>U(c=>{var h;return this.getAnimationState(c)==="paused"&&((h=this.getAxisMotionValue(c).animation)===null||h===void 0?void 0:h.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new Ui(e,{onSessionStart:i,onStart:o,onMove:r,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:Xi(this.visualElement)})}stop(e,n){const s=this.isDragging;if(this.cancel(),!s)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:o}=this.getProps();o&&V.postRender(()=>o(e,n))}cancel(){this.isDragging=!1;const{projection:e,animationState:n}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:s}=this.getProps();!s&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(e,n,s){const{drag:i}=this.getProps();if(!s||!jt(e,i,this.currentDirection))return;const o=this.getAxisMotionValue(e);let r=this.originPoint[e]+s[e];this.constraints&&this.constraints[e]&&(r=xl(r,this.constraints[e],this.elastic[e])),o.set(r)}resolveConstraints(){var e;const{dragConstraints:n,dragElastic:s}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(e=this.visualElement.projection)===null||e===void 0?void 0:e.layout,o=this.constraints;n&&ot(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=Tl(i.layoutBox,n):this.constraints=!1,this.elastic=bl(s),o!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&U(r=>{this.constraints!==!1&&this.getAxisMotionValue(r)&&(this.constraints[r]=Al(i.layoutBox[r],this.constraints[r]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!ot(e))return!1;const s=e.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=Dl(s,i.root,this.visualElement.getTransformPagePoint());let r=Pl(i.layout.layoutBox,o);if(n){const a=n(wl(r));this.hasMutatedConstraints=!!a,a&&(r=$i(a))}return r}startAnimation(e){const{drag:n,dragMomentum:s,dragElastic:i,dragTransition:o,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},u=U(c=>{if(!jt(c,n,this.currentDirection))return;let h=l&&l[c]||{};r&&(h={min:0,max:0});const f=i?200:1e6,d=i?40:1e7,p={type:"inertia",velocity:s?e[c]:0,bounceStiffness:f,bounceDamping:d,timeConstant:750,restDelta:1,restSpeed:10,...o,...h};return this.startAxisValueAnimation(c,p)});return Promise.all(u).then(a)}startAxisValueAnimation(e,n){const s=this.getAxisMotionValue(e);return ye(this.visualElement,e),s.start(cn(e,s,0,n,this.visualElement,!1))}stopAnimation(){U(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){U(e=>{var n;return(n=this.getAxisMotionValue(e).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(e){var n;return(n=this.getAxisMotionValue(e).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(e){const n=`_drag${e.toUpperCase()}`,s=this.visualElement.getProps(),i=s[n];return i||this.visualElement.getValue(e,(s.initial?s.initial[e]:void 0)||0)}snapToCursor(e){U(n=>{const{drag:s}=this.getProps();if(!jt(n,s,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:r,max:a}=i.layout.layoutBox[n];o.set(e[n]-C(r,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:n}=this.getProps(),{projection:s}=this.visualElement;if(!ot(n)||!s||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};U(r=>{const a=this.getAxisMotionValue(r);if(a&&this.constraints!==!1){const l=a.get();i[r]=Sl({min:l,max:l},this.constraints[r])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",s.root&&s.root.updateScroll(),s.updateLayout(),this.resolveConstraints(),U(r=>{if(!jt(r,e,null))return;const a=this.getAxisMotionValue(r),{min:l,max:u}=this.constraints[r];a.set(C(l,u,i[r]))})}addListeners(){if(!this.visualElement.current)return;Ml.set(this.visualElement,this);const e=this.visualElement.current,n=Pt(e,"pointerdown",l=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(l)}),s=()=>{const{dragConstraints:l}=this.getProps();ot(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,o=i.addEventListener("measure",s);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),V.read(s);const r=Dt(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&(U(c=>{const h=this.getAxisMotionValue(c);h&&(this.originPoint[c]+=l[c].translate,h.set(h.get()+l[c].translate))}),this.visualElement.render())});return()=>{r(),n(),o(),a&&a()}}getProps(){const e=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:s=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:r=we,dragMomentum:a=!0}=e;return{...e,drag:n,dragDirectionLock:s,dragPropagation:i,dragConstraints:o,dragElastic:r,dragMomentum:a}}}function jt(t,e,n){return(e===!0||e===t)&&(n===null||n===t)}function El(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}class Ll extends J{constructor(e){super(e),this.removeGroupControls=j,this.removeListeners=j,this.controls=new Rl(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||j}unmount(){this.removeGroupControls(),this.removeListeners()}}const ns=t=>(e,n)=>{t&&V.postRender(()=>t(e,n))};class Fl extends J{constructor(){super(...arguments),this.removePointerDownListener=j}onPointerDown(e){this.session=new Ui(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Xi(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:n,onPan:s,onPanEnd:i}=this.node.getProps();return{onSessionStart:ns(e),onStart:ns(n),onMove:s,onEnd:(o,r)=>{delete this.session,i&&V.postRender(()=>i(o,r))}}}mount(){this.removePointerDownListener=Pt(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Nt={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ss(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const gt={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(S.test(t))t=parseFloat(t);else return t;const n=ss(t,e.target.x),s=ss(t,e.target.y);return`${n}% ${s}%`}},Bl={correct:(t,{treeScale:e,projectionDelta:n})=>{const s=t,i=Z.parse(t);if(i.length>5)return s;const o=Z.createTransformer(t),r=typeof i[0]!="number"?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;i[0+r]/=a,i[1+r]/=l;const u=C(a,l,.5);return typeof i[2+r]=="number"&&(i[2+r]/=u),typeof i[3+r]=="number"&&(i[3+r]/=u),o(i)}};class kl extends v.Component{componentDidMount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s,layoutId:i}=this.props,{projection:o}=e;to(jl),o&&(n.group&&n.group.add(o),s&&s.register&&i&&s.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),Nt.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:n,visualElement:s,drag:i,isPresent:o}=this.props,r=s.projection;return r&&(r.isPresent=o,i||e.layoutDependency!==n||n===void 0?r.willUpdate():this.safeToRemove(),e.isPresent!==o&&(o?r.promote():r.relegate()||V.postRender(()=>{const a=r.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),Ie.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s}=this.props,{projection:i}=e;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),s&&s.deregister&&s.deregister(i))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function Yi(t){const[e,n]=Es(),s=v.useContext(Re);return G.jsx(kl,{...t,layoutGroup:s,switchLayoutGroup:v.useContext(Ns),isPresent:e,safeToRemove:n})}const jl={borderRadius:{...gt,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:gt,borderTopRightRadius:gt,borderBottomLeftRadius:gt,borderBottomRightRadius:gt,boxShadow:Bl};function Ol(t,e,n){const s=B(t)?t:Vt(t);return s.start(cn("",s,e,n)),s.animation}function Il(t){return t instanceof SVGElement&&t.tagName!=="svg"}const Nl=(t,e)=>t.depth-e.depth;class Ul{constructor(){this.children=[],this.isDirty=!1}add(e){Je(this.children,e),this.isDirty=!0}remove(e){Qe(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Nl),this.isDirty=!1,this.children.forEach(e)}}function _l(t,e){const n=W.now(),s=({timestamp:i})=>{const o=i-n;o>=e&&(q(s),t(o-e))};return V.read(s,!0),()=>q(s)}const qi=["TopLeft","TopRight","BottomLeft","BottomRight"],Kl=qi.length,is=t=>typeof t=="string"?parseFloat(t):t,rs=t=>typeof t=="number"||S.test(t);function Wl(t,e,n,s,i,o){i?(t.opacity=C(0,n.opacity!==void 0?n.opacity:1,$l(s)),t.opacityExit=C(e.opacity!==void 0?e.opacity:1,0,Gl(s))):o&&(t.opacity=C(e.opacity!==void 0?e.opacity:1,n.opacity!==void 0?n.opacity:1,s));for(let r=0;r<Kl;r++){const a=`border${qi[r]}Radius`;let l=os(e,a),u=os(n,a);if(l===void 0&&u===void 0)continue;l||(l=0),u||(u=0),l===0||u===0||rs(l)===rs(u)?(t[a]=Math.max(C(is(l),is(u),s),0),(K.test(u)||K.test(l))&&(t[a]+="%")):t[a]=u}(e.rotate||n.rotate)&&(t.rotate=C(e.rotate||0,n.rotate||0,s))}function os(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const $l=Zi(0,.5,mi),Gl=Zi(.5,.95,j);function Zi(t,e,n){return s=>s<t?0:s>e?1:n(ht(t,e,s))}function as(t,e){t.min=e.min,t.max=e.max}function N(t,e){as(t.x,e.x),as(t.y,e.y)}function ls(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function us(t,e,n,s,i){return t-=e,t=zt(t,1/n,s),i!==void 0&&(t=zt(t,1/i,s)),t}function zl(t,e=0,n=1,s=.5,i,o=t,r=t){if(K.test(e)&&(e=parseFloat(e),e=C(r.min,r.max,e/100)-r.min),typeof e!="number")return;let a=C(o.min,o.max,s);t===o&&(a-=e),t.min=us(t.min,e,n,a,i),t.max=us(t.max,e,n,a,i)}function cs(t,e,[n,s,i],o,r){zl(t,e[n],e[s],e[i],e.scale,o,r)}const Hl=["x","scaleX","originX"],Xl=["y","scaleY","originY"];function hs(t,e,n,s){cs(t.x,e,Hl,n?n.x:void 0,s?s.x:void 0),cs(t.y,e,Xl,n?n.y:void 0,s?s.y:void 0)}function fs(t){return t.translate===0&&t.scale===1}function Ji(t){return fs(t.x)&&fs(t.y)}function ds(t,e){return t.min===e.min&&t.max===e.max}function Yl(t,e){return ds(t.x,e.x)&&ds(t.y,e.y)}function ps(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function Qi(t,e){return ps(t.x,e.x)&&ps(t.y,e.y)}function ms(t){return O(t.x)/O(t.y)}function gs(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class ql{constructor(){this.members=[]}add(e){Je(this.members,e),e.scheduleRender()}remove(e){if(Qe(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(e){const n=this.members.findIndex(i=>e===i);if(n===0)return!1;let s;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){s=o;break}}return s?(this.promote(s),!0):!1}promote(e,n){const s=this.lead;if(e!==s&&(this.prevLead=s,this.lead=e,e.show(),s)){s.instance&&s.scheduleRender(),e.scheduleRender(),e.resumeFrom=s,n&&(e.resumeFrom.preserveOpacity=!0),s.snapshot&&(e.snapshot=s.snapshot,e.snapshot.latestValues=s.animationValues||s.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:i}=e.options;i===!1&&s.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:n,resumingFrom:s}=e;n.onExitComplete&&n.onExitComplete(),s&&s.options.onExitComplete&&s.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Zl(t,e,n){let s="";const i=t.x.translate/e.x,o=t.y.translate/e.y,r=(n==null?void 0:n.z)||0;if((i||o||r)&&(s=`translate3d(${i}px, ${o}px, ${r}px) `),(e.x!==1||e.y!==1)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:u,rotate:c,rotateX:h,rotateY:f,skewX:d,skewY:p}=n;u&&(s=`perspective(${u}px) ${s}`),c&&(s+=`rotate(${c}deg) `),h&&(s+=`rotateX(${h}deg) `),f&&(s+=`rotateY(${f}deg) `),d&&(s+=`skewX(${d}deg) `),p&&(s+=`skewY(${p}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return(a!==1||l!==1)&&(s+=`scale(${a}, ${l})`),s||"none"}const et={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},xt=typeof window<"u"&&window.MotionDebug!==void 0,ce=["","X","Y","Z"],Jl={visibility:"hidden"},ys=1e3;let Ql=0;function he(t,e,n,s){const{latestValues:i}=e;i[t]&&(n[t]=i[t],e.setStaticValue(t,0),s&&(s[t]=0))}function tr(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=li(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:i,layoutId:o}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",V,!(i||o))}const{parent:s}=t;s&&!s.hasCheckedOptimisedAppear&&tr(s)}function er({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:s,resetTransform:i}){return class{constructor(r={},a=e==null?void 0:e()){this.id=Ql++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,xt&&(et.totalNodes=et.resolvedTargetDeltas=et.recalculatedProjection=0),this.nodes.forEach(nu),this.nodes.forEach(au),this.nodes.forEach(lu),this.nodes.forEach(su),xt&&window.MotionDebug.record(et)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=r,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new Ul)}addEventListener(r,a){return this.eventHandlers.has(r)||this.eventHandlers.set(r,new tn),this.eventHandlers.get(r).add(a)}notifyListeners(r,...a){const l=this.eventHandlers.get(r);l&&l.notify(...a)}hasListeners(r){return this.eventHandlers.has(r)}mount(r,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=Il(r),this.instance=r;const{layoutId:l,layout:u,visualElement:c}=this.options;if(c&&!c.current&&c.mount(r),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(u||l)&&(this.isLayoutDirty=!0),t){let h;const f=()=>this.root.updateBlockedByResize=!1;t(r,()=>{this.root.updateBlockedByResize=!0,h&&h(),h=_l(f,250),Nt.hasAnimatedSinceResize&&(Nt.hasAnimatedSinceResize=!1,this.nodes.forEach(xs))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&c&&(l||u)&&this.addEventListener("didUpdate",({delta:h,hasLayoutChanged:f,hasRelativeTargetChanged:d,layout:p})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const m=this.options.transition||c.getDefaultTransition()||du,{onLayoutAnimationStart:y,onLayoutAnimationComplete:g}=c.getProps(),T=!this.targetLayout||!Qi(this.targetLayout,p)||d,P=!f&&d;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||P||f&&(T||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(h,P);const b={...Xe(m,"layout"),onPlay:y,onComplete:g};(c.shouldReduceMotion||this.options.layoutRoot)&&(b.delay=0,b.type=!1),this.startAnimation(b)}else f||xs(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=p})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const r=this.getStack();r&&r.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,q(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(uu),this.animationId++)}getTransformTemplate(){const{visualElement:r}=this.options;return r&&r.getProps().transformTemplate}willUpdate(r=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&tr(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const h=this.path[c];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),r&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(vs);return}this.isUpdating||this.nodes.forEach(ru),this.isUpdating=!1,this.nodes.forEach(ou),this.nodes.forEach(tu),this.nodes.forEach(eu),this.clearAllSnapshots();const a=W.now();E.delta=X(0,1e3/60,a-E.timestamp),E.timestamp=a,E.isProcessing=!0,ne.update.process(E),ne.preRender.process(E),ne.render.process(E),E.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Ie.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(iu),this.sharedNodes.forEach(cu)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,V.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){V.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const r=this.layout;this.layout=this.measure(!1),this.layoutCorrected=R(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,r?r.layoutBox:void 0)}updateScroll(r="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===r&&(a=!1),a){const l=s(this.instance);this.scroll={animationId:this.root.animationId,phase:r,isRoot:l,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!i)return;const r=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!Ji(this.projectionDelta),l=this.getTransformTemplate(),u=l?l(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;r&&(a||tt(this.latestValues)||c)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(r=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return r&&(l=this.removeTransform(l)),pu(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){var r;const{visualElement:a}=this.options;if(!a)return R();const l=a.measureViewportBox();if(!(((r=this.scroll)===null||r===void 0?void 0:r.wasRoot)||this.path.some(mu))){const{scroll:c}=this.root;c&&(ut(l.x,c.offset.x),ut(l.y,c.offset.y))}return l}removeElementScroll(r){var a;const l=R();if(N(l,r),!((a=this.scroll)===null||a===void 0)&&a.wasRoot)return l;for(let u=0;u<this.path.length;u++){const c=this.path[u],{scroll:h,options:f}=c;c!==this.root&&h&&f.layoutScroll&&(h.wasRoot&&N(l,r),ut(l.x,h.offset.x),ut(l.y,h.offset.y))}return l}applyTransform(r,a=!1){const l=R();N(l,r);for(let u=0;u<this.path.length;u++){const c=this.path[u];!a&&c.options.layoutScroll&&c.scroll&&c!==c.root&&ct(l,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),tt(c.latestValues)&&ct(l,c.latestValues)}return tt(this.latestValues)&&ct(l,this.latestValues),l}removeTransform(r){const a=R();N(a,r);for(let l=0;l<this.path.length;l++){const u=this.path[l];if(!u.instance||!tt(u.latestValues))continue;Ve(u.latestValues)&&u.updateSnapshot();const c=R(),h=u.measurePageBox();N(c,h),hs(a,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return tt(this.latestValues)&&hs(a,this.latestValues),a}setTargetDelta(r){this.targetDelta=r,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(r){this.options={...this.options,...r,crossfade:r.crossfade!==void 0?r.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==E.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(r=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==l;if(!(r||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:h,layoutId:f}=this.options;if(!(!this.layout||!(h||f))){if(this.resolvedRelativeTargetAt=E.timestamp,!this.targetDelta&&!this.relativeTarget){const d=this.getClosestProjectingParent();d&&d.layout&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=R(),this.relativeTargetOrigin=R(),At(this.relativeTargetOrigin,this.layout.layoutBox,d.layout.layoutBox),N(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=R(),this.targetWithTransforms=R()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),vl(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):N(this.target,this.layout.layoutBox),zi(this.target,this.targetDelta)):N(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const d=this.getClosestProjectingParent();d&&!!d.resumingFrom==!!this.resumingFrom&&!d.options.layoutScroll&&d.target&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=R(),this.relativeTargetOrigin=R(),At(this.relativeTargetOrigin,this.target,d.target),N(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}xt&&et.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Ve(this.parent.latestValues)||Gi(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var r;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let u=!0;if((this.isProjectionDirty||!((r=this.parent)===null||r===void 0)&&r.isProjectionDirty)&&(u=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===E.timestamp&&(u=!1),u)return;const{layout:c,layoutId:h}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||h))return;N(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,d=this.treeScale.y;Cl(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=R());const{target:p}=a;if(!p){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(ls(this.prevProjectionDelta.x,this.projectionDelta.x),ls(this.prevProjectionDelta.y,this.projectionDelta.y)),St(this.projectionDelta,this.layoutCorrected,p,this.latestValues),(this.treeScale.x!==f||this.treeScale.y!==d||!gs(this.projectionDelta.x,this.prevProjectionDelta.x)||!gs(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",p)),xt&&et.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(r=!0){var a;if((a=this.options.visualElement)===null||a===void 0||a.scheduleRender(),r){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=lt(),this.projectionDelta=lt(),this.projectionDeltaWithTransform=lt()}setAnimationOrigin(r,a=!1){const l=this.snapshot,u=l?l.latestValues:{},c={...this.latestValues},h=lt();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=R(),d=l?l.source:void 0,p=this.layout?this.layout.source:void 0,m=d!==p,y=this.getStack(),g=!y||y.members.length<=1,T=!!(m&&!g&&this.options.crossfade===!0&&!this.path.some(fu));this.animationProgress=0;let P;this.mixTargetDelta=b=>{const x=b/1e3;Ts(h.x,r.x,x),Ts(h.y,r.y,x),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(At(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),hu(this.relativeTarget,this.relativeTargetOrigin,f,x),P&&Yl(this.relativeTarget,P)&&(this.isProjectionDirty=!1),P||(P=R()),N(P,this.relativeTarget)),m&&(this.animationValues=c,Wl(c,u,this.latestValues,x,T,g)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=x},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(r){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(q(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=V.update(()=>{Nt.hasAnimatedSinceResize=!0,this.currentAnimation=Ol(0,ys,{...r,onUpdate:a=>{this.mixTargetDelta(a),r.onUpdate&&r.onUpdate(a)},onComplete:()=>{r.onComplete&&r.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const r=this.getStack();r&&r.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(ys),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const r=this.getLead();let{targetWithTransforms:a,target:l,layout:u,latestValues:c}=r;if(!(!a||!l||!u)){if(this!==r&&this.layout&&u&&nr(this.options.animationType,this.layout.layoutBox,u.layoutBox)){l=this.target||R();const h=O(this.layout.layoutBox.x);l.x.min=r.target.x.min,l.x.max=l.x.min+h;const f=O(this.layout.layoutBox.y);l.y.min=r.target.y.min,l.y.max=l.y.min+f}N(a,l),ct(a,c),St(this.projectionDeltaWithTransform,this.layoutCorrected,a,c)}}registerSharedNode(r,a){this.sharedNodes.has(r)||this.sharedNodes.set(r,new ql),this.sharedNodes.get(r).add(a);const u=a.options.initialPromotionConfig;a.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(a):void 0})}isLead(){const r=this.getStack();return r?r.lead===this:!0}getLead(){var r;const{layoutId:a}=this.options;return a?((r=this.getStack())===null||r===void 0?void 0:r.lead)||this:this}getPrevLead(){var r;const{layoutId:a}=this.options;return a?(r=this.getStack())===null||r===void 0?void 0:r.prevLead:void 0}getStack(){const{layoutId:r}=this.options;if(r)return this.root.sharedNodes.get(r)}promote({needsReset:r,transition:a,preserveFollowOpacity:l}={}){const u=this.getStack();u&&u.promote(this,l),r&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const r=this.getStack();return r?r.relegate(this):!1}resetSkewAndRotation(){const{visualElement:r}=this.options;if(!r)return;let a=!1;const{latestValues:l}=r;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const u={};l.z&&he("z",r,u,this.animationValues);for(let c=0;c<ce.length;c++)he(`rotate${ce[c]}`,r,u,this.animationValues),he(`skew${ce[c]}`,r,u,this.animationValues);r.render();for(const c in u)r.setStaticValue(c,u[c]),this.animationValues&&(this.animationValues[c]=u[c]);r.scheduleRender()}getProjectionStyles(r){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return Jl;const u={visibility:""},c=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=Ot(r==null?void 0:r.pointerEvents)||"",u.transform=c?c(this.latestValues,""):"none",u;const h=this.getLead();if(!this.projectionDelta||!this.layout||!h.target){const m={};return this.options.layoutId&&(m.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,m.pointerEvents=Ot(r==null?void 0:r.pointerEvents)||""),this.hasProjected&&!tt(this.latestValues)&&(m.transform=c?c({},""):"none",this.hasProjected=!1),m}const f=h.animationValues||h.latestValues;this.applyTransformsToTarget(),u.transform=Zl(this.projectionDeltaWithTransform,this.treeScale,f),c&&(u.transform=c(f,u.transform));const{x:d,y:p}=this.projectionDelta;u.transformOrigin=`${d.origin*100}% ${p.origin*100}% 0`,h.animationValues?u.opacity=h===this?(l=(a=f.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:u.opacity=h===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const m in _t){if(f[m]===void 0)continue;const{correct:y,applyTo:g}=_t[m],T=u.transform==="none"?f[m]:y(f[m],h);if(g){const P=g.length;for(let b=0;b<P;b++)u[g[b]]=T}else u[m]=T}return this.options.layoutId&&(u.pointerEvents=h===this?Ot(r==null?void 0:r.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(r=>{var a;return(a=r.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(vs),this.root.sharedNodes.clear()}}}function tu(t){t.updateLayout()}function eu(t){var e;const n=((e=t.resumeFrom)===null||e===void 0?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&n&&t.hasListeners("didUpdate")){const{layoutBox:s,measuredBox:i}=t.layout,{animationType:o}=t.options,r=n.source!==t.layout.source;o==="size"?U(h=>{const f=r?n.measuredBox[h]:n.layoutBox[h],d=O(f);f.min=s[h].min,f.max=f.min+d}):nr(o,n.layoutBox,s)&&U(h=>{const f=r?n.measuredBox[h]:n.layoutBox[h],d=O(s[h]);f.max=f.min+d,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[h].max=t.relativeTarget[h].min+d)});const a=lt();St(a,s,n.layoutBox);const l=lt();r?St(l,t.applyTransform(i,!0),n.measuredBox):St(l,s,n.layoutBox);const u=!Ji(a);let c=!1;if(!t.resumeFrom){const h=t.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:f,layout:d}=h;if(f&&d){const p=R();At(p,n.layoutBox,f.layoutBox);const m=R();At(m,s,d.layoutBox),Qi(p,m)||(c=!0),h.options.layoutRoot&&(t.relativeTarget=m,t.relativeTargetOrigin=p,t.relativeParent=h)}}}t.notifyListeners("didUpdate",{layout:s,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:u,hasRelativeTargetChanged:c})}else if(t.isLead()){const{onExitComplete:s}=t.options;s&&s()}t.options.transition=void 0}function nu(t){xt&&et.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function su(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function iu(t){t.clearSnapshot()}function vs(t){t.clearMeasurements()}function ru(t){t.isLayoutDirty=!1}function ou(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function xs(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function au(t){t.resolveTargetDelta()}function lu(t){t.calcProjection()}function uu(t){t.resetSkewAndRotation()}function cu(t){t.removeLeadSnapshot()}function Ts(t,e,n){t.translate=C(e.translate,0,n),t.scale=C(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function Ps(t,e,n,s){t.min=C(e.min,n.min,s),t.max=C(e.max,n.max,s)}function hu(t,e,n,s){Ps(t.x,e.x,n.x,s),Ps(t.y,e.y,n.y,s)}function fu(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const du={duration:.45,ease:[.4,0,.1,1]},Ss=t=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),As=Ss("applewebkit/")&&!Ss("chrome/")?Math.round:j;function bs(t){t.min=As(t.min),t.max=As(t.max)}function pu(t){bs(t.x),bs(t.y)}function nr(t,e,n){return t==="position"||t==="preserve-aspect"&&!yl(ms(e),ms(n),.2)}function mu(t){var e;return t!==t.root&&((e=t.scroll)===null||e===void 0?void 0:e.wasRoot)}const gu=er({attachResizeListener:(t,e)=>Dt(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),fe={current:void 0},sr=er({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!fe.current){const t=new gu({});t.mount(window),t.setOptions({layoutScroll:!0}),fe.current=t}return fe.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),yu={pan:{Feature:Fl},drag:{Feature:Ll,ProjectionNode:sr,MeasureLayout:Yi}};function ws(t,e,n){const{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover",n==="Start");const i="onHover"+n,o=s[i];o&&V.postRender(()=>o(e,Lt(e)))}class vu extends J{mount(){const{current:e}=this.node;e&&(this.unmount=yo(e,n=>(ws(this.node,n,"Start"),s=>ws(this.node,s,"End"))))}unmount(){}}class xu extends J{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Et(Dt(this.node.current,"focus",()=>this.onFocus()),Dt(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Vs(t,e,n){const{props:s}=t;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap",n==="Start");const i="onTap"+(n==="End"?"":n),o=s[i];o&&V.postRender(()=>o(e,Lt(e)))}class Tu extends J{mount(){const{current:e}=this.node;e&&(this.unmount=Po(e,n=>(Vs(this.node,n,"Start"),(s,{success:i})=>Vs(this.node,s,i?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const De=new WeakMap,de=new WeakMap,Pu=t=>{const e=De.get(t.target);e&&e(t)},Su=t=>{t.forEach(Pu)};function Au({root:t,...e}){const n=t||document;de.has(n)||de.set(n,{});const s=de.get(n),i=JSON.stringify(e);return s[i]||(s[i]=new IntersectionObserver(Su,{root:t,...e})),s[i]}function bu(t,e,n){const s=Au(e);return De.set(t,n),s.observe(t),()=>{De.delete(t),s.unobserve(t)}}const wu={some:0,all:1};class Vu extends J{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:n,margin:s,amount:i="some",once:o}=e,r={root:n?n.current:void 0,rootMargin:s,threshold:typeof i=="number"?i:wu[i]},a=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,o&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:h}=this.node.getProps(),f=u?c:h;f&&f(l)};return bu(this.node.current,r,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:n}=this.node;["amount","margin","root"].some(Cu(e,n))&&this.startObserver()}unmount(){}}function Cu({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}const Du={inView:{Feature:Vu},tap:{Feature:Tu},focus:{Feature:xu},hover:{Feature:vu}},Mu={layout:{ProjectionNode:sr,MeasureLayout:Yi}},Me={current:null},ir={current:!1};function Ru(){if(ir.current=!0,!!Fe)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Me.current=t.matches;t.addListener(e),e()}else Me.current=!1}const Eu=[...Mi,F,Z],Lu=t=>Eu.find(Di(t)),Cs=new WeakMap;function Fu(t,e,n){for(const s in e){const i=e[s],o=n[s];if(B(i))t.addValue(s,i);else if(B(o))t.addValue(s,Vt(i,{owner:t}));else if(o!==i)if(t.hasValue(s)){const r=t.getValue(s);r.liveStyle===!0?r.jump(i):r.hasAnimated||r.set(i)}else{const r=t.getStaticValue(s);t.addValue(s,Vt(r!==void 0?r:i,{owner:t}))}}for(const s in n)e[s]===void 0&&t.removeValue(s);return e}const Ds=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Bu{scrapeMotionValuesFromProps(e,n,s){return{}}constructor({parent:e,props:n,presenceContext:s,reducedMotionConfig:i,blockInitialAnimation:o,visualState:r},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=an,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const d=W.now();this.renderScheduledAt<d&&(this.renderScheduledAt=d,V.render(this.render,!1,!0))};const{latestValues:l,renderState:u,onUpdate:c}=r;this.onUpdate=c,this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=u,this.parent=e,this.props=n,this.presenceContext=s,this.depth=e?e.depth+1:0,this.reducedMotionConfig=i,this.options=a,this.blockInitialAnimation=!!o,this.isControllingVariants=Zt(n),this.isVariantNode=Os(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:h,...f}=this.scrapeMotionValuesFromProps(n,{},this);for(const d in f){const p=f[d];l[d]!==void 0&&B(p)&&p.set(l[d],!1)}}mount(e){this.current=e,Cs.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,s)=>this.bindToMotionValue(s,n)),ir.current||Ru(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Me.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){Cs.delete(this.current),this.projection&&this.projection.unmount(),q(this.notifyUpdate),q(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features){const n=this.features[e];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(e,n){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const s=it.has(e),i=n.on("change",a=>{this.latestValues[e]=a,this.props.onUpdate&&V.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);let r;window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,e,n)),this.valueSubscriptions.set(e,()=>{i(),o(),r&&r(),n.owner&&n.stop()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}updateFeatures(){let e="animation";for(e in ft){const n=ft[e];if(!n)continue;const{isEnabled:s,Feature:i}=n;if(!this.features[e]&&i&&s(this.props)&&(this.features[e]=new i(this)),this.features[e]){const o=this.features[e];o.isMounted?o.update():(o.mount(),o.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):R()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}update(e,n){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let s=0;s<Ds.length;s++){const i=Ds[s];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const o="on"+i,r=e[o];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=Fu(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(e),()=>n.variantChildren.delete(e)}addValue(e,n){const s=this.values.get(e);n!==s&&(s&&this.removeValue(e),this.bindToMotionValue(e,n),this.values.set(e,n),this.latestValues[e]=n.get())}removeValue(e){this.values.delete(e);const n=this.valueSubscriptions.get(e);n&&(n(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return s===void 0&&n!==void 0&&(s=Vt(n===null?void 0:n,{owner:this}),this.addValue(e,s)),s}readValue(e,n){var s;let i=this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:(s=this.getBaseTargetFromProps(this.props,e))!==null&&s!==void 0?s:this.readValueFromInstance(this.current,e,this.options);return i!=null&&(typeof i=="string"&&(Vi(i)||yi(i))?i=parseFloat(i):!Lu(i)&&Z.test(n)&&(i=Ai(e,n)),this.setBaseTarget(e,B(i)?i.get():i)),B(i)?i.get():i}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){var n;const{initial:s}=this.props;let i;if(typeof s=="string"||typeof s=="object"){const r=Ue(this.props,s,(n=this.presenceContext)===null||n===void 0?void 0:n.custom);r&&(i=r[e])}if(s&&i!==void 0)return i;const o=this.getBaseTargetFromProps(this.props,e);return o!==void 0&&!B(o)?o:this.initialValues[e]!==void 0&&i===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new tn),this.events[e].add(n)}notify(e,...n){this.events[e]&&this.events[e].notify(...n)}}class rr extends Bu{constructor(){super(...arguments),this.KeyframeResolver=Ri}sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){return e.style?e.style[n]:void 0}removeValueFromRenderState(e,{vars:n,style:s}){delete n[e],delete s[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;B(e)&&(this.childSubscription=e.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function ku(t){return window.getComputedStyle(t)}class ju extends rr{constructor(){super(...arguments),this.type="html",this.renderInstance=zs}readValueFromInstance(e,n){if(it.has(n)){const s=on(n);return s&&s.default||0}else{const s=ku(e),i=(Ws(n)?s.getPropertyValue(n):s[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(e,{transformPagePoint:n}){return Hi(e,n)}build(e,n,s){We(e,n,s.transformTemplate)}scrapeMotionValuesFromProps(e,n,s){return He(e,n,s)}}class Ou extends rr{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=R}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){if(it.has(n)){const s=on(n);return s&&s.default||0}return n=Hs.has(n)?n:Oe(n),e.getAttribute(n)}scrapeMotionValuesFromProps(e,n,s){return qs(e,n,s)}build(e,n,s){$e(e,n,this.isSVGTag,s.transformTemplate)}renderInstance(e,n,s,i){Xs(e,n,s,i)}mount(e){this.isSVGTag=ze(e.tagName),super.mount(e)}}const Iu=(t,e)=>Ne(t)?new Ou(e):new ju(e,{allowProjection:t!==v.Fragment}),Nu=uo({...ll,...Du,...yu,...Mu},Iu),Gu=br(Nu);export{Wu as A,G as j,Gu as m};
