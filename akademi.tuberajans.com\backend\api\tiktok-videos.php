<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../classes/Auth.php';
require_once __DIR__ . '/../utils/helpers.php';

// CORS headers
header('Access-Control-Allow-Origin: https://akademi.tuberajans.com');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');
header('Content-Type: application/json');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Session başlat
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

try {
    // Veritabanı bağlantısını al
    $db = getDB();
    
    // Auth sınıfını başlat
    $auth = new Auth($db);
    
    // Kullanıcı kontrolü
    $user = $auth->getCurrentUser();
    if (!$user) {
        sendErrorResponse('Giriş yapmanız gerekiyor', 401);
        exit;
    }

    // TikTok Manager'ı başlat
    require_once __DIR__ . '/../classes/TikTokManager.php';
    $tiktokManager = new TikTokManager($db);

    // Token kontrolü ve yenileme
    $tokenResult = $tiktokManager->checkAndRefreshToken($user['id']);
    if (!$tokenResult['success']) {
        sendErrorResponse($tokenResult['error'], 404);
    }

    // Kullanıcının videolarını çek
    $stmt = $db->prepare("
        SELECT 
            video_id,
            title,
            description,
            cover_image_url,
            share_url,
            duration,
            view_count,
            like_count,
            comment_count,
            share_count,
            engagement_rate,
            hashtags,
            publish_time,
            created_at
        FROM user_tiktok_videos 
        WHERE user_id = ? 
        ORDER BY publish_time DESC 
        LIMIT 20
    ");
    
    $stmt->execute([$user['id']]);
    $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Video verilerini işle
    foreach ($videos as &$video) {
        // Hashtag'leri JSON'dan array'e çevir
        if ($video['hashtags']) {
            $video['hashtags'] = json_decode($video['hashtags'], true) ?: [];
        } else {
            $video['hashtags'] = [];
        }

        // Sayıları integer'a çevir
        $video['view_count'] = (int)$video['view_count'];
        $video['like_count'] = (int)$video['like_count'];
        $video['comment_count'] = (int)$video['comment_count'];
        $video['share_count'] = (int)$video['share_count'];
        $video['duration'] = (int)$video['duration'];
        $video['engagement_rate'] = (float)$video['engagement_rate'];

        // Thumbnail URL'ini düzelt
        if (empty($video['cover_image_url']) || $video['cover_image_url'] === 'NULL' || $video['cover_image_url'] === 'null') {
            // Eğer cover_image_url boşsa, placeholder kullan
            $video['cover_image_url'] = "https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=300&h=400&fit=crop&random=" . $video['video_id'];
        }

        // Frontend için thumbnail field'ı da ekle
        $video['thumbnail'] = $video['cover_image_url'];

        // Tarih formatını düzelt
        if (!empty($video['publish_time'])) {
            // Unix timestamp'i ISO formatına çevir
            if (is_numeric($video['publish_time'])) {
                $video['publish_time'] = date('Y-m-d H:i:s', $video['publish_time']);
            }
            $video['create_time'] = $video['publish_time'];
        } else if (!empty($video['created_at'])) {
            $video['create_time'] = $video['created_at'];
        }
    }
    
    // Analytics hesapla
    $analytics = [
        'total_videos' => count($videos),
        'total_views' => 0,
        'total_likes' => 0,
        'total_comments' => 0,
        'total_shares' => 0,
        'avg_engagement_rate' => 0,
        'top_video' => null
    ];
    
    if (!empty($videos)) {
        $totalEngagement = 0;
        $topVideo = $videos[0];
        
        foreach ($videos as $video) {
            $analytics['total_views'] += $video['view_count'];
            $analytics['total_likes'] += $video['like_count'];
            $analytics['total_comments'] += $video['comment_count'];
            $analytics['total_shares'] += $video['share_count'];
            $totalEngagement += $video['engagement_rate'];
            
            // En çok görüntülenen video
            if ($video['view_count'] > $topVideo['view_count']) {
                $topVideo = $video;
            }
        }
        
        $videoCount = count($videos);
        $analytics['avg_engagement_rate'] = round($totalEngagement / $videoCount, 2);
        $analytics['engagement_rate'] = round($totalEngagement / $videoCount, 2); // Frontend için

        // Ortalama değerleri hesapla
        $analytics['average_views'] = round($analytics['total_views'] / $videoCount);
        $analytics['average_likes'] = round($analytics['total_likes'] / $videoCount);
        $analytics['average_comments'] = round($analytics['total_comments'] / $videoCount);
        $analytics['average_shares'] = round($analytics['total_shares'] / $videoCount);

        $analytics['top_video'] = $topVideo;

        // Performance data için video listesini hazırla
        $performanceData = [];
        foreach ($videos as $video) {
            $performanceData[] = [
                'date' => date('Y-m-d', strtotime($video['publish_time'])),
                'title' => $video['title'],
                'views' => $video['view_count'],
                'likes' => $video['like_count'],
                'comments' => $video['comment_count'],
                'shares' => $video['share_count'],
                'engagement_rate' => $video['engagement_rate']
            ];
        }
        $analytics['performance_data'] = $performanceData;
    }

    sendSuccessResponse([
        'videos' => $videos,
        'analytics' => $analytics
    ], 'Videolar başarıyla alındı');
    
} catch (Exception $e) {
    writeLog('TikTok Videos API Error: ' . $e->getMessage(), 'tiktok_videos_error.log');
    sendErrorResponse('Sunucu hatası', 500);
}
?>
