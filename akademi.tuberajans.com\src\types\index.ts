/**
 * Global Type Definitions
 * Tüm proje genelinde <PERSON> type'lar
 */

// User Types
export interface User {
  username: string;
  email: string;
  role: 'user' | 'admin';
  status?: 'active' | 'inactive';
  profile_image?: string;
  bio?: string;
  social_media?: Record<string, string>;
  permissions?: Record<string, unknown>;
  
  // TikTok related fields
  tiktok_username?: string;
  tiktok_open_id?: string;
  tiktok_union_id?: string;
  tiktok_display_name?: string;
  tiktok_avatar_url?: string;
  tiktok_bio?: string;
  followers_count?: number;
  following_count?: number;
  likes_count?: number;
  video_count?: number;
  is_verified?: boolean;
  tiktok_linked_at?: string;
  
  // Agency fields
  is_agency_publisher?: boolean;
  publisher_verified_at?: string;
  
  // Timestamps
  created_at?: string;
  updated_at?: string;
}

// TikTok Types
export interface TikTokUser {
  username: string;
  tiktok_username?: string;
  display_name?: string;
  avatar_url?: string;
  linked_at?: string;
  followers_count?: number;
  following_count?: number;
  likes_count?: number;
  video_count?: number;
  is_verified?: boolean;
  bio?: string;
  tiktok_bio?: string;
  tiktok_open_id?: string;
}

export interface TikTokVideo {
  id: string;
  video_id: string;
  title?: string;
  video_description?: string;
  cover_image_url?: string;
  thumbnail?: string;
  share_url?: string;
  duration?: number;
  height?: number;
  width?: number;
  create_time?: string;
  view_count?: number;
  like_count?: number;
  comment_count?: number;
  share_count?: number;
  engagement_rate?: number;
  hashtags?: string[];
}

export interface TikTokAnalytics {
  performance_data?: Array<{
    date: string;
    views: number;
    likes: number;
    comments: number;
    shares: number;
    engagement_rate: number;
  }>;
  total_views?: number;
  total_likes?: number;
  total_comments?: number;
  total_shares?: number;
  average_engagement_rate?: number;
}

// Content Types
export interface Announcement {
  id: number;
  title: string;
  content: string;
  type: 'Genel' | 'Etkinlik' | 'Önemli' | 'Marka İş Birliği';
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  status?: 'draft' | 'published' | 'archived';
  created_by?: number;
  target_audience?: 'all' | 'publishers' | 'admins';
  expires_at?: string;
  created_at: string;
  updated_at?: string;
}

export interface Course {
  id: number;
  title: string;
  description: string;
  content: string;
  category: string;
  image?: string;
  icon?: string;
  featured?: boolean;
  status?: 'draft' | 'active' | 'archived';
  created_by?: number;
  created_at: string;
  updated_at?: string;
}

export interface Event {
  id: number;
  title: string;
  description: string;
  event_date: string;
  location?: string;
  type: 'webinar' | 'meeting' | 'workshop' | 'conference';
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled';
  max_participants?: number;
  created_by?: number;
  created_at: string;
  updated_at?: string;
}

export interface SupportTicket {
  id: number;
  user_id: number;
  title: string;
  description: string;
  category: 'technical' | 'ban_appeal' | 'restriction' | 'general';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  assigned_to?: number;
  created_at: string;
  updated_at?: string;
}

export interface SupportTicketReply {
  id: number;
  ticket_id: number;
  user_id: number;
  content: string;
  is_staff_reply: boolean;
  created_at: string;
}

export interface Notification {
  id: number;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  source: string;
  date: string;
  read: boolean;
  link?: string;
}

export interface FeedPost {
  id: number;
  user_id: number;
  content: string;
  media_url?: string;
  media_type?: 'image' | 'video' | 'gif';
  created_at: string;
  updated_at?: string;
  user?: {
    id: number;
    username: string;
    profile_image?: string;
  };
  likes_count?: number;
  comments_count?: number;
  is_liked?: boolean;
  comments?: FeedComment[];
}

export interface FeedComment {
  id: number;
  post_id: number;
  user_id: number;
  content: string;
  created_at: string;
  updated_at?: string;
  user?: {
    id: number;
    username: string;
    profile_image?: string;
  };
}

// API Response Types
export interface ApiResponse<T = unknown> {
  status: 'success' | 'error';
  message: string;
  timestamp?: string;
  data?: T;
  errors?: Record<string, string[]>;
  code?: number;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination?: {
    current_page: number;
    total_pages: number;
    total_items: number;
    items_per_page: number;
  };
}

// Context Types
export interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  login: (username: string, password: string) => Promise<{ success: boolean; message?: string }>;
  logout: () => void;
  isAuthenticated: boolean;
}

export interface TikTokContextType {
  tiktokUser: TikTokUser | null;
  loading: boolean;
  error: string | null;
  linkTikTokAccount: (tiktokData: Record<string, unknown>) => Promise<boolean>;
  unlinkTikTokAccount: () => Promise<boolean>;
  refreshTikTokData: () => Promise<void>;
  clearError: () => void;
}

export interface SidebarContextType {
  isSidebarOpen: boolean;
  toggleSidebar: () => void;
  closeSidebar: () => void;
  openSidebar: () => void;
}

// Component Props Types
export interface DashboardProps {
  onLogout: () => void;
}

export interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  color?: 'primary' | 'secondary' | 'white' | 'gray';
  text?: string;
  fullScreen?: boolean;
  className?: string;
}

// Utility Types
export type Theme = 'light' | 'dark';
export type Language = 'tr' | 'en';
export type DeviceType = 'mobile' | 'tablet' | 'desktop';

// Form Types
export interface LoginFormData {
  username: string;
  password: string;
}

export interface RegisterFormData {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

export interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
  timestamp: string;
}

// Environment Types
export interface ImportMetaEnv {
  readonly VITE_API_URL: string;
  readonly VITE_TIKTOK_CLIENT_KEY: string;
  readonly VITE_TIKTOK_CLIENT_SECRET: string;
  readonly VITE_TIKTOK_REDIRECT_URI: string;
  readonly VITE_DISABLE_AUTH: string;
  readonly MODE: string;
  readonly DEV: boolean;
  readonly PROD: boolean;
}

export interface ImportMeta {
  readonly env: ImportMetaEnv;
}
