import axios from 'axios';
import { API_CONFIG, SECURITY_CONFIG } from '../config';

// Token'ı localStorage'dan al
const getAuthHeaders = () => {
  const token = localStorage.getItem(SECURITY_CONFIG.TOKEN_KEY);
  return token ? { Authorization: `Bear<PERSON> ${token}` } : {};
};

// E-posta gönderme için tip tanımları
export interface EmailData {
  emails: string[];
  subject: string;
  content: string;
  fromEmail?: string;
  attachments?: Array<{
    filename: string;
    content: string;
    contentType: string;
  }>;
}

export interface SingleEmailData {
  email: string;
  subject: string;
  content: string;
  fromEmail?: string;
  attachments?: Array<{
    filename: string;
    content: string;
    contentType: string;
  }>;
}

// Tekil e-posta gönderme
export const sendSingleEmail = async (emailData: SingleEmailData) => {
  try {
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.SEND_EMAIL}`,
      {
        emails: [emailData.email],
        subject: emailData.subject,
        content: emailData.content,
        fromEmail: emailData.fromEmail,
        attachments: emailData.attachments || []
      },
      {
        headers: {
          ...getAuthHeaders(),
          'Content-Type': 'application/json'
        }
      }
    );

    return response.data;
  } catch (error) {
    console.error('Tekil e-posta gönderme hatası:', error);
    throw error;
  }
};

// Toplu e-posta gönderme
export const sendBulkEmail = async (emailData: EmailData) => {
  try {
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.SEND_EMAIL}`,
      emailData,
      {
        headers: {
          ...getAuthHeaders(),
          'Content-Type': 'application/json'
        }
      }
    );

    return response.data;
  } catch (error) {
    console.error('Toplu e-posta gönderme hatası:', error);
    throw error;
  }
};

// Gmail tarzı sade e-posta template'leri (Hiç CSS yok)
export const createEmailTemplate = (
  recipientName: string,
  customMessage: string = '',
  templateType: 'general' | 'weekly_report' | 'custom' = 'general'
) => {
  // Sadece düz metin, hiç CSS yok
  let content = '';

  switch (templateType) {
    case 'weekly_report':
      content = `Merhaba ${recipientName},

Bu hafta sizin için hazırladığımız performans raporunuz hazır.

${customMessage || ''}`;
      break;

    case 'custom':
      content = `Merhaba ${recipientName},

${customMessage || ''}`;
      break;

    default: // general
      content = `Merhaba ${recipientName},

${customMessage || ''}`;
  }

  return content;
};

// E-posta şablon yönetimi
export interface EmailTemplate {
  id: number;
  name: string;
  type: string;
  subject: string;
  content: string;
  created_at: string;
  updated_at?: string;
}

// Şablonları getir
export const getEmailTemplates = async (): Promise<EmailTemplate[]> => {
  try {
    const response = await axios.get(
      `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.EMAIL_TEMPLATES}`,
      {
        headers: getAuthHeaders()
      }
    );
    return response.data.templates || [];
  } catch (error) {
    console.error('Şablonları getirme hatası:', error);
    throw error;
  }
};

// Yeni şablon ekle
export const createNewEmailTemplate = async (template: Omit<EmailTemplate, 'id' | 'created_at' | 'updated_at'>) => {
  try {
    const response = await axios.post(
      `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.EMAIL_TEMPLATES}`,
      template,
      {
        headers: {
          ...getAuthHeaders(),
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data.template;
  } catch (error) {
    console.error('Şablon oluşturma hatası:', error);
    throw error;
  }
};

// Şablon güncelle
export const updateEmailTemplate = async (template: EmailTemplate) => {
  try {
    const response = await axios.put(
      `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.EMAIL_TEMPLATES}`,
      template,
      {
        headers: {
          ...getAuthHeaders(),
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data.template;
  } catch (error) {
    console.error('Şablon güncelleme hatası:', error);
    throw error;
  }
};

// Şablon sil
export const deleteEmailTemplate = async (id: number) => {
  try {
    const response = await axios.delete(
      `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.EMAIL_TEMPLATES}`,
      {
        data: { id },
        headers: {
          ...getAuthHeaders(),
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data;
  } catch (error) {
    console.error('Şablon silme hatası:', error);
    throw error;
  }
};
