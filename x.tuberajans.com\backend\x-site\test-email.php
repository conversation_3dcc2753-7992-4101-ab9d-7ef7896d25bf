<?php
// Test SendGrid API
ini_set('display_errors', 1);
error_reporting(E_ALL);
header('Content-Type: application/json; charset=utf-8');

require_once __DIR__ . '/../config/config.php';

// Test SendGrid API key
$sendgridApiKey = getenv('SENDGRID_API_KEY') ?: (defined('SENDGRID_API_KEY') ? SENDGRID_API_KEY : null);

echo json_encode([
    'sendgrid_api_key_exists' => !empty($sendgridApiKey),
    'sendgrid_api_key_length' => strlen($sendgridApiKey ?: ''),
    'sendgrid_api_key_starts_with_sg' => strpos($sendgridApiKey ?: '', 'SG.') === 0,
    'config_loaded' => defined('SENDGRID_API_KEY'),
    'env_var_exists' => !empty(getenv('SENDGRID_API_KEY'))
]);
